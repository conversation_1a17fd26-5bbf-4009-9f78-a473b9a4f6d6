// إصلاح شامل لجميع العملات في التطبيق
class CurrencyFixer {
    constructor() {
        this.init();
    }

    init() {
        console.log('🔧 Starting comprehensive currency fix...');
        
        // إصلاح العملة فور تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                this.fixAllCurrencies();
            }, 500); // انتظار قليل لضمان تحميل جميع العناصر
        });

        // إصلاح العملة عند تغيير إعدادات الشركة
        window.addEventListener('companyDataUpdated', () => {
            setTimeout(() => {
                this.fixAllCurrencies();
            }, 100);
        });

        // إصلاح العملة عند تغيير العملة
        window.addEventListener('currencyChanged', () => {
            setTimeout(() => {
                this.fixAllCurrencies();
            }, 100);
        });
    }

    // إصلاح شامل لجميع العملات
    fixAllCurrencies() {
        console.log('🔄 Fixing all currencies in the application...');
        
        try {
            // الحصول على العملة الصحيحة
            const currencyInfo = this.getCurrentCurrencyInfo();
            console.log('Current currency info:', currencyInfo);

            // إصلاح العناصر المختلفة
            this.fixCurrencySymbols(currencyInfo.symbol);
            this.fixAmountDisplays(currencyInfo);
            this.fixStatistics(currencyInfo);
            this.fixTables(currencyInfo);
            this.fixForms(currencyInfo);
            this.fixSpecificElements(currencyInfo);

            console.log('✅ All currencies fixed successfully');
        } catch (error) {
            console.error('❌ Error fixing currencies:', error);
        }
    }

    // الحصول على معلومات العملة الحالية
    getCurrentCurrencyInfo() {
        if (window.currencyManager) {
            return window.currencyManager.getCurrentCurrencyInfo();
        }

        // Fallback
        const companyData = this.getCompanyData();
        const currency = companyData.currency || 'SAR';
        const symbol = companyData.currencySymbol || this.getCurrencySymbol(currency);
        
        return {
            code: currency,
            symbol: symbol,
            name: this.getCurrencyName(currency),
            position: 'after'
        };
    }

    // إصلاح رموز العملة
    fixCurrencySymbols(symbol) {
        // إصلاح عناصر رمز العملة المباشرة
        document.querySelectorAll('.currency-symbol, [data-currency-symbol]').forEach(el => {
            el.textContent = symbol;
        });

        // إصلاح العناصر التي تحتوي على كود العملة بدلاً من الرمز
        document.querySelectorAll('*').forEach(el => {
            if (el.textContent && el.textContent.includes(' SAR') && !el.querySelector('*')) {
                el.textContent = el.textContent.replace(/ SAR/g, ` ${symbol}`);
            }
            if (el.textContent && el.textContent.includes(' YER') && !el.querySelector('*')) {
                el.textContent = el.textContent.replace(/ YER/g, ` ${symbol}`);
            }
            if (el.textContent && el.textContent.includes(' USD') && !el.querySelector('*')) {
                el.textContent = el.textContent.replace(/ USD/g, ` ${symbol}`);
            }
        });
    }

    // إصلاح عرض المبالغ
    fixAmountDisplays(currencyInfo) {
        // إصلاح العناصر التي تحتوي على data-amount
        document.querySelectorAll('[data-amount]').forEach(el => {
            const amount = parseFloat(el.dataset.amount) || 0;
            el.textContent = this.formatCurrency(amount, currencyInfo);
        });

        // إصلاح عناصر الأرصدة والمبالغ
        document.querySelectorAll('.balance-display, .amount-display, .total-amount, .stat-amount').forEach(el => {
            const amount = this.extractAmount(el.textContent);
            if (amount !== null) {
                el.textContent = this.formatCurrency(amount, currencyInfo);
                el.dataset.amount = amount;
            }
        });
    }

    // إصلاح الإحصائيات
    fixStatistics(currencyInfo) {
        // إصلاح إحصائيات الموظفين
        const totalSalariesElement = document.getElementById('totalSalaries');
        if (totalSalariesElement) {
            const amount = this.extractAmount(totalSalariesElement.textContent);
            if (amount !== null) {
                totalSalariesElement.textContent = this.formatCurrency(amount, currencyInfo);
                totalSalariesElement.dataset.amount = amount;
            }
        }

        // إصلاح إحصائيات الصناديق
        const totalBalanceElement = document.getElementById('totalBalance');
        if (totalBalanceElement) {
            const amount = this.extractAmount(totalBalanceElement.textContent);
            if (amount !== null) {
                totalBalanceElement.textContent = this.formatCurrency(amount, currencyInfo);
                totalBalanceElement.dataset.amount = amount;
            }
        }

        // إصلاح إحصائيات أخرى
        document.querySelectorAll('#totalAmount, #currentBalance, #subtotalAmount, #taxAmount').forEach(el => {
            const amount = this.extractAmount(el.textContent);
            if (amount !== null) {
                el.textContent = this.formatCurrency(amount, currencyInfo);
                el.dataset.amount = amount;
            }
        });
    }

    // إصلاح الجداول
    fixTables(currencyInfo) {
        // إصلاح خلايا الجداول التي تحتوي على مبالغ
        document.querySelectorAll('td, th').forEach(cell => {
            if (cell.dataset.amount) {
                const amount = parseFloat(cell.dataset.amount) || 0;
                cell.textContent = this.formatCurrency(amount, currencyInfo);
            } else {
                const amount = this.extractAmount(cell.textContent);
                if (amount !== null && this.isCurrencyCell(cell)) {
                    cell.textContent = this.formatCurrency(amount, currencyInfo);
                    cell.dataset.amount = amount;
                }
            }
        });
    }

    // إصلاح النماذج
    fixForms(currencyInfo) {
        // تحديث خيارات العملة في النماذج
        document.querySelectorAll('select[name="currency"], #currency, #baseCurrency').forEach(select => {
            if (select.value !== currencyInfo.code) {
                select.value = currencyInfo.code;
            }
        });

        // تحديث حقول رمز العملة
        document.querySelectorAll('input[name="currencySymbol"], #currencySymbol').forEach(input => {
            if (input.value !== currencyInfo.symbol) {
                input.value = currencyInfo.symbol;
            }
        });
    }

    // إصلاح عناصر محددة
    fixSpecificElements(currencyInfo) {
        // إصلاح عناصر نقطة البيع
        document.querySelectorAll('.product-price, .cart-item-price, .total-display').forEach(el => {
            const amount = this.extractAmount(el.textContent);
            if (amount !== null) {
                el.textContent = this.formatCurrency(amount, currencyInfo);
            }
        });

        // إصلاح عناصر الفواتير
        document.querySelectorAll('.invoice-amount, .receipt-amount').forEach(el => {
            const amount = this.extractAmount(el.textContent);
            if (amount !== null) {
                el.textContent = this.formatCurrency(amount, currencyInfo);
            }
        });
    }

    // استخراج المبلغ من النص
    extractAmount(text) {
        if (!text) return null;
        
        // إزالة الفواصل والرموز والحصول على الرقم
        const cleanText = text.replace(/[^\d.-]/g, '');
        const amount = parseFloat(cleanText);
        
        return isNaN(amount) ? null : amount;
    }

    // تحديد ما إذا كانت الخلية تحتوي على عملة
    isCurrencyCell(cell) {
        const text = cell.textContent.toLowerCase();
        return text.includes('ر.س') || text.includes('sar') || text.includes('$') || 
               text.includes('€') || text.includes('د.إ') || text.includes('ر.ي') ||
               /\d+[.,]\d+/.test(text); // يحتوي على أرقام بفواصل عشرية
    }

    // تنسيق العملة
    formatCurrency(amount, currencyInfo) {
        if (window.currencyManager) {
            return window.currencyManager.formatCurrency(amount);
        }
        
        // Fallback formatting
        const formattedAmount = parseFloat(amount).toFixed(2);
        return `${formattedAmount} ${currencyInfo.symbol}`;
    }

    // الحصول على بيانات الشركة
    getCompanyData() {
        try {
            const data = localStorage.getItem('anwar_bakery_company');
            return data ? JSON.parse(data) : {};
        } catch (error) {
            console.error('Error parsing company data:', error);
            return {};
        }
    }

    // الحصول على رمز العملة
    getCurrencySymbol(currency) {
        const symbols = {
            'SAR': 'ر.س',
            'YER': '﷼',
            'USD': '$',
            'EUR': '€',
            'AED': 'د.إ',
            'KWD': 'د.ك',
            'QAR': 'ر.ق'
        };
        return symbols[currency] || 'ر.س';
    }

    // الحصول على اسم العملة
    getCurrencyName(currency) {
        const names = {
            'SAR': 'ريال سعودي',
            'YER': 'ريال يمني',
            'USD': 'دولار أمريكي',
            'EUR': 'يورو',
            'AED': 'درهم إماراتي',
            'KWD': 'دينار كويتي',
            'QAR': 'ريال قطري'
        };
        return names[currency] || 'ريال سعودي';
    }
}

// إنشاء مثيل عام لمصحح العملة
window.currencyFixer = new CurrencyFixer();

// وظيفة مساعدة عامة
window.fixAllCurrencies = () => window.currencyFixer.fixAllCurrencies();

console.log('✅ Currency Fixer loaded successfully');
