// إصلاح تنسيق الأرقام والعملة
(function() {
    'use strict';
    
    // وظيفة لتنسيق الأرقام بالشكل الإنجليزي
    function formatNumber(number) {
        // تحويل إلى رقم إنجليزي مع فواصل
        return Number(number).toLocaleString('en-US');
    }
    
    // وظيفة لتنسيق العملة
    function formatCurrency(amount) {
        const formattedNumber = formatNumber(amount);
        const currencySymbol = getCurrentCurrencySymbol();
        return `${formattedNumber} ${currencySymbol}`;
    }
    
    // الحصول على رمز العملة الحالي
    function getCurrentCurrencySymbol() {
        try {
            const companyData = localStorage.getItem('anwar_bakery_company');
            if (companyData) {
                const company = JSON.parse(companyData);
                
                // أولاً: تحقق من وجود رمز العملة المحفوظ
                if (company.currencySymbol) {
                    return company.currencySymbol;
                }
                
                // ثانياً: حول كود العملة إلى رمز
                const currencySymbols = {
                    'SAR': 'ر.س',
                    'YER': 'ر.ي',
                    'USD': '$',
                    'EUR': '€',
                    'AED': 'د.إ',
                    'KWD': 'د.ك',
                    'QAR': 'ر.ق'
                };
                
                if (company.currency && currencySymbols[company.currency]) {
                    return currencySymbols[company.currency];
                }
            }
        } catch (error) {
            console.error('Error getting currency symbol:', error);
        }
        
        return 'ر.س';
    }
    
    // إصلاح تنسيق الأرقام في الصفحة
    function fixNumberFormatting() {
        // إصلاح الإحصائيات
        const totalSalariesElement = document.getElementById('totalSalaries');
        if (totalSalariesElement) {
            const text = totalSalariesElement.textContent;
            const number = parseFloat(text.replace(/[^\d.-]/g, ''));
            if (!isNaN(number)) {
                totalSalariesElement.textContent = formatCurrency(number);
            }
        }
        
        // إصلاح جدول الموظفين
        document.querySelectorAll('td').forEach(cell => {
            const text = cell.textContent;
            // إذا كان النص يحتوي على أرقام عربية أو رموز عملة
            if (text.includes('٠') || text.includes('١') || text.includes('٢') || 
                text.includes('٣') || text.includes('٤') || text.includes('٥') || 
                text.includes('٦') || text.includes('٧') || text.includes('٨') || 
                text.includes('٩') || text.includes('٬')) {
                
                // تحويل الأرقام العربية إلى إنجليزية
                let englishText = text
                    .replace(/٠/g, '0')
                    .replace(/١/g, '1')
                    .replace(/٢/g, '2')
                    .replace(/٣/g, '3')
                    .replace(/٤/g, '4')
                    .replace(/٥/g, '5')
                    .replace(/٦/g, '6')
                    .replace(/٧/g, '7')
                    .replace(/٨/g, '8')
                    .replace(/٩/g, '9')
                    .replace(/٬/g, ',');
                
                // إذا كان يحتوي على رمز عملة، أعد تنسيقه
                const currencySymbol = getCurrentCurrencySymbol();
                if (text.includes('ر.س') || text.includes('ر.ي') || text.includes('$') || 
                    text.includes('€') || text.includes('د.إ') || text.includes('د.ك') || 
                    text.includes('ر.ق')) {
                    
                    const number = parseFloat(englishText.replace(/[^\d.-]/g, ''));
                    if (!isNaN(number)) {
                        englishText = formatCurrency(number);
                    }
                }
                
                cell.textContent = englishText;
            }
        });
        
        console.log('✅ Number formatting fixed');
    }
    
    // تطبيق الإصلاح عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(fixNumberFormatting, 1000);
    });
    
    // تطبيق الإصلاح عند تحديث البيانات
    window.addEventListener('storage', function(event) {
        if (event.key === 'anwar_bakery_company') {
            setTimeout(fixNumberFormatting, 500);
        }
    });
    
    // إضافة وظائف عامة
    window.formatNumber = formatNumber;
    window.formatCurrency = formatCurrency;
    window.fixNumberFormatting = fixNumberFormatting;
    
    console.log('✅ Number Format Fixer loaded');
    
})();
