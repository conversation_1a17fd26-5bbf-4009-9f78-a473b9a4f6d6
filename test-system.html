<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام - مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-xl shadow-lg p-8">
            <h1 class="text-3xl font-bold text-center text-gray-800 mb-8">🍞 نظام إدارة مخبز أنوار الحي</h1>
            
            <!-- System Status -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-full">
                            <span class="text-2xl">✅</span>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-semibold text-green-800">تحويل المبالغ</h3>
                            <p class="text-sm text-green-600">تم إضافة نظام تحويل المبالغ بين الصناديق</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-full">
                            <span class="text-2xl">💰</span>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-semibold text-blue-800">العملة الموحدة</h3>
                            <p class="text-sm text-blue-600">تم ربط العملة بإعدادات المنشأة</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-100 rounded-full">
                            <span class="text-2xl">🏙️</span>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-semibold text-purple-800">حقول المدن</h3>
                            <p class="text-sm text-purple-600">تم تحويل قوائم المدن إلى مربعات نص</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="p-2 bg-orange-100 rounded-full">
                            <span class="text-2xl">👨‍💼</span>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-semibold text-orange-800">إدارة الموظفين</h3>
                            <p class="text-sm text-orange-600">نظام شامل للمرتبات والجزاءات</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Access Links -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <a href="customers.html" class="block bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg p-6 hover:from-blue-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center">
                        <span class="text-3xl ml-4">👤</span>
                        <div>
                            <h3 class="text-xl font-bold">إدارة العملاء</h3>
                            <p class="text-blue-100">إضافة وإدارة العملاء والحسابات</p>
                        </div>
                    </div>
                </a>
                
                <a href="suppliers.html" class="block bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg p-6 hover:from-green-600 hover:to-green-700 transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center">
                        <span class="text-3xl ml-4">🏭</span>
                        <div>
                            <h3 class="text-xl font-bold">إدارة الموردين</h3>
                            <p class="text-green-100">إدارة الموردين والمشتريات</p>
                        </div>
                    </div>
                </a>
                
                <a href="cash-registers.html" class="block bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg p-6 hover:from-purple-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center">
                        <span class="text-3xl ml-4">💰</span>
                        <div>
                            <h3 class="text-xl font-bold">إدارة الصناديق</h3>
                            <p class="text-purple-100">الصناديق وتحويل المبالغ</p>
                        </div>
                    </div>
                </a>
                
                <a href="employees.html" class="block bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-lg p-6 hover:from-orange-600 hover:to-orange-700 transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center">
                        <span class="text-3xl ml-4">👨‍💼</span>
                        <div>
                            <h3 class="text-xl font-bold">إدارة الموظفين</h3>
                            <p class="text-orange-100">الموظفين والمرتبات والجزاءات</p>
                        </div>
                    </div>
                </a>
                
                <a href="products.html" class="block bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg p-6 hover:from-red-600 hover:to-red-700 transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center">
                        <span class="text-3xl ml-4">📦</span>
                        <div>
                            <h3 class="text-xl font-bold">إدارة الأصناف</h3>
                            <p class="text-red-100">المنتجات والمخزون</p>
                        </div>
                    </div>
                </a>
                
                <a href="invoices.html" class="block bg-gradient-to-r from-indigo-500 to-indigo-600 text-white rounded-lg p-6 hover:from-indigo-600 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center">
                        <span class="text-3xl ml-4">🧾</span>
                        <div>
                            <h3 class="text-xl font-bold">إدارة الفواتير</h3>
                            <p class="text-indigo-100">فواتير البيع والشراء</p>
                        </div>
                    </div>
                </a>
            </div>

            <!-- Features Summary -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">✨ المميزات الجديدة</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-700 mb-2">🔄 تحويل المبالغ بين الصناديق</h3>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• تحويل آمن بين الصناديق المختلفة</li>
                            <li>• التحقق من الأرصدة قبل التحويل</li>
                            <li>• تسجيل تفصيلي لجميع العمليات</li>
                            <li>• ملخص فوري للعملية</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-semibold text-gray-700 mb-2">💰 نظام العملة الموحد</h3>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• ربط العملة بإعدادات المنشأة</li>
                            <li>• عرض موحد في جميع الصفحات</li>
                            <li>• سهولة تغيير العملة من مكان واحد</li>
                            <li>• دعم العملات المختلفة</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-semibold text-gray-700 mb-2">🏙️ تحسين حقول المدن</h3>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• تحويل القوائم المنسدلة إلى مربعات نص</li>
                            <li>• مرونة أكبر في إدخال أسماء المدن</li>
                            <li>• دعم المدن غير المدرجة مسبقاً</li>
                            <li>• واجهة أبسط وأسرع</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-semibold text-gray-700 mb-2">👨‍💼 إدارة شاملة للموظفين</h3>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• نظام المرتبات الشهرية</li>
                            <li>• إدارة الجزاءات والخصومات</li>
                            <li>• تقارير مفصلة لكل موظف</li>
                            <li>• ربط مع النظام المحاسبي</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Test Instructions -->
            <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <h2 class="text-xl font-bold text-yellow-800 mb-4">📋 تعليمات الاختبار</h2>
                <div class="text-sm text-yellow-700 space-y-2">
                    <p><strong>1. اختبار تحويل المبالغ:</strong> انتقل إلى صفحة الصناديق واضغط على "تحويل مبلغ"</p>
                    <p><strong>2. اختبار العملة:</strong> تحقق من عرض العملة في جميع الصفحات</p>
                    <p><strong>3. اختبار حقول المدن:</strong> جرب إدخال أسماء مدن مختلفة في النماذج</p>
                    <p><strong>4. اختبار إدارة الموظفين:</strong> جرب إضافة مرتبات وجزاءات للموظفين</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Set default company data for testing
        if (!localStorage.getItem('anwar_bakery_company')) {
            const defaultCompany = {
                companyNameAr: 'مخبز أنوار الحي',
                companyNameEn: 'Anwar Al-Hay Bakery',
                currency: 'ر.س',
                phone: '0112345678',
                email: '<EMAIL>',
                address: 'الرياض، المملكة العربية السعودية'
            };
            localStorage.setItem('anwar_bakery_company', JSON.stringify(defaultCompany));
        }

        // Set default session for testing
        if (!localStorage.getItem('anwar_bakery_session')) {
            const defaultSession = {
                fullName: 'أحمد محمد',
                role: 'مدير النظام',
                userId: 1
            };
            localStorage.setItem('anwar_bakery_session', JSON.stringify(defaultSession));
        }

        console.log('✅ تم تحضير البيانات التجريبية');
        console.log('🚀 النظام جاهز للاختبار');
    </script>
</body>
</html>
