// Owners Management System
let owners = [];
let totalCapital = 1000000; // Default total capital

// Check authentication
function checkAuth() {
    const session = localStorage.getItem('anwar_bakery_session') ||
                   sessionStorage.getItem('anwar_bakery_session');

    if (!session) {
        window.location.href = 'login.html';
        return null;
    }

    return JSON.parse(session);
}

// Load user info
function loadUserInfo() {
    const session = checkAuth();
    if (session) {
        document.getElementById('userFullName').textContent = session.fullName;
        document.getElementById('userDisplayName').textContent = session.fullName;
        document.getElementById('userRole').textContent = session.role;

        const loginTime = new Date(session.loginTime);
        document.getElementById('loginTime').textContent =
            'آخر دخول: ' + loginTime.toLocaleString('ar-SA');
    }
}

// Load company info
function loadCompanyInfo() {
    const savedData = localStorage.getItem('anwar_bakery_company');
    if (savedData) {
        const companyData = JSON.parse(savedData);
        if (companyData.companyNameAr) {
            document.getElementById('sidebarCompanyName').textContent = companyData.companyNameAr;
            document.title = `إدارة الملاك - ${companyData.companyNameAr}`;
        }
        // Load capital from company data if available
        if (companyData.capital) {
            totalCapital = companyData.capital;
        }
    }
}

// Load owners from localStorage
function loadOwners() {
    const savedOwners = localStorage.getItem('anwar_bakery_owners');
    if (savedOwners) {
        owners = JSON.parse(savedOwners);
    } else {
        // Create sample owners
        owners = [
            {
                id: 1,
                ownerName: 'أنور محمد الأحمد',
                nationalId: '1234567890',
                partnershipType: 'founder',
                sharePercentage: 60.00,
                shareValue: 600000.00,
                phoneNumber: '0501234567',
                email: '<EMAIL>',
                address: 'الرياض، المملكة العربية السعودية',
                joinDate: '2020-01-01',
                isActive: true,
                createdAt: '2024-01-15'
            },
            {
                id: 2,
                ownerName: 'سارة أحمد العلي',
                nationalId: '0987654321',
                partnershipType: 'partner',
                sharePercentage: 25.00,
                shareValue: 250000.00,
                phoneNumber: '0507654321',
                email: '<EMAIL>',
                address: 'جدة، المملكة العربية السعودية',
                joinDate: '2020-06-15',
                isActive: true,
                createdAt: '2024-01-20'
            },
            {
                id: 3,
                ownerName: 'محمد عبدالله الخالد',
                nationalId: '1122334455',
                partnershipType: 'investor',
                sharePercentage: 15.00,
                shareValue: 150000.00,
                phoneNumber: '0551122334',
                email: '<EMAIL>',
                address: 'الدمام، المملكة العربية السعودية',
                joinDate: '2021-03-10',
                isActive: true,
                createdAt: '2024-02-01'
            }
        ];
        saveOwners();
    }
    renderOwners();
    updateStats();
}

// Save owners to localStorage
function saveOwners() {
    localStorage.setItem('anwar_bakery_owners', JSON.stringify(owners));
}

// Update statistics
function updateStats() {
    const totalOwners = owners.length;
    const activeOwners = owners.filter(o => o.isActive).length;
    const totalShares = owners.reduce((sum, o) => sum + (o.sharePercentage || 0), 0);

    document.getElementById('totalOwners').textContent = totalOwners;
    document.getElementById('activeOwners').textContent = activeOwners;
    document.getElementById('totalShares').textContent = totalShares.toFixed(2) + '%';
    document.getElementById('totalCapital').textContent = totalCapital.toFixed(2);
}

// Render owners table
function renderOwners(filteredOwners = owners) {
    const tbody = document.getElementById('ownersTableBody');
    tbody.innerHTML = '';

    filteredOwners.forEach(owner => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';

        const partnershipTypeNames = {
            'founder': 'مؤسس',
            'partner': 'شريك',
            'investor': 'مستثمر'
        };

        const statusClass = owner.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
        const statusText = owner.isActive ? 'نشط' : 'معطل';

        row.innerHTML = `
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <div class="font-medium">${owner.ownerName}</div>
                <div class="text-xs text-gray-500">${owner.email || ''}</div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm font-mono text-gray-900">${owner.nationalId}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <span class="px-2 py-1 text-xs font-medium rounded-full ${owner.partnershipType === 'founder' ? 'bg-purple-100 text-purple-800' : 
                    owner.partnershipType === 'partner' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}">
                    ${partnershipTypeNames[owner.partnershipType] || 'غير محدد'}
                </span>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 font-semibold">
                ${(owner.sharePercentage || 0).toFixed(2)}%
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 font-semibold text-green-600">
                ${(owner.shareValue || 0).toFixed(2)} ر.س
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">${owner.phoneNumber || '-'}</td>
            <td class="px-4 py-3 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                    ${statusText}
                </span>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-center">
                <div class="flex justify-center space-x-1">
                    <button onclick="editOwner(${owner.id})" class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50" title="تعديل">
                        ✏️
                    </button>
                    <button onclick="viewOwnerDetails(${owner.id})" class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50" title="عرض التفاصيل">
                        👁️
                    </button>
                    <button onclick="toggleOwnerStatus(${owner.id})" class="text-yellow-600 hover:text-yellow-900 p-1 rounded hover:bg-yellow-50" title="${owner.isActive ? 'إلغاء تفعيل' : 'تفعيل'}">
                        ${owner.isActive ? '⏸️' : '▶️'}
                    </button>
                    <button onclick="deleteOwner(${owner.id})" class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50" title="حذف">
                        🗑️
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Filter owners
function filterOwners() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;

    const filtered = owners.filter(owner => {
        const matchesSearch = owner.ownerName.toLowerCase().includes(searchTerm) ||
                            owner.nationalId.toLowerCase().includes(searchTerm) ||
                            (owner.email && owner.email.toLowerCase().includes(searchTerm));
        
        let matchesStatus = true;
        if (statusFilter === 'active') {
            matchesStatus = owner.isActive;
        } else if (statusFilter === 'inactive') {
            matchesStatus = !owner.isActive;
        }

        const matchesType = !typeFilter || owner.partnershipType === typeFilter;

        return matchesSearch && matchesStatus && matchesType;
    });

    renderOwners(filtered);
}

// Open add modal
function openAddModal() {
    document.getElementById('modalTitle').textContent = 'إضافة مالك جديد';
    document.getElementById('ownerForm').reset();
    document.getElementById('ownerId').value = '';
    document.getElementById('isActive').checked = true;
    document.getElementById('joinDate').value = new Date().toISOString().split('T')[0];
    document.getElementById('ownerModal').classList.add('active');
}

// Edit owner
function editOwner(id) {
    const owner = owners.find(o => o.id === id);
    if (owner) {
        document.getElementById('modalTitle').textContent = 'تعديل المالك';
        document.getElementById('ownerId').value = owner.id;
        document.getElementById('ownerName').value = owner.ownerName;
        document.getElementById('nationalId').value = owner.nationalId;
        document.getElementById('partnershipType').value = owner.partnershipType;
        document.getElementById('sharePercentage').value = owner.sharePercentage || 0;
        document.getElementById('shareValue').value = owner.shareValue || 0;
        document.getElementById('phoneNumber').value = owner.phoneNumber || '';
        document.getElementById('email').value = owner.email || '';
        document.getElementById('address').value = owner.address || '';
        document.getElementById('joinDate').value = owner.joinDate || '';
        document.getElementById('isActive').checked = owner.isActive;
        document.getElementById('ownerModal').classList.add('active');
    }
}

// Calculate share value based on percentage
function calculateShareValue() {
    const percentage = parseFloat(document.getElementById('sharePercentage').value) || 0;
    const value = (totalCapital * percentage) / 100;
    document.getElementById('shareValue').value = value.toFixed(2);
}

// Calculate share percentage based on value
function calculateSharePercentage() {
    const value = parseFloat(document.getElementById('shareValue').value) || 0;
    const percentage = (value / totalCapital) * 100;
    document.getElementById('sharePercentage').value = percentage.toFixed(2);
}

// Save owner
function saveOwner(event) {
    event.preventDefault();

    const ownerId = document.getElementById('ownerId').value;
    const ownerData = {
        ownerName: document.getElementById('ownerName').value,
        nationalId: document.getElementById('nationalId').value,
        partnershipType: document.getElementById('partnershipType').value,
        sharePercentage: parseFloat(document.getElementById('sharePercentage').value) || 0,
        shareValue: parseFloat(document.getElementById('shareValue').value) || 0,
        phoneNumber: document.getElementById('phoneNumber').value,
        email: document.getElementById('email').value,
        address: document.getElementById('address').value,
        joinDate: document.getElementById('joinDate').value,
        isActive: document.getElementById('isActive').checked
    };

    // Check if national ID already exists
    const existingOwner = owners.find(o => o.nationalId === ownerData.nationalId && o.id != ownerId);
    if (existingOwner) {
        showMessage('رقم الهوية موجود مسبقاً!', 'error');
        return;
    }

    // Validate total shares don't exceed 100%
    const currentTotalShares = owners.reduce((sum, o) => {
        if (o.id != ownerId) {
            return sum + (o.sharePercentage || 0);
        }
        return sum;
    }, 0);

    if (currentTotalShares + ownerData.sharePercentage > 100) {
        showMessage('إجمالي الحصص لا يمكن أن يتجاوز 100%!', 'error');
        return;
    }

    if (ownerId) {
        // Update existing owner
        const ownerIndex = owners.findIndex(o => o.id == ownerId);
        if (ownerIndex !== -1) {
            owners[ownerIndex] = { ...owners[ownerIndex], ...ownerData };
            showMessage('تم تحديث المالك بنجاح!', 'success');
        }
    } else {
        // Add new owner
        ownerData.id = Date.now();
        ownerData.createdAt = new Date().toISOString().split('T')[0];
        owners.push(ownerData);
        showMessage('تم إضافة المالك بنجاح!', 'success');
    }

    saveOwners();
    renderOwners();
    updateStats();
    closeModal();
}

// Close modal
function closeModal() {
    document.getElementById('ownerModal').classList.remove('active');
}

// Toggle owner status
function toggleOwnerStatus(id) {
    const owner = owners.find(o => o.id === id);
    if (owner) {
        owner.isActive = !owner.isActive;
        saveOwners();
        renderOwners();
        updateStats();
        showMessage(`تم ${owner.isActive ? 'تفعيل' : 'إلغاء تفعيل'} المالك بنجاح!`, 'success');
    }
}

// Delete owner
function deleteOwner(id) {
    if (confirm('هل أنت متأكد من حذف هذا المالك؟')) {
        owners = owners.filter(o => o.id !== id);
        saveOwners();
        renderOwners();
        updateStats();
        showMessage('تم حذف المالك بنجاح!', 'success');
    }
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Update current date time
function updateDateTime() {
    const now = new Date();
    const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    document.getElementById('currentDateTime').textContent =
        now.toLocaleDateString('ar-SA', options);
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (sidebar.classList.contains('translate-x-full')) {
        sidebar.classList.remove('translate-x-full');
        overlay.classList.remove('hidden');
    } else {
        sidebar.classList.add('translate-x-full');
        overlay.classList.add('hidden');
    }
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_session');
        sessionStorage.removeItem('anwar_bakery_session');
        window.location.href = 'login.html';
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    checkAuth();
    loadUserInfo();
    loadCompanyInfo();
    loadOwners();
    updateDateTime();
    setInterval(updateDateTime, 60000);
});

// Close sidebar when clicking overlay
document.getElementById('mobileOverlay').addEventListener('click', toggleSidebar);
