<?php
// Reset Database Except Chart of Accounts
require_once __DIR__ . '/../config.php';

header('Content-Type: application/json');

try {
    $conn = getDBConnection();
    if (!$conn) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }

    // Disable foreign key checks
    $conn->query('SET FOREIGN_KEY_CHECKS = 0');

    // List of tables to truncate (except chart_of_accounts)
    $tables = array(
        'users',
        'branches',
        'units',
        'product_categories',
        'products',
        'inventory_movements',
        'customers',
        'suppliers',
        'journal_entries',
        'journal_entry_details',
        'company_settings'
        // Add more tables as needed
    );
    foreach ($tables as $table) {
        $conn->query("TRUNCATE TABLE `$table`");
    }

    // Reset balances in chart_of_accounts
    $conn->query('UPDATE chart_of_accounts SET opening_balance = 0, current_balance = 0');

    // Enable foreign key checks
    $conn->query('SET FOREIGN_KEY_CHECKS = 1');

    echo json_encode(array('success' => true, 'message' => 'تم تصفير جميع البيانات ماعدا شجرة الحسابات.'));
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(array('success' => false, 'error' => $e->getMessage()));
}
