// Reset System to Clean State
// إعادة تعيين النظام إلى حالة نظيفة

/**
 * Reset all system data to clean state with zero values
 * إعادة تعيين جميع بيانات النظام إلى حالة نظيفة بقيم صفر
 */
function resetSystemToCleanState() {
    if (!confirm('هل أنت متأكد من إعادة تعيين النظام إلى حالة نظيفة؟\n\nسيتم حذف جميع البيانات والعودة للحالة الافتراضية.')) {
        return;
    }

    try {
        // Clear all localStorage data except login session
        const session = localStorage.getItem('anwar_bakery_session');
        
        // Clear all anwar_bakery data
        Object.keys(localStorage).forEach(key => {
            if (key.startsWith('anwar_bakery_') && key !== 'anwar_bakery_session') {
                localStorage.removeItem(key);
            }
        });
        
        // Restore session if it existed
        if (session) {
            localStorage.setItem('anwar_bakery_session', session);
        }
        
        // Initialize clean company settings
        const cleanCompanySettings = {
            companyNameAr: 'مخبز أنوار الحي',
            companyNameEn: 'Anwar Bakery',
            companySlogan: '',
            businessType: 'bakery',
            phone: '',
            email: '',
            address: 'أدخل عنوان المخبز',
            commercialRegister: '',
            taxNumber: '',
            workingHours: '',
            workingDays: '',
            notes: '',
            logo: null,
            currency: 'SAR'
        };
        localStorage.setItem('anwar_bakery_company', JSON.stringify(cleanCompanySettings));
        
        // Initialize clean users (admin only)
        const cleanUsers = [
            {
                id: 1,
                fullName: 'مدير النظام',
                username: 'admin',
                email: '',
                phone: '',
                role: 'admin',
                isActive: true,
                createdAt: new Date().toISOString().split('T')[0]
            }
        ];
        localStorage.setItem('anwar_bakery_users', JSON.stringify(cleanUsers));
        
        // Initialize empty arrays for all other data
        const emptyArrays = [
            'anwar_bakery_products',
            'anwar_bakery_customers',
            'anwar_bakery_suppliers',
            'anwar_bakery_employees',
            'anwar_bakery_cash_registers',
            'anwar_bakery_banks',
            'anwar_bakery_owners',
            'anwar_bakery_invoices',
            'anwar_bakery_vouchers',
            'anwar_bakery_journal_entries',
            'anwar_bakery_inventory_movements',
            'anwar_bakery_branches',
            'anwar_bakery_units',
            'anwar_bakery_categories'
        ];
        
        emptyArrays.forEach(key => {
            localStorage.setItem(key, JSON.stringify([]));
        });
        
        // Initialize basic units
        const basicUnits = [
            { id: 1, name: 'قطعة', symbol: 'قطعة', type: 'piece', isActive: true },
            { id: 2, name: 'كيلوجرام', symbol: 'كجم', type: 'weight', isActive: true },
            { id: 3, name: 'جرام', symbol: 'جم', type: 'weight', isActive: true },
            { id: 4, name: 'لتر', symbol: 'لتر', type: 'volume', isActive: true }
        ];
        localStorage.setItem('anwar_bakery_units', JSON.stringify(basicUnits));
        
        // Initialize basic chart of accounts with zero balances
        const basicAccounts = [
            { id: 1, code: '1000', name: 'الأصول', type: 'assets', parentId: null, level: 1, balance: 0, isActive: true },
            { id: 2, code: '1100', name: 'الأصول المتداولة', type: 'assets', parentId: 1, level: 2, balance: 0, isActive: true },
            { id: 3, code: '1110', name: 'النقدية', type: 'assets', parentId: 2, level: 3, balance: 0, isActive: true },
            { id: 4, code: '1120', name: 'المخزون', type: 'assets', parentId: 2, level: 3, balance: 0, isActive: true },
            { id: 5, code: '1130', name: 'العملاء', type: 'assets', parentId: 2, level: 3, balance: 0, isActive: true },
            { id: 6, code: '2000', name: 'الخصوم', type: 'liabilities', parentId: null, level: 1, balance: 0, isActive: true },
            { id: 7, code: '2100', name: 'الخصوم المتداولة', type: 'liabilities', parentId: 6, level: 2, balance: 0, isActive: true },
            { id: 8, code: '2110', name: 'الموردين', type: 'liabilities', parentId: 7, level: 3, balance: 0, isActive: true },
            { id: 9, code: '3000', name: 'حقوق الملكية', type: 'equity', parentId: null, level: 1, balance: 0, isActive: true },
            { id: 10, code: '3100', name: 'رأس المال', type: 'equity', parentId: 9, level: 2, balance: 0, isActive: true },
            { id: 11, code: '4000', name: 'الإيرادات', type: 'revenue', parentId: null, level: 1, balance: 0, isActive: true },
            { id: 12, code: '4100', name: 'إيرادات المبيعات', type: 'revenue', parentId: 11, level: 2, balance: 0, isActive: true },
            { id: 13, code: '5000', name: 'المصروفات', type: 'expense', parentId: null, level: 1, balance: 0, isActive: true },
            { id: 14, code: '5100', name: 'تكلفة البضاعة المباعة', type: 'expense', parentId: 13, level: 2, balance: 0, isActive: true },
            { id: 15, code: '5200', name: 'المصروفات التشغيلية', type: 'expense', parentId: 13, level: 2, balance: 0, isActive: true }
        ];
        localStorage.setItem('anwar_bakery_accounts', JSON.stringify(basicAccounts));
        
        // Initialize system settings with clean values
        const systemSettings = {
            fiscalYearStart: new Date().getFullYear() + '-01-01',
            fiscalYearEnd: new Date().getFullYear() + '-12-31',
            autoBackup: true,
            backupTime: '02:00',
            taxRate: 15,
            invoicePrefix: 'INV-',
            voucherPrefix: 'VOU-',
            lastInvoiceNumber: 0,
            lastVoucherNumber: 0,
            initialized: true,
            resetDate: new Date().toISOString()
        };
        localStorage.setItem('anwar_bakery_settings', JSON.stringify(systemSettings));
        
        // Clear any cached statistics
        localStorage.removeItem('anwar_bakery_stats_cache');
        localStorage.removeItem('anwar_bakery_dashboard_cache');
        
        alert('✅ تم إعادة تعيين النظام بنجاح!\n\nالنظام الآن في حالة نظيفة مع قيم صفر.');
        
        // Reload the page to reflect changes
        window.location.reload();
        
    } catch (error) {
        console.error('Error resetting system:', error);
        alert('❌ حدث خطأ أثناء إعادة تعيين النظام!');
    }
}

/**
 * Reset only financial data (keep products, customers, etc.)
 * إعادة تعيين البيانات المالية فقط (الاحتفاظ بالمنتجات والعملاء)
 */
function resetFinancialDataOnly() {
    if (!confirm('هل تريد إعادة تعيين البيانات المالية فقط؟\n\nسيتم حذف الفواتير والسندات والقيود مع الاحتفاظ بالمنتجات والعملاء.')) {
        return;
    }

    try {
        // Clear financial data only
        const financialKeys = [
            'anwar_bakery_invoices',
            'anwar_bakery_vouchers',
            'anwar_bakery_journal_entries',
            'anwar_bakery_inventory_movements'
        ];
        
        financialKeys.forEach(key => {
            localStorage.setItem(key, JSON.stringify([]));
        });
        
        // Reset account balances to zero
        const accounts = JSON.parse(localStorage.getItem('anwar_bakery_accounts') || '[]');
        accounts.forEach(account => {
            account.balance = 0;
        });
        localStorage.setItem('anwar_bakery_accounts', JSON.stringify(accounts));
        
        // Reset cash register balances
        const cashRegisters = JSON.parse(localStorage.getItem('anwar_bakery_cash_registers') || '[]');
        cashRegisters.forEach(register => {
            register.currentBalance = 0;
        });
        localStorage.setItem('anwar_bakery_cash_registers', JSON.stringify(cashRegisters));
        
        // Reset bank balances
        const banks = JSON.parse(localStorage.getItem('anwar_bakery_banks') || '[]');
        banks.forEach(bank => {
            bank.currentBalance = 0;
        });
        localStorage.setItem('anwar_bakery_banks', JSON.stringify(banks));
        
        // Reset customer balances
        const customers = JSON.parse(localStorage.getItem('anwar_bakery_customers') || '[]');
        customers.forEach(customer => {
            customer.currentBalance = 0;
        });
        localStorage.setItem('anwar_bakery_customers', JSON.stringify(customers));
        
        // Reset supplier balances
        const suppliers = JSON.parse(localStorage.getItem('anwar_bakery_suppliers') || '[]');
        suppliers.forEach(supplier => {
            supplier.currentBalance = 0;
        });
        localStorage.setItem('anwar_bakery_suppliers', JSON.stringify(suppliers));
        
        alert('✅ تم إعادة تعيين البيانات المالية بنجاح!\n\nجميع الأرصدة أصبحت صفر مع الاحتفاظ بالبيانات الأساسية.');
        
        // Reload the page to reflect changes
        window.location.reload();
        
    } catch (error) {
        console.error('Error resetting financial data:', error);
        alert('❌ حدث خطأ أثناء إعادة تعيين البيانات المالية!');
    }
}

/**
 * Initialize dashboard with zero values
 * تهيئة لوحة التحكم بقيم صفر
 */
function initializeDashboardWithZeros() {
    // Update dashboard statistics to show zeros
    const elements = {
        'totalSales': '0',
        'todayOrders': '0',
        'totalProducts': '0',
        'lowStock': '0'
    };
    
    Object.keys(elements).forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = elements[id];
        }
    });
    
    // Clear recent activity
    const recentActivity = document.getElementById('recentActivity');
    if (recentActivity) {
        recentActivity.innerHTML = `
            <div class="flex items-center justify-center p-8 bg-gray-50 rounded-lg">
                <div class="text-center">
                    <div class="text-4xl text-gray-400 mb-2">📋</div>
                    <p class="text-sm font-medium text-gray-500">لا توجد أنشطة حتى الآن</p>
                    <p class="text-xs text-gray-400 mt-1">ابدأ باستخدام النظام لرؤية الأنشطة هنا</p>
                </div>
            </div>
        `;
    }
}

/**
 * Check if system needs initialization
 * التحقق من حاجة النظام للتهيئة
 */
function checkSystemInitialization() {
    const settings = localStorage.getItem('anwar_bakery_settings');
    if (!settings) {
        // System not initialized, show welcome message
        if (confirm('مرحباً بك في نظام إدارة مخبز أنوار الحي!\n\nهل تريد تهيئة النظام بالبيانات الأساسية؟')) {
            resetSystemToCleanState();
        }
    }
}

// Auto-initialize dashboard on page load
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on dashboard page
    if (window.location.pathname.includes('dashboard.html') || window.location.pathname === '/') {
        initializeDashboardWithZeros();
    }
    
    // Check system initialization
    checkSystemInitialization();
});

// Export functions for global use
window.resetSystemToCleanState = resetSystemToCleanState;
window.resetFinancialDataOnly = resetFinancialDataOnly;
window.initializeDashboardWithZeros = initializeDashboardWithZeros;
