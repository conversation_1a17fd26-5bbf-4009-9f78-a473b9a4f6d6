// <PERSON>ript to add system-admin.html link to all sidebar menus
// This is a utility script to help update all HTML files

const fs = require('fs');
const path = require('path');

// List of HTML files to update (excluding system-admin.html itself)
const htmlFiles = [
    'users-management.html',
    'company-settings.html',
    'system-settings.html',
    'branches-warehouses.html',
    'units.html',
    'products.html',
    'damage-entry.html',
    'customers.html',
    'suppliers.html',
    'employees.html',
    'cash-registers.html',
    'banks.html',
    'owners.html',
    'inventory.html'
];

// The link HTML to add
const systemAdminLink = `                <a href="system-admin.html" class="flex items-center px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 hover:text-red-700 rounded-md border border-red-200">
                    <span class="ml-3">🔧</span>
                    إدارة النظام المتقدمة
                </a>`;

// Pattern to find system-settings.html link
const systemSettingsPattern = /(\s*<a href="system-settings\.html"[^>]*>[\s\S]*?<\/a>)/;

function updateFile(filename) {
    try {
        if (!fs.existsSync(filename)) {
            console.log(`⚠️  File not found: ${filename}`);
            return false;
        }

        let content = fs.readFileSync(filename, 'utf8');
        
        // Check if system-admin link already exists
        if (content.includes('system-admin.html')) {
            console.log(`✅ ${filename} already has system-admin link`);
            return true;
        }

        // Find system-settings link and add system-admin link after it
        const match = content.match(systemSettingsPattern);
        if (match) {
            const replacement = match[1] + '\n' + systemAdminLink;
            content = content.replace(systemSettingsPattern, replacement);
            
            fs.writeFileSync(filename, content, 'utf8');
            console.log(`✅ Updated ${filename}`);
            return true;
        } else {
            console.log(`⚠️  Could not find system-settings link in ${filename}`);
            return false;
        }
    } catch (error) {
        console.error(`❌ Error updating ${filename}:`, error.message);
        return false;
    }
}

// Main execution
console.log('🔄 Starting sidebar update process...\n');

let successCount = 0;
let totalCount = htmlFiles.length;

htmlFiles.forEach(filename => {
    if (updateFile(filename)) {
        successCount++;
    }
});

console.log(`\n📊 Update Summary:`);
console.log(`✅ Successfully updated: ${successCount}/${totalCount} files`);
console.log(`❌ Failed to update: ${totalCount - successCount}/${totalCount} files`);

if (successCount === totalCount) {
    console.log('\n🎉 All files updated successfully!');
} else {
    console.log('\n⚠️  Some files need manual update.');
}

// Instructions for manual update
console.log('\n📝 Manual Update Instructions:');
console.log('If any files failed to update automatically, add this link after the system-settings link:');
console.log('\n' + systemAdminLink);
