<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - مخبز أنور</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .card-hover { transition: transform 0.2s ease-in-out; }
        .card-hover:hover { transform: translateY(-2px); }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Mobile menu overlay -->
    <div id="mobileOverlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 lg:hidden hidden"></div>

    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform translate-x-full lg:translate-x-0 lg:static lg:inset-0 sidebar-transition flex flex-col">
            <div class="h-16 px-4 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between flex-shrink-0">
                <h1 id="sidebarCompanyName" class="text-white text-lg font-bold">مخبز أنور</h1>
                <button onclick="toggleSidebar()" class="lg:hidden text-white">
                    <span class="text-xl">✕</span>
                </button>
            </div>

            <nav class="flex-1 overflow-y-auto px-2 py-4 space-y-1">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">
                    <span class="ml-3">🏠</span>
                    لوحة التحكم
                </a>

                <!-- إدارة النظام -->
                <div class="pt-4 pb-2">
                    <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">إدارة النظام</h3>
                </div>
                <a href="users-management.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👥</span>
                    إدارة المستخدمين
                </a>
                <a href="company-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏢</span>
                    بيانات المنشأة
                </a>
                <a href="branches-warehouses.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏪</span>
                    الفروع والمخازن
                </a>
                <a href="units.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📏</span>
                    وحدات القياس
                </a>
                <a href="system-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">⚙️</span>
                    إعدادات النظام
                </a>
                <a href="system-admin.html" class="flex items-center px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 hover:text-red-700 rounded-md border border-red-200">
                    <span class="ml-3">🔧</span>
                    إدارة النظام المتقدمة
                </a>

                <!-- إدارة المنتجات والعملاء -->
                <div class="pt-4 pb-2">
                    <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">المنتجات والعملاء</h3>
                </div>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🚚</span>
                    الموردين
                </a>
                <a href="employees.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👨‍💼</span>
                    الموظفين
                </a>

                <!-- المحاسبة والمالية -->
                <div class="pt-4 pb-2">
                    <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">المحاسبة والمالية</h3>
                </div>
                <a href="chart-of-accounts.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📊</span>
                    دليل الحسابات
                </a>
                <a href="journal-entry.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📝</span>
                    القيود اليومية
                </a>
                <a href="vouchers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    السندات
                </a>
                <a href="banks.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏦</span>
                    البنوك
                </a>
                <a href="owners.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👑</span>
                    الملاك
                </a>

                <!-- المبيعات والمشتريات -->
                <div class="pt-4 pb-2">
                    <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">المبيعات والمشتريات</h3>
                </div>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>

                <!-- المخزون والتقارير -->
                <div class="pt-4 pb-2">
                    <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">المخزون والتقارير</h3>
                </div>
                <a href="inventory.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📊</span>
                    المخزون
                </a>
                <a href="reports.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📈</span>
                    التقارير
                </a>

            </nav>

            <!-- User Info & Logout -->
            <div class="flex-shrink-0 p-4 border-t border-gray-200">
                <div class="bg-gray-50 rounded-lg p-3 mb-2">
                    <p id="userFullName" class="text-sm font-medium text-gray-900">مدير النظام</p>
                    <p id="userRole" class="text-xs text-gray-500">admin</p>
                    <p id="loginTime" class="text-xs text-gray-400"></p>
                </div>
                <button onclick="logout()" class="w-full text-right px-3 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50 flex items-center">
                    <span class="ml-3">🚪</span>
                    تسجيل الخروج
                </button>
            </div>
        </div>

        <!-- Main content -->
        <div class="flex-1 overflow-hidden">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <button onclick="toggleSidebar()" class="lg:hidden text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">☰</span>
                            </button>
                            <span class="text-lg font-semibold text-gray-900">لوحة التحكم الرئيسية</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-500" id="currentDateTime"></span>
                            <div class="relative">
                                <button class="bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700">
                                    <span>🔔</span>
                                </button>
                                <span class="absolute -top-1 -left-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Welcome Section -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">
                        مرحباً بك، <span id="welcomeUserName">مدير النظام</span>! 👋
                    </h1>
                    <p class="text-gray-600">
                        إليك نظرة سريعة على أداء <span id="companyNameInWelcome">مخبز أنور</span> اليوم
                    </p>
                </div>

                <!-- Stats Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <!-- Total Sales -->
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-lg p-6 text-white card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100 text-sm">إجمالي المبيعات</p>
                                <p class="text-3xl font-bold" id="totalSales">0</p>
                                <p class="text-blue-100 text-sm mt-1">ابدأ أول عملية بيع</p>
                            </div>
                            <div class="text-4xl opacity-80">💰</div>
                        </div>
                    </div>

                    <!-- Today Orders -->
                    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg p-6 text-white card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-100 text-sm">طلبات اليوم</p>
                                <p class="text-3xl font-bold" id="todayOrders">0</p>
                                <p class="text-green-100 text-sm mt-1">لا توجد طلبات اليوم</p>
                            </div>
                            <div class="text-4xl opacity-80">🛒</div>
                        </div>
                    </div>

                    <!-- Products -->
                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl shadow-lg p-6 text-white card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-100 text-sm">المنتجات</p>
                                <p class="text-3xl font-bold" id="totalProducts">0</p>
                                <p class="text-purple-100 text-sm mt-1">أضف منتجاتك الأولى</p>
                            </div>
                            <div class="text-4xl opacity-80">📦</div>
                        </div>
                    </div>

                    <!-- Low Stock -->
                    <div class="bg-gradient-to-r from-red-500 to-red-600 rounded-xl shadow-lg p-6 text-white card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-red-100 text-sm">نفاد المخزون</p>
                                <p class="text-3xl font-bold" id="lowStock">0</p>
                                <p class="text-red-100 text-sm mt-1">المخزون جيد</p>
                            </div>
                            <div class="text-4xl opacity-80">⚠️</div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Quick Actions Card -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <span class="ml-2">⚡</span>
                            الإجراءات السريعة
                        </h3>
                        <div class="grid grid-cols-2 gap-4">
                            <button onclick="window.location.href='invoices.html'" class="p-4 border-2 border-dashed border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors text-center">
                                <div class="text-2xl mb-2">🧾</div>
                                <p class="text-sm font-medium text-gray-700">فاتورة جديدة</p>
                            </button>
                            <button onclick="window.location.href='products.html'" class="p-4 border-2 border-dashed border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors text-center">
                                <div class="text-2xl mb-2">➕</div>
                                <p class="text-sm font-medium text-gray-700">إضافة منتج</p>
                            </button>
                            <button onclick="window.location.href='customers.html'" class="p-4 border-2 border-dashed border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors text-center">
                                <div class="text-2xl mb-2">👤</div>
                                <p class="text-sm font-medium text-gray-700">عميل جديد</p>
                            </button>
                            <button onclick="window.location.href='reports.html'" class="p-4 border-2 border-dashed border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors text-center">
                                <div class="text-2xl mb-2">📊</div>
                                <p class="text-sm font-medium text-gray-700">التقارير</p>
                            </button>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <span class="ml-2">📋</span>
                            النشاط الأخير
                        </h3>
                        <div class="space-y-3" id="recentActivity">
                            <div class="flex items-center justify-center p-8 bg-gray-50 rounded-lg">
                                <div class="text-center">
                                    <div class="text-4xl text-gray-400 mb-2">📋</div>
                                    <p class="text-sm font-medium text-gray-500">لا توجد أنشطة حتى الآن</p>
                                    <p class="text-xs text-gray-400 mt-1">ابدأ باستخدام النظام لرؤية الأنشطة هنا</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Sales Chart -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <span class="ml-2">📈</span>
                            مبيعات الأسبوع
                        </h3>
                        <div class="h-64 flex items-end justify-between space-x-2">
                            <div class="flex flex-col items-center">
                                <div class="bg-blue-500 w-8 rounded-t" style="height: 60%"></div>
                                <span class="text-xs text-gray-500 mt-2">السبت</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="bg-blue-500 w-8 rounded-t" style="height: 80%"></div>
                                <span class="text-xs text-gray-500 mt-2">الأحد</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="bg-blue-500 w-8 rounded-t" style="height: 45%"></div>
                                <span class="text-xs text-gray-500 mt-2">الاثنين</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="bg-blue-500 w-8 rounded-t" style="height: 90%"></div>
                                <span class="text-xs text-gray-500 mt-2">الثلاثاء</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="bg-blue-500 w-8 rounded-t" style="height: 70%"></div>
                                <span class="text-xs text-gray-500 mt-2">الأربعاء</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="bg-blue-500 w-8 rounded-t" style="height: 100%"></div>
                                <span class="text-xs text-gray-500 mt-2">الخميس</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="bg-blue-600 w-8 rounded-t" style="height: 85%"></div>
                                <span class="text-xs text-gray-500 mt-2">الجمعة</span>
                            </div>
                        </div>
                    </div>

                    <!-- Top Products -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <span class="ml-2">🏆</span>
                            أكثر المنتجات مبيعاً
                        </h3>
                        <div class="flex items-center justify-center p-8">
                            <div class="text-center">
                                <div class="text-4xl text-gray-400 mb-2">🏆</div>
                                <p class="text-sm font-medium text-gray-500">لا توجد مبيعات حتى الآن</p>
                                <p class="text-xs text-gray-400 mt-1">أضف منتجات وابدأ البيع لرؤية الإحصائيات</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Include advanced libraries -->
    <script src="advanced-excel.js"></script>
    <script src="advanced-print.js"></script>
    <script src="excel-utils.js"></script>
    <script src="global-advanced-features.js"></script>
    <script src="activate-advanced-features.js"></script>
    <script src="system-wide-activation.js"></script>
    <script src="update-admin-user.js"></script>

    <!-- Include reset system utilities -->
    <script src="reset-system.js"></script>
    <script>
        // Check authentication
        function checkAuth() {
            const session = localStorage.getItem('anwar_bakery_session') ||
                           sessionStorage.getItem('anwar_bakery_session');

            if (!session) {
                window.location.href = 'login.html';
                return null;
            }

            return JSON.parse(session);
        }

        // Load user info
        function loadUserInfo() {
            const session = checkAuth();
            if (session) {
                document.getElementById('userFullName').textContent = session.fullName;
                document.getElementById('userRole').textContent = session.role;
                document.getElementById('welcomeUserName').textContent = session.fullName;

                const loginTime = new Date(session.loginTime);
                document.getElementById('loginTime').textContent =
                    'آخر دخول: ' + loginTime.toLocaleString('ar-SA');
            }
        }

        // Load company info
        function loadCompanyInfo() {
            const savedData = localStorage.getItem('anwar_bakery_company');
            if (savedData) {
                const companyData = JSON.parse(savedData);

                // Update company name in sidebar
                if (companyData.companyNameAr) {
                    document.getElementById('sidebarCompanyName').textContent = companyData.companyNameAr;
                }

                // Update company name in welcome message
                if (companyData.companyNameAr) {
                    document.getElementById('companyNameInWelcome').textContent = companyData.companyNameAr;
                }

                // Update page title
                if (companyData.companyNameAr) {
                    document.title = `لوحة التحكم - ${companyData.companyNameAr}`;
                }
            }
        }

        // Update current date time
        function updateDateTime() {
            const now = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            document.getElementById('currentDateTime').textContent =
                now.toLocaleDateString('ar-SA', options);
        }

        // Toggle sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobileOverlay');

            if (sidebar.classList.contains('translate-x-full')) {
                sidebar.classList.remove('translate-x-full');
                overlay.classList.remove('hidden');
            } else {
                sidebar.classList.add('translate-x-full');
                overlay.classList.add('hidden');
            }
        }

        // Close sidebar when clicking overlay
        document.getElementById('mobileOverlay').addEventListener('click', toggleSidebar);

        // Logout function
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('anwar_bakery_session');
                sessionStorage.removeItem('anwar_bakery_session');
                window.location.href = 'login.html';
            }
        }

        // Simulate real-time data updates
        function updateStats() {
            // Simulate random changes in stats
            const totalSales = document.getElementById('totalSales');
            const todayOrders = document.getElementById('todayOrders');
            const lowStock = document.getElementById('lowStock');

            // Add some random variation
            const currentSales = parseInt(totalSales.textContent.replace(',', ''));
            const variation = Math.floor(Math.random() * 100) - 50;
            totalSales.textContent = (currentSales + variation).toLocaleString();
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadUserInfo();
            loadCompanyInfo();
            updateDateTime();

            // Update time every minute
            setInterval(updateDateTime, 60000);

            // Update stats every 30 seconds (simulation)
            setInterval(updateStats, 30000);
        });

        // Listen for company data updates
        window.addEventListener('companyDataUpdated', function(event) {
            loadCompanyInfo();
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 1024) {
                document.getElementById('sidebar').classList.remove('translate-x-full');
                document.getElementById('mobileOverlay').classList.add('hidden');
            }
        });
    </script>
</body>
</html>
