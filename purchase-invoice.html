<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة مشتريات - مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .custom-scroll {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 #f7fafc;
        }

        .custom-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scroll::-webkit-scrollbar-track {
            background: #f7fafc;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        .invoice-item-row:hover {
            background-color: #f8fafc;
        }

        .required-field {
            border-color: #ef4444;
        }

        .supplier-customer-toggle {
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="bg-white w-64 shadow-lg flex flex-col">
            <div class="p-4 flex-shrink-0">
                <div class="flex items-center mb-8">
                    <span class="text-2xl ml-3">🍞</span>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800" id="companyName">مخبز أنوار الحي</h1>
                        <p class="text-sm text-gray-600" id="companySlogan">جودة تستحق الثقة</p>
                    </div>
                </div>
            </div>

            <nav class="flex-1 overflow-y-auto px-4 pb-4 custom-scroll">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    الرئيسية
                </a>
                <a href="employees.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👨‍💼</span>
                    إدارة الموظفين
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏭</span>
                    الموردين
                </a>

                <!-- User Info & Logout -->
                <div class="mt-auto p-4 border-t border-gray-200">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                            أ
                        </div>
                        <div class="mr-3 flex-1">
                            <p id="userFullName" class="text-sm font-medium text-gray-900">أحمد محمد</p>
                            <p id="userRole" class="text-xs text-gray-500">مدير النظام</p>
                        </div>
                        <button onclick="logout()" class="text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50" title="تسجيل الخروج">
                            <span class="text-lg">🚪</span>
                        </button>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col min-h-0">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <a href="invoices.html" class="text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">←</span>
                            </a>
                            <h2 class="text-xl font-semibold text-gray-900">فاتورة مشتريات جديدة</h2>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="window.history.back()" class="text-gray-600 hover:text-gray-800 px-2 py-2 rounded text-sm">
                                ← رجوع
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6 custom-scroll">
                <!-- Success/Error Messages -->
                <div id="messageContainer" class="mb-6"></div>

                <!-- Invoice Form -->
                <div class="bg-white rounded-lg shadow-sm">
                    <!-- Invoice Header - Compact -->
                    <div class="p-4 border-b border-gray-200">
                        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                            <!-- Invoice Number -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">رقم الفاتورة</label>
                                <input type="text" id="invoiceNumber" readonly
                                       class="w-full px-2 py-1.5 border border-gray-300 rounded bg-gray-50 text-sm"
                                       value="PUR-2024-001">
                            </div>

                            <!-- Invoice Date -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">التاريخ *</label>
                                <input type="date" id="invoiceDate" required
                                       class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                            </div>

                            <!-- Branch -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">الفرع *</label>
                                <select id="branchId" required onchange="loadBranchData()"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                                    <option value="">اختر الفرع</option>
                                </select>
                            </div>

                            <!-- Seller Type Toggle -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">نوع البائع</label>
                                <div class="flex space-x-2">
                                    <label class="flex items-center text-xs">
                                        <input type="radio" name="sellerType" value="supplier" checked onchange="toggleSellerType()"
                                               class="ml-1 text-blue-600 focus:ring-blue-500 scale-75">
                                        مورد
                                    </label>
                                    <label class="flex items-center text-xs">
                                        <input type="radio" name="sellerType" value="customer" onchange="toggleSellerType()"
                                               class="ml-1 text-blue-600 focus:ring-blue-500 scale-75">
                                        عميل
                                    </label>
                                </div>
                            </div>

                            <!-- Supplier/Customer Selection -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1" id="sellerLabel">المورد *</label>
                                <select id="sellerId" required onchange="loadSellerData()"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                                    <option value="">اختر المورد</option>
                                </select>
                            </div>

                            <!-- Cash Register -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">الصندوق *</label>
                                <select id="cashRegisterId" required
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                                    <option value="">اختر الصندوق</option>
                                </select>
                            </div>
                        </div>

                        <!-- Second Row - Optional Fields -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-3 mt-3">
                            <!-- Warehouse -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">المخزن</label>
                                <select id="warehouseId"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                                    <option value="">اختر المخزن</option>
                                </select>
                            </div>

                            <!-- Reference Number -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">رقم المرجع</label>
                                <input type="text" id="referenceNumber" placeholder="رقم فاتورة المورد"
                                       class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                            </div>

                            <!-- Payment Method -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">طريقة الدفع</label>
                                <select id="paymentMethod" onchange="toggleBankField()"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                                    <option value="cash">نقدي</option>
                                    <option value="credit">آجل</option>
                                    <option value="bank">تحويل بنكي</option>
                                </select>
                            </div>

                            <!-- Bank Selection (Hidden by default) -->
                            <div id="bankField" style="display: none;">
                                <label class="block text-xs font-medium text-gray-600 mb-1">البنك *</label>
                                <select id="bankId"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                                    <option value="">اختر البنك</option>
                                    <option value="1">البنك الأهلي السعودي</option>
                                    <option value="2">بنك الراجحي</option>
                                    <option value="3">بنك الرياض</option>
                                    <option value="4">البنك السعودي للاستثمار</option>
                                    <option value="5">البنك السعودي الفرنسي</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Items Section - Compact -->
                    <div class="p-4">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="text-base font-semibold text-gray-900">أصناف الفاتورة</h3>
                            <div class="flex space-x-2">
                                <button onclick="showAddRawMaterialModal()" class="bg-yellow-600 text-white px-3 py-1.5 rounded hover:bg-yellow-700 flex items-center text-sm">
                                    <span class="ml-1">🌾</span>
                                    إضافة خامة جديدة
                                </button>
                                <button onclick="addInvoiceItem()" class="bg-green-600 text-white px-3 py-1.5 rounded hover:bg-green-700 flex items-center text-sm">
                                    <span class="ml-1">➕</span>
                                    إضافة صنف
                                </button>
                            </div>
                        </div>

                        <!-- Smart Item Search Row -->
                        <div id="addItemRow" class="mb-3 p-4 bg-blue-50 rounded border border-blue-200" style="display: none;">
                            <!-- Search Section -->
                            <div class="mb-3">
                                <label class="block text-sm font-medium text-gray-700 mb-2">🔍 البحث الذكي عن الأصناف</label>
                                <div class="relative">
                                    <input type="text" id="itemSearchInput" placeholder="ابحث بالاسم، الكود، الباركود، أو أي جزء من الصنف..."
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                           oninput="searchItems()" onkeydown="handleSearchKeydown(event)">
                                    <div class="absolute left-3 top-2.5 text-gray-400">
                                        <span class="text-lg">🔍</span>
                                    </div>
                                </div>

                                <!-- Search Results Dropdown -->
                                <div id="searchResults" class="absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg mt-1 max-h-60 overflow-y-auto" style="display: none;">
                                    <!-- Search results will be populated here -->
                                </div>
                            </div>

                            <!-- Selected Item Details -->
                            <div id="selectedItemDetails" class="mb-3 p-3 bg-white rounded border" style="display: none;">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
                                    <div>
                                        <label class="block text-xs font-medium text-gray-600 mb-1">الصنف المحدد</label>
                                        <div class="flex items-center">
                                            <span id="selectedItemIcon" class="text-lg ml-2">📦</span>
                                            <div>
                                                <div id="selectedItemName" class="font-medium text-gray-900"></div>
                                                <div id="selectedItemCode" class="text-xs text-gray-500"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-xs font-medium text-gray-600 mb-1">المخزون المتاح</label>
                                        <div id="availableStock" class="text-sm font-medium text-green-600">0</div>
                                    </div>
                                    <div>
                                        <label class="block text-xs font-medium text-gray-600 mb-1">آخر سعر شراء</label>
                                        <div id="lastPurchasePrice" class="text-sm font-medium text-blue-600">0</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Item Entry Form -->
                            <div class="grid grid-cols-2 md:grid-cols-6 gap-2">
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-1">الكمية *</label>
                                    <input type="number" id="newItemQuantity" placeholder="الكمية" min="0" step="0.01"
                                           class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                                           onchange="calculateItemTotal()" onkeyup="calculateItemTotal()">
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-1">الوحدة</label>
                                    <select id="newItemUnit" onchange="updateUnitPrice()"
                                            class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                                        <option value="">اختر الوحدة</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-1">سعر الوحدة *</label>
                                    <input type="number" id="newItemPrice" placeholder="السعر" min="0" step="0.01"
                                           class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                                           onchange="calculateItemTotal()" onkeyup="calculateItemTotal()">
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-1">الخصم</label>
                                    <input type="number" id="newItemDiscount" placeholder="0" min="0" step="0.01"
                                           class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                                           onchange="calculateItemTotal()" onkeyup="calculateItemTotal()">
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-1">الإجمالي</label>
                                    <input type="number" id="newItemTotal" readonly placeholder="الإجمالي"
                                           class="w-full px-2 py-1.5 border border-gray-300 rounded bg-gray-100 text-sm font-medium">
                                </div>
                                <div class="flex flex-col space-y-1">
                                    <button onclick="confirmAddItem()" class="bg-green-600 text-white px-2 py-1.5 rounded hover:bg-green-700 text-xs">
                                        ✓ إضافة
                                    </button>
                                    <button onclick="cancelAddItem()" class="bg-gray-600 text-white px-2 py-1.5 rounded hover:bg-gray-700 text-xs">
                                        ✕ إلغاء
                                    </button>
                                </div>
                            </div>

                            <!-- Quick Add Buttons -->
                            <div class="mt-3 flex flex-wrap gap-2">
                                <button onclick="addQuickItem('raw_materials')" class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded text-xs hover:bg-yellow-200">
                                    🌾 خامات
                                </button>
                                <button onclick="addQuickItem('products')" class="bg-green-100 text-green-800 px-3 py-1 rounded text-xs hover:bg-green-200">
                                    🍞 منتجات
                                </button>
                                <button onclick="addQuickItem('services')" class="bg-blue-100 text-blue-800 px-3 py-1 rounded text-xs hover:bg-blue-200">
                                    🔧 خدمات
                                </button>
                                <button onclick="addQuickItem('supplies')" class="bg-purple-100 text-purple-800 px-3 py-1 rounded text-xs hover:bg-purple-200">
                                    📦 مستلزمات
                                </button>
                            </div>
                        </div>

                        <!-- Items Table - Compact -->
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">الصنف</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">الكود</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">الكمية</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">الوحدة</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">السعر</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">الخصم</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">الإجمالي</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">المخزن</th>
                                        <th class="px-2 py-2 text-center text-xs font-medium text-gray-500">إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="invoiceItemsTable" class="bg-white divide-y divide-gray-200">
                                    <!-- Items will be added here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Invoice Totals - Compact -->
                    <div class="p-4 bg-gray-50 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                            <!-- Notes -->
                            <div class="lg:col-span-2">
                                <label class="block text-xs font-medium text-gray-600 mb-1">ملاحظات</label>
                                <textarea id="invoiceNotes" rows="3"
                                          class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                                          placeholder="ملاحظات إضافية..."></textarea>
                            </div>

                            <!-- Totals - Compact -->
                            <div class="bg-white p-3 rounded border">
                                <div class="space-y-2">
                                    <div class="flex justify-between items-center text-sm">
                                        <span class="text-gray-600">المجموع الفرعي:</span>
                                        <span id="subtotalAmount" class="font-semibold text-gray-900">0</span>
                                    </div>
                                    <div class="flex justify-between items-center text-sm">
                                        <span class="text-gray-600">الضريبة (15%):</span>
                                        <span id="taxAmount" class="font-semibold text-gray-900">0</span>
                                    </div>
                                    <div class="flex justify-between items-center pt-2 border-t border-gray-200">
                                        <span class="font-bold text-gray-900">الإجمالي النهائي:</span>
                                        <span id="totalAmount" class="text-lg font-bold text-blue-600">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons - Compact -->
                        <div class="flex justify-between items-center mt-4 pt-3 border-t border-gray-200">
                            <!-- Voucher Actions -->
                            <div class="flex space-x-2">
                                <button onclick="createPaymentVoucher()" class="bg-red-600 text-white px-3 py-2 rounded hover:bg-red-700 text-sm flex items-center transition-all duration-200 hover:shadow-lg">
                                    <span class="ml-1">💸</span>
                                    إنشاء سند صرف
                                </button>
                                <button onclick="viewVouchers()" class="bg-purple-600 text-white px-3 py-2 rounded hover:bg-purple-700 text-sm flex items-center transition-all duration-200 hover:shadow-lg">
                                    <span class="ml-1">📋</span>
                                    السندات المرتبطة
                                </button>
                            </div>

                            <!-- Main Actions -->
                            <div class="flex space-x-2">
                                <button onclick="exportToExcel()" class="bg-emerald-600 text-white px-3 py-2 rounded hover:bg-emerald-700 text-sm flex items-center">
                                    <span class="ml-1">📊</span>
                                    تصدير Excel
                                </button>
                                <button onclick="printInvoice()" class="bg-indigo-600 text-white px-3 py-2 rounded hover:bg-indigo-700 text-sm flex items-center">
                                    <span class="ml-1">🖨️</span>
                                    طباعة
                                </button>
                                <button onclick="printReceipt()" class="bg-teal-600 text-white px-3 py-2 rounded hover:bg-teal-700 text-sm flex items-center">
                                    <span class="ml-1">🧾</span>
                                    إيصال
                                </button>
                                <button onclick="saveDraft()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 text-sm">
                                    💾 حفظ مسودة
                                </button>
                                <button onclick="saveAndPrint()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm">
                                    🖨️ حفظ وطباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Raw Material Modal -->
    <div id="addRawMaterialModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900 flex items-center">
                        <span class="text-2xl ml-2">🌾</span>
                        إضافة خامة جديدة
                    </h3>
                    <button onclick="closeAddRawMaterialModal()" class="text-gray-400 hover:text-gray-600">
                        <span class="text-xl">×</span>
                    </button>
                </div>

                <!-- Raw Material Form -->
                <form id="rawMaterialForm" onsubmit="saveRawMaterial(event)">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Item Name -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-1">اسم الخامة *</label>
                            <input type="text" id="rawMaterialName" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500"
                                   placeholder="مثال: دقيق أبيض فاخر">
                            <div id="nameValidation" class="text-xs mt-1"></div>
                        </div>

                        <!-- Item Code -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">كود الخامة *</label>
                            <input type="text" id="rawMaterialCode" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500"
                                   placeholder="مثال: FL001">
                            <div id="codeValidation" class="text-xs mt-1"></div>
                        </div>

                        <!-- Barcode -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الباركود</label>
                            <input type="text" id="rawMaterialBarcode"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500"
                                   placeholder="مثال: 1234567890123">
                            <div id="barcodeValidation" class="text-xs mt-1"></div>
                        </div>

                        <!-- Category -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الفئة</label>
                            <select id="rawMaterialCategory"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500">
                                <option value="خامات">خامات</option>
                                <option value="دقيق">دقيق</option>
                                <option value="سكر">سكر</option>
                                <option value="زيوت">زيوت</option>
                                <option value="بيض ومنتجات ألبان">بيض ومنتجات ألبان</option>
                                <option value="توابل ومحسنات">توابل ومحسنات</option>
                                <option value="مواد حافظة">مواد حافظة</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>

                        <!-- Warehouse -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">المخزن</label>
                            <select id="rawMaterialWarehouse"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500">
                                <option value="1">مخزن الخامات الرئيسي</option>
                                <option value="2">مخزن الخامات الفرعي</option>
                                <option value="3">مخزن التبريد</option>
                            </select>
                        </div>

                        <!-- Units Section -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">وحدات القياس</label>
                            <div class="border border-gray-300 rounded-md p-3">
                                <div id="unitsContainer">
                                    <!-- Default unit -->
                                    <div class="unit-row grid grid-cols-4 gap-2 mb-2">
                                        <div>
                                            <input type="text" placeholder="اسم الوحدة" class="unit-name w-full px-2 py-1 border border-gray-300 rounded text-sm" value="كيلو">
                                        </div>
                                        <div>
                                            <input type="number" placeholder="المعامل" class="unit-factor w-full px-2 py-1 border border-gray-300 rounded text-sm" value="1" step="0.001">
                                        </div>
                                        <div>
                                            <input type="number" placeholder="السعر" class="unit-price w-full px-2 py-1 border border-gray-300 rounded text-sm" step="0.01">
                                        </div>
                                        <div class="flex items-center">
                                            <label class="flex items-center text-xs">
                                                <input type="checkbox" class="unit-default ml-1" checked>
                                                افتراضي
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" onclick="addUnitRow()" class="bg-gray-500 text-white px-2 py-1 rounded text-xs hover:bg-gray-600">
                                    + إضافة وحدة
                                </button>
                            </div>
                        </div>

                        <!-- Stock Information -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الحد الأدنى للمخزون</label>
                            <input type="number" id="rawMaterialMinStock" min="0" step="0.01"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500"
                                   placeholder="20">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الحد الأقصى للمخزون</label>
                            <input type="number" id="rawMaterialMaxStock" min="0" step="0.01"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500"
                                   placeholder="500">
                        </div>

                        <!-- Description -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
                            <textarea id="rawMaterialDescription" rows="2"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500"
                                      placeholder="وصف مختصر للخامة..."></textarea>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeAddRawMaterialModal()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                            إلغاء
                        </button>
                        <button type="submit" class="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700">
                            حفظ الخامة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Include advanced libraries -->
    <script src="advanced-excel.js"></script>
    <script src="advanced-print.js"></script>
    <script src="excel-utils.js"></script>
    <script src="global-advanced-features.js"></script>
    <script src="activate-advanced-features.js"></script>

    <script>
        // Toggle bank field based on payment method
        function toggleBankField() {
            const paymentMethod = document.getElementById('paymentMethod').value;
            const bankField = document.getElementById('bankField');

            if (paymentMethod === 'bank') {
                bankField.style.display = 'block';
                document.getElementById('bankId').required = true;
            } else {
                bankField.style.display = 'none';
                document.getElementById('bankId').required = false;
            }
        }

        // Create payment voucher from invoice data
        function createPaymentVoucher() {
            const invoiceData = {
                invoiceNumber: document.getElementById('invoiceNumber').value,
                date: document.getElementById('invoiceDate').value,
                sellerId: document.getElementById('sellerId').value,
                sellerType: document.querySelector('input[name="sellerType"]:checked').value,
                amount: document.getElementById('totalAmount').textContent,
                paymentMethod: document.getElementById('paymentMethod').value,
                bankId: document.getElementById('bankId').value,
                notes: document.getElementById('invoiceNotes').value
            };

            // Store invoice data for voucher creation
            localStorage.setItem('voucherData', JSON.stringify({
                type: 'payment', // سند صرف
                source: 'purchase_invoice',
                data: invoiceData
            }));

            // Open payment voucher page
            window.open('payment-voucher.html', '_blank');
        }

        // View related vouchers
        function viewVouchers() {
            const invoiceNumber = document.getElementById('invoiceNumber').value;
            if (!invoiceNumber) {
                alert('يجب حفظ الفاتورة أولاً لعرض السندات المرتبطة');
                return;
            }

            // Open vouchers list filtered by this invoice
            window.open(`vouchers.html?invoice=${invoiceNumber}`, '_blank');
        }

        // Advanced printing function
        function printInvoice() {
            const invoiceData = collectInvoiceData();
            if (typeof window.advancedPrint !== 'undefined') {
                window.advancedPrint.printInvoice(invoiceData, {
                    showLogo: true,
                    showHeader: true,
                    showFooter: true,
                    copies: 1,
                    paperSize: 'A4'
                });
            } else {
                window.print();
            }
        }

        // Print thermal receipt
        function printReceipt() {
            const invoiceData = collectInvoiceData();
            if (typeof window.advancedPrint !== 'undefined') {
                window.advancedPrint.printReceipt(invoiceData, {
                    width: '80mm',
                    fontSize: '12px'
                });
            } else {
                alert('نظام الطباعة الحرارية غير متوفر');
            }
        }

        // Export to Excel
        function exportToExcel() {
            const invoiceData = collectInvoiceData();

            if (typeof window.advancedExcel !== 'undefined') {
                const reportData = {
                    main: [invoiceData],
                    summary: {
                        totalAmount: invoiceData.total,
                        itemsCount: invoiceData.items ? invoiceData.items.length : 0
                    }
                };

                window.advancedExcel.exportFinancialReport(
                    reportData,
                    'purchase_invoice',
                    { date: invoiceData.date }
                );
            } else {
                if (typeof window.excelUtils !== 'undefined') {
                    const data = [{
                        'رقم الفاتورة': invoiceData.number,
                        'التاريخ': invoiceData.date,
                        'المورد': invoiceData.supplier_name || 'مورد نقدي',
                        'الإجمالي': invoiceData.total
                    }];
                    window.excelUtils.exportToExcel(data, 'فاتورة_مشتريات', 'الفاتورة');
                }
            }
        }

        // Collect invoice data
        function collectInvoiceData() {
            try {
                const invoiceData = {
                    number: document.getElementById('invoiceNumber').value,
                    date: document.getElementById('invoiceDate').value,
                    time: new Date().toISOString(),
                    type: 'purchase',
                    branch_id: document.getElementById('branchId').value,
                    supplier_id: document.getElementById('sellerId').value,
                    supplier_name: document.getElementById('sellerId').selectedOptions[0]?.text || 'مورد نقدي',
                    cash_register_id: document.getElementById('cashRegisterId').value,
                    warehouse_id: document.getElementById('warehouseId').value,
                    reference_number: document.getElementById('referenceNumber').value,
                    payment_method: document.getElementById('paymentMethod').value,
                    bank_id: document.getElementById('bankId').value,
                    items: getInvoiceItems(),
                    subtotal: parseFloat(document.getElementById('subtotalAmount')?.textContent.replace(/[^\d.-]/g, '')) || 0,
                    tax: parseFloat(document.getElementById('taxAmount')?.textContent.replace(/[^\d.-]/g, '')) || 0,
                    total: parseFloat(document.getElementById('totalAmount')?.textContent.replace(/[^\d.-]/g, '')) || 0,
                    notes: document.getElementById('invoiceNotes').value,
                    created_by: getCurrentUser(),
                    seller_type: document.querySelector('input[name="sellerType"]:checked')?.value || 'supplier'
                };

                console.log('Collected invoice data:', invoiceData);
                return invoiceData;
            } catch (error) {
                console.error('Error collecting invoice data:', error);
                return null;
            }
        }

        // Get invoice items from table
        function getInvoiceItems() {
            try {
                const items = [];
                const tableBody = document.getElementById('invoiceItemsTable');

                if (tableBody) {
                    const rows = tableBody.querySelectorAll('tr');

                    rows.forEach((row, index) => {
                        const cells = row.querySelectorAll('td');
                        if (cells.length >= 7) {
                            const item = {
                                id: Date.now() + index,
                                name: cells[0]?.textContent?.trim() || '',
                                code: cells[1]?.textContent?.trim() || '',
                                quantity: parseFloat(cells[2]?.textContent?.replace(/[^\d.-]/g, '')) || 0,
                                unit: cells[3]?.textContent?.trim() || '',
                                price: parseFloat(cells[4]?.textContent?.replace(/[^\d.-]/g, '')) || 0,
                                discount: parseFloat(cells[5]?.textContent?.replace(/[^\d.-]/g, '')) || 0,
                                total: parseFloat(cells[6]?.textContent?.replace(/[^\d.-]/g, '')) || 0,
                                warehouse: cells[7]?.textContent?.trim() || ''
                            };

                            if (item.name && item.quantity > 0) {
                                items.push(item);
                            }
                        }
                    });
                }

                // If no items in table, return sample data for testing
                if (items.length === 0) {
                    return [
                        {
                            id: Date.now(),
                            name: 'دقيق أبيض فاخر',
                            code: 'FL001',
                            quantity: 50,
                            unit: 'كيس 50 كيلو',
                            price: 85.00,
                            discount: 0,
                            total: 4250.00,
                            warehouse: 'المخزن الرئيسي'
                        }
                    ];
                }

                return items;
            } catch (error) {
                console.error('Error getting invoice items:', error);
                return [];
            }
        }

        // Get current user
        function getCurrentUser() {
            try {
                const user = JSON.parse(localStorage.getItem('anwar_bakery_current_user') || '{}');
                return user.username || 'admin';
            } catch (error) {
                return 'admin';
            }
        }

        // Save draft invoice
        function saveDraft() {
            try {
                const invoiceData = collectInvoiceData();

                // Validate required fields
                if (!invoiceData.date || !invoiceData.supplier_id) {
                    showMessage('يرجى ملء الحقول المطلوبة (التاريخ والمورد)', 'error');
                    return false;
                }

                // Add draft status
                invoiceData.status = 'draft';
                invoiceData.id = Date.now();
                invoiceData.createdAt = new Date().toISOString();
                invoiceData.updatedAt = new Date().toISOString();

                // Save to localStorage
                const savedResult = saveInvoiceToStorage(invoiceData);

                if (savedResult.success) {
                    showMessage('✅ تم حفظ المسودة بنجاح!', 'success');

                    // Update invoice number counter
                    updateInvoiceCounter();

                    // Generate new invoice number for next invoice
                    generateInvoiceNumber();

                    return true;
                } else {
                    showMessage('❌ خطأ في حفظ المسودة: ' + savedResult.error, 'error');
                    return false;
                }
            } catch (error) {
                console.error('Error saving draft:', error);
                showMessage('❌ حدث خطأ أثناء حفظ المسودة', 'error');
                return false;
            }
        }

        // Save and print invoice
        function saveAndPrint() {
            try {
                const invoiceData = collectInvoiceData();

                // Validate required fields
                if (!invoiceData.date || !invoiceData.supplier_id) {
                    showMessage('يرجى ملء الحقول المطلوبة (التاريخ والمورد)', 'error');
                    return false;
                }

                if (!invoiceData.items || invoiceData.items.length === 0) {
                    showMessage('يرجى إضافة أصناف للفاتورة', 'error');
                    return false;
                }

                // Add confirmed status
                invoiceData.status = 'confirmed';
                invoiceData.id = Date.now();
                invoiceData.createdAt = new Date().toISOString();
                invoiceData.updatedAt = new Date().toISOString();

                // Save to localStorage
                const savedResult = saveInvoiceToStorage(invoiceData);

                if (savedResult.success) {
                    showMessage('✅ تم حفظ الفاتورة بنجاح!', 'success');

                    // Update invoice number counter
                    updateInvoiceCounter();

                    // Create accounting entries
                    createAccountingEntries(invoiceData);

                    // Update inventory
                    updateInventory(invoiceData);

                    // Print invoice
                    setTimeout(() => {
                        printInvoiceAdvanced(invoiceData);
                    }, 1000);

                    // Generate new invoice number for next invoice
                    generateInvoiceNumber();

                    return true;
                } else {
                    showMessage('❌ خطأ في حفظ الفاتورة: ' + savedResult.error, 'error');
                    return false;
                }
            } catch (error) {
                console.error('Error saving and printing:', error);
                showMessage('❌ حدث خطأ أثناء حفظ وطباعة الفاتورة', 'error');
                return false;
            }
        }

        // Save invoice to localStorage
        function saveInvoiceToStorage(invoiceData) {
            try {
                // Get existing invoices
                let invoices = JSON.parse(localStorage.getItem('anwar_bakery_purchase_invoices') || '[]');

                // Add new invoice
                invoices.push(invoiceData);

                // Save back to localStorage
                localStorage.setItem('anwar_bakery_purchase_invoices', JSON.stringify(invoices));

                console.log('Invoice saved successfully:', invoiceData);
                return { success: true };
            } catch (error) {
                console.error('Error saving invoice:', error);
                return { success: false, error: error.message };
            }
        }

        // Update invoice counter
        function updateInvoiceCounter() {
            try {
                const currentCounter = parseInt(localStorage.getItem('anwar_bakery_purchase_counter') || '0');
                localStorage.setItem('anwar_bakery_purchase_counter', (currentCounter + 1).toString());
            } catch (error) {
                console.error('Error updating counter:', error);
            }
        }

        // Generate new invoice number
        function generateInvoiceNumber() {
            try {
                const counter = parseInt(localStorage.getItem('anwar_bakery_purchase_counter') || '0') + 1;
                const invoiceNumber = `PUR-${new Date().getFullYear()}-${counter.toString().padStart(3, '0')}`;
                document.getElementById('invoiceNumber').value = invoiceNumber;
            } catch (error) {
                console.error('Error generating invoice number:', error);
            }
        }

        // Create accounting entries
        function createAccountingEntries(invoiceData) {
            try {
                const journalEntries = [];
                const entryDate = new Date().toISOString();

                // Purchase entry: Debit Inventory, Credit Supplier/Cash
                const purchaseEntry = {
                    id: Date.now(),
                    date: invoiceData.date,
                    reference: invoiceData.number,
                    description: `فاتورة مشتريات رقم ${invoiceData.number}`,
                    entries: [
                        {
                            account: 'المخزون',
                            debit: invoiceData.total,
                            credit: 0
                        },
                        {
                            account: invoiceData.payment_method === 'cash' ? 'الصندوق' : 'الموردين',
                            debit: 0,
                            credit: invoiceData.total
                        }
                    ],
                    createdAt: entryDate
                };

                journalEntries.push(purchaseEntry);

                // Save journal entries
                const existingEntries = JSON.parse(localStorage.getItem('anwar_bakery_journal_entries') || '[]');
                existingEntries.push(...journalEntries);
                localStorage.setItem('anwar_bakery_journal_entries', JSON.stringify(existingEntries));

                console.log('Accounting entries created:', journalEntries);
            } catch (error) {
                console.error('Error creating accounting entries:', error);
            }
        }

        // Update inventory
        function updateInventory(invoiceData) {
            try {
                if (!invoiceData.items || invoiceData.items.length === 0) return;

                // Get existing inventory
                let inventory = JSON.parse(localStorage.getItem('anwar_bakery_inventory') || '[]');

                invoiceData.items.forEach(item => {
                    const existingItem = inventory.find(inv => inv.itemCode === item.code);

                    if (existingItem) {
                        // Update existing item
                        existingItem.quantity += parseFloat(item.quantity);
                        existingItem.lastPurchasePrice = parseFloat(item.price);
                        existingItem.lastPurchaseDate = invoiceData.date;
                        existingItem.updatedAt = new Date().toISOString();
                    } else {
                        // Add new item to inventory
                        inventory.push({
                            id: Date.now() + Math.random(),
                            itemCode: item.code,
                            itemName: item.name,
                            quantity: parseFloat(item.quantity),
                            unit: item.unit,
                            lastPurchasePrice: parseFloat(item.price),
                            lastPurchaseDate: invoiceData.date,
                            createdAt: new Date().toISOString(),
                            updatedAt: new Date().toISOString()
                        });
                    }
                });

                // Save updated inventory
                localStorage.setItem('anwar_bakery_inventory', JSON.stringify(inventory));

                console.log('Inventory updated successfully');
            } catch (error) {
                console.error('Error updating inventory:', error);
            }
        }

        // Advanced print function
        function printInvoiceAdvanced(invoiceData) {
            try {
                if (typeof window.advancedPrint !== 'undefined') {
                    window.advancedPrint.printInvoice(invoiceData, {
                        showLogo: true,
                        showHeader: true,
                        showFooter: true,
                        copies: 1,
                        paperSize: 'A4',
                        orientation: 'portrait'
                    });
                    showMessage('✅ تم إرسال الفاتورة للطباعة!', 'success');
                } else {
                    // Fallback to basic print
                    window.print();
                    showMessage('✅ تم إرسال الصفحة للطباعة!', 'success');
                }
            } catch (error) {
                console.error('Error printing invoice:', error);
                showMessage('❌ خطأ في طباعة الفاتورة: ' + error.message, 'error');
            }
        }

        // Show message function
        function showMessage(message, type = 'info') {
            const messageContainer = document.getElementById('messageContainer');
            const messageDiv = document.createElement('div');

            const bgColor = type === 'success' ? 'bg-green-100 border-green-400 text-green-700' :
                           type === 'error' ? 'bg-red-100 border-red-400 text-red-700' :
                           'bg-blue-100 border-blue-400 text-blue-700';

            messageDiv.className = `border-l-4 p-4 mb-4 ${bgColor}`;
            messageDiv.innerHTML = `
                <div class="flex">
                    <div class="flex-shrink-0">
                        <span class="text-lg">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                    </div>
                    <div class="mr-3">
                        <p class="text-sm">${message}</p>
                    </div>
                </div>
            `;

            messageContainer.appendChild(messageDiv);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 5000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Set today's date
            document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];

            // Generate initial invoice number
            generateInvoiceNumber();

            console.log('Purchase invoice page initialized');
        });
    </script>
    <script src="purchase-invoice.js"></script>
    <script src="activate-printing-system.js"></script>
</body>
</html>
