<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قيد محاسبي - مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .custom-scroll {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 #f7fafc;
        }

        .custom-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scroll::-webkit-scrollbar-track {
            background: #f7fafc;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        .required-field {
            border-color: #ef4444;
        }

        .balance-warning {
            background-color: #fef2f2;
            border-color: #fecaca;
            color: #dc2626;
        }

        .balance-balanced {
            background-color: #f0fdf4;
            border-color: #bbf7d0;
            color: #16a34a;
        }

        .entry-row {
            transition: all 0.3s ease;
        }

        .entry-row:hover {
            background-color: #f9fafb;
        }

        .delete-btn {
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .entry-row:hover .delete-btn {
            opacity: 1;
        }

        .totals-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .validation-error {
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Mobile menu overlay -->
    <div id="mobileOverlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 lg:hidden hidden"></div>

    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform translate-x-full lg:translate-x-0 lg:static lg:inset-0 sidebar-transition">
            <div class="h-16 px-4 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between">
                <h1 id="sidebarCompanyName" class="text-white text-lg font-bold">مخبز أنوار الحي</h1>
                <button onclick="toggleSidebar()" class="lg:hidden text-white">
                    <span class="text-xl">✕</span>
                </button>
            </div>

            <nav class="mt-5 px-2 space-y-1 h-full overflow-y-auto pb-20">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    لوحة التحكم
                </a>
                <a href="users-management.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👥</span>
                    إدارة المستخدمين
                </a>
                <a href="company-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏢</span>
                    بيانات المنشأة
                </a>
                <a href="system-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">⚙️</span>
                    إعدادات النظام
                </a>
                <a href="system-admin.html" class="flex items-center px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 hover:text-red-700 rounded-md border border-red-200">
                    <span class="ml-3">🔧</span>
                    إدارة النظام المتقدمة
                </a>
                <a href="branches-warehouses.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏪</span>
                    الفروع والمخازن
                </a>
                <a href="units.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📏</span>
                    وحدات القياس
                </a>
                <a href="chart-of-accounts.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🌳</span>
                    شجرة الحسابات
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="damage-entry.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🗑️</span>
                    قيد التالف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🚚</span>
                    الموردين
                </a>
                <a href="employees.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👨‍💼</span>
                    الموظفين
                </a>
                <a href="cash-registers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">💰</span>
                    الصناديق
                </a>
                <a href="banks.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏦</span>
                    البنوك
                </a>
                <a href="owners.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👑</span>
                    الملاك
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="vouchers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📋</span>
                    السندات
                </a>
                <a href="journal-entry.html" class="flex items-center px-3 py-2 text-sm font-medium text-purple-600 bg-purple-50 rounded-md">
                    <span class="ml-3">📝</span>
                    القيود اليومية
                </a>
                <a href="inventory.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📊</span>
                    المخزون
                </a>
                <a href="reports.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📈</span>
                    التقارير
                </a>

                <!-- User Info & Logout -->
                <div class="absolute bottom-4 left-2 right-2">
                    <div class="bg-gray-50 rounded-lg p-3 mb-2">
                        <p id="userFullName" class="text-sm font-medium text-gray-900">مدير النظام</p>
                        <p id="userRole" class="text-xs text-gray-500">admin</p>
                    </div>
                    <button onclick="logout()" class="w-full text-right px-3 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50 flex items-center">
                        <span class="ml-3">🚪</span>
                        تسجيل الخروج
                    </button>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col min-h-0">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <button onclick="toggleSidebar()" class="lg:hidden text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">☰</span>
                            </button>
                            <a href="vouchers.html" class="text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">←</span>
                            </a>
                            <span class="text-lg font-semibold text-gray-900">قيد محاسبي جديد</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-500" id="currentDateTime"></span>
                            <button onclick="window.history.back()" class="text-gray-600 hover:text-gray-800 px-2 py-2 rounded text-sm">
                                ← رجوع
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6 custom-scroll">
                <!-- Success/Error Messages -->
                <div id="messageContainer" class="mb-6"></div>

                <!-- Balance Status -->
                <div id="balanceStatus" class="mb-6" style="display: none;">
                    <div class="border rounded-lg p-4">
                        <div class="flex items-center">
                            <span class="ml-2" id="balanceIcon">⚖️</span>
                            <span id="balanceText">حالة توازن القيد</span>
                        </div>
                    </div>
                </div>

                <!-- Journal Entry Form -->
                <div class="bg-white rounded-lg shadow-sm">
                    <!-- Entry Header -->
                    <div class="p-4 border-b border-gray-200">
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                            <!-- Entry Number -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">رقم القيد</label>
                                <input type="text" id="entryNumber" readonly
                                       class="w-full px-2 py-1.5 border border-gray-300 rounded bg-gray-50 text-sm"
                                       value="JE-2024-001">
                            </div>

                            <!-- Entry Date -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">التاريخ *</label>
                                <input type="date" id="entryDate" required
                                       class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-purple-500 text-sm">
                            </div>

                            <!-- Branch -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">الفرع *</label>
                                <select id="branchId" required
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-purple-500 text-sm">
                                    <option value="">اختر الفرع</option>
                                </select>
                            </div>

                            <!-- Entry Type -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">نوع القيد</label>
                                <select id="entryType"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-purple-500 text-sm">
                                    <option value="general">قيد عام</option>
                                    <option value="adjustment">قيد تسوية</option>
                                    <option value="closing">قيد إقفال</option>
                                    <option value="opening">قيد افتتاحي</option>
                                </select>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mt-3">
                            <label class="block text-xs font-medium text-gray-600 mb-1">البيان *</label>
                            <input type="text" id="description" required placeholder="وصف القيد المحاسبي..."
                                   class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-purple-500 text-sm">
                        </div>
                    </div>

                    <!-- Journal Entries Table -->
                    <div class="p-4">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-base font-semibold text-gray-900">تفاصيل القيد</h3>
                            <button onclick="addEntryRow()" class="bg-purple-600 text-white px-3 py-1.5 rounded hover:bg-purple-700 flex items-center text-sm">
                                <span class="ml-1">➕</span>
                                إضافة سطر
                            </button>
                        </div>

                        <!-- Entries Table -->
                        <div class="overflow-x-auto">
                            <table class="w-full border border-gray-200 rounded-lg">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 border-b">#</th>
                                        <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 border-b">الحساب *</th>
                                        <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 border-b">البيان</th>
                                        <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 border-b">مدين</th>
                                        <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 border-b">دائن</th>
                                        <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 border-b">إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="entriesTableBody">
                                    <!-- Entry rows will be added here -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Quick Add Buttons -->
                        <div class="mt-4 flex flex-wrap gap-2">
                            <button onclick="addQuickEntry('cash')" class="bg-green-100 text-green-800 px-3 py-1 rounded text-xs hover:bg-green-200">
                                💰 نقدية
                            </button>
                            <button onclick="addQuickEntry('bank')" class="bg-blue-100 text-blue-800 px-3 py-1 rounded text-xs hover:bg-blue-200">
                                🏦 بنوك
                            </button>
                            <button onclick="addQuickEntry('customer')" class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded text-xs hover:bg-yellow-200">
                                👤 عملاء
                            </button>
                            <button onclick="addQuickEntry('supplier')" class="bg-orange-100 text-orange-800 px-3 py-1 rounded text-xs hover:bg-orange-200">
                                🏭 موردين
                            </button>
                            <button onclick="addQuickEntry('expense')" class="bg-red-100 text-red-800 px-3 py-1 rounded text-xs hover:bg-red-200">
                                💸 مصروفات
                            </button>
                            <button onclick="addQuickEntry('revenue')" class="bg-purple-100 text-purple-800 px-3 py-1 rounded text-xs hover:bg-purple-200">
                                💵 إيرادات
                            </button>
                        </div>
                    </div>

                    <!-- Totals Section -->
                    <div class="totals-section p-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold" id="totalDebit">0</div>
                                <div class="text-sm opacity-90">إجمالي المدين</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold" id="totalCredit">0</div>
                                <div class="text-sm opacity-90">إجمالي الدائن</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold" id="difference">0</div>
                                <div class="text-sm opacity-90">الفرق</div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="p-4 border-t border-gray-200">
                        <div class="flex justify-between items-center">
                            <div class="flex space-x-2">
                                <button onclick="previewEntry()" class="bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 text-sm">
                                    👁️ معاينة
                                </button>
                                <button onclick="validateEntry()" class="bg-yellow-600 text-white px-3 py-2 rounded hover:bg-yellow-700 text-sm">
                                    ✅ تحقق
                                </button>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="saveDraft()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 text-sm">
                                    💾 حفظ مسودة
                                </button>
                                <button onclick="saveAndPost()" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 text-sm" id="saveButton" disabled>
                                    📋 حفظ وترحيل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Smart Suggestions -->
                <div id="smartSuggestions" class="mt-6 bg-white rounded-lg shadow-sm p-4" style="display: none;">
                    <h3 class="text-base font-semibold text-gray-900 mb-3">💡 اقتراحات ذكية</h3>
                    <div id="suggestionsContent" class="space-y-2">
                        <!-- Suggestions will be populated here -->
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Print Template (Hidden) -->
    <div id="printTemplate" style="display: none;">
        <div style="max-width: 800px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
            <!-- Header -->
            <div style="text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px;">
                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 15px;">
                    <div style="font-size: 48px; margin-left: 15px;">🍞</div>
                    <div>
                        <h1 id="printCompanyName" style="margin: 0; font-size: 24px; color: #333;">مخبز أنوار الحي</h1>
                        <p id="printCompanySlogan" style="margin: 5px 0 0 0; color: #666; font-size: 14px;">جودة تستحق الثقة</p>
                    </div>
                </div>
                <h2 style="margin: 0; font-size: 20px; color: #7c3aed;">قيد محاسبي</h2>
                <p style="margin: 5px 0 0 0; color: #666;">رقم القيد: <span id="printEntryNumber"></span></p>
            </div>

            <!-- Entry Details -->
            <div style="margin-bottom: 30px;">
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold; width: 25%;">التاريخ:</td>
                        <td style="padding: 8px; border: 1px solid #ddd;" id="printDate"></td>
                        <td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold; width: 25%;">الفرع:</td>
                        <td style="padding: 8px; border: 1px solid #ddd;" id="printBranch"></td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">نوع القيد:</td>
                        <td style="padding: 8px; border: 1px solid #ddd;" id="printEntryType"></td>
                        <td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">المدخل:</td>
                        <td style="padding: 8px; border: 1px solid #ddd;" id="printCreatedBy"></td>
                    </tr>
                    <tr>
                        <td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">البيان:</td>
                        <td colspan="3" style="padding: 8px; border: 1px solid #ddd;" id="printDescription"></td>
                    </tr>
                </table>
            </div>

            <!-- Journal Entries -->
            <div style="margin-bottom: 30px;">
                <h3 style="color: #333; border-bottom: 1px solid #ddd; padding-bottom: 5px;">تفاصيل القيد:</h3>
                <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                    <thead>
                        <tr style="background: #f3f4f6;">
                            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">#</th>
                            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">الحساب</th>
                            <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">البيان</th>
                            <th style="padding: 8px; border: 1px solid #ddd; text-align: center;">مدين</th>
                            <th style="padding: 8px; border: 1px solid #ddd; text-align: center;">دائن</th>
                        </tr>
                    </thead>
                    <tbody id="printEntries">
                        <!-- Entries will be populated here -->
                    </tbody>
                    <tfoot>
                        <tr style="background: #f3f4f6; font-weight: bold;">
                            <td colspan="3" style="padding: 8px; border: 1px solid #ddd; text-align: right;">الإجمالي:</td>
                            <td style="padding: 8px; border: 1px solid #ddd; text-align: center;" id="printTotalDebit">0</td>
                            <td style="padding: 8px; border: 1px solid #ddd; text-align: center;" id="printTotalCredit">0</td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- Footer -->
            <div style="margin-top: 50px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #ddd; padding-top: 15px;">
                <p>تم إنشاء القيد بواسطة: <span id="printUserName"></span></p>
                <p>تاريخ الإنشاء: <span id="printCreatedAt"></span></p>
            </div>
        </div>
    </div>

    <script src="journal-entry.js"></script>
</body>
</html>
