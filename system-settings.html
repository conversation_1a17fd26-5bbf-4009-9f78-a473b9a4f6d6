<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - مخبز أنور</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="app-settings.js"></script>
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .tab-button.active {
            border-color: #3B82F6;
            color: #3B82F6;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Mobile menu overlay -->
    <div id="mobileOverlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 lg:hidden hidden"></div>

    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform translate-x-full lg:translate-x-0 lg:static lg:inset-0 sidebar-transition">
            <div class="h-16 px-4 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between">
                <h1 id="sidebarCompanyName" class="text-white text-lg font-bold">مخبز أنور</h1>
                <button onclick="toggleSidebar()" class="lg:hidden text-white">
                    <span class="text-xl">✕</span>
                </button>
            </div>

            <nav class="mt-5 px-2 space-y-1 h-full overflow-y-auto pb-20">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    لوحة التحكم
                </a>
                <a href="users-management.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👥</span>
                    إدارة المستخدمين
                </a>
                <a href="company-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏢</span>
                    بيانات المنشأة
                </a>
                <a href="system-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">
                    <span class="ml-3">⚙️</span>
                    إعدادات النظام
                </a>
                <a href="branches-warehouses.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏪</span>
                    الفروع والمخازن
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🚚</span>
                    الموردين
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="inventory.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📊</span>
                    المخزون
                </a>
                <a href="reports.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📈</span>
                    التقارير
                </a>

                <!-- User Info & Logout -->
                <div class="absolute bottom-4 left-2 right-2">
                    <div class="bg-gray-50 rounded-lg p-3 mb-2">
                        <p id="userFullName" class="text-sm font-medium text-gray-900">مدير النظام</p>
                        <p id="userRole" class="text-xs text-gray-500">admin</p>
                    </div>
                    <button onclick="logout()" class="w-full text-right px-3 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50 flex items-center">
                        <span class="ml-3">🚪</span>
                        تسجيل الخروج
                    </button>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 overflow-hidden">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <button onclick="toggleSidebar()" class="lg:hidden text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">☰</span>
                            </button>
                            <span class="text-lg font-semibold text-gray-900">إعدادات النظام</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button onclick="exportSettings()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center">
                                <span class="ml-2">📤</span>
                                تصدير الإعدادات
                            </button>
                            <button onclick="importSettings()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center">
                                <span class="ml-2">📥</span>
                                استيراد الإعدادات
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2 flex items-center">
                        <span class="ml-3">⚙️</span>
                        إعدادات النظام
                    </h1>
                    <p class="text-gray-600">
                        إدارة الإعدادات المالية والتقنية للنظام مع تطبيق فوري للتغييرات
                    </p>
                </div>

                <!-- Success/Error Messages -->
                <div id="messageContainer" class="mb-6"></div>

                <!-- Tabs -->
                <div class="mb-6">
                    <div class="border-b border-gray-200">
                        <nav class="-mb-px flex space-x-8">
                            <button onclick="switchTab('financial')" class="tab-button py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600 active">
                                💰 الإعدادات المالية
                            </button>
                            <button onclick="switchTab('datetime')" class="tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                                🕐 التاريخ والوقت
                            </button>
                            <button onclick="switchTab('system')" class="tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                                🖥️ إعدادات النظام
                            </button>
                            <button onclick="switchTab('printing')" class="tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                                🖨️ إعدادات الطباعة
                            </button>
                        </nav>
                    </div>
                </div>

                <!-- Financial Settings Tab -->
                <div id="financialTab" class="tab-content active">
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                            <span class="ml-2">💰</span>
                            الإعدادات المالية والعملات
                        </h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">العملة الأساسية *</label>
                                <select id="baseCurrency" 
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        onchange="updateCurrencySettings()">
                                    <option value="SAR">ريال سعودي (SAR)</option>
                                    <option value="YER">ريال يمني (YER)</option>
                                    <option value="USD">دولار أمريكي (USD)</option>
                                    <option value="EUR">يورو (EUR)</option>
                                    <option value="AED">درهم إماراتي (AED)</option>
                                    <option value="KWD">دينار كويتي (KWD)</option>
                                    <option value="QAR">ريال قطري (QAR)</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">رمز العملة</label>
                                <input type="text" id="currencySymbol" readonly
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50"
                                       placeholder="ر.س">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">موضع رمز العملة</label>
                                <select id="currencyPosition" 
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="after">بعد المبلغ (100 ر.س)</option>
                                    <option value="before">قبل المبلغ (ر.س 100)</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">عدد الخانات العشرية</label>
                                <select id="decimalPlaces" 
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="0">0 (100)</option>
                                    <option value="1">1 (100.0)</option>
                                    <option value="2">2 (100.00)</option>
                                    <option value="3">3 (100.000)</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">نسبة الضريبة (%)</label>
                                <input type="number" id="taxRate" min="0" max="100" step="0.01"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="15">
                            </div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" id="enableTax" 
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="enableTax" class="mr-2 block text-sm text-gray-900">تفعيل الضريبة</label>
                            </div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" id="enableDiscount" 
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="enableDiscount" class="mr-2 block text-sm text-gray-900">تفعيل الخصومات</label>
                            </div>
                            
                            <div class="flex items-center">
                                <input type="checkbox" id="enableMultiCurrency" 
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="enableMultiCurrency" class="mr-2 block text-sm text-gray-900">دعم العملات المتعددة</label>
                            </div>
                        </div>
                        
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <button onclick="saveFinancialSettings()" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-medium flex items-center">
                                <span class="ml-2">💾</span>
                                حفظ الإعدادات المالية
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Other tabs content will be added here -->
                <div id="datetimeTab" class="tab-content">
                    <div class="text-center py-12">
                        <p class="text-gray-500 text-lg">إعدادات التاريخ والوقت قيد التطوير...</p>
                    </div>
                </div>

                <div id="systemTab" class="tab-content">
                    <div class="text-center py-12">
                        <p class="text-gray-500 text-lg">إعدادات النظام قيد التطوير...</p>
                    </div>
                </div>

                <div id="printingTab" class="tab-content">
                    <div class="text-center py-12">
                        <p class="text-gray-500 text-lg">إعدادات الطباعة قيد التطوير...</p>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="system-settings.js"></script>
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadUserInfo();
            loadCompanyInfo();
            loadSystemSettings();
        });
    </script>

</body>
</html>
