<?php
// api/get-items.php
header('Content-Type: application/json');
require_once __DIR__ . '/../config.php'; // تأكد من وجود بيانات الاتصال

$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(array('error' => 'Database connection failed'));
    exit;
}

// دعم الفلترة حسب الفرع إذا تم تمرير branchId
$branchId = isset($_GET['branchId']) ? intval($_GET['branchId']) : 0;

// دعم الفلترة حسب المخزن إذا تم تمرير warehouseId
$warehouseId = isset($_GET['warehouseId']) ? intval($_GET['warehouseId']) : 0;

// دعم البحث بالباركود أو كود الصنف أو الاسم
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// بناء الاستعلام بشكل ديناميكي
$where = array("isActive = 1");
if ($branchId > 0) $where[] = "branchId = $branchId";
if ($warehouseId > 0) $where[] = "warehouseId = $warehouseId";
if ($search !== '') {
    $search = $conn->real_escape_string($search);
    $where[] = "(itemName LIKE '%$search%' OR itemCode LIKE '%$search%' OR barcode LIKE '%$search%')";
}
$whereSql = implode(' AND ', $where);

$sql = "SELECT id, itemName, itemCode, barcode, unit, price, cost, stockQty, isActive, branchId, warehouseId, accountId, category, itemType FROM items WHERE $whereSql ORDER BY itemName ASC";

$result = $conn->query($sql);

$items = array();
if ($result) {
    while ($row = $result->fetch_assoc()) {
        // تحويل القيم الرقمية
        $row['price'] = (float)$row['price'];
        $row['cost'] = (float)$row['cost'];
        $row['stockQty'] = (float)$row['stockQty'];
        $row['isActive'] = (int)$row['isActive'];
        $row['branchId'] = (int)$row['branchId'];
        $row['warehouseId'] = isset($row['warehouseId']) ? (int)$row['warehouseId'] : null;
        $row['accountId'] = isset($row['accountId']) ? (int)$row['accountId'] : null;
        $items[] = $row;
    }
}

$conn->close();
echo json_encode(array('items' => $items));

// استبدال كل ظهور ثابت لر.س في HTML أو JS بظهور ديناميكي
// مثال: في التقارير والجداول استخدم:
// `${formatCurrency(amount)}` بدلاً من `${amount} ر.س`
// أو `${amount} <span class="currency-symbol"></span>`
// ثم في جافاسكريبت عند تحميل الصفحة:
// document.querySelectorAll('.currency-symbol').forEach(el => { el.textContent = getCurrencySymbol(); });
// أو استخدم window.currencyManager.formatCurrency(amount)
