// Employees Management System
let employees = [];
let filteredEmployees = [];

// Sample employees data
const sampleEmployees = [
    {
        id: 1,
        employeeNumber: 'EMP001',
        fullName: 'أحمد محمد العلي',
        nationalId: '1234567890',
        phone: '+966501234567',
        email: '<EMAIL>',
        branchId: 1,
        branchName: 'فرع الرياض الرئيسي',
        department: 'management',
        position: 'manager',
        basicSalary: 8000,
        hireDate: '2023-01-15',
        status: 'active',
        address: 'الرياض، حي النخيل',
        notes: 'مدير الفرع الرئيسي',
        createdAt: '2023-01-15'
    },
    {
        id: 2,
        employeeNumber: 'EMP002',
        fullName: 'فاطمة أحمد السالم',
        nationalId: '**********',
        phone: '+************',
        email: '<EMAIL>',
        branchId: 1,
        branchName: 'فرع الرياض الرئيسي',
        department: 'accounting',
        position: 'accountant',
        basicSalary: 5500,
        hireDate: '2023-02-01',
        status: 'active',
        address: 'الرياض، حي الملز',
        notes: 'محاسبة رئيسية',
        createdAt: '2023-02-01'
    },
    {
        id: 3,
        employeeNumber: 'EMP003',
        fullName: 'محمد سالم الأحمد',
        nationalId: '**********',
        phone: '+************',
        email: '<EMAIL>',
        branchId: 1,
        branchName: 'فرع الرياض الرئيسي',
        department: 'production',
        position: 'baker',
        basicSalary: 4000,
        hireDate: '2023-03-10',
        status: 'active',
        address: 'الرياض، حي الشفا',
        notes: 'خباز ماهر',
        createdAt: '2023-03-10'
    },
    {
        id: 4,
        employeeNumber: 'EMP004',
        fullName: 'نورا عبدالله المطيري',
        nationalId: '**********',
        phone: '+************',
        email: '<EMAIL>',
        branchId: 2,
        branchName: 'فرع جدة',
        department: 'sales',
        position: 'cashier',
        basicSalary: 3500,
        hireDate: '2023-04-05',
        status: 'vacation',
        address: 'جدة، حي الروضة',
        notes: 'كاشيرة متميزة',
        createdAt: '2023-04-05'
    }
];

// Load employees from localStorage
function loadEmployees() {
    const savedEmployees = localStorage.getItem('anwar_bakery_employees');
    if (savedEmployees) {
        employees = JSON.parse(savedEmployees);
    } else {
        employees = [...sampleEmployees];
        saveEmployees();
    }
    filteredEmployees = [...employees];
    renderEmployees();
    updateStatistics();
}

// Save employees to localStorage
function saveEmployees() {
    localStorage.setItem('anwar_bakery_employees', JSON.stringify(employees));
}

// Load branches for dropdowns
function loadBranches() {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    const branchSelects = document.querySelectorAll('#branchId, #branchFilter');

    branchSelects.forEach(select => {
        if (select.id === 'branchFilter') {
            select.innerHTML = '<option value="">جميع الفروع</option>';
        } else {
            select.innerHTML = '<option value="">اختر الفرع</option>';
        }

        if (savedBranches) {
            const branches = JSON.parse(savedBranches);
            branches.filter(branch => branch.isActive).forEach(branch => {
                const option = document.createElement('option');
                option.value = branch.id;
                option.textContent = branch.branchName;
                select.appendChild(option);
            });
        }
    });
}

// Get currency symbol from settings
function getCurrencySymbol() {
    const savedSettings = localStorage.getItem('anwar_bakery_settings');
    if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        return settings.currency || '';
    }
    return '';
}

// Load user info
function loadUserInfo() {
    const savedUser = localStorage.getItem('anwar_bakery_current_user');
    if (savedUser) {
        const user = JSON.parse(savedUser);
        const userFullNameElement = document.getElementById('userFullName');
        const userRoleElement = document.getElementById('userRole');

        if (userFullNameElement) {
            userFullNameElement.textContent = user.fullName || 'أحمد محمد';
        }
        if (userRoleElement) {
            userRoleElement.textContent = user.role === 'admin' ? 'مدير النظام' : (user.role || 'مدير النظام');
        }
    }
}

// Load company info
function loadCompanyInfo() {
    const savedCompany = localStorage.getItem('anwar_bakery_company');
    if (savedCompany) {
        const company = JSON.parse(savedCompany);
        const companyNameElement = document.getElementById('companyName');
        const companySloganElement = document.getElementById('companySlogan');

        if (companyNameElement) {
            companyNameElement.textContent = company.companyName || 'مخبز أنوار الحي';
        }
        if (companySloganElement) {
            companySloganElement.textContent = company.slogan || 'جودة تستحق الثقة';
        }
    }
}

// Open add employee modal
function openAddEmployeeModal() {
    document.getElementById('modalTitle').textContent = 'إضافة موظف جديد';
    document.getElementById('employeeForm').reset();
    document.getElementById('employeeId').value = '';

    // Generate employee number
    const nextNumber = employees.length + 1;
    document.getElementById('employeeNumber').value = `EMP${nextNumber.toString().padStart(3, '0')}`;

    document.getElementById('employeeModal').classList.add('active');
}

// Close employee modal
function closeEmployeeModal() {
    document.getElementById('employeeModal').classList.remove('active');
}

// Render employees table
function renderEmployees() {
    const tableBody = document.getElementById('employeesTableBody');

    if (filteredEmployees.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="11" class="px-4 py-12 text-center text-gray-500">
                    <div class="flex flex-col items-center">
                        <span class="text-4xl mb-4">👨‍💼</span>
                        <p class="text-lg mb-2">لا توجد موظفين</p>
                        <p class="text-sm">ابدأ بإضافة موظف جديد</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    const currencySymbol = getCurrencySymbol();

    tableBody.innerHTML = filteredEmployees.map(employee => {
        const statusColors = {
            'active': 'bg-green-100 text-green-800',
            'inactive': 'bg-red-100 text-red-800',
            'vacation': 'bg-yellow-100 text-yellow-800'
        };

        const statusNames = {
            'active': 'نشط',
            'inactive': 'غير نشط',
            'vacation': 'في إجازة'
        };

        const departmentNames = {
            'management': 'الإدارة',
            'production': 'الإنتاج',
            'sales': 'المبيعات',
            'accounting': 'المحاسبة',
            'maintenance': 'الصيانة'
        };

        const positionNames = {
            'manager': 'مدير',
            'supervisor': 'مشرف',
            'baker': 'خباز',
            'cashier': 'كاشير',
            'accountant': 'محاسب',
            'worker': 'عامل'
        };

        return `
            <tr class="hover:bg-gray-50">
                <td class="px-4 py-3 whitespace-nowrap">
                    <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                        ${employee.fullName.charAt(0)}
                    </div>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                    ${employee.employeeNumber}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                    <div>
                        <div class="font-medium">${employee.fullName}</div>
                        <div class="text-xs text-gray-500">${employee.phone}</div>
                    </div>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                    ${positionNames[employee.position] || employee.position}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                    ${departmentNames[employee.department] || employee.department}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                    ${employee.branchName}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 font-semibold">
                    ${formatCurrencyAmount(employee.basicSalary)}
                </td>
                <td class="px-4 py-3 whitespace-nowrap">
                    <span class="px-2 py-1 text-xs font-medium rounded-full ${statusColors[employee.status]}">
                        ${statusNames[employee.status]}
                    </span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-center">
                    <button onclick="viewEmployeeSalaries(${employee.id})" class="bg-green-100 text-green-700 px-2 py-1 rounded text-xs hover:bg-green-200">
                        💰 المرتبات
                    </button>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-center">
                    <button onclick="viewEmployeePenalties(${employee.id})" class="bg-red-100 text-red-700 px-2 py-1 rounded text-xs hover:bg-red-200">
                        ⚠️ الجزاءات
                    </button>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                        <button onclick="viewEmployee(${employee.id})" class="text-blue-600 hover:text-blue-900 text-xs">عرض</button>
                        <button onclick="editEmployee(${employee.id})" class="text-green-600 hover:text-green-900 text-xs">تعديل</button>
                        <button onclick="deleteEmployee(${employee.id})" class="text-red-600 hover:text-red-900 text-xs">حذف</button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// Update statistics
function updateStatistics() {
    const totalEmployeesElement = document.getElementById('totalEmployees');
    const activeEmployeesElement = document.getElementById('activeEmployees');
    const totalSalariesElement = document.getElementById('totalSalaries');
    const presentTodayElement = document.getElementById('presentToday');

    const totalEmployees = employees.length;
    const activeEmployees = employees.filter(emp => emp.status === 'active').length;
    const totalSalaries = employees.reduce((sum, emp) => sum + emp.basicSalary, 0);
    const presentToday = employees.filter(emp => emp.status === 'active').length; // Simplified for now

    if (totalEmployeesElement) {
        totalEmployeesElement.textContent = totalEmployees;
    }
    if (activeEmployeesElement) {
        activeEmployeesElement.textContent = activeEmployees;
    }
    if (totalSalariesElement) {
        totalSalariesElement.textContent = formatCurrencyAmount(totalSalaries);
    }
    if (presentTodayElement) {
        presentTodayElement.textContent = presentToday;
    }
}

// Filter employees
function filterEmployees() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const branchFilter = document.getElementById('branchFilter').value;
    const departmentFilter = document.getElementById('departmentFilter').value;
    const positionFilter = document.getElementById('positionFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;

    filteredEmployees = employees.filter(employee => {
        const matchesSearch = employee.fullName.toLowerCase().includes(searchTerm) ||
                            employee.employeeNumber.toLowerCase().includes(searchTerm);
        const matchesBranch = !branchFilter || employee.branchId == branchFilter;
        const matchesDepartment = !departmentFilter || employee.department === departmentFilter;
        const matchesPosition = !positionFilter || employee.position === positionFilter;
        const matchesStatus = !statusFilter || employee.status === statusFilter;

        return matchesSearch && matchesBranch && matchesDepartment && matchesPosition && matchesStatus;
    });

    renderEmployees();
}

// View employee details
function viewEmployee(id) {
    const employee = employees.find(emp => emp.id === id);
    if (employee) {
        const currencySymbol = getCurrencySymbol();

        const departmentNames = {
            'management': 'الإدارة',
            'production': 'الإنتاج',
            'sales': 'المبيعات',
            'accounting': 'المحاسبة',
            'maintenance': 'الصيانة'
        };

        const positionNames = {
            'manager': 'مدير',
            'supervisor': 'مشرف',
            'baker': 'خباز',
            'cashier': 'كاشير',
            'accountant': 'محاسب',
            'worker': 'عامل'
        };

        const statusNames = {
            'active': 'نشط',
            'inactive': 'غير نشط',
            'vacation': 'في إجازة'
        };

        let message = `تفاصيل الموظف: ${employee.fullName}\n\n`;
        message += `🆔 الرقم الوظيفي: ${employee.employeeNumber}\n`;
        message += `📱 رقم الجوال: ${employee.phone}\n`;
        message += `📧 البريد الإلكتروني: ${employee.email || 'غير محدد'}\n`;
        message += `🏢 الفرع: ${employee.branchName}\n`;
        message += `🏷️ القسم: ${departmentNames[employee.department] || employee.department}\n`;
        message += `👔 المنصب: ${positionNames[employee.position] || employee.position}\n`;
        message += `💰 الراتب الأساسي: ${employee.basicSalary.toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}\n`;
        message += `📅 تاريخ التوظيف: ${new Date(employee.hireDate).toLocaleDateString('ar-SA')}\n`;
        message += `📊 الحالة: ${statusNames[employee.status]}\n`;

        if (employee.address) {
            message += `📍 العنوان: ${employee.address}\n`;
        }

        if (employee.notes) {
            message += `📝 ملاحظات: ${employee.notes}\n`;
        }

        alert(message);
    }
}

// Edit employee
function editEmployee(id) {
    const employee = employees.find(emp => emp.id === id);
    if (employee) {
        document.getElementById('modalTitle').textContent = 'تعديل بيانات الموظف';
        document.getElementById('employeeId').value = employee.id;
        document.getElementById('fullName').value = employee.fullName;
        document.getElementById('nationalId').value = employee.nationalId;
        document.getElementById('phone').value = employee.phone;
        document.getElementById('email').value = employee.email || '';
        document.getElementById('employeeNumber').value = employee.employeeNumber;
        document.getElementById('branchId').value = employee.branchId;
        document.getElementById('department').value = employee.department;
        document.getElementById('position').value = employee.position;
        document.getElementById('basicSalary').value = employee.basicSalary;
        document.getElementById('hireDate').value = employee.hireDate;
        document.getElementById('status').value = employee.status;
        document.getElementById('address').value = employee.address || '';
        document.getElementById('notes').value = employee.notes || '';

        document.getElementById('employeeModal').classList.add('active');
    }
}

// Save employee
function saveEmployee(event) {
    event.preventDefault();

    const employeeId = document.getElementById('employeeId').value;
    const branchId = document.getElementById('branchId').value;

    // Get branch name
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    let branchName = '';
    if (savedBranches) {
        const branches = JSON.parse(savedBranches);
        const branch = branches.find(b => b.id == branchId);
        branchName = branch ? branch.branchName : '';
    }

    const employeeData = {
        fullName: document.getElementById('fullName').value,
        nationalId: document.getElementById('nationalId').value,
        phone: document.getElementById('phone').value,
        email: document.getElementById('email').value,
        employeeNumber: document.getElementById('employeeNumber').value,
        branchId: parseInt(branchId),
        branchName: branchName,
        department: document.getElementById('department').value,
        position: document.getElementById('position').value,
        basicSalary: parseFloat(document.getElementById('basicSalary').value),
        hireDate: document.getElementById('hireDate').value,
        status: document.getElementById('status').value,
        address: document.getElementById('address').value,
        notes: document.getElementById('notes').value
    };

    if (employeeId) {
        // Edit existing employee
        const employeeIndex = employees.findIndex(emp => emp.id == employeeId);
        if (employeeIndex !== -1) {
            employees[employeeIndex] = { ...employees[employeeIndex], ...employeeData };
        }
    } else {
        // Add new employee
        const newEmployee = {
            id: Math.max(...employees.map(emp => emp.id), 0) + 1,
            ...employeeData,
            createdAt: new Date().toISOString().split('T')[0]
        };
        employees.push(newEmployee);
    }

    saveEmployees();
    loadEmployees(); // Reload to update filtered list
    closeEmployeeModal();
    showMessage('تم حفظ بيانات الموظف بنجاح', 'success');
}

// Delete employee
function deleteEmployee(id) {
    const employee = employees.find(emp => emp.id === id);
    if (employee && confirm(`هل أنت متأكد من حذف الموظف: ${employee.fullName}؟`)) {
        employees = employees.filter(emp => emp.id !== id);
        saveEmployees();
        loadEmployees();
        showMessage('تم حذف الموظف بنجاح', 'success');
    }
}

// Export employees
function exportEmployees() {
    const currencySymbol = getCurrencySymbol();
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += `الرقم الوظيفي,الاسم الكامل,رقم الهوية,رقم الجوال,البريد الإلكتروني,الفرع,القسم,المنصب,الراتب الأساسي${currencySymbol ? ' (' + currencySymbol + ')' : ''},تاريخ التوظيف,الحالة,العنوان,ملاحظات\n`;

    employees.forEach(employee => {
        const departmentNames = {
            'management': 'الإدارة',
            'production': 'الإنتاج',
            'sales': 'المبيعات',
            'accounting': 'المحاسبة',
            'maintenance': 'الصيانة'
        };

        const positionNames = {
            'manager': 'مدير',
            'supervisor': 'مشرف',
            'baker': 'خباز',
            'cashier': 'كاشير',
            'accountant': 'محاسب',
            'worker': 'عامل'
        };

        const statusNames = {
            'active': 'نشط',
            'inactive': 'غير نشط',
            'vacation': 'في إجازة'
        };

        csvContent += `"${employee.employeeNumber}","${employee.fullName}","${employee.nationalId}","${employee.phone}","${employee.email || ''}","${employee.branchName}","${departmentNames[employee.department] || employee.department}","${positionNames[employee.position] || employee.position}","${employee.basicSalary}","${new Date(employee.hireDate).toLocaleDateString('ar-SA')}","${statusNames[employee.status]}","${employee.address || ''}","${employee.notes || ''}"\n`;
    });

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `employees_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showMessage('تم تصدير بيانات الموظفين بنجاح', 'success');
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    if (!container) return;

    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Get currency symbol from system settings - FINAL CORRECTED VERSION
function getCurrencySymbol() {
    try {
        // خريطة تحويل العملات
        const currencyMap = {
            'SAR': 'ر.س',
            'YER': 'ر.ي',
            'USD': '$',
            'EUR': '€',
            'AED': 'د.إ',
            'KWD': 'د.ك',
            'QAR': 'ر.ق'
        };

        // أولاً: محاولة استخدام نظام appSettings
        if (window.appSettings) {
            const financial = window.appSettings.get('financial');
            if (financial) {
                // البحث عن رمز العملة مباشرة
                if (financial.currencySymbol) {
                    return financial.currencySymbol;
                }
                // البحث عن العملة الأساسية وتحويلها
                if (financial.baseCurrency && currencyMap[financial.baseCurrency]) {
                    return currencyMap[financial.baseCurrency];
                }
            }
        }

        // ثانياً: محاولة قراءة من localStorage مباشرة
        const settingsDataString = localStorage.getItem('anwar_bakery_settings');
        if (settingsDataString && settingsDataString !== 'null') {
            const settings = JSON.parse(settingsDataString);

            // البحث في الإعدادات المالية
            if (settings.financial) {
                if (settings.financial.currencySymbol) {
                    return settings.financial.currencySymbol;
                }
                if (settings.financial.baseCurrency && currencyMap[settings.financial.baseCurrency]) {
                    return currencyMap[settings.financial.baseCurrency];
                }
            }

            // البحث في المستوى الأعلى
            if (settings.currencySymbol) {
                return settings.currencySymbol;
            }
            if (settings.currency && currencyMap[settings.currency]) {
                return currencyMap[settings.currency];
            }
            if (settings.baseCurrency && currencyMap[settings.baseCurrency]) {
                return currencyMap[settings.baseCurrency];
            }
        }

        return 'ر.س';

    } catch (error) {
        console.error('Error getting currency from settings:', error);
        return 'ر.س';
    }
}

// Force currency update - EMERGENCY FIX
function forceCurrencyUpdate() {
    console.log('🚨 FORCING CURRENCY UPDATE...');

    // إجبار إعادة قراءة البيانات
    const symbol = getCurrencySymbol();
    console.log('Current currency symbol:', symbol);

    // تحديث الإحصائيات فوراً
    updateStatistics();

    // تحديث الجدول فوراً
    loadEmployees();

    console.log('✅ CURRENCY UPDATE FORCED');
}

// Format currency amount - REBUILT VERSION
function formatCurrencyAmount(amount) {
    try {
        const number = parseFloat(amount) || 0;
        const formattedNumber = number.toLocaleString('en-US');
        const currencySymbol = getCurrencySymbol();
        return `${formattedNumber} ${currencySymbol}`;
    } catch (error) {
        console.error('Error formatting currency:', error);
        return `${amount} ر.س`;
    }
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_current_user');
        window.location.href = 'login.html';
    }
}

// Salaries, Penalties, and Deductions Management
let salaries = [];
let penalties = [];
let deductions = [];

// Initialize salaries, penalties, and deductions
function initializeSalariesData() {
    const storedSalaries = localStorage.getItem('anwar_bakery_employee_salaries');
    const storedPenalties = localStorage.getItem('anwar_bakery_employee_penalties');
    const storedDeductions = localStorage.getItem('anwar_bakery_employee_deductions');

    if (storedSalaries) {
        salaries = JSON.parse(storedSalaries);
    }
    if (storedPenalties) {
        penalties = JSON.parse(storedPenalties);
    }
    if (storedDeductions) {
        deductions = JSON.parse(storedDeductions);
    }
}

// Save data to localStorage
function saveSalariesData() {
    localStorage.setItem('anwar_bakery_employee_salaries', JSON.stringify(salaries));
    localStorage.setItem('anwar_bakery_employee_penalties', JSON.stringify(penalties));
    localStorage.setItem('anwar_bakery_employee_deductions', JSON.stringify(deductions));
}

// Open Salaries Modal
function openSalariesModal() {
    document.getElementById('salariesModal').classList.add('active');
    populateEmployeeDropdowns();
    loadSalariesTable();
    checkMissingSalaries(); // فحص المرتبات المفقودة
}

// Close Salaries Modal
function closeSalariesModal() {
    document.getElementById('salariesModal').classList.remove('active');
}

// Open Penalties Modal
function openPenaltiesModal() {
    document.getElementById('penaltiesModal').classList.add('active');
    populateEmployeeDropdowns();
    loadPenaltiesTable();
    document.getElementById('penaltyDate').value = new Date().toISOString().split('T')[0];
}

// Close Penalties Modal
function closePenaltiesModal() {
    document.getElementById('penaltiesModal').classList.remove('active');
}

// Open Deductions Modal
function openDeductionsModal() {
    document.getElementById('deductionsModal').classList.add('active');
    populateEmployeeDropdowns();
    loadDeductionsTable();
    document.getElementById('deductionDate').value = new Date().toISOString().split('T')[0];
}

// Close Deductions Modal
function closeDeductionsModal() {
    document.getElementById('deductionsModal').classList.remove('active');
}

// Populate employee dropdowns
function populateEmployeeDropdowns() {
    const dropdowns = ['penaltyEmployeeId', 'deductionEmployeeId'];

    dropdowns.forEach(dropdownId => {
        const dropdown = document.getElementById(dropdownId);
        if (dropdown) {
            dropdown.innerHTML = '<option value="">اختر الموظف</option>';
            employees.filter(emp => emp.status === 'active').forEach(employee => {
                const option = document.createElement('option');
                option.value = employee.id;
                option.textContent = `${employee.fullName} (${employee.employeeNumber})`;
                dropdown.appendChild(option);
            });
        }
    });
}

// Generate Salaries
function generateSalaries() {
    const month = document.getElementById('salaryMonth').value;
    const year = document.getElementById('salaryYear').value;
    const branchFilter = document.getElementById('salaryBranchFilter').value;

    if (!month || !year) {
        alert('يجب اختيار الشهر والسنة');
        return;
    }

    // Filter employees based on branch if selected
    let employeesToProcess = employees.filter(emp => emp.status === 'active');
    if (branchFilter) {
        employeesToProcess = employeesToProcess.filter(emp => emp.branchId == branchFilter);
    }

    // Generate salary records
    employeesToProcess.forEach(employee => {
        const salaryId = `${employee.id}-${year}-${month}`;

        // Check if salary already exists
        const existingSalary = salaries.find(s => s.id === salaryId);
        if (!existingSalary) {
            // Calculate deductions and penalties for this month
            const monthPenalties = penalties.filter(p => {
                const penaltyDate = new Date(p.date);
                return p.employeeId === employee.id &&
                       penaltyDate.getMonth() + 1 == month &&
                       penaltyDate.getFullYear() == year;
            });

            const monthDeductions = deductions.filter(d => {
                const deductionDate = new Date(d.date);
                return d.employeeId === employee.id &&
                       deductionDate.getMonth() + 1 == month &&
                       deductionDate.getFullYear() == year;
            });

            const totalPenalties = monthPenalties.reduce((sum, p) => sum + p.amount, 0);
            const totalDeductions = monthDeductions.reduce((sum, d) => sum + d.amount, 0);
            const allowances = 0; // Can be expanded later

            const netSalary = employee.basicSalary + allowances - totalDeductions - totalPenalties;

            const salaryRecord = {
                id: salaryId,
                employeeId: employee.id,
                employeeName: employee.fullName,
                employeeNumber: employee.employeeNumber,
                month: parseInt(month),
                year: parseInt(year),
                basicSalary: employee.basicSalary,
                allowances: allowances,
                deductions: totalDeductions,
                penalties: totalPenalties,
                netSalary: netSalary,
                status: 'pending',
                createdAt: new Date().toISOString()
            };

            salaries.push(salaryRecord);
        }
    });

    saveSalariesData();
    loadSalariesTable();
    showMessage('تم احتساب المرتبات بنجاح', 'success');
}

// Load Salaries Table
function loadSalariesTable() {
    const tbody = document.getElementById('salariesTableBody');
    const month = document.getElementById('salaryMonth').value;
    const year = document.getElementById('salaryYear').value;

    if (!month || !year) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center py-4 text-gray-500">اختر الشهر والسنة لعرض المرتبات</td></tr>';
        return;
    }

    const filteredSalaries = salaries.filter(s => s.month == month && s.year == year);

    // تحديث حسابات الخصومات والجزاءات للرواتب المعلقة
    filteredSalaries.forEach(salary => {
        if (salary.status === 'pending') {
            updateSalaryCalculations(salary);
        }
    });

    if (filteredSalaries.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center py-4 text-gray-500">لا توجد مرتبات لهذا الشهر</td></tr>';
        return;
    }

    tbody.innerHTML = filteredSalaries.map(salary => `
        <tr>
            <td class="px-3 py-2">
                <div class="text-sm font-medium">${salary.employeeName}</div>
                <div class="text-xs text-gray-500">${salary.employeeNumber}</div>
            </td>
            <td class="px-3 py-2 font-medium">${formatCurrencyAmount(salary.basicSalary)}</td>
            <td class="px-3 py-2">${formatCurrencyAmount(salary.allowances)}</td>
            <td class="px-3 py-2 text-red-600">${formatCurrencyAmount(salary.deductions)}</td>
            <td class="px-3 py-2 text-red-600">${formatCurrencyAmount(salary.penalties)}</td>
            <td class="px-3 py-2 font-bold text-green-600">${formatCurrencyAmount(salary.netSalary)}</td>
            <td class="px-3 py-2">
                <span class="px-2 py-1 text-xs rounded ${
                    salary.status === 'paid' ? 'bg-green-100 text-green-800' :
                    salary.status === 'accrued' ? 'bg-blue-100 text-blue-800' :
                    'bg-yellow-100 text-yellow-800'
                }">
                    ${
                        salary.status === 'paid' ? 'مدفوع' :
                        salary.status === 'accrued' ? 'مستحق' :
                        'معلق'
                    }
                </span>
            </td>
            <td class="px-3 py-2 text-center">
                <div class="flex justify-center space-x-1">
                    <button onclick="paySalary('${salary.id}')" class="text-green-600 hover:text-green-900 text-xs" title="${
                        salary.status === 'pending' ? 'قيد استحقاق الراتب' :
                        salary.status === 'accrued' ? 'دفع الراتب (سند صرف)' :
                        'مدفوع'
                    }">
                        ${
                            salary.status === 'paid' ? '✅' :
                            salary.status === 'accrued' ? '💸' :
                            '📝'
                        }
                    </button>
                    ${salary.status === 'pending' ? `
                        <button onclick="editSalary('${salary.id}')" class="text-blue-600 hover:text-blue-900 text-xs" title="تعديل الراتب">
                            ✏️
                        </button>
                        <button onclick="deleteSalary('${salary.id}')" class="text-red-600 hover:text-red-900 text-xs" title="حذف الراتب">
                            🗑️
                        </button>
                    ` : salary.status === 'accrued' ? `
                        <button onclick="editAccruedSalary('${salary.id}')" class="text-blue-600 hover:text-blue-900 text-xs" title="تعديل القيد المحاسبي">
                            ✏️
                        </button>
                        <button onclick="reverseSalaryAccrual('${salary.id}')" class="text-orange-600 hover:text-orange-900 text-xs" title="عكس قيد الاستحقاق">
                            ↩️
                        </button>
                    ` : `
                        <button onclick="editPaidSalary('${salary.id}')" class="text-blue-600 hover:text-blue-900 text-xs" title="تعديل السند">
                            ✏️
                        </button>
                        <button onclick="reverseSalaryPayment('${salary.id}')" class="text-red-600 hover:text-red-900 text-xs" title="عكس الدفع">
                            ↩️
                        </button>
                    `}
                </div>
            </td>
        </tr>
    `).join('');
}

// Add Penalty
function addPenalty(event) {
    event.preventDefault();

    const penaltyData = {
        id: Date.now(),
        employeeId: parseInt(document.getElementById('penaltyEmployeeId').value),
        employeeName: employees.find(e => e.id == document.getElementById('penaltyEmployeeId').value)?.fullName,
        type: document.getElementById('penaltyType').value,
        amount: parseFloat(document.getElementById('penaltyAmount').value),
        date: document.getElementById('penaltyDate').value,
        reason: document.getElementById('penaltyReason').value,
        createdAt: new Date().toISOString()
    };

    penalties.push(penaltyData);
    saveSalariesData();
    loadPenaltiesTable();

    // تحديث المرتبات المعلقة للموظف
    updateEmployeePendingSalaries(penaltyData.employeeId);

    document.getElementById('penaltyForm').reset();
    document.getElementById('penaltyDate').value = new Date().toISOString().split('T')[0];
    showMessage('تم إضافة الجزاء بنجاح وتحديث المرتبات', 'success');
}

// Load Penalties Table
function loadPenaltiesTable() {
    const tbody = document.getElementById('penaltiesTableBody');

    if (penalties.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center py-4 text-gray-500">لا توجد جزاءات</td></tr>';
        return;
    }

    tbody.innerHTML = penalties.map(penalty => `
        <tr>
            <td class="px-3 py-2">${formatDate(penalty.date)}</td>
            <td class="px-3 py-2">${penalty.employeeName}</td>
            <td class="px-3 py-2">${getPenaltyTypeText(penalty.type)}</td>
            <td class="px-3 py-2 font-medium text-red-600">${penalty.amount.toFixed(2)} ${getCurrencySymbol()}</td>
            <td class="px-3 py-2">${penalty.reason || 'لا يوجد'}</td>
            <td class="px-3 py-2 text-center">
                <button onclick="deletePenalty(${penalty.id})" class="text-red-600 hover:text-red-900 text-xs">
                    🗑️
                </button>
            </td>
        </tr>
    `).join('');
}

// Add Deduction
function addDeduction(event) {
    event.preventDefault();

    const deductionData = {
        id: Date.now(),
        employeeId: parseInt(document.getElementById('deductionEmployeeId').value),
        employeeName: employees.find(e => e.id == document.getElementById('deductionEmployeeId').value)?.fullName,
        type: document.getElementById('deductionType').value,
        amount: parseFloat(document.getElementById('deductionAmount').value),
        date: document.getElementById('deductionDate').value,
        description: document.getElementById('deductionDescription').value,
        createdAt: new Date().toISOString()
    };

    deductions.push(deductionData);
    saveSalariesData();
    loadDeductionsTable();

    // تحديث المرتبات المعلقة للموظف
    updateEmployeePendingSalaries(deductionData.employeeId);

    document.getElementById('deductionForm').reset();
    document.getElementById('deductionDate').value = new Date().toISOString().split('T')[0];
    showMessage('تم إضافة الخصم بنجاح وتحديث المرتبات', 'success');
}

// Load Deductions Table
function loadDeductionsTable() {
    const tbody = document.getElementById('deductionsTableBody');

    if (deductions.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center py-4 text-gray-500">لا توجد خصومات</td></tr>';
        return;
    }

    tbody.innerHTML = deductions.map(deduction => `
        <tr>
            <td class="px-3 py-2">${formatDate(deduction.date)}</td>
            <td class="px-3 py-2">${deduction.employeeName}</td>
            <td class="px-3 py-2">${getDeductionTypeText(deduction.type)}</td>
            <td class="px-3 py-2 font-medium text-orange-600">${deduction.amount.toFixed(2)} ${getCurrencySymbol()}</td>
            <td class="px-3 py-2">${deduction.description || 'لا يوجد'}</td>
            <td class="px-3 py-2 text-center">
                <button onclick="deleteDeduction(${deduction.id})" class="text-red-600 hover:text-red-900 text-xs">
                    🗑️
                </button>
            </td>
        </tr>
    `).join('');
}

// Helper functions
function getPenaltyTypeText(type) {
    const types = {
        'late': 'تأخير',
        'absence': 'غياب',
        'misconduct': 'سوء سلوك',
        'violation': 'مخالفة',
        'other': 'أخرى'
    };
    return types[type] || type;
}

function getDeductionTypeText(type) {
    const types = {
        'insurance': 'تأمين',
        'loan': 'قرض',
        'advance': 'سلفة',
        'tax': 'ضريبة',
        'other': 'أخرى'
    };
    return types[type] || type;
}

// Update salary calculations with current penalties and deductions
function updateSalaryCalculations(salary) {
    // حساب الجزاءات الفعلية للموظف
    const employeePenalties = penalties.filter(p => p.employeeId === salary.employeeId);
    const totalPenalties = employeePenalties.reduce((sum, p) => sum + (parseFloat(p.amount) || 0), 0);

    // حساب الخصومات الفعلية للموظف
    const employeeDeductions = deductions.filter(d => d.employeeId === salary.employeeId);
    const totalDeductions = employeeDeductions.reduce((sum, d) => sum + (parseFloat(d.amount) || 0), 0);

    // تحديث بيانات الراتب
    const oldNetSalary = salary.netSalary;
    salary.penalties = totalPenalties;
    salary.deductions = totalDeductions;
    salary.netSalary = (parseFloat(salary.basicSalary) || 0) + (parseFloat(salary.allowances) || 0) - totalPenalties - totalDeductions;

    // تسجيل التغييرات إذا حدثت
    if (oldNetSalary !== salary.netSalary) {
        console.log(`💰 Updated salary calculations for ${salary.employeeName}:`);
        console.log(`  - Basic Salary: ${salary.basicSalary}`);
        console.log(`  - Allowances: ${salary.allowances}`);
        console.log(`  - Penalties: ${totalPenalties} (${employeePenalties.length} items)`);
        console.log(`  - Deductions: ${totalDeductions} (${employeeDeductions.length} items)`);
        console.log(`  - Net Salary: ${oldNetSalary} → ${salary.netSalary}`);

        // حفظ التغييرات
        saveSalariesData();
    }

    return salary;
}

// Update all pending salaries for a specific employee
function updateEmployeePendingSalaries(employeeId) {
    const employeePendingSalaries = salaries.filter(s =>
        s.employeeId === employeeId && s.status === 'pending'
    );

    let updatedCount = 0;
    employeePendingSalaries.forEach(salary => {
        const oldNetSalary = salary.netSalary;
        updateSalaryCalculations(salary);
        if (oldNetSalary !== salary.netSalary) {
            updatedCount++;
        }
    });

    if (updatedCount > 0) {
        loadSalariesTable(); // إعادة تحميل الجدول لإظهار التحديثات
        console.log(`✅ Updated ${updatedCount} pending salaries for employee ${employeeId}`);
    }
}

function getMonthName(month) {
    const months = [
        '', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return months[parseInt(month)] || month;
}

// Check for missing salaries - CORRECTED LOGIC
function checkMissingSalaries() {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;
    const currentDay = currentDate.getDate();

    const missingSalaries = [];

    // فحص كل موظف نشط
    const activeEmployees = employees.filter(emp => emp.status === 'active');

    activeEmployees.forEach(employee => {
        const hireDate = new Date(employee.hireDate);
        const hireYear = hireDate.getFullYear();
        const hireMonth = hireDate.getMonth() + 1;
        const hireDay = hireDate.getDate();

        console.log(`🔍 Checking employee: ${employee.fullName}`);
        console.log(`📅 Hire date: ${employee.hireDate}`);
        console.log(`📅 Current date: ${currentDate.toISOString().split('T')[0]}`);

        // حساب عدد الأيام منذ التعيين
        const daysSinceHire = Math.floor((currentDate - hireDate) / (1000 * 60 * 60 * 24));
        console.log(`⏰ Days since hire: ${daysSinceHire}`);

        // المنطق الصحيح: الموظف يستحق راتب فقط للأشهر المكتملة
        for (let year = hireYear; year <= currentYear; year++) {
            const startMonth = (year === hireYear) ? hireMonth : 1;
            let endMonth;

            if (year === currentYear) {
                // في السنة الحالية، نحتاج للتحقق من اكتمال الشهر
                if (currentDay >= hireDay && currentMonth > hireMonth) {
                    // الشهر الماضي مكتمل
                    endMonth = currentMonth - 1;
                } else if (currentMonth === hireMonth && daysSinceHire >= 30) {
                    // الشهر الأول مكتمل (30 يوم على الأقل)
                    endMonth = hireMonth;
                } else {
                    // لا يوجد أشهر مكتملة بعد
                    endMonth = startMonth - 1;
                }
            } else {
                // السنوات السابقة مكتملة
                endMonth = 12;
            }

            console.log(`📊 Year ${year}: checking months ${startMonth} to ${endMonth}`);

            for (let month = startMonth; month <= endMonth; month++) {
                // التحقق من اكتمال الشهر
                let monthCompleted = false;

                if (year < currentYear) {
                    // السنوات السابقة مكتملة
                    monthCompleted = true;
                } else if (year === currentYear) {
                    if (month < currentMonth) {
                        // الأشهر السابقة في السنة الحالية
                        if (month === hireMonth) {
                            // الشهر الأول: يحتاج 30 يوم على الأقل
                            const endOfHireMonth = new Date(hireYear, hireMonth, 0).getDate();
                            const daysWorkedInFirstMonth = endOfHireMonth - hireDay + 1;
                            monthCompleted = daysWorkedInFirstMonth >= 15; // نصف شهر على الأقل
                        } else {
                            monthCompleted = true;
                        }
                    } else if (month === currentMonth) {
                        // الشهر الحالي: يجب أن يكون مكتمل
                        monthCompleted = currentDay >= 28; // نهاية الشهر تقريب<|im_start|>
                    }
                }

                if (monthCompleted) {
                    // البحث عن راتب هذا الموظف في هذا الشهر
                    const existingSalary = salaries.find(salary =>
                        salary.employeeId === employee.id &&
                        salary.month === month &&
                        salary.year === year
                    );

                    if (!existingSalary) {
                        console.log(`❌ Missing salary for ${getMonthName(month)} ${year}`);
                        missingSalaries.push({
                            employee: employee,
                            month: month,
                            year: year,
                            monthName: getMonthName(month)
                        });
                    } else {
                        console.log(`✅ Salary exists for ${getMonthName(month)} ${year}`);
                    }
                } else {
                    console.log(`⏳ Month ${getMonthName(month)} ${year} not completed yet`);
                }
            }
        }
    });

    console.log(`📋 Total missing salaries found: ${missingSalaries.length}`);

    // عرض التنبيهات فقط إذا كان هناك مرتبات مفقودة فعلية
    if (missingSalaries.length > 0) {
        showMissingSalariesAlert(missingSalaries);
    } else {
        console.log('✅ No missing salaries - all employees are up to date');
    }
}

// Show missing salaries alert
function showMissingSalariesAlert(missingSalaries) {
    // إنشاء نافذة التنبيه
    const alertModal = document.createElement('div');
    alertModal.className = 'fixed inset-0 bg-red-900 bg-opacity-75 z-50 flex items-center justify-center';
    alertModal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-4xl mx-4 max-h-96 overflow-y-auto">
            <div class="text-center mb-4">
                <div class="text-4xl mb-2">⚠️</div>
                <h2 class="text-xl font-bold text-red-600">تنبيه: مرتبات غير مقيدة</h2>
                <p class="text-gray-600">يوجد ${missingSalaries.length} راتب غير مقيد للموظفين التاليين:</p>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full text-sm">
                    <thead class="bg-red-50">
                        <tr>
                            <th class="px-3 py-2 text-right font-medium text-red-800">الموظف</th>
                            <th class="px-3 py-2 text-right font-medium text-red-800">الشهر</th>
                            <th class="px-3 py-2 text-right font-medium text-red-800">السنة</th>
                            <th class="px-3 py-2 text-right font-medium text-red-800">الراتب الأساسي</th>
                            <th class="px-3 py-2 text-center font-medium text-red-800">إجراء</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        ${missingSalaries.map(missing => `
                            <tr class="hover:bg-gray-50">
                                <td class="px-3 py-2">${missing.employee.fullName}</td>
                                <td class="px-3 py-2">${missing.monthName}</td>
                                <td class="px-3 py-2">${missing.year}</td>
                                <td class="px-3 py-2 font-medium">${formatCurrencyAmount(missing.employee.basicSalary)}</td>
                                <td class="px-3 py-2 text-center">
                                    <button onclick="quickAddSalary(${missing.employee.id}, ${missing.month}, ${missing.year})"
                                            class="bg-green-600 text-white px-2 py-1 rounded text-xs hover:bg-green-700">
                                        قيد سريع
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            <div class="flex justify-center space-x-3 mt-6">
                <button onclick="closeMissingSalariesAlert()"
                        class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                    إغلاق
                </button>
                <button onclick="generateAllMissingSalaries()"
                        class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    قيد جميع المرتبات
                </button>
            </div>
        </div>
    `;

    alertModal.id = 'missingSalariesAlert';
    document.body.appendChild(alertModal);
}

// Close missing salaries alert
function closeMissingSalariesAlert() {
    const alert = document.getElementById('missingSalariesAlert');
    if (alert) {
        alert.remove();
    }
}

// Quick add salary for specific employee and month - CORRECTED
function quickAddSalary(employeeId, month, year) {
    const employee = employees.find(emp => emp.id === employeeId);
    if (!employee) {
        showMessage('الموظف غير موجود', 'error');
        return;
    }

    // التحقق من صحة الراتب الأساسي
    const basicSalary = parseFloat(employee.basicSalary);
    if (isNaN(basicSalary) || basicSalary <= 0) {
        showMessage(`خطأ: راتب الموظف ${employee.fullName} غير صحيح (${employee.basicSalary})`, 'error');
        return;
    }

    // التحقق من عدم وجود راتب مسبق لنفس الشهر
    const existingSalary = salaries.find(salary =>
        salary.employeeId === employeeId &&
        salary.month === month &&
        salary.year === year
    );

    if (existingSalary) {
        showMessage(`راتب ${employee.fullName} لشهر ${getMonthName(month)} ${year} موجود مسبق<|im_start|>`, 'warning');
        return;
    }

    console.log(`💰 Creating salary for ${employee.fullName}:`);
    console.log(`  - Employee basic salary: ${employee.basicSalary}`);
    console.log(`  - Month: ${getMonthName(month)} ${year}`);

    const salaryData = {
        id: `${employeeId}-${year}-${month.toString().padStart(2, '0')}`,
        employeeId: employeeId,
        employeeName: employee.fullName,
        employeeNumber: employee.employeeNumber,
        month: month,
        year: year,
        basicSalary: basicSalary, // استخدام الراتب الصحيح من بيانات الموظف
        allowances: 0,
        deductions: 0,
        penalties: 0,
        netSalary: basicSalary,
        status: 'pending',
        createdAt: new Date().toISOString(),
        createdBy: 'system'
    };

    salaries.push(salaryData);
    saveSalariesData();
    loadSalariesTable();
    updateStatistics();

    showMessage(`تم قيد راتب ${employee.fullName} (${formatCurrencyAmount(basicSalary)}) لشهر ${getMonthName(month)} ${year}`, 'success');

    // إعادة فحص المرتبات المفقودة
    setTimeout(() => {
        closeMissingSalariesAlert();
        checkMissingSalaries();
    }, 1000);
}

// Generate all missing salaries - CORRECTED LOGIC
function generateAllMissingSalaries() {
    console.log('🔄 Generating all missing salaries with corrected logic...');

    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;
    const currentDay = currentDate.getDate();

    let addedCount = 0;
    const activeEmployees = employees.filter(emp => emp.status === 'active');

    activeEmployees.forEach(employee => {
        const hireDate = new Date(employee.hireDate);
        const hireYear = hireDate.getFullYear();
        const hireMonth = hireDate.getMonth() + 1;
        const hireDay = hireDate.getDate();

        // التحقق من صحة الراتب
        const basicSalary = parseFloat(employee.basicSalary);
        if (isNaN(basicSalary) || basicSalary <= 0) {
            console.log(`❌ Skipping ${employee.fullName} - invalid salary: ${employee.basicSalary}`);
            return;
        }

        console.log(`👤 Processing ${employee.fullName} (hired: ${employee.hireDate})`);

        // نفس المنطق المصحح من checkMissingSalaries
        for (let year = hireYear; year <= currentYear; year++) {
            const startMonth = (year === hireYear) ? hireMonth : 1;
            let endMonth;

            if (year === currentYear) {
                // في السنة الحالية، نحتاج للتحقق من اكتمال الشهر
                const daysSinceHire = Math.floor((currentDate - hireDate) / (1000 * 60 * 60 * 24));

                if (currentDay >= hireDay && currentMonth > hireMonth) {
                    endMonth = currentMonth - 1;
                } else if (currentMonth === hireMonth && daysSinceHire >= 30) {
                    endMonth = hireMonth;
                } else {
                    endMonth = startMonth - 1; // لا يوجد أشهر مكتملة
                }
            } else {
                endMonth = 12;
            }

            for (let month = startMonth; month <= endMonth; month++) {
                // التحقق من اكتمال الشهر
                let monthCompleted = false;

                if (year < currentYear) {
                    monthCompleted = true;
                } else if (year === currentYear) {
                    if (month < currentMonth) {
                        if (month === hireMonth) {
                            const endOfHireMonth = new Date(hireYear, hireMonth, 0).getDate();
                            const daysWorkedInFirstMonth = endOfHireMonth - hireDay + 1;
                            monthCompleted = daysWorkedInFirstMonth >= 15;
                        } else {
                            monthCompleted = true;
                        }
                    } else if (month === currentMonth) {
                        monthCompleted = currentDay >= 28;
                    }
                }

                if (monthCompleted) {
                    const existingSalary = salaries.find(salary =>
                        salary.employeeId === employee.id &&
                        salary.month === month &&
                        salary.year === year
                    );

                    if (!existingSalary) {
                        const salaryData = {
                            id: `${employee.id}-${year}-${month.toString().padStart(2, '0')}`,
                            employeeId: employee.id,
                            employeeName: employee.fullName,
                            employeeNumber: employee.employeeNumber,
                            month: month,
                            year: year,
                            basicSalary: basicSalary, // استخدام الراتب الصحيح
                            allowances: 0,
                            deductions: 0,
                            penalties: 0,
                            netSalary: basicSalary,
                            status: 'pending',
                            createdAt: new Date().toISOString(),
                            createdBy: 'system'
                        };

                        salaries.push(salaryData);
                        addedCount++;
                        console.log(`✅ Added salary for ${employee.fullName} - ${getMonthName(month)} ${year} (${formatCurrencyAmount(basicSalary)})`);
                    }
                }
            }
        }
    });

    if (addedCount > 0) {
        saveSalariesData();
        loadSalariesTable();
        updateStatistics();
        showMessage(`تم قيد ${addedCount} راتب بنجاح بالمنطق المصحح`, 'success');
    } else {
        showMessage('لا توجد مرتبات مفقودة لإضافتها', 'info');
    }

    closeMissingSalariesAlert();
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('ar-SA');
}

// Create accounting entry for salary payment - NEW FEATURE
function createSalaryAccountingEntry(salary) {
    try {
        // البحث عن الحسابات المطلوبة أو إنشاؤها
        ensureSalaryAccounts();

        // قراءة شجرة الحسابات
        let accounts = [];
        const savedAccounts = localStorage.getItem('anwar_bakery_chart_of_accounts');
        if (savedAccounts) {
            accounts = JSON.parse(savedAccounts);
        }

        // البحث عن الحسابات
        const salariesExpenseAccount = accounts.find(acc => acc.code === '5210'); // مصروف الرواتب
        const employeeAccount = accounts.find(acc => acc.code === `2120-${salary.employeeId}`); // حساب الموظف
        const cashAccount = accounts.find(acc => acc.code === '1111'); // الصندوق

        if (!salariesExpenseAccount || !employeeAccount || !cashAccount) {
            console.error('Required accounts not found for salary entry');
            return;
        }

        // إنشاء القيد المحاسبي
        const journalEntry = {
            id: Date.now(),
            entryNumber: generateJournalEntryNumber(),
            type: 'salary',
            date: new Date().toISOString().split('T')[0],
            description: `راتب ${salary.employeeName} - ${getMonthName(salary.month)} ${salary.year}`,
            entries: [
                // من حـ/ مصروف الرواتب (مدين)
                {
                    lineNumber: 1,
                    accountId: salariesExpenseAccount.id,
                    account: {
                        id: salariesExpenseAccount.id,
                        code: salariesExpenseAccount.code,
                        name: salariesExpenseAccount.name
                    },
                    description: `راتب ${salary.employeeName}`,
                    debit: salary.netSalary,
                    credit: 0
                },
                // إلى حـ/ الموظف (دائن) - إذا كان هناك خصومات أو جزاءات
                ...(salary.deductions > 0 || salary.penalties > 0 ? [{
                    lineNumber: 2,
                    accountId: employeeAccount.id,
                    account: {
                        id: employeeAccount.id,
                        code: employeeAccount.code,
                        name: employeeAccount.name
                    },
                    description: `خصومات وجزاءات ${salary.employeeName}`,
                    debit: 0,
                    credit: salary.deductions + salary.penalties
                }] : []),
                // إلى حـ/ الصندوق (دائن)
                {
                    lineNumber: (salary.deductions > 0 || salary.penalties > 0) ? 3 : 2,
                    accountId: cashAccount.id,
                    account: {
                        id: cashAccount.id,
                        code: cashAccount.code,
                        name: cashAccount.name
                    },
                    description: `دفع راتب ${salary.employeeName}`,
                    debit: 0,
                    credit: salary.netSalary
                }
            ],
            totalDebit: salary.basicSalary + salary.allowances,
            totalCredit: salary.basicSalary + salary.allowances,
            createdBy: 'system',
            createdAt: new Date().toISOString(),
            salaryId: salary.id,
            employeeId: salary.employeeId
        };

        // حفظ القيد في دفتر اليومية
        saveJournalEntry(journalEntry);

        // تحديث أرصدة الحسابات
        updateAccountBalances(journalEntry);

        console.log('✅ Salary accounting entry created successfully');

    } catch (error) {
        console.error('❌ Error creating salary accounting entry:', error);
        showMessage('خطأ في إنشاء القيد المحاسبي للراتب', 'error');
    }
}

// Create salary accrual entry (Step 1: Record salary liability)
function createSalaryAccrualEntry(salary) {
    try {
        // التأكد من وجود الحسابات المطلوبة
        ensureSalaryAccounts();

        // قراءة شجرة الحسابات
        let accounts = [];
        const savedAccounts = localStorage.getItem('anwar_bakery_chart_of_accounts');
        if (savedAccounts) {
            accounts = JSON.parse(savedAccounts);
        }

        // البحث عن الحسابات
        const salariesExpenseAccount = accounts.find(acc => acc.code === '5210'); // مصروف الرواتب
        const employeeAccount = accounts.find(acc => acc.code === `2120-${salary.employeeId}`); // حساب الموظف

        if (!salariesExpenseAccount || !employeeAccount) {
            console.error('Required accounts not found for salary accrual');
            return;
        }

        // حساب الخصومات والجزاءات الفعلية
        const employeePenalties = penalties.filter(p => p.employeeId === salary.employeeId);
        const employeeDeductions = deductions.filter(d => d.employeeId === salary.employeeId);

        const totalPenalties = employeePenalties.reduce((sum, p) => sum + p.amount, 0);
        const totalDeductions = employeeDeductions.reduce((sum, d) => sum + d.amount, 0);

        // تحديث بيانات الراتب بالخصومات والجزاءات الفعلية
        salary.penalties = totalPenalties;
        salary.deductions = totalDeductions;
        salary.netSalary = salary.basicSalary + salary.allowances - totalPenalties - totalDeductions;

        console.log(`💰 Salary calculation for ${salary.employeeName}:`);
        console.log(`  - Basic Salary: ${salary.basicSalary}`);
        console.log(`  - Allowances: ${salary.allowances}`);
        console.log(`  - Penalties: ${totalPenalties}`);
        console.log(`  - Deductions: ${totalDeductions}`);
        console.log(`  - Net Salary: ${salary.netSalary}`);

        // إنشاء قيد الاستحقاق مع الخصومات والجزاءات
        const journalEntry = {
            id: Date.now(),
            entryNumber: generateJournalEntryNumber(),
            type: 'salary_accrual',
            date: new Date().toISOString().split('T')[0],
            description: `استحقاق راتب ${salary.employeeName} - ${getMonthName(salary.month)} ${salary.year}`,
            entries: [
                // من حـ/ مصروف الرواتب (مدين) - الراتب الأساسي + البدلات
                {
                    lineNumber: 1,
                    accountId: salariesExpenseAccount.id,
                    account: {
                        id: salariesExpenseAccount.id,
                        code: salariesExpenseAccount.code,
                        name: salariesExpenseAccount.name
                    },
                    description: `راتب أساسي وبدلات ${salary.employeeName}`,
                    debit: salary.basicSalary + salary.allowances,
                    credit: 0
                },
                // إلى حـ/ الموظف (دائن) - صافي الراتب المستحق
                {
                    lineNumber: 2,
                    accountId: employeeAccount.id,
                    account: {
                        id: employeeAccount.id,
                        code: employeeAccount.code,
                        name: employeeAccount.name
                    },
                    description: `صافي راتب ${salary.employeeName}`,
                    debit: 0,
                    credit: salary.netSalary
                }
            ],
            totalDebit: salary.basicSalary + salary.allowances,
            totalCredit: salary.netSalary,
            createdBy: 'system',
            createdAt: new Date().toISOString(),
            salaryId: salary.id,
            employeeId: salary.employeeId,
            penalties: totalPenalties,
            deductions: totalDeductions
        };

        // إضافة قيد للخصومات والجزاءات إذا وجدت
        if (totalPenalties > 0 || totalDeductions > 0) {
            // إضافة قيد الخصومات والجزاءات
            journalEntry.entries.push({
                lineNumber: 3,
                accountId: salariesExpenseAccount.id, // يمكن إنشاء حساب منفصل للخصومات
                account: {
                    id: salariesExpenseAccount.id,
                    code: salariesExpenseAccount.code,
                    name: salariesExpenseAccount.name
                },
                description: `خصومات وجزاءات ${salary.employeeName}`,
                debit: 0,
                credit: totalPenalties + totalDeductions
            });

            journalEntry.totalCredit = salary.basicSalary + salary.allowances;
        }

        // حفظ القيد
        saveJournalEntry(journalEntry);
        updateAccountBalances(journalEntry);

        // إجبار تحديث شجرة الحسابات
        window.dispatchEvent(new CustomEvent('chartOfAccountsUpdated'));

        // اختبار فوري للأرصدة
        testAccountBalancesAfterEntry();

        console.log('✅ Salary accrual entry created successfully');

    } catch (error) {
        console.error('❌ Error creating salary accrual entry:', error);
        showMessage('خطأ في إنشاء قيد استحقاق الراتب', 'error');
    }
}

function paySalary(salaryId) {
    const salaryIndex = salaries.findIndex(s => s.id === salaryId);
    if (salaryIndex !== -1) {
        const salary = salaries[salaryIndex];

        if (salary.status === 'pending') {
            // المرحلة الأولى: قيد استحقاق الراتب
            createSalaryAccrualEntry(salary);

            // تحديث حالة الراتب إلى "مستحق"
            salaries[salaryIndex].status = 'accrued';
            salaries[salaryIndex].accruedAt = new Date().toISOString();

            showMessage('تم قيد استحقاق الراتب في شجرة الحسابات', 'success');
        } else if (salary.status === 'accrued') {
            // المرحلة الثانية: سند صرف للدفع الفعلي
            createSalaryPaymentVoucher(salary);

            // تحديث حالة الراتب إلى "مدفوع"
            salaries[salaryIndex].status = 'paid';
            salaries[salaryIndex].paidAt = new Date().toISOString();

            showMessage('تم إنشاء سند صرف ودفع الراتب للموظف', 'success');
        }

        saveSalariesData();
        loadSalariesTable();
    }
}

// Create salary payment voucher (Step 2: Cash payment to employee)
function createSalaryPaymentVoucher(salary) {
    try {
        // التأكد من وجود الحسابات المطلوبة
        ensureSalaryAccounts();

        // قراءة شجرة الحسابات
        let accounts = [];
        const savedAccounts = localStorage.getItem('anwar_bakery_chart_of_accounts');
        if (savedAccounts) {
            accounts = JSON.parse(savedAccounts);
        }

        // البحث عن الحسابات
        const employeeAccount = accounts.find(acc => acc.code === `2120-${salary.employeeId}`); // حساب الموظف
        const cashAccount = accounts.find(acc => acc.code === '1111'); // الصندوق الرئيسي

        if (!employeeAccount || !cashAccount) {
            console.error('Required accounts not found for salary payment');
            return;
        }

        // إنشاء سند الصرف
        const paymentVoucher = {
            id: Date.now(),
            voucherNumber: generateVoucherNumber('payment'),
            type: 'payment',
            date: new Date().toISOString().split('T')[0],
            description: `دفع راتب ${salary.employeeName} - ${getMonthName(salary.month)} ${salary.year}`,
            amount: salary.netSalary,
            paymentMethod: 'cash',
            cashRegisterId: 1, // الصندوق الرئيسي
            status: 'posted',
            journalEntries: [
                // من حـ/ الموظف (مدين) - تسوية الالتزام
                {
                    lineNumber: 1,
                    accountId: employeeAccount.id,
                    account: {
                        id: employeeAccount.id,
                        code: employeeAccount.code,
                        name: employeeAccount.name
                    },
                    description: `دفع راتب ${salary.employeeName}`,
                    debit: salary.netSalary,
                    credit: 0
                },
                // إلى حـ/ الصندوق (دائن) - خروج النقدية
                {
                    lineNumber: 2,
                    accountId: cashAccount.id,
                    account: {
                        id: cashAccount.id,
                        code: cashAccount.code,
                        name: cashAccount.name
                    },
                    description: `دفع راتب ${salary.employeeName}`,
                    debit: 0,
                    credit: salary.netSalary
                }
            ],
            totalDebit: salary.netSalary,
            totalCredit: salary.netSalary,
            createdBy: 'system',
            createdAt: new Date().toISOString(),
            salaryId: salary.id,
            employeeId: salary.employeeId
        };

        // حفظ سند الصرف
        savePaymentVoucher(paymentVoucher);
        updateAccountBalances(paymentVoucher);

        // إجبار تحديث شجرة الحسابات
        window.dispatchEvent(new CustomEvent('chartOfAccountsUpdated'));

        console.log('✅ Salary payment voucher created successfully');

    } catch (error) {
        console.error('❌ Error creating salary payment voucher:', error);
        showMessage('خطأ في إنشاء سند صرف الراتب', 'error');
    }
}

// Helper functions for accounting integration
function ensureSalaryAccounts() {
    let accounts = [];

    // محاولة قراءة من المكان الصحيح لشجرة الحسابات
    const savedAccounts = localStorage.getItem('anwar_bakery_accounts') || localStorage.getItem('anwar_bakery_chart_of_accounts');
    if (savedAccounts) {
        accounts = JSON.parse(savedAccounts);
    }

    console.log('🔍 Ensuring salary accounts...');
    console.log('Current accounts count:', accounts.length);

    // التأكد من وجود الحسابات الأساسية أولاً
    ensureBasicAccounts(accounts);

    // التأكد من وجود حساب مصروف الرواتب
    if (!accounts.find(acc => acc.code === '5210')) {
        const maxId = Math.max(...accounts.map(acc => acc.id), 0);
        const expensesParent = accounts.find(acc => acc.code === '5000' || acc.type === 'expenses');
        accounts.push({
            id: maxId + 1,
            code: '5210',
            name: 'مصروف الرواتب',
            type: 'expenses',
            level: 2,
            parentId: expensesParent?.id || null,
            isActive: true,
            balance: 0,
            debitBalance: 0,
            creditBalance: 0,
            hasChildren: false,
            isExpanded: false,
            isDefault: false,
            canDelete: true,
            description: 'حساب مصروفات رواتب الموظفين'
        });
        console.log('✅ Created salary expense account');
    }

    // التأكد من وجود حساب الموظفين الرئيسي
    if (!accounts.find(acc => acc.code === '2120')) {
        const maxId = Math.max(...accounts.map(acc => acc.id), 0);
        const liabilitiesParent = accounts.find(acc => acc.code === '2000' || acc.type === 'liabilities');
        accounts.push({
            id: maxId + 1,
            code: '2120',
            name: 'الموظفين',
            type: 'liabilities',
            level: 2,
            parentId: liabilitiesParent?.id || null,
            isActive: true,
            balance: 0,
            debitBalance: 0,
            creditBalance: 0,
            hasChildren: true,
            isExpanded: false,
            isDefault: false,
            canDelete: true,
            description: 'حساب التزامات الموظفين'
        });
        console.log('✅ Created employees liability account');
    }

    // إنشاء حسابات فرعية للموظفين النشطين
    const activeEmployees = employees.filter(emp => emp.status === 'active');
    activeEmployees.forEach(employee => {
        const employeeAccountCode = `2120-${employee.id}`;
        if (!accounts.find(acc => acc.code === employeeAccountCode)) {
            const maxId = Math.max(...accounts.map(acc => acc.id), 0);
            const employeesParent = accounts.find(acc => acc.code === '2120');
            accounts.push({
                id: maxId + 1,
                code: employeeAccountCode,
                name: `الموظف: ${employee.fullName}`,
                type: 'liabilities',
                level: 3,
                parentId: employeesParent?.id || null,
                isActive: true,
                balance: 0,
                debitBalance: 0,
                creditBalance: 0,
                hasChildren: false,
                isExpanded: false,
                isDefault: false,
                canDelete: true,
                description: `حساب الموظف: ${employee.fullName} - ${employee.employeeNumber}`,
                employeeId: employee.id
            });
            console.log(`✅ Created account for employee: ${employee.fullName}`);
        }
    });

    // حفظ في كلا المكانين للتوافق
    localStorage.setItem('anwar_bakery_accounts', JSON.stringify(accounts));
    localStorage.setItem('anwar_bakery_chart_of_accounts', JSON.stringify(accounts));
    console.log('💾 Salary accounts saved to localStorage');
}

// Ensure basic chart of accounts structure
function ensureBasicAccounts(accounts) {
    const basicAccounts = [
        { code: '1000', name: 'الأصول', type: 'assets', level: 1 },
        { code: '1100', name: 'الأصول المتداولة', type: 'assets', level: 2, parentCode: '1000' },
        { code: '1111', name: 'الصندوق', type: 'assets', level: 3, parentCode: '1100' },
        { code: '2000', name: 'الخصوم', type: 'liabilities', level: 1 },
        { code: '2100', name: 'الخصوم المتداولة', type: 'liabilities', level: 2, parentCode: '2000' },
        { code: '5000', name: 'المصروفات', type: 'expenses', level: 1 }
    ];

    basicAccounts.forEach(basicAccount => {
        if (!accounts.find(acc => acc.code === basicAccount.code)) {
            const maxId = Math.max(...accounts.map(acc => acc.id), 0);
            const parent = basicAccount.parentCode ? accounts.find(acc => acc.code === basicAccount.parentCode) : null;

            accounts.push({
                id: maxId + 1,
                code: basicAccount.code,
                name: basicAccount.name,
                type: basicAccount.type,
                level: basicAccount.level,
                parentId: parent?.id || null,
                isActive: true,
                balance: 0,
                debitBalance: 0,
                creditBalance: 0,
                hasChildren: basicAccount.level < 3,
                isExpanded: false,
                isDefault: true,
                canDelete: false,
                description: `حساب ${basicAccount.name}`
            });
            console.log(`✅ Created basic account: ${basicAccount.name}`);
        }
    });
}

function generateJournalEntryNumber() {
    let counter = parseInt(localStorage.getItem('anwar_bakery_journal_counter') || '0');
    counter++;
    localStorage.setItem('anwar_bakery_journal_counter', counter.toString());
    return `JE${counter.toString().padStart(6, '0')}`;
}

function generateVoucherNumber(type) {
    const prefix = type === 'payment' ? 'PV' : 'RV';
    let counter = parseInt(localStorage.getItem(`anwar_bakery_${type}_counter`) || '0');
    counter++;
    localStorage.setItem(`anwar_bakery_${type}_counter`, counter.toString());
    return `${prefix}${counter.toString().padStart(6, '0')}`;
}

function saveJournalEntry(entry) {
    let journalEntries = [];
    const savedEntries = localStorage.getItem('anwar_bakery_journal_entries');
    if (savedEntries) {
        journalEntries = JSON.parse(savedEntries);
    }
    journalEntries.push(entry);
    localStorage.setItem('anwar_bakery_journal_entries', JSON.stringify(journalEntries));
}

function savePaymentVoucher(voucher) {
    let vouchers = [];
    const savedVouchers = localStorage.getItem('anwar_bakery_vouchers');
    if (savedVouchers) {
        vouchers = JSON.parse(savedVouchers);
    }
    vouchers.push(voucher);
    localStorage.setItem('anwar_bakery_vouchers', JSON.stringify(vouchers));
}

function updateAccountBalances(entry) {
    let accounts = [];
    const savedAccounts = localStorage.getItem('anwar_bakery_accounts') || localStorage.getItem('anwar_bakery_chart_of_accounts');
    if (savedAccounts) {
        accounts = JSON.parse(savedAccounts);
    }

    console.log('🔄 Updating account balances...');

    // تحديث أرصدة الحسابات
    const entriesToProcess = entry.journalEntries || entry.entries || [];
    entriesToProcess.forEach(journalEntry => {
        const account = accounts.find(acc => acc.id === journalEntry.accountId);
        if (account) {
            const debitAmount = parseFloat(journalEntry.debit) || 0;
            const creditAmount = parseFloat(journalEntry.credit) || 0;

            account.debitBalance = (parseFloat(account.debitBalance) || 0) + debitAmount;
            account.creditBalance = (parseFloat(account.creditBalance) || 0) + creditAmount;

            // حساب الرصيد النهائي حسب نوع الحساب
            if (['assets', 'expenses'].includes(account.type)) {
                account.balance = account.debitBalance - account.creditBalance;
            } else {
                account.balance = account.creditBalance - account.debitBalance;
            }

            console.log(`💰 Updated ${account.name}: Balance = ${account.balance}`);
        } else {
            console.error(`❌ Account not found with ID: ${journalEntry.accountId}`);
        }
    });

    // حفظ في كلا المكانين للتوافق
    localStorage.setItem('anwar_bakery_accounts', JSON.stringify(accounts));
    localStorage.setItem('anwar_bakery_chart_of_accounts', JSON.stringify(accounts));
    console.log('✅ Account balances updated and saved');
}

// Test accounting system
function testAccountingSystem() {
    console.log('🧪 Testing accounting system...');

    // فحص شجرة الحسابات
    const savedAccounts = localStorage.getItem('anwar_bakery_accounts') || localStorage.getItem('anwar_bakery_chart_of_accounts');
    if (savedAccounts) {
        const accounts = JSON.parse(savedAccounts);
        console.log(`📊 Chart of accounts has ${accounts.length} accounts`);

        // فحص الحسابات المطلوبة للمرتبات
        const salaryExpenseAccount = accounts.find(acc => acc.code === '5210');
        const employeesAccount = accounts.find(acc => acc.code === '2120');
        const cashAccount = accounts.find(acc => acc.code === '1111');

        console.log('Required accounts check:');
        console.log('- Salary Expense (5210):', salaryExpenseAccount ? '✅' : '❌');
        console.log('- Employees (2120):', employeesAccount ? '✅' : '❌');
        console.log('- Cash (1111):', cashAccount ? '✅' : '❌');

        // عرض حسابات الموظفين
        const employeeAccounts = accounts.filter(acc => acc.code.startsWith('2120-'));
        console.log(`👥 Employee accounts: ${employeeAccounts.length}`);
        employeeAccounts.forEach(acc => {
            console.log(`  - ${acc.name} (${acc.code}): Balance = ${acc.balance}`);
        });

    } else {
        console.log('❌ No chart of accounts found');
    }

    // فحص القيود المحاسبية
    const savedEntries = localStorage.getItem('anwar_bakery_journal_entries');
    if (savedEntries) {
        const entries = JSON.parse(savedEntries);
        const salaryEntries = entries.filter(entry => entry.type === 'salary_accrual');
        console.log(`📝 Salary journal entries: ${salaryEntries.length}`);
    }

    // فحص سندات الصرف
    const savedVouchers = localStorage.getItem('anwar_bakery_vouchers');
    if (savedVouchers) {
        const vouchers = JSON.parse(savedVouchers);
        const salaryVouchers = vouchers.filter(voucher => voucher.salaryId);
        console.log(`💸 Salary payment vouchers: ${salaryVouchers.length}`);
    }
}

function deletePenalty(penaltyId) {
    if (confirm('هل أنت متأكد من حذف هذا الجزاء؟')) {
        const penaltyIndex = penalties.findIndex(p => p.id === penaltyId);
        if (penaltyIndex !== -1) {
            const deletedPenalty = penalties[penaltyIndex];
            penalties.splice(penaltyIndex, 1);
            saveSalariesData();
            loadPenaltiesTable();

            // تحديث المرتبات المعلقة للموظف
            updateEmployeePendingSalaries(deletedPenalty.employeeId);

            showMessage('تم حذف الجزاء وتحديث المرتبات', 'success');
        }
    }
}

// Salary Report Functions
function openSalaryReportModal() {
    document.getElementById('salaryReportModal').classList.add('active');

    // تعيين التواريخ الافتراضية
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    document.getElementById('reportFromDate').value = firstDayOfMonth.toISOString().split('T')[0];
    document.getElementById('reportToDate').value = lastDayOfMonth.toISOString().split('T')[0];

    // تعبئة قائمة الموظفين
    populateReportEmployeeFilter();
}

function closeSalaryReportModal() {
    document.getElementById('salaryReportModal').classList.remove('active');
}

function populateReportEmployeeFilter() {
    const select = document.getElementById('reportEmployeeFilter');
    select.innerHTML = '<option value="">جميع الموظفين</option>';

    employees.forEach(employee => {
        const option = document.createElement('option');
        option.value = employee.id;
        option.textContent = employee.fullName;
        select.appendChild(option);
    });
}

function generateSalaryReport() {
    const fromDate = document.getElementById('reportFromDate').value;
    const toDate = document.getElementById('reportToDate').value;
    const employeeFilter = document.getElementById('reportEmployeeFilter').value;

    if (!fromDate || !toDate) {
        showMessage('يرجى تحديد التواريخ', 'error');
        return;
    }

    // فلترة المرتبات حسب التواريخ والموظف
    let filteredSalaries = salaries.filter(salary => {
        const salaryDate = new Date(salary.createdAt || salary.year + '-' + salary.month.toString().padStart(2, '0') + '-01');
        const from = new Date(fromDate);
        const to = new Date(toDate);

        const dateMatch = salaryDate >= from && salaryDate <= to;
        const employeeMatch = !employeeFilter || salary.employeeId == employeeFilter;

        return dateMatch && employeeMatch;
    });

    // حساب الإحصائيات
    const stats = {
        total: filteredSalaries.reduce((sum, s) => sum + (s.basicSalary + s.allowances), 0),
        paid: filteredSalaries.filter(s => s.status === 'paid').reduce((sum, s) => sum + s.netSalary, 0),
        accrued: filteredSalaries.filter(s => s.status === 'accrued').reduce((sum, s) => sum + s.netSalary, 0),
        pending: filteredSalaries.filter(s => s.status === 'pending').reduce((sum, s) => sum + s.netSalary, 0)
    };

    // عرض الإحصائيات
    document.getElementById('reportSummary').classList.remove('hidden');
    document.getElementById('totalSalariesReport').textContent = formatCurrencyAmount(stats.total);
    document.getElementById('totalPaidReport').textContent = formatCurrencyAmount(stats.paid);
    document.getElementById('totalAccruedReport').textContent = formatCurrencyAmount(stats.accrued);
    document.getElementById('totalPendingReport').textContent = formatCurrencyAmount(stats.pending);

    // عرض الجدول
    loadSalaryReportTable(filteredSalaries);
}

function loadSalaryReportTable(salariesData) {
    const tbody = document.getElementById('salaryReportTableBody');

    if (salariesData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-8 text-gray-500">
                    لا توجد مرتبات في الفترة المحددة
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = salariesData.map(salary => {
        const employee = employees.find(emp => emp.id === salary.employeeId);
        const salaryDate = new Date(salary.createdAt || salary.year + '-' + salary.month.toString().padStart(2, '0') + '-01');

        return `
            <tr class="hover:bg-gray-50">
                <td class="px-3 py-2">${salaryDate.toLocaleDateString('ar-SA')}</td>
                <td class="px-3 py-2">${employee ? employee.fullName : salary.employeeName}</td>
                <td class="px-3 py-2">${getMonthName(salary.month)} ${salary.year}</td>
                <td class="px-3 py-2">${formatCurrencyAmount(salary.basicSalary)}</td>
                <td class="px-3 py-2 font-medium">${formatCurrencyAmount(salary.netSalary)}</td>
                <td class="px-3 py-2">
                    <span class="px-2 py-1 text-xs rounded ${
                        salary.status === 'paid' ? 'bg-green-100 text-green-800' :
                        salary.status === 'accrued' ? 'bg-blue-100 text-blue-800' :
                        'bg-yellow-100 text-yellow-800'
                    }">
                        ${
                            salary.status === 'paid' ? 'مدفوع' :
                            salary.status === 'accrued' ? 'مستحق' :
                            'معلق'
                        }
                    </span>
                </td>
                <td class="px-3 py-2 text-center">
                    ${salary.status !== 'pending' ? `
                        <button onclick="viewJournalEntry('${salary.id}')"
                                class="text-blue-600 hover:text-blue-900 text-xs bg-blue-50 px-2 py-1 rounded">
                            📋 عرض القيد
                        </button>
                    ` : `
                        <span class="text-gray-400 text-xs">لا يوجد قيد</span>
                    `}
                </td>
            </tr>
        `;
    }).join('');
}

function viewJournalEntry(salaryId) {
    // البحث عن القيود المحاسبية المرتبطة بهذا الراتب
    let journalEntries = [];
    const savedEntries = localStorage.getItem('anwar_bakery_journal_entries');
    if (savedEntries) {
        journalEntries = JSON.parse(savedEntries);
    }

    let vouchers = [];
    const savedVouchers = localStorage.getItem('anwar_bakery_vouchers');
    if (savedVouchers) {
        vouchers = JSON.parse(savedVouchers);
    }

    // البحث عن القيود المرتبطة بهذا الراتب
    const relatedEntries = journalEntries.filter(entry => entry.salaryId === salaryId);
    const relatedVouchers = vouchers.filter(voucher => voucher.salaryId === salaryId);

    const salary = salaries.find(s => s.id === salaryId);
    const employee = employees.find(emp => emp.id === salary?.employeeId);

    if (relatedEntries.length === 0 && relatedVouchers.length === 0) {
        showMessage('لا توجد قيود محاسبية مرتبطة بهذا الراتب', 'info');
        return;
    }

    // عرض تفاصيل القيود
    let content = `
        <div class="space-y-4">
            <div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="font-medium text-blue-800 mb-2">معلومات الراتب</h4>
                <div class="grid grid-cols-2 gap-2 text-sm">
                    <div><span class="font-medium">الموظف:</span> ${employee?.fullName || salary?.employeeName}</div>
                    <div><span class="font-medium">الشهر:</span> ${getMonthName(salary?.month)} ${salary?.year}</div>
                    <div><span class="font-medium">الراتب الأساسي:</span> ${formatCurrencyAmount(salary?.basicSalary)}</div>
                    <div><span class="font-medium">صافي الراتب:</span> ${formatCurrencyAmount(salary?.netSalary)}</div>
                </div>
            </div>
    `;

    // عرض قيد الاستحقاق
    relatedEntries.forEach(entry => {
        content += `
            <div class="bg-green-50 p-4 rounded-lg">
                <h4 class="font-medium text-green-800 mb-2">قيد الاستحقاق - ${entry.entryNumber}</h4>
                <div class="text-sm text-gray-600 mb-2">${entry.description}</div>
                <div class="text-sm text-gray-500 mb-3">التاريخ: ${new Date(entry.date).toLocaleDateString('ar-SA')}</div>

                <table class="w-full text-sm">
                    <thead class="bg-green-100">
                        <tr>
                            <th class="px-2 py-1 text-right">الحساب</th>
                            <th class="px-2 py-1 text-right">البيان</th>
                            <th class="px-2 py-1 text-right">مدين</th>
                            <th class="px-2 py-1 text-right">دائن</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${entry.entries.map(line => `
                            <tr class="border-b">
                                <td class="px-2 py-1">${line.account.name}</td>
                                <td class="px-2 py-1">${line.description}</td>
                                <td class="px-2 py-1 text-red-600">${line.debit > 0 ? formatCurrencyAmount(line.debit) : '-'}</td>
                                <td class="px-2 py-1 text-green-600">${line.credit > 0 ? formatCurrencyAmount(line.credit) : '-'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                    <tfoot class="bg-green-100 font-medium">
                        <tr>
                            <td colspan="2" class="px-2 py-1">الإجمالي</td>
                            <td class="px-2 py-1 text-red-600">${formatCurrencyAmount(entry.totalDebit)}</td>
                            <td class="px-2 py-1 text-green-600">${formatCurrencyAmount(entry.totalCredit)}</td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        `;
    });

    // عرض سند الصرف
    relatedVouchers.forEach(voucher => {
        content += `
            <div class="bg-purple-50 p-4 rounded-lg">
                <h4 class="font-medium text-purple-800 mb-2">سند الصرف - ${voucher.voucherNumber}</h4>
                <div class="text-sm text-gray-600 mb-2">${voucher.description}</div>
                <div class="text-sm text-gray-500 mb-3">التاريخ: ${new Date(voucher.date).toLocaleDateString('ar-SA')}</div>

                <table class="w-full text-sm">
                    <thead class="bg-purple-100">
                        <tr>
                            <th class="px-2 py-1 text-right">الحساب</th>
                            <th class="px-2 py-1 text-right">البيان</th>
                            <th class="px-2 py-1 text-right">مدين</th>
                            <th class="px-2 py-1 text-right">دائن</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${voucher.journalEntries.map(line => `
                            <tr class="border-b">
                                <td class="px-2 py-1">${line.account.name}</td>
                                <td class="px-2 py-1">${line.description}</td>
                                <td class="px-2 py-1 text-red-600">${line.debit > 0 ? formatCurrencyAmount(line.debit) : '-'}</td>
                                <td class="px-2 py-1 text-green-600">${line.credit > 0 ? formatCurrencyAmount(line.credit) : '-'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                    <tfoot class="bg-purple-100 font-medium">
                        <tr>
                            <td colspan="2" class="px-2 py-1">الإجمالي</td>
                            <td class="px-2 py-1 text-red-600">${formatCurrencyAmount(voucher.totalDebit)}</td>
                            <td class="px-2 py-1 text-green-600">${formatCurrencyAmount(voucher.totalCredit)}</td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        `;
    });

    content += '</div>';

    document.getElementById('journalEntryTitle').textContent = `القيود المحاسبية - راتب ${employee?.fullName || salary?.employeeName}`;
    document.getElementById('journalEntryContent').innerHTML = content;
    document.getElementById('journalEntryModal').classList.add('active');
}

function closeJournalEntryModal() {
    document.getElementById('journalEntryModal').classList.remove('active');
}

function exportSalaryReport() {
    const fromDate = document.getElementById('reportFromDate').value;
    const toDate = document.getElementById('reportToDate').value;
    const employeeFilter = document.getElementById('reportEmployeeFilter').value;

    if (!fromDate || !toDate) {
        showMessage('يرجى إنشاء التقرير أولاً', 'error');
        return;
    }

    // فلترة المرتبات
    let filteredSalaries = salaries.filter(salary => {
        const salaryDate = new Date(salary.createdAt || salary.year + '-' + salary.month.toString().padStart(2, '0') + '-01');
        const from = new Date(fromDate);
        const to = new Date(toDate);

        const dateMatch = salaryDate >= from && salaryDate <= to;
        const employeeMatch = !employeeFilter || salary.employeeId == employeeFilter;

        return dateMatch && employeeMatch;
    });

    // إنشاء ملف Excel
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "التاريخ,الموظف,الشهر,الراتب الأساسي,البدلات,الخصومات,الجزاءات,صافي الراتب,الحالة\n";

    filteredSalaries.forEach(salary => {
        const employee = employees.find(emp => emp.id === salary.employeeId);
        const salaryDate = new Date(salary.createdAt || salary.year + '-' + salary.month.toString().padStart(2, '0') + '-01');
        const statusText = salary.status === 'paid' ? 'مدفوع' : salary.status === 'accrued' ? 'مستحق' : 'معلق';

        csvContent += `${salaryDate.toLocaleDateString('ar-SA')},${employee?.fullName || salary.employeeName},${getMonthName(salary.month)} ${salary.year},${salary.basicSalary},${salary.allowances},${salary.deductions},${salary.penalties},${salary.netSalary},${statusText}\n`;
    });

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `تقرير_المرتبات_${fromDate}_${toDate}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showMessage('تم تصدير التقرير بنجاح', 'success');
}

// Edit salary entry
function editSalary(salaryId) {
    const salary = salaries.find(s => s.id === salaryId);
    if (!salary) {
        showMessage('الراتب غير موجود', 'error');
        return;
    }

    if (salary.status !== 'pending') {
        showMessage('لا يمكن تعديل راتب مقيد محاسبي<|im_start|>', 'error');
        return;
    }

    const employee = employees.find(emp => emp.id === salary.employeeId);
    if (!employee) {
        showMessage('الموظف غير موجود', 'error');
        return;
    }

    // فتح نموذج التعديل
    const newBasicSalary = prompt(`تعديل الراتب الأساسي للموظف: ${employee.fullName}\n\nالراتب الحالي: ${salary.basicSalary}`, salary.basicSalary);
    if (newBasicSalary === null) return;

    const basicSalary = parseFloat(newBasicSalary);
    if (isNaN(basicSalary) || basicSalary < 0) {
        showMessage('يرجى إدخال راتب صحيح', 'error');
        return;
    }

    const newAllowances = prompt(`البدلات:\n\nالبدلات الحالية: ${salary.allowances}`, salary.allowances);
    if (newAllowances === null) return;

    const allowances = parseFloat(newAllowances) || 0;
    if (allowances < 0) {
        showMessage('يرجى إدخال بدلات صحيحة', 'error');
        return;
    }

    // تحديث الراتب
    salary.basicSalary = basicSalary;
    salary.allowances = allowances;
    salary.netSalary = basicSalary + allowances - salary.deductions - salary.penalties;
    salary.updatedAt = new Date().toISOString();

    saveSalariesData();
    loadSalariesTable();
    showMessage('تم تحديث الراتب بنجاح', 'success');
}

// Delete salary entry
function deleteSalary(salaryId) {
    const salary = salaries.find(s => s.id === salaryId);
    if (!salary) {
        showMessage('الراتب غير موجود', 'error');
        return;
    }

    if (salary.status !== 'pending') {
        showMessage('لا يمكن حذف راتب مقيد محاسبي<|im_start|>. يجب عكس القيد أولاً', 'error');
        return;
    }

    const employee = employees.find(emp => emp.id === salary.employeeId);
    const employeeName = employee ? employee.fullName : salary.employeeName;

    if (!confirm(`هل أنت متأكد من حذف راتب ${employeeName} لشهر ${getMonthName(salary.month)} ${salary.year}؟`)) {
        return;
    }

    // حذف الراتب
    const salaryIndex = salaries.findIndex(s => s.id === salaryId);
    salaries.splice(salaryIndex, 1);

    saveSalariesData();
    loadSalariesTable();
    updateStatistics();
    showMessage('تم حذف الراتب بنجاح', 'success');
}

// Reverse salary accrual (عكس قيد الاستحقاق)
function reverseSalaryAccrual(salaryId) {
    const salary = salaries.find(s => s.id === salaryId);
    if (!salary) {
        showMessage('الراتب غير موجود', 'error');
        return;
    }

    if (salary.status !== 'accrued') {
        showMessage('هذا الراتب ليس في حالة مستحق', 'error');
        return;
    }

    const employee = employees.find(emp => emp.id === salary.employeeId);
    const employeeName = employee ? employee.fullName : salary.employeeName;

    if (!confirm(`هل أنت متأكد من عكس قيد استحقاق راتب ${employeeName}؟\n\nسيتم إنشاء قيد عكسي وإرجاع الراتب لحالة "معلق"`)) {
        return;
    }

    try {
        // إنشاء قيد عكسي
        createReversalJournalEntry(salary, 'accrual');

        // تحديث حالة الراتب
        salary.status = 'pending';
        salary.accruedAt = null;
        salary.reversedAt = new Date().toISOString();

        saveSalariesData();
        loadSalariesTable();
        showMessage('تم عكس قيد الاستحقاق بنجاح', 'success');

    } catch (error) {
        console.error('Error reversing salary accrual:', error);
        showMessage('خطأ في عكس قيد الاستحقاق', 'error');
    }
}

// Reverse salary payment (عكس الدفع)
function reverseSalaryPayment(salaryId) {
    const salary = salaries.find(s => s.id === salaryId);
    if (!salary) {
        showMessage('الراتب غير موجود', 'error');
        return;
    }

    if (salary.status !== 'paid') {
        showMessage('هذا الراتب ليس في حالة مدفوع', 'error');
        return;
    }

    const employee = employees.find(emp => emp.id === salary.employeeId);
    const employeeName = employee ? employee.fullName : salary.employeeName;

    if (!confirm(`هل أنت متأكد من عكس دفع راتب ${employeeName}؟\n\nسيتم إنشاء سند عكسي وإرجاع الراتب لحالة "مستحق"`)) {
        return;
    }

    try {
        // إنشاء سند عكسي
        createReversalPaymentVoucher(salary);

        // تحديث حالة الراتب
        salary.status = 'accrued';
        salary.paidAt = null;
        salary.paymentReversedAt = new Date().toISOString();

        saveSalariesData();
        loadSalariesTable();
        showMessage('تم عكس دفع الراتب بنجاح', 'success');

    } catch (error) {
        console.error('Error reversing salary payment:', error);
        showMessage('خطأ في عكس دفع الراتب', 'error');
    }
}

// Edit accrued salary (with accounting entry)
function editAccruedSalary(salaryId) {
    const salary = salaries.find(s => s.id === salaryId);
    if (!salary) {
        showMessage('الراتب غير موجود', 'error');
        return;
    }

    if (salary.status !== 'accrued') {
        showMessage('يمكن تعديل الرواتب المستحقة فقط', 'error');
        return;
    }

    const employee = employees.find(emp => emp.id === salary.employeeId);
    if (!employee) {
        showMessage('الموظف غير موجود', 'error');
        return;
    }

    // عرض نموذج التعديل
    const newBasicSalary = prompt(`تعديل الراتب الأساسي للموظف: ${employee.fullName}\n\nالراتب الحالي: ${salary.basicSalary}`, salary.basicSalary);
    if (newBasicSalary === null) return;

    const basicSalary = parseFloat(newBasicSalary);
    if (isNaN(basicSalary) || basicSalary < 0) {
        showMessage('يرجى إدخال راتب صحيح', 'error');
        return;
    }

    const newAllowances = prompt(`البدلات:\n\nالبدلات الحالية: ${salary.allowances}`, salary.allowances);
    if (newAllowances === null) return;

    const allowances = parseFloat(newAllowances) || 0;
    if (allowances < 0) {
        showMessage('يرجى إدخال بدلات صحيحة', 'error');
        return;
    }

    if (!confirm(`هل أنت متأكد من تعديل راتب ${employee.fullName}؟\n\nسيتم:\n1. عكس القيد المحاسبي الحالي\n2. إنشاء قيد جديد بالمبالغ المحدثة`)) {
        return;
    }

    try {
        // عكس القيد المحاسبي الحالي
        createReversalJournalEntry(salary, 'accrual');

        // تحديث بيانات الراتب
        salary.basicSalary = basicSalary;
        salary.allowances = allowances;

        // إعادة حساب الخصومات والجزاءات
        const employeePenalties = penalties.filter(p => p.employeeId === salary.employeeId);
        const employeeDeductions = deductions.filter(d => d.employeeId === salary.employeeId);

        salary.penalties = employeePenalties.reduce((sum, p) => sum + p.amount, 0);
        salary.deductions = employeeDeductions.reduce((sum, d) => sum + d.amount, 0);
        salary.netSalary = basicSalary + allowances - salary.penalties - salary.deductions;
        salary.updatedAt = new Date().toISOString();

        // إنشاء قيد جديد
        createSalaryAccrualEntry(salary);

        saveSalariesData();
        loadSalariesTable();
        showMessage('تم تعديل الراتب والقيد المحاسبي بنجاح', 'success');

    } catch (error) {
        console.error('Error editing accrued salary:', error);
        showMessage('خطأ في تعديل الراتب المستحق', 'error');
    }
}

// Edit paid salary (with voucher)
function editPaidSalary(salaryId) {
    const salary = salaries.find(s => s.id === salaryId);
    if (!salary) {
        showMessage('الراتب غير موجود', 'error');
        return;
    }

    if (salary.status !== 'paid') {
        showMessage('يمكن تعديل الرواتب المدفوعة فقط', 'error');
        return;
    }

    const employee = employees.find(emp => emp.id === salary.employeeId);
    if (!employee) {
        showMessage('الموظف غير موجود', 'error');
        return;
    }

    showMessage('لتعديل راتب مدفوع، يجب أولاً عكس الدفع ثم عكس الاستحقاق، ثم إعادة القيد بالمبالغ الجديدة', 'info');

    if (!confirm(`هل تريد عكس دفع راتب ${employee.fullName} لتتمكن من تعديله؟`)) {
        return;
    }

    // عكس الدفع أولاً
    reverseSalaryPayment(salaryId);
}

// Create reversal journal entry
function createReversalJournalEntry(salary, type) {
    try {
        let accounts = [];
        const savedAccounts = localStorage.getItem('anwar_bakery_accounts') || localStorage.getItem('anwar_bakery_chart_of_accounts');
        if (savedAccounts) {
            accounts = JSON.parse(savedAccounts);
        }

        const salariesExpenseAccount = accounts.find(acc => acc.code === '5210');
        const employeeAccount = accounts.find(acc => acc.code === `2120-${salary.employeeId}`);

        if (!salariesExpenseAccount || !employeeAccount) {
            throw new Error('Required accounts not found');
        }

        // إنشاء قيد عكسي (عكس القيد الأصلي)
        const reversalEntry = {
            id: Date.now(),
            entryNumber: generateJournalEntryNumber(),
            type: 'salary_accrual_reversal',
            date: new Date().toISOString().split('T')[0],
            description: `عكس قيد استحقاق راتب ${salary.employeeName} - ${getMonthName(salary.month)} ${salary.year}`,
            entries: [
                // من حـ/ الموظف (مدين) - عكس الدائن
                {
                    lineNumber: 1,
                    accountId: employeeAccount.id,
                    account: {
                        id: employeeAccount.id,
                        code: employeeAccount.code,
                        name: employeeAccount.name
                    },
                    description: `عكس استحقاق راتب ${salary.employeeName}`,
                    debit: salary.basicSalary + salary.allowances,
                    credit: 0
                },
                // إلى حـ/ مصروف الرواتب (دائن) - عكس المدين
                {
                    lineNumber: 2,
                    accountId: salariesExpenseAccount.id,
                    account: {
                        id: salariesExpenseAccount.id,
                        code: salariesExpenseAccount.code,
                        name: salariesExpenseAccount.name
                    },
                    description: `عكس استحقاق راتب ${salary.employeeName}`,
                    debit: 0,
                    credit: salary.basicSalary + salary.allowances
                }
            ],
            totalDebit: salary.basicSalary + salary.allowances,
            totalCredit: salary.basicSalary + salary.allowances,
            createdBy: 'system',
            createdAt: new Date().toISOString(),
            salaryId: salary.id,
            employeeId: salary.employeeId,
            isReversal: true,
            originalType: 'salary_accrual'
        };

        saveJournalEntry(reversalEntry);
        updateAccountBalances(reversalEntry);
        window.dispatchEvent(new CustomEvent('chartOfAccountsUpdated'));

        console.log('✅ Salary accrual reversal entry created successfully');

    } catch (error) {
        console.error('❌ Error creating reversal entry:', error);
        throw error;
    }
}

// Create reversal payment voucher
function createReversalPaymentVoucher(salary) {
    try {
        let accounts = [];
        const savedAccounts = localStorage.getItem('anwar_bakery_accounts') || localStorage.getItem('anwar_bakery_chart_of_accounts');
        if (savedAccounts) {
            accounts = JSON.parse(savedAccounts);
        }

        const employeeAccount = accounts.find(acc => acc.code === `2120-${salary.employeeId}`);
        const cashAccount = accounts.find(acc => acc.code === '1111');

        if (!employeeAccount || !cashAccount) {
            throw new Error('Required accounts not found');
        }

        // إنشاء سند عكسي (عكس سند الصرف الأصلي)
        const reversalVoucher = {
            id: Date.now(),
            voucherNumber: generateVoucherNumber('payment'),
            type: 'payment_reversal',
            date: new Date().toISOString().split('T')[0],
            description: `عكس دفع راتب ${salary.employeeName} - ${getMonthName(salary.month)} ${salary.year}`,
            amount: salary.netSalary,
            paymentMethod: 'cash',
            cashRegisterId: 1,
            status: 'posted',
            journalEntries: [
                // من حـ/ الصندوق (مدين) - استرداد النقدية
                {
                    lineNumber: 1,
                    accountId: cashAccount.id,
                    account: {
                        id: cashAccount.id,
                        code: cashAccount.code,
                        name: cashAccount.name
                    },
                    description: `عكس دفع راتب ${salary.employeeName}`,
                    debit: salary.netSalary,
                    credit: 0
                },
                // إلى حـ/ الموظف (دائن) - إعادة الالتزام
                {
                    lineNumber: 2,
                    accountId: employeeAccount.id,
                    account: {
                        id: employeeAccount.id,
                        code: employeeAccount.code,
                        name: employeeAccount.name
                    },
                    description: `عكس دفع راتب ${salary.employeeName}`,
                    debit: 0,
                    credit: salary.netSalary
                }
            ],
            totalDebit: salary.netSalary,
            totalCredit: salary.netSalary,
            createdBy: 'system',
            createdAt: new Date().toISOString(),
            salaryId: salary.id,
            employeeId: salary.employeeId,
            isReversal: true,
            originalType: 'salary_payment'
        };

        savePaymentVoucher(reversalVoucher);
        updateAccountBalances(reversalVoucher);
        window.dispatchEvent(new CustomEvent('chartOfAccountsUpdated'));

        console.log('✅ Salary payment reversal voucher created successfully');

    } catch (error) {
        console.error('❌ Error creating reversal voucher:', error);
        throw error;
    }
}

function deleteDeduction(deductionId) {
    if (confirm('هل أنت متأكد من حذف هذا الخصم؟')) {
        const deductionIndex = deductions.findIndex(d => d.id === deductionId);
        if (deductionIndex !== -1) {
            const deletedDeduction = deductions[deductionIndex];
            deductions.splice(deductionIndex, 1);
            saveSalariesData();
            loadDeductionsTable();

            // تحديث المرتبات المعلقة للموظف
            updateEmployeePendingSalaries(deletedDeduction.employeeId);

            showMessage('تم حذف الخصم وتحديث المرتبات', 'success');
        }
    }
}

// View employee salaries
function viewEmployeeSalaries(employeeId) {
    const employee = employees.find(emp => emp.id === employeeId);
    if (!employee) return;

    const employeeSalaries = salaries.filter(s => s.employeeId === employeeId);

    if (employeeSalaries.length === 0) {
        alert(`لا توجد مرتبات مسجلة للموظف: ${employee.fullName}`);
        return;
    }

    let message = `مرتبات الموظف: ${employee.fullName}\n\n`;
    employeeSalaries.forEach(salary => {
        message += `📅 ${getMonthName(salary.month)} ${salary.year}\n`;
        message += `💰 الراتب الأساسي: ${salary.basicSalary.toFixed(2)} ${getCurrencySymbol()}\n`;
        message += `📈 البدلات: ${salary.allowances.toFixed(2)} ${getCurrencySymbol()}\n`;
        message += `📉 الخصومات: ${salary.deductions.toFixed(2)} ${getCurrencySymbol()}\n`;
        message += `⚠️ الجزاءات: ${salary.penalties.toFixed(2)} ${getCurrencySymbol()}\n`;
        message += `💵 صافي الراتب: ${salary.netSalary.toFixed(2)} ${getCurrencySymbol()}\n`;
        message += `📊 الحالة: ${salary.status === 'paid' ? 'مدفوع' : 'معلق'}\n`;
        message += `\n`;
    });

    alert(message);
}

// View employee penalties
function viewEmployeePenalties(employeeId) {
    const employee = employees.find(emp => emp.id === employeeId);
    if (!employee) return;

    const employeePenalties = penalties.filter(p => p.employeeId === employeeId);

    if (employeePenalties.length === 0) {
        alert(`لا توجد جزاءات مسجلة للموظف: ${employee.fullName}`);
        return;
    }

    let message = `جزاءات الموظف: ${employee.fullName}\n\n`;
    employeePenalties.forEach(penalty => {
        message += `📅 التاريخ: ${formatDate(penalty.date)}\n`;
        message += `⚠️ النوع: ${getPenaltyTypeText(penalty.type)}\n`;
        message += `💰 المبلغ: ${penalty.amount.toFixed(2)} ${getCurrencySymbol()}\n`;
        message += `📝 السبب: ${penalty.reason || 'لا يوجد'}\n`;
        message += `\n`;
    });

    const totalPenalties = employeePenalties.reduce((sum, p) => sum + p.amount, 0);
    message += `📊 إجمالي الجزاءات: ${totalPenalties.toFixed(2)} ${getCurrencySymbol()}`;

    alert(message);
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_session');
        sessionStorage.removeItem('anwar_bakery_session');
        window.location.href = 'login.html';
    }
}

// Currency update listener - REBUILT VERSION
function refreshCurrencyDisplay() {
    console.log('🔄 Refreshing currency display...');
    updateStatistics();
    loadEmployees();
    if (typeof loadSalariesTable === 'function') {
        loadSalariesTable();
    }
    console.log('✅ Currency display refreshed');
}

// Listen for system settings changes - CORRECTED
window.addEventListener('storage', function(event) {
    if (event.key === 'anwar_bakery_settings') {
        console.log('📢 System settings changed, refreshing currency...');
        setTimeout(refreshCurrencyDisplay, 200);
    }
});

// Listen for custom events
window.addEventListener('settingsUpdated', function() {
    console.log('📢 Settings updated event received');
    setTimeout(refreshCurrencyDisplay, 200);
});

// Check chart of accounts status
function checkChartOfAccountsStatus() {
    console.log('🔍 Checking Chart of Accounts Status...');

    // فحص البيانات في localStorage
    const accountsData = localStorage.getItem('anwar_bakery_accounts');
    const chartData = localStorage.getItem('anwar_bakery_chart_of_accounts');

    console.log('📊 Chart of Accounts Status:');
    console.log(`- anwar_bakery_accounts: ${accountsData ? 'موجود' : 'غير موجود'}`);
    console.log(`- anwar_bakery_chart_of_accounts: ${chartData ? 'موجود' : 'غير موجود'}`);

    if (accountsData) {
        const accounts = JSON.parse(accountsData);
        console.log(`📈 Total accounts: ${accounts.length}`);

        // فحص الحسابات المطلوبة للمرتبات
        const salaryAccounts = {
            salaryExpense: accounts.find(acc => acc.code === '5210'),
            employees: accounts.find(acc => acc.code === '2120'),
            cash: accounts.find(acc => acc.code === '1111'),
            employeeAccounts: accounts.filter(acc => acc.code.startsWith('2120-'))
        };

        console.log('💼 Salary accounts status:');
        console.log(`  - Salary Expense (5210): ${salaryAccounts.salaryExpense ? '✅' : '❌'}`);
        console.log(`  - Employees (2120): ${salaryAccounts.employees ? '✅' : '❌'}`);
        console.log(`  - Cash (1111): ${salaryAccounts.cash ? '✅' : '❌'}`);
        console.log(`  - Employee accounts: ${salaryAccounts.employeeAccounts.length}`);

        // عرض الأرصدة
        if (salaryAccounts.salaryExpense) {
            console.log(`💰 Salary Expense Balance: ${salaryAccounts.salaryExpense.balance || 0}`);
        }
        salaryAccounts.employeeAccounts.forEach(acc => {
            console.log(`👤 ${acc.name}: ${acc.balance || 0}`);
        });

    } else {
        console.log('❌ No chart of accounts found - using default data');
    }

    // فحص القيود المحاسبية
    const journalEntries = localStorage.getItem('anwar_bakery_journal_entries');
    const vouchers = localStorage.getItem('anwar_bakery_vouchers');

    console.log('📝 Accounting entries:');
    console.log(`  - Journal entries: ${journalEntries ? JSON.parse(journalEntries).length : 0}`);
    console.log(`  - Vouchers: ${vouchers ? JSON.parse(vouchers).length : 0}`);
}

// Clean up incorrect salary data
function cleanupIncorrectSalaryData() {
    console.log('🧹 Cleaning up incorrect salary data...');

    let cleanedCount = 0;
    let correctedCount = 0;

    // فحص كل راتب موجود
    const salariesToRemove = [];

    salaries.forEach((salary, index) => {
        const employee = employees.find(emp => emp.id === salary.employeeId);

        if (!employee) {
            console.log(`❌ Salary for non-existent employee: ${salary.employeeName}`);
            salariesToRemove.push(index);
            return;
        }

        // فحص صحة الراتب
        const employeeBasicSalary = parseFloat(employee.basicSalary);
        const salaryBasicSalary = parseFloat(salary.basicSalary);

        if (isNaN(employeeBasicSalary) || employeeBasicSalary <= 0) {
            console.log(`❌ Employee ${employee.fullName} has invalid salary: ${employee.basicSalary}`);
            return;
        }

        // تصحيح الراتب إذا كان مختلف
        if (salaryBasicSalary !== employeeBasicSalary) {
            console.log(`🔧 Correcting salary for ${employee.fullName}: ${salaryBasicSalary} → ${employeeBasicSalary}`);
            salary.basicSalary = employeeBasicSalary;
            salary.netSalary = employeeBasicSalary + (salary.allowances || 0) - (salary.deductions || 0) - (salary.penalties || 0);
            correctedCount++;
        }

        // فحص منطق الاستحقاق
        const hireDate = new Date(employee.hireDate);
        const currentDate = new Date();
        const salaryDate = new Date(salary.year, salary.month - 1, 1);

        // التحقق من أن الراتب ليس قبل تاريخ التعيين
        if (salaryDate < hireDate) {
            console.log(`❌ Salary before hire date for ${employee.fullName}: ${getMonthName(salary.month)} ${salary.year}`);
            salariesToRemove.push(index);
            return;
        }

        // التحقق من منطق الاستحقاق للموظفين الجدد
        const daysSinceHire = Math.floor((currentDate - hireDate) / (1000 * 60 * 60 * 24));
        const isCurrentMonth = salary.year === currentDate.getFullYear() && salary.month === (currentDate.getMonth() + 1);

        if (isCurrentMonth && daysSinceHire < 30) {
            console.log(`❌ Premature salary for ${employee.fullName}: only ${daysSinceHire} days since hire`);
            salariesToRemove.push(index);
            return;
        }

        // التحقق من الراتب للمستقبل
        if (salaryDate > currentDate) {
            console.log(`❌ Future salary for ${employee.fullName}: ${getMonthName(salary.month)} ${salary.year}`);
            salariesToRemove.push(index);
            return;
        }
    });

    // حذف الرواتب الخاطئة (من الآخر للأول لتجنب تغيير الفهارس)
    salariesToRemove.sort((a, b) => b - a).forEach(index => {
        const removedSalary = salaries[index];
        console.log(`🗑️ Removing incorrect salary: ${removedSalary.employeeName} - ${getMonthName(removedSalary.month)} ${removedSalary.year}`);
        salaries.splice(index, 1);
        cleanedCount++;
    });

    // حفظ التغييرات
    if (cleanedCount > 0 || correctedCount > 0) {
        saveSalariesData();
        loadSalariesTable();
        updateStatistics();

        let message = '';
        if (cleanedCount > 0) message += `تم حذف ${cleanedCount} راتب خاطئ`;
        if (correctedCount > 0) {
            if (message) message += ' و ';
            message += `تم تصحيح ${correctedCount} راتب`;
        }

        showMessage(message, 'success');
        console.log(`✅ Cleanup completed: ${cleanedCount} removed, ${correctedCount} corrected`);
    } else {
        console.log('✅ No incorrect salary data found');
    }
}

// Test account balances after entry
function testAccountBalancesAfterEntry() {
    console.log('🧪 Testing account balances after entry...');

    const savedAccounts = localStorage.getItem('anwar_bakery_accounts') || localStorage.getItem('anwar_bakery_chart_of_accounts');
    if (!savedAccounts) {
        console.log('❌ No accounts data found');
        return;
    }

    const accounts = JSON.parse(savedAccounts);

    // فحص الحسابات المهمة
    const salaryExpenseAccount = accounts.find(acc => acc.code === '5210');
    const employeeAccounts = accounts.filter(acc => acc.code.startsWith('2120-'));
    const cashAccount = accounts.find(acc => acc.code === '1111');

    console.log('📊 Current account balances:');

    if (salaryExpenseAccount) {
        console.log(`💰 Salary Expense (5210): ${salaryExpenseAccount.balance || 0} ${getCurrencySymbol()}`);
    } else {
        console.log('❌ Salary Expense account not found');
    }

    if (employeeAccounts.length > 0) {
        employeeAccounts.forEach(acc => {
            console.log(`👤 ${acc.name} (${acc.code}): ${acc.balance || 0} ${getCurrencySymbol()}`);
        });
    } else {
        console.log('❌ No employee accounts found');
    }

    if (cashAccount) {
        console.log(`💵 Cash (1111): ${cashAccount.balance || 0} ${getCurrencySymbol()}`);
    } else {
        console.log('❌ Cash account not found');
    }

    // فحص التوازن
    const totalSalaryExpense = salaryExpenseAccount ? (salaryExpenseAccount.balance || 0) : 0;
    const totalEmployeeLiabilities = employeeAccounts.reduce((sum, acc) => sum + (acc.balance || 0), 0);

    console.log('⚖️ Balance check:');
    console.log(`  - Total Salary Expense: ${totalSalaryExpense}`);
    console.log(`  - Total Employee Liabilities: ${totalEmployeeLiabilities}`);

    if (Math.abs(totalSalaryExpense - totalEmployeeLiabilities) < 0.01) {
        console.log('✅ Accounts are balanced');
    } else {
        console.log('❌ Accounts are not balanced');
    }

    console.log('🔗 Check Chart of Accounts page for visual confirmation');
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadUserInfo();
    loadCompanyInfo();
    loadBranches();
    loadEmployees();
    initializeSalariesData();

    // إنشاء الحسابات المحاسبية المطلوبة للمرتبات
    ensureSalaryAccounts();

    // اختبار النظام المحاسبي
    testAccountingSystem();

    // فحص حالة شجرة الحسابات
    checkChartOfAccountsStatus();

    // فحص وتنظيف البيانات الخاطئة
    cleanupIncorrectSalaryData();

    // إجبار تحديث شجرة الحسابات في الصفحات الأخرى
    window.dispatchEvent(new CustomEvent('chartOfAccountsUpdated'));

    // Initial currency check
    console.log('💰 Initial currency symbol:', getCurrencySymbol());
});


