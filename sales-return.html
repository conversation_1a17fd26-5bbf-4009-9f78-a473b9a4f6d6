<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مرتجع مبيعات - مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .custom-scroll {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 #f7fafc;
        }

        .custom-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scroll::-webkit-scrollbar-track {
            background: #f7fafc;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        .invoice-item-row:hover {
            background-color: #f8fafc;
        }

        .required-field {
            border-color: #ef4444;
        }

        .return-reason-card {
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .return-reason-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .return-reason-card.selected {
            border-color: #dc2626;
            background-color: #fef2f2;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="bg-white w-64 shadow-lg flex flex-col">
            <div class="p-4 flex-shrink-0">
                <div class="flex items-center mb-8">
                    <span class="text-2xl ml-3">🍞</span>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800" id="companyName">مخبز أنوار الحي</h1>
                        <p class="text-sm text-gray-600" id="companySlogan">جودة تستحق الثقة</p>
                    </div>
                </div>
            </div>

            <nav class="flex-1 overflow-y-auto px-4 pb-4 custom-scroll">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    الرئيسية
                </a>
                <a href="employees.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👨‍💼</span>
                    إدارة الموظفين
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-red-600 bg-red-50 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏭</span>
                    الموردين
                </a>

                <!-- User Info & Logout -->
                <div class="mt-auto p-4 border-t border-gray-200">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                            أ
                        </div>
                        <div class="mr-3 flex-1">
                            <p id="userFullName" class="text-sm font-medium text-gray-900">أحمد محمد</p>
                            <p id="userRole" class="text-xs text-gray-500">مدير النظام</p>
                        </div>
                        <button onclick="logout()" class="text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50" title="تسجيل الخروج">
                            <span class="text-lg">🚪</span>
                        </button>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col min-h-0">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <a href="invoices.html" class="text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">←</span>
                            </a>
                            <h2 class="text-xl font-semibold text-gray-900">مرتجع مبيعات جديد</h2>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="window.history.back()" class="text-gray-600 hover:text-gray-800 px-2 py-2 rounded text-sm">
                                ← رجوع
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6 custom-scroll">
                <!-- Success/Error Messages -->
                <div id="messageContainer" class="mb-6"></div>

                <!-- Return Form -->
                <div class="bg-white rounded-lg shadow-sm">
                    <!-- Return Header - Compact -->
                    <div class="p-4 border-b border-gray-200">
                        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                            <!-- Return Number -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">رقم المرتجع</label>
                                <input type="text" id="returnNumber" readonly
                                       class="w-full px-2 py-1.5 border border-gray-300 rounded bg-gray-50 text-sm"
                                       value="SR-2024-001">
                            </div>

                            <!-- Return Date -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">التاريخ *</label>
                                <input type="date" id="returnDate" required
                                       class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 text-sm">
                            </div>

                            <!-- Branch -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">الفرع *</label>
                                <select id="branchId" required onchange="loadBranchData()"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 text-sm">
                                    <option value="">اختر الفرع</option>
                                </select>
                            </div>

                            <!-- Original Invoice -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">الفاتورة الأصلية</label>
                                <select id="originalInvoiceId" onchange="loadOriginalInvoice()"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 text-sm">
                                    <option value="">اختر الفاتورة</option>
                                </select>
                            </div>

                            <!-- Return Type Selection -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">نوع المرتجع *</label>
                                <select id="returnType" required onchange="toggleReturnParty()"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 text-sm">
                                    <option value="">اختر نوع المرتجع</option>
                                    <option value="from_customer">مرتجع من عميل</option>
                                    <option value="to_supplier">مرتجع إلى مورد</option>
                                </select>
                            </div>

                            <!-- Customer/Supplier Selection -->
                            <div id="customerSection" style="display: none;">
                                <label class="block text-xs font-medium text-gray-600 mb-1">العميل *</label>
                                <select id="customerId" onchange="loadCustomerData()"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 text-sm">
                                    <option value="">اختر العميل</option>
                                </select>
                            </div>

                            <div id="supplierSection" style="display: none;">
                                <label class="block text-xs font-medium text-gray-600 mb-1">المورد *</label>
                                <select id="supplierId" onchange="loadSupplierData()"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 text-sm">
                                    <option value="">اختر المورد</option>
                                </select>
                            </div>

                            <!-- Cash Register -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">الصندوق</label>
                                <select id="cashRegisterId"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 text-sm">
                                    <option value="">اختر الصندوق</option>
                                </select>
                            </div>
                        </div>

                        <!-- Second Row - Return Reason -->
                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-3">سبب الإرجاع *</label>
                            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                                <div class="return-reason-card border border-gray-200 rounded-lg p-3 text-center" onclick="selectReturnReason('damaged')">
                                    <div class="text-2xl mb-1">💔</div>
                                    <div class="text-xs font-medium">تالف</div>
                                </div>
                                <div class="return-reason-card border border-gray-200 rounded-lg p-3 text-center" onclick="selectReturnReason('expired')">
                                    <div class="text-2xl mb-1">⏰</div>
                                    <div class="text-xs font-medium">منتهي الصلاحية</div>
                                </div>
                                <div class="return-reason-card border border-gray-200 rounded-lg p-3 text-center" onclick="selectReturnReason('wrong_item')">
                                    <div class="text-2xl mb-1">❌</div>
                                    <div class="text-xs font-medium">صنف خاطئ</div>
                                </div>
                                <div class="return-reason-card border border-gray-200 rounded-lg p-3 text-center" onclick="selectReturnReason('quality_issue')">
                                    <div class="text-2xl mb-1">⚠️</div>
                                    <div class="text-xs font-medium">مشكلة جودة</div>
                                </div>
                                <div class="return-reason-card border border-gray-200 rounded-lg p-3 text-center" onclick="selectReturnReason('customer_request')">
                                    <div class="text-2xl mb-1">🙋</div>
                                    <div class="text-xs font-medium">طلب العميل</div>
                                </div>
                                <div class="return-reason-card border border-gray-200 rounded-lg p-3 text-center" onclick="selectReturnReason('other')">
                                    <div class="text-2xl mb-1">📝</div>
                                    <div class="text-xs font-medium">أخرى</div>
                                </div>
                            </div>
                            <input type="hidden" id="returnReason" required>
                        </div>
                    </div>

                    <!-- Items Section -->
                    <div class="p-4">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="text-base font-semibold text-gray-900">أصناف المرتجع</h3>
                            <div class="flex space-x-2">
                                <button onclick="addReturnItem()" class="bg-red-600 text-white px-3 py-1.5 rounded hover:bg-red-700 flex items-center text-sm">
                                    <span class="ml-1">➕</span>
                                    إضافة صنف
                                </button>
                            </div>
                        </div>

                        <!-- Items Table -->
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">المنتج</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">الكود</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">الكمية المرتجعة</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">الوحدة</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">السعر</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">الإجمالي</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">حالة الصنف</th>
                                        <th class="px-2 py-2 text-center text-xs font-medium text-gray-500">إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="returnItemsTable" class="bg-white divide-y divide-gray-200">
                                    <!-- Items will be added here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Return Totals & Notes -->
                    <div class="p-4 bg-gray-50 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                            <!-- Notes -->
                            <div class="lg:col-span-2">
                                <label class="block text-xs font-medium text-gray-600 mb-1">ملاحظات الإرجاع</label>
                                <textarea id="returnNotes" rows="3"
                                          class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 text-sm"
                                          placeholder="تفاصيل إضافية حول سبب الإرجاع..."></textarea>
                            </div>

                            <!-- Totals -->
                            <div class="bg-white p-3 rounded border">
                                <div class="space-y-2">
                                    <div class="flex justify-between items-center text-sm">
                                        <span class="text-gray-600">المجموع الفرعي:</span>
                                        <span id="returnSubtotal" class="font-semibold text-gray-900">0</span>
                                    </div>
                                    <div class="flex justify-between items-center text-sm">
                                        <span class="text-gray-600">الضريبة (15%):</span>
                                        <span id="returnTax" class="font-semibold text-gray-900">0</span>
                                    </div>
                                    <div class="flex justify-between items-center pt-2 border-t border-gray-200">
                                        <span class="font-bold text-gray-900">إجمالي المرتجع:</span>
                                        <span id="returnTotal" class="text-lg font-bold text-red-600">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex justify-between items-center mt-4 pt-3 border-t border-gray-200">
                            <!-- Voucher Actions -->
                            <div class="flex space-x-2">
                                <button onclick="createReturnVoucher()" class="bg-red-600 text-white px-3 py-2 rounded hover:bg-red-700 text-sm flex items-center transition-all duration-200 hover:shadow-lg">
                                    <span class="ml-1">💸</span>
                                    إنشاء سند صرف
                                </button>
                                <button onclick="viewReturnVouchers()" class="bg-purple-600 text-white px-3 py-2 rounded hover:bg-purple-700 text-sm flex items-center transition-all duration-200 hover:shadow-lg">
                                    <span class="ml-1">📋</span>
                                    السندات المرتبطة
                                </button>
                            </div>

                            <!-- Main Actions -->
                            <div class="flex space-x-2">
                                <button onclick="saveDraft()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 text-sm">
                                    💾 حفظ مسودة
                                </button>
                                <button onclick="saveAndPrint()" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 text-sm">
                                    🖨️ حفظ وطباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Sample data
        let returnItems = [];
        let selectedReason = '';
        let currentReturnType = '';

        // Toggle return party based on return type
        function toggleReturnParty() {
            const returnType = document.getElementById('returnType').value;
            const customerSection = document.getElementById('customerSection');
            const supplierSection = document.getElementById('supplierSection');

            currentReturnType = returnType;

            // Hide both sections first
            customerSection.style.display = 'none';
            supplierSection.style.display = 'none';

            // Clear previous selections
            document.getElementById('customerId').value = '';
            document.getElementById('supplierId').value = '';

            // Show appropriate section
            if (returnType === 'from_customer') {
                customerSection.style.display = 'block';
                loadCustomers();
                updateVoucherButton('payment'); // سند صرف للعميل
            } else if (returnType === 'to_supplier') {
                supplierSection.style.display = 'block';
                loadSuppliers();
                updateVoucherButton('receipt'); // سند قبض من المورد
            }

            // Update page title and styling
            updatePageStyling(returnType);
        }

        // Update voucher button based on return type
        function updateVoucherButton(voucherType) {
            const voucherButton = document.querySelector('[onclick="createReturnVoucher()"]');
            if (voucherButton) {
                if (voucherType === 'payment') {
                    voucherButton.innerHTML = '<span class="ml-1">💸</span> إنشاء سند صرف';
                    voucherButton.className = voucherButton.className.replace('bg-green-600', 'bg-red-600').replace('hover:bg-green-700', 'hover:bg-red-700');
                } else {
                    voucherButton.innerHTML = '<span class="ml-1">💰</span> إنشاء سند قبض';
                    voucherButton.className = voucherButton.className.replace('bg-red-600', 'bg-green-600').replace('hover:bg-red-700', 'hover:bg-green-700');
                }
            }
        }

        // Update page styling based on return type
        function updatePageStyling(returnType) {
            const title = document.querySelector('h2');
            if (title) {
                if (returnType === 'from_customer') {
                    title.textContent = 'مرتجع مبيعات من عميل';
                } else if (returnType === 'to_supplier') {
                    title.textContent = 'مرتجع مبيعات إلى مورد';
                } else {
                    title.textContent = 'مرتجع مبيعات جديد';
                }
            }
        }

        // Load customers
        function loadCustomers() {
            try {
                const customers = JSON.parse(localStorage.getItem('anwar_bakery_customers') || '[]');
                const customerSelect = document.getElementById('customerId');

                customerSelect.innerHTML = '<option value="">اختر العميل</option>';
                customers.forEach(customer => {
                    const option = document.createElement('option');
                    option.value = customer.id;
                    option.textContent = customer.customerName;
                    customerSelect.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading customers:', error);
            }
        }

        // Load suppliers
        function loadSuppliers() {
            try {
                const suppliers = JSON.parse(localStorage.getItem('anwar_bakery_suppliers') || '[]');
                const supplierSelect = document.getElementById('supplierId');

                supplierSelect.innerHTML = '<option value="">اختر المورد</option>';
                suppliers.forEach(supplier => {
                    const option = document.createElement('option');
                    option.value = supplier.id;
                    option.textContent = supplier.supplierName;
                    supplierSelect.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading suppliers:', error);
            }
        }

        // Load customer data
        function loadCustomerData() {
            const customerId = document.getElementById('customerId').value;
            if (customerId) {
                // Load customer-specific data if needed
                console.log('Loading data for customer:', customerId);
            }
        }

        // Load supplier data
        function loadSupplierData() {
            const supplierId = document.getElementById('supplierId').value;
            if (supplierId) {
                // Load supplier-specific data if needed
                console.log('Loading data for supplier:', supplierId);
            }
        }

        // Select return reason
        function selectReturnReason(reason) {
            // Remove previous selection
            document.querySelectorAll('.return-reason-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Add selection to clicked card
            event.currentTarget.classList.add('selected');

            // Store selected reason
            selectedReason = reason;
            document.getElementById('returnReason').value = reason;
        }

        // Load original invoice
        function loadOriginalInvoice() {
            const invoiceId = document.getElementById('originalInvoiceId').value;
            if (invoiceId) {
                // Here you would load the invoice items
                // For demo, we'll add some sample items
                returnItems = [
                    {
                        id: 1,
                        name: 'خبز أبيض',
                        code: 'BR001',
                        quantity: 10,
                        unit: 'رغيف',
                        price: 2.50,
                        condition: 'expired'
                    }
                ];
                updateReturnItemsTable();
            }
        }

        // Add return item
        function addReturnItem() {
            // In a real application, this would open a modal to select items
            alert('سيتم فتح نافذة لاختيار الأصناف المراد إرجاعها');
        }

        // Update return items table
        function updateReturnItemsTable() {
            const tbody = document.getElementById('returnItemsTable');

            if (returnItems.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center py-4 text-gray-500">لا توجد أصناف مرتجعة</td></tr>';
                updateReturnTotals();
                return;
            }

            tbody.innerHTML = returnItems.map(item => `
                <tr class="invoice-item-row">
                    <td class="px-2 py-2">
                        <div class="flex items-center">
                            <span class="text-lg ml-2">🍞</span>
                            <div>
                                <div class="font-medium text-gray-900">${item.name}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-2 py-2 text-gray-600">${item.code}</td>
                    <td class="px-2 py-2 font-medium">${item.quantity}</td>
                    <td class="px-2 py-2 text-gray-600">${item.unit}</td>
                    <td class="px-2 py-2 font-medium">${item.price.toFixed(2)}</td>
                    <td class="px-2 py-2 font-bold text-red-600">${(item.quantity * item.price).toFixed(2)}</td>
                    <td class="px-2 py-2">
                        <span class="px-2 py-1 text-xs rounded ${getConditionClass(item.condition)}">
                            ${getConditionText(item.condition)}
                        </span>
                    </td>
                    <td class="px-2 py-2 text-center">
                        <button onclick="removeReturnItem(${item.id})" class="text-red-600 hover:text-red-800 text-sm">
                            🗑️
                        </button>
                    </td>
                </tr>
            `).join('');

            updateReturnTotals();
        }

        // Get condition class for styling
        function getConditionClass(condition) {
            const classes = {
                'damaged': 'bg-red-100 text-red-800',
                'expired': 'bg-yellow-100 text-yellow-800',
                'good': 'bg-green-100 text-green-800',
                'defective': 'bg-orange-100 text-orange-800'
            };
            return classes[condition] || 'bg-gray-100 text-gray-800';
        }

        // Get condition text
        function getConditionText(condition) {
            const texts = {
                'damaged': 'تالف',
                'expired': 'منتهي الصلاحية',
                'good': 'سليم',
                'defective': 'معيب'
            };
            return texts[condition] || 'غير محدد';
        }

        // Remove return item
        function removeReturnItem(itemId) {
            returnItems = returnItems.filter(item => item.id !== itemId);
            updateReturnItemsTable();
        }

        // Update return totals
        function updateReturnTotals() {
            const subtotal = returnItems.reduce((sum, item) => sum + (item.quantity * item.price), 0);
            const tax = subtotal * 0.15;
            const total = subtotal + tax;

            document.getElementById('returnSubtotal').textContent = subtotal.toFixed(2);
            document.getElementById('returnTax').textContent = tax.toFixed(2);
            document.getElementById('returnTotal').textContent = total.toFixed(2);
        }

        // Create return voucher
        function createReturnVoucher() {
            if (!currentReturnType) {
                alert('يجب اختيار نوع المرتجع أولاً');
                return;
            }

            let partyId, partyName;
            let voucherType, voucherPage;

            if (currentReturnType === 'from_customer') {
                partyId = document.getElementById('customerId').value;
                if (!partyId) {
                    alert('يجب اختيار العميل');
                    return;
                }
                const customerSelect = document.getElementById('customerId');
                partyName = customerSelect.options[customerSelect.selectedIndex].text;
                voucherType = 'payment'; // سند صرف للعميل
                voucherPage = 'payment-voucher.html';
            } else if (currentReturnType === 'to_supplier') {
                partyId = document.getElementById('supplierId').value;
                if (!partyId) {
                    alert('يجب اختيار المورد');
                    return;
                }
                const supplierSelect = document.getElementById('supplierId');
                partyName = supplierSelect.options[supplierSelect.selectedIndex].text;
                voucherType = 'receipt'; // سند قبض من المورد
                voucherPage = 'receipt-voucher.html';
            }

            const returnData = {
                returnNumber: document.getElementById('returnNumber').value,
                date: document.getElementById('returnDate').value,
                returnType: currentReturnType,
                partyId: partyId,
                partyName: partyName,
                customerId: currentReturnType === 'from_customer' ? partyId : null,
                supplierId: currentReturnType === 'to_supplier' ? partyId : null,
                amount: document.getElementById('returnTotal').textContent,
                reason: selectedReason,
                notes: document.getElementById('returnNotes').value
            };

            // Store return data for voucher creation
            localStorage.setItem('voucherData', JSON.stringify({
                type: voucherType,
                source: 'sales_return',
                data: returnData
            }));

            // Open appropriate voucher page
            window.open(voucherPage, '_blank');
        }

        // View return vouchers
        function viewReturnVouchers() {
            const returnNumber = document.getElementById('returnNumber').value;
            if (!returnNumber) {
                alert('يجب حفظ المرتجع أولاً لعرض السندات المرتبطة');
                return;
            }

            window.open(`vouchers.html?return=${returnNumber}`, '_blank');
        }

        // Save functions
        function saveDraft() {
            if (!selectedReason) {
                alert('يجب اختيار سبب الإرجاع');
                return;
            }
            alert('تم حفظ المسودة بنجاح');
        }

        function saveAndPrint() {
            if (!selectedReason) {
                alert('يجب اختيار سبب الإرجاع');
                return;
            }
            if (returnItems.length === 0) {
                alert('يجب إضافة أصناف للمرتجع');
                return;
            }
            alert('تم حفظ المرتجع وإرساله للطباعة');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Set today's date
            document.getElementById('returnDate').value = new Date().toISOString().split('T')[0];

            // Initialize table
            updateReturnItemsTable();
        });
    </script>

</body>
</html>