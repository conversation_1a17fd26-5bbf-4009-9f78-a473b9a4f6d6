<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصناديق - مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto p-6">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">🏦 إدارة الصناديق</h1>
            
            <!-- Test Message -->
            <div id="testMessage" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                ✅ صفحة الصناديق تعمل بشكل صحيح!
            </div>
            
            <!-- Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="text-blue-600 text-2xl mb-2">💰</div>
                    <div class="text-sm text-gray-600">إجمالي الصناديق</div>
                    <div class="text-xl font-bold" id="totalRegisters">0</div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="text-green-600 text-2xl mb-2">✅</div>
                    <div class="text-sm text-gray-600">الصناديق النشطة</div>
                    <div class="text-xl font-bold" id="activeRegisters">0</div>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg">
                    <div class="text-yellow-600 text-2xl mb-2">🔒</div>
                    <div class="text-sm text-gray-600">الصناديق المغلقة</div>
                    <div class="text-xl font-bold" id="closedRegisters">0</div>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="text-purple-600 text-2xl mb-2">💵</div>
                    <div class="text-sm text-gray-600">إجمالي الأرصدة</div>
                    <div class="text-xl font-bold" id="totalBalance">0 ر.ي</div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex space-x-4 mb-6">
                <button onclick="addSampleData()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    ➕ إضافة بيانات تجريبية
                </button>
                <button onclick="clearData()" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                    🗑️ مسح البيانات
                </button>
                <button onclick="testChartOfAccounts()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    🔗 اختبار شجرة الحسابات
                </button>
                <a href="cash-registers.html" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                    🔄 الصفحة الكاملة
                </a>
            </div>
            
            <!-- Cash Registers List -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h3 class="text-lg font-semibold mb-4">قائمة الصناديق</h3>
                <div id="registersList" class="space-y-2">
                    <div class="text-gray-500 text-center py-4">لا توجد صناديق</div>
                </div>
            </div>
            
            <!-- Debug Info -->
            <div class="mt-6 bg-gray-100 p-4 rounded-lg">
                <h4 class="font-semibold mb-2">معلومات التشخيص:</h4>
                <div id="debugInfo" class="text-sm text-gray-600 space-y-1">
                    <div>جاري التحميل...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple cash registers management
        let cashRegisters = [];
        
        // Initialize
        function init() {
            console.log('🔄 Initializing cash registers page...');
            loadData();
            updateStats();
            displayRegisters();
            showDebugInfo();
        }
        
        // Load data from localStorage
        function loadData() {
            try {
                const stored = localStorage.getItem('anwar_bakery_cash_registers');
                if (stored) {
                    cashRegisters = JSON.parse(stored);
                    console.log('✅ Loaded cash registers:', cashRegisters.length);
                } else {
                    console.log('ℹ️ No stored cash registers found');
                    cashRegisters = [];
                }
            } catch (error) {
                console.error('❌ Error loading cash registers:', error);
                cashRegisters = [];
            }
        }
        
        // Add sample data
        function addSampleData() {
            cashRegisters = [
                {
                    id: 1,
                    code: 'CR001',
                    name: 'الصندوق الرئيسي',
                    type: 'main',
                    branchName: 'الفرع الرئيسي',
                    currentBalance: 50000,
                    status: 'active',
                    createdAt: new Date().toISOString().split('T')[0]
                },
                {
                    id: 2,
                    code: 'CR002',
                    name: 'نقطة البيع 1',
                    type: 'pos',
                    branchName: 'الفرع الرئيسي',
                    currentBalance: 15000,
                    status: 'active',
                    createdAt: new Date().toISOString().split('T')[0]
                },
                {
                    id: 3,
                    code: 'CR003',
                    name: 'صندوق المصروفات الصغيرة',
                    type: 'petty',
                    branchName: 'الفرع الرئيسي',
                    currentBalance: 2000,
                    status: 'active',
                    createdAt: new Date().toISOString().split('T')[0]
                }
            ];
            
            saveData();
            updateStats();
            displayRegisters();
            showMessage('تم إضافة البيانات التجريبية بنجاح!', 'success');
        }
        
        // Clear data
        function clearData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                cashRegisters = [];
                localStorage.removeItem('anwar_bakery_cash_registers');
                updateStats();
                displayRegisters();
                showMessage('تم مسح البيانات بنجاح!', 'success');
            }
        }
        
        // Save data
        function saveData() {
            try {
                localStorage.setItem('anwar_bakery_cash_registers', JSON.stringify(cashRegisters));
                console.log('✅ Data saved successfully');
            } catch (error) {
                console.error('❌ Error saving data:', error);
            }
        }
        
        // Update statistics
        function updateStats() {
            const total = cashRegisters.length;
            const active = cashRegisters.filter(r => r.status === 'active').length;
            const closed = cashRegisters.filter(r => r.status === 'closed').length;
            const totalBalance = cashRegisters.reduce((sum, r) => sum + (r.currentBalance || 0), 0);
            
            document.getElementById('totalRegisters').textContent = total;
            document.getElementById('activeRegisters').textContent = active;
            document.getElementById('closedRegisters').textContent = closed;
            document.getElementById('totalBalance').textContent = totalBalance.toLocaleString() + ' ر.ي';
        }
        
        // Display registers
        function displayRegisters() {
            const container = document.getElementById('registersList');
            
            if (cashRegisters.length === 0) {
                container.innerHTML = '<div class="text-gray-500 text-center py-4">لا توجد صناديق</div>';
                return;
            }
            
            container.innerHTML = cashRegisters.map(register => `
                <div class="bg-white p-4 rounded-lg border">
                    <div class="flex justify-between items-center">
                        <div>
                            <h4 class="font-semibold">${register.name}</h4>
                            <p class="text-sm text-gray-600">${register.code} - ${register.branchName}</p>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-green-600">${(register.currentBalance || 0).toLocaleString()} ر.ي</div>
                            <div class="text-sm text-gray-500">${register.status === 'active' ? 'نشط' : 'مغلق'}</div>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // Test chart of accounts integration
        function testChartOfAccounts() {
            try {
                const accounts = localStorage.getItem('anwar_bakery_accounts');
                if (accounts) {
                    const parsed = JSON.parse(accounts);
                    const cashAccounts = parsed.filter(acc => acc.type === 'assets' && acc.name.includes('صندوق'));
                    showMessage(`تم العثور على ${cashAccounts.length} حساب نقدي في شجرة الحسابات`, 'info');
                } else {
                    showMessage('لم يتم العثور على شجرة الحسابات', 'warning');
                }
            } catch (error) {
                showMessage('خطأ في قراءة شجرة الحسابات: ' + error.message, 'error');
            }
        }
        
        // Show debug info
        function showDebugInfo() {
            const debugInfo = document.getElementById('debugInfo');
            const info = [
                `عدد الصناديق: ${cashRegisters.length}`,
                `localStorage متاح: ${typeof(Storage) !== "undefined" ? 'نعم' : 'لا'}`,
                `البيانات المحفوظة: ${localStorage.getItem('anwar_bakery_cash_registers') ? 'موجودة' : 'غير موجودة'}`,
                `التاريخ: ${new Date().toLocaleDateString('ar-SA')}`,
                `الوقت: ${new Date().toLocaleTimeString('ar-SA')}`
            ];
            
            debugInfo.innerHTML = info.map(item => `<div>• ${item}</div>`).join('');
        }
        
        // Show message
        function showMessage(message, type) {
            const colors = {
                success: 'bg-green-100 border-green-400 text-green-700',
                error: 'bg-red-100 border-red-400 text-red-700',
                warning: 'bg-yellow-100 border-yellow-400 text-yellow-700',
                info: 'bg-blue-100 border-blue-400 text-blue-700'
            };
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `border px-4 py-3 rounded mb-4 ${colors[type] || colors.info}`;
            messageDiv.textContent = message;
            
            const container = document.querySelector('.container > div');
            container.insertBefore(messageDiv, container.children[1]);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
