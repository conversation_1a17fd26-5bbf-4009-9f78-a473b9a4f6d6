// Currency information
const currencyInfo = {
    'SAR': { symbol: 'ر.س', name: 'ريال سعودي' },
    'YER': { symbol: 'ر.ي', name: 'ريال يمني' },
    'USD': { symbol: '$', name: 'دولار أمريكي' },
    'EUR': { symbol: '€', name: 'يورو' },
    'AED': { symbol: 'د.إ', name: 'درهم إماراتي' },
    'KWD': { symbol: 'د.ك', name: 'دينار كويتي' },
    'QAR': { symbol: 'ر.ق', name: 'ريال قطري' }
};

// Check authentication
function checkAuth() {
    const session = localStorage.getItem('anwar_bakery_session') || 
                   sessionStorage.getItem('anwar_bakery_session');
    
    if (!session) {
        window.location.href = 'login.html';
        return null;
    }
    
    return JSON.parse(session);
}

// Load user info
function loadUserInfo() {
    const session = checkAuth();
    if (session) {
        document.getElementById('userFullName').textContent = session.fullName;
        document.getElementById('userRole').textContent = session.role;
    }
}

// Load company info
function loadCompanyInfo() {
    // Use new settings system if available
    if (window.appSettings) {
        const companyName = window.appSettings.get('company', 'companyNameAr');
        if (companyName) {
            document.getElementById('sidebarCompanyName').textContent = companyName;
            document.title = `إعدادات النظام - ${companyName}`;
        }
    } else {
        // Fallback to old system
        const savedData = localStorage.getItem('anwar_bakery_company');
        if (savedData) {
            const companyData = JSON.parse(savedData);
            
            if (companyData.companyNameAr) {
                document.getElementById('sidebarCompanyName').textContent = companyData.companyNameAr;
                document.title = `إعدادات النظام - ${companyData.companyNameAr}`;
            }
        }
    }
}

// Load system settings
function loadSystemSettings() {
    if (window.appSettings) {
        // Load financial settings
        const financial = window.appSettings.get('financial');
        if (financial) {
            document.getElementById('baseCurrency').value = financial.baseCurrency || 'SAR';
            document.getElementById('currencySymbol').value = financial.currencySymbol || 'ر.س';
            document.getElementById('currencyPosition').value = financial.currencyPosition || 'after';
            document.getElementById('decimalPlaces').value = financial.decimalPlaces || 2;
            document.getElementById('taxRate').value = financial.taxRate || 15;
            document.getElementById('enableTax').checked = financial.enableTax !== false;
            document.getElementById('enableDiscount').checked = financial.enableDiscount !== false;
            document.getElementById('enableMultiCurrency').checked = financial.enableMultiCurrency || false;
        }
    }
}

// Update currency settings when currency changes
function updateCurrencySettings() {
    const selectedCurrency = document.getElementById('baseCurrency').value;
    const currency = currencyInfo[selectedCurrency];
    
    if (currency) {
        document.getElementById('currencySymbol').value = currency.symbol;
    }
}

// Save financial settings
function saveFinancialSettings() {
    if (!window.appSettings) {
        showMessage('نظام الإعدادات غير متاح!', 'error');
        return;
    }

    const financialSettings = {
        baseCurrency: document.getElementById('baseCurrency').value,
        currencySymbol: document.getElementById('currencySymbol').value,
        currencyPosition: document.getElementById('currencyPosition').value,
        decimalPlaces: parseInt(document.getElementById('decimalPlaces').value),
        taxRate: parseFloat(document.getElementById('taxRate').value) || 15,
        enableTax: document.getElementById('enableTax').checked,
        enableDiscount: document.getElementById('enableDiscount').checked,
        enableMultiCurrency: document.getElementById('enableMultiCurrency').checked
    };

    // Update settings
    window.appSettings.setCategory('financial', financialSettings);

    showMessage('تم حفظ الإعدادات المالية وتطبيقها على كامل النظام بنجاح!', 'success');
}

// Tab switching
function switchTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active', 'border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500');
    });
    
    // Show selected tab content
    document.getElementById(tabName + 'Tab').classList.add('active');
    
    // Activate selected tab button
    event.target.classList.remove('border-transparent', 'text-gray-500');
    event.target.classList.add('active', 'border-blue-500', 'text-blue-600');
}

// Export settings
function exportSettings() {
    if (window.appSettings) {
        window.appSettings.exportSettings();
        showMessage('تم تصدير الإعدادات بنجاح!', 'success');
    } else {
        showMessage('نظام الإعدادات غير متاح!', 'error');
    }
}

// Import settings
function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (file && window.appSettings) {
            window.appSettings.importSettings(file);
        }
    };
    input.click();
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');
    
    if (sidebar.classList.contains('translate-x-full')) {
        sidebar.classList.remove('translate-x-full');
        overlay.classList.remove('hidden');
    } else {
        sidebar.classList.add('translate-x-full');
        overlay.classList.add('hidden');
    }
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_session');
        sessionStorage.removeItem('anwar_bakery_session');
        window.location.href = 'login.html';
    }
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Close sidebar when clicking overlay
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('mobileOverlay').addEventListener('click', toggleSidebar);
    
    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 1024) {
            document.getElementById('sidebar').classList.remove('translate-x-full');
            document.getElementById('mobileOverlay').classList.add('hidden');
        }
    });
});
