// Activate Advanced Features - تفعيل الميزات المتقدمة
// This script ensures all advanced features are properly activated across the system

/**
 * Main activation function
 * دالة التفعيل الرئيسية
 */
function activateAdvancedFeatures() {
    console.log('🚀 Activating Advanced Features...');
    
    // Check if libraries are loaded
    checkLibrariesStatus();
    
    // Activate features based on current page
    activatePageSpecificFeatures();
    
    // Add global event listeners
    addGlobalEventListeners();
    
    // Initialize advanced UI components
    initializeAdvancedUI();
    
    // Show activation status
    showActivationStatus();
    
    console.log('✅ Advanced Features Activated Successfully!');
}

/**
 * Check if all required libraries are loaded
 * فحص تحميل المكتبات المطلوبة
 */
function checkLibrariesStatus() {
    const libraries = [
        { name: 'Advanced Excel', check: () => typeof window.advancedExcel !== 'undefined' },
        { name: 'Advanced Print', check: () => typeof window.advancedPrint !== 'undefined' },
        { name: 'Excel Utils', check: () => typeof window.excelUtils !== 'undefined' }
    ];
    
    const status = {
        loaded: [],
        missing: []
    };
    
    libraries.forEach(lib => {
        if (lib.check()) {
            status.loaded.push(lib.name);
        } else {
            status.missing.push(lib.name);
        }
    });
    
    console.log('📚 Libraries Status:', status);
    
    // Store status for later use
    window.librariesStatus = status;
    
    return status;
}

/**
 * Activate features specific to current page
 * تفعيل الميزات الخاصة بالصفحة الحالية
 */
function activatePageSpecificFeatures() {
    const currentPage = getCurrentPageName();
    
    switch (currentPage) {
        case 'sales-invoice':
        case 'purchase-invoice':
        case 'sales-return':
        case 'purchase-return':
            activateInvoiceFeatures();
            break;
            
        case 'receipt-voucher':
        case 'payment-voucher':
            activateVoucherFeatures();
            break;
            
        case 'products':
            activateProductsFeatures();
            break;
            
        case 'customers':
            activateCustomersFeatures();
            break;
            
        case 'suppliers':
            activateSuppliersFeatures();
            break;
            
        case 'employees':
            activateEmployeesFeatures();
            break;
            
        case 'reports':
            activateReportsFeatures();
            break;
            
        default:
            activateGeneralFeatures();
    }
}

/**
 * Get current page name from URL
 * الحصول على اسم الصفحة الحالية
 */
function getCurrentPageName() {
    const path = window.location.pathname;
    const filename = path.split('/').pop().split('.')[0];
    return filename;
}

/**
 * Activate invoice-specific features
 * تفعيل ميزات الفواتير
 */
function activateInvoiceFeatures() {
    // Ensure invoice functions are available
    if (typeof exportToExcel === 'undefined') {
        window.exportToExcel = function() {
            const invoiceData = collectInvoiceData();
            if (typeof window.advancedExcel !== 'undefined') {
                window.advancedExcel.exportInvoice(invoiceData);
            } else if (typeof window.excelUtils !== 'undefined') {
                window.excelUtils.exportToExcel([invoiceData], 'فاتورة', 'الفاتورة');
            }
        };
    }
    
    if (typeof printInvoice === 'undefined') {
        window.printInvoice = function() {
            const invoiceData = collectInvoiceData();
            if (typeof window.advancedPrint !== 'undefined') {
                window.advancedPrint.printInvoice(invoiceData);
            } else {
                window.print();
            }
        };
    }
    
    console.log('📄 Invoice features activated');
}

/**
 * Activate voucher-specific features
 * تفعيل ميزات السندات
 */
function activateVoucherFeatures() {
    // Ensure voucher functions are available
    if (typeof exportToExcel === 'undefined') {
        window.exportToExcel = function() {
            const voucherData = collectVoucherData();
            if (typeof window.advancedExcel !== 'undefined') {
                window.advancedExcel.exportVoucher(voucherData);
            } else if (typeof window.excelUtils !== 'undefined') {
                window.excelUtils.exportToExcel([voucherData], 'سند', 'السند');
            }
        };
    }
    
    console.log('🧾 Voucher features activated');
}

/**
 * Activate products-specific features
 * تفعيل ميزات المنتجات
 */
function activateProductsFeatures() {
    // Products features are already implemented in products.js
    console.log('📦 Products features activated');
}

/**
 * Activate customers-specific features
 * تفعيل ميزات العملاء
 */
function activateCustomersFeatures() {
    // Customers features are implemented in global-advanced-features.js
    console.log('👤 Customers features activated');
}

/**
 * Activate suppliers-specific features
 * تفعيل ميزات الموردين
 */
function activateSuppliersFeatures() {
    // Suppliers features are implemented in global-advanced-features.js
    console.log('🚚 Suppliers features activated');
}

/**
 * Activate employees-specific features
 * تفعيل ميزات الموظفين
 */
function activateEmployeesFeatures() {
    // Employees features are implemented in global-advanced-features.js
    console.log('👨‍💼 Employees features activated');
}

/**
 * Activate reports-specific features
 * تفعيل ميزات التقارير
 */
function activateReportsFeatures() {
    // Reports features are already implemented in reports.js
    console.log('📈 Reports features activated');
}

/**
 * Activate general features for all pages
 * تفعيل الميزات العامة لجميع الصفحات
 */
function activateGeneralFeatures() {
    // Add general export/print capabilities
    if (typeof window.generalExport === 'undefined') {
        window.generalExport = function() {
            const pageData = collectPageData();
            if (typeof window.excelUtils !== 'undefined') {
                window.excelUtils.exportToExcel(pageData, 'بيانات_الصفحة', 'البيانات');
            }
        };
    }
    
    if (typeof window.generalPrint === 'undefined') {
        window.generalPrint = function() {
            window.print();
        };
    }
    
    console.log('🌐 General features activated');
}

/**
 * Add global event listeners
 * إضافة مستمعي الأحداث العامة
 */
function addGlobalEventListeners() {
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+Shift+E for Excel export
        if (e.ctrlKey && e.shiftKey && e.key === 'E') {
            e.preventDefault();
            if (typeof exportToExcel === 'function') {
                exportToExcel();
            } else if (typeof window.generalExport === 'function') {
                window.generalExport();
            }
        }
        
        // Ctrl+Shift+P for advanced print
        if (e.ctrlKey && e.shiftKey && e.key === 'P') {
            e.preventDefault();
            if (typeof printInvoice === 'function') {
                printInvoice();
            } else if (typeof window.generalPrint === 'function') {
                window.generalPrint();
            }
        }
    });
    
    console.log('⌨️ Global event listeners added');
}

/**
 * Initialize advanced UI components
 * تهيئة مكونات واجهة المستخدم المتقدمة
 */
function initializeAdvancedUI() {
    // Add advanced styles
    addAdvancedStyles();
    
    // Add status indicator
    addStatusIndicator();
    
    console.log('🎨 Advanced UI components initialized');
}

/**
 * Add advanced CSS styles
 * إضافة أنماط CSS متقدمة
 */
function addAdvancedStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .advanced-features-active {
            position: relative;
        }
        
        .advanced-features-active::after {
            content: '🚀';
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 9999;
            background: rgba(34, 197, 94, 0.9);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
        }
        
        .advanced-button {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .advanced-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .advanced-button:active {
            transform: translateY(0);
        }
        
        .feature-status {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 9998;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .feature-status.show {
            opacity: 1;
        }
    `;
    document.head.appendChild(style);
}

/**
 * Add status indicator
 * إضافة مؤشر الحالة
 */
function addStatusIndicator() {
    document.body.classList.add('advanced-features-active');
}

/**
 * Show activation status
 * عرض حالة التفعيل
 */
function showActivationStatus() {
    const status = window.librariesStatus;
    const statusDiv = document.createElement('div');
    statusDiv.className = 'feature-status';
    statusDiv.innerHTML = `
        <div>🚀 الميزات المتقدمة نشطة</div>
        <div style="font-size: 10px; margin-top: 2px;">
            ✅ ${status.loaded.length} مكتبة محملة
            ${status.missing.length > 0 ? `⚠️ ${status.missing.length} مكتبة مفقودة` : ''}
        </div>
    `;
    
    document.body.appendChild(statusDiv);
    
    // Show status for 3 seconds
    setTimeout(() => {
        statusDiv.classList.add('show');
        setTimeout(() => {
            statusDiv.classList.remove('show');
            setTimeout(() => {
                if (statusDiv.parentNode) {
                    statusDiv.parentNode.removeChild(statusDiv);
                }
            }, 300);
        }, 3000);
    }, 100);
}

/**
 * Collect page data for general export
 * جمع بيانات الصفحة للتصدير العام
 */
function collectPageData() {
    // Try to collect data from common table structures
    const tables = document.querySelectorAll('table');
    const data = [];
    
    tables.forEach(table => {
        const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim());
        const rows = Array.from(table.querySelectorAll('tbody tr'));
        
        rows.forEach(row => {
            const cells = Array.from(row.querySelectorAll('td'));
            const rowData = {};
            
            cells.forEach((cell, index) => {
                if (headers[index]) {
                    rowData[headers[index]] = cell.textContent.trim();
                }
            });
            
            if (Object.keys(rowData).length > 0) {
                data.push(rowData);
            }
        });
    });
    
    return data.length > 0 ? data : [{ 'البيانات': 'لا توجد بيانات متاحة للتصدير' }];
}

// Auto-activate when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for other scripts to load
    setTimeout(activateAdvancedFeatures, 1000);
});

// Also activate when window loads (fallback)
window.addEventListener('load', function() {
    if (!document.body.classList.contains('advanced-features-active')) {
        setTimeout(activateAdvancedFeatures, 500);
    }
});

// Export for manual activation
window.activateAdvancedFeatures = activateAdvancedFeatures;
