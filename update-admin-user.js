// Update Admin User Script - تحديث بيانات المستخدم الإداري
// This script updates the first user to have admin credentials

(function() {
    'use strict';
    
    console.log('🔧 تحديث بيانات المستخدم الإداري...');
    
    // Get existing users or create new array
    let users = [];
    const existingUsers = localStorage.getItem('anwar_bakery_users');
    
    if (existingUsers) {
        users = JSON.parse(existingUsers);
        console.log('📋 تم العثور على مستخدمين موجودين:', users.length);
    }
    
    // Create or update admin user
    const adminUser = {
        id: 1,
        username: 'admin',
        password: 'admin123', // In production, this should be hashed
        fullName: 'مدير النظام',
        email: '<EMAIL>',
        role: 'admin',
        permissions: {
            dashboard: true,
            products: true,
            customers: true,
            suppliers: true,
            employees: true,
            invoices: true,
            vouchers: true,
            reports: true,
            settings: true,
            users: true,
            branches: true,
            cashRegisters: true,
            chartOfAccounts: true
        },
        isActive: true,
        branchId: 1,
        branchName: 'الفرع الرئيسي',
        createdAt: new Date().toISOString().split('T')[0],
        lastLogin: null,
        loginAttempts: 0,
        isLocked: false
    };
    
    // Check if admin user exists
    const adminIndex = users.findIndex(user => user.id === 1 || user.username === 'admin');
    
    if (adminIndex !== -1) {
        // Update existing admin user
        users[adminIndex] = {
            ...users[adminIndex],
            username: 'admin',
            password: 'admin123',
            fullName: 'مدير النظام',
            role: 'admin',
            permissions: adminUser.permissions,
            isActive: true,
            loginAttempts: 0,
            isLocked: false
        };
        console.log('✅ تم تحديث المستخدم الإداري الموجود');
    } else {
        // Add new admin user at the beginning
        users.unshift(adminUser);
        console.log('✅ تم إنشاء مستخدم إداري جديد');
    }
    
    // Add additional sample users if needed
    if (users.length === 1) {
        const sampleUsers = [
            {
                id: 2,
                username: 'cashier1',
                password: 'cashier123',
                fullName: 'أحمد محمد - أمين صندوق',
                email: '<EMAIL>',
                role: 'cashier',
                permissions: {
                    dashboard: true,
                    products: true,
                    customers: true,
                    invoices: true,
                    vouchers: true,
                    reports: false,
                    settings: false,
                    users: false,
                    branches: false,
                    cashRegisters: true,
                    chartOfAccounts: false
                },
                isActive: true,
                branchId: 1,
                branchName: 'الفرع الرئيسي',
                createdAt: new Date().toISOString().split('T')[0],
                lastLogin: null,
                loginAttempts: 0,
                isLocked: false
            },
            {
                id: 3,
                username: 'manager1',
                password: 'manager123',
                fullName: 'سارة أحمد - مدير فرع',
                email: '<EMAIL>',
                role: 'manager',
                permissions: {
                    dashboard: true,
                    products: true,
                    customers: true,
                    suppliers: true,
                    employees: true,
                    invoices: true,
                    vouchers: true,
                    reports: true,
                    settings: false,
                    users: false,
                    branches: false,
                    cashRegisters: true,
                    chartOfAccounts: true
                },
                isActive: true,
                branchId: 2,
                branchName: 'فرع الملك فهد',
                createdAt: new Date().toISOString().split('T')[0],
                lastLogin: null,
                loginAttempts: 0,
                isLocked: false
            }
        ];
        
        users.push(...sampleUsers);
        console.log('✅ تم إضافة مستخدمين نموذجيين');
    }
    
    // Save updated users
    localStorage.setItem('anwar_bakery_users', JSON.stringify(users));
    
    // Also ensure company data exists
    const companyData = localStorage.getItem('anwar_bakery_company');
    if (!companyData) {
        const defaultCompany = {
            companyName: 'مخبز أنوار الحي',
            companyNameEn: 'Anwar Al-Hay Bakery',
            taxNumber: '*********',
            commercialRegister: 'CR*********',
            phone: '**********',
            email: '<EMAIL>',
            address: 'الرياض، المملكة العربية السعودية',
            city: 'الرياض',
            postalCode: '12345',
            currency: 'ر.س',
            taxRate: 15,
            logo: '',
            website: 'www.anwarbakery.com',
            establishedDate: '2020-01-01',
            isActive: true
        };
        
        localStorage.setItem('anwar_bakery_company', JSON.stringify(defaultCompany));
        console.log('✅ تم إنشاء بيانات الشركة الافتراضية');
    }
    
    // Ensure branches exist
    const branchesData = localStorage.getItem('anwar_bakery_branches');
    if (!branchesData) {
        const defaultBranches = [
            {
                id: 1,
                branchName: 'الفرع الرئيسي',
                branchCode: 'BR001',
                address: 'الرياض، حي النخيل',
                phone: '**********',
                manager: 'مدير النظام',
                isActive: true,
                openingDate: '2020-01-01'
            },
            {
                id: 2,
                branchName: 'فرع الملك فهد',
                branchCode: 'BR002',
                address: 'الرياض، طريق الملك فهد',
                phone: '0112345679',
                manager: 'سارة أحمد',
                isActive: true,
                openingDate: '2021-06-01'
            },
            {
                id: 3,
                branchName: 'فرع العليا',
                branchCode: 'BR003',
                address: 'الرياض، حي العليا',
                phone: '0112345680',
                manager: 'محمد علي',
                isActive: true,
                openingDate: '2022-03-01'
            }
        ];
        
        localStorage.setItem('anwar_bakery_branches', JSON.stringify(defaultBranches));
        console.log('✅ تم إنشاء بيانات الفروع الافتراضية');
    }
    
    console.log('🎉 تم تحديث بيانات النظام بنجاح!');
    console.log('📋 بيانات تسجيل الدخول:');
    console.log('👤 اسم المستخدم: admin');
    console.log('🔑 كلمة المرور: admin123');
    
    // Display success message
    if (typeof showMessage === 'function') {
        showMessage('✅ تم تحديث بيانات المستخدم الإداري بنجاح!', 'success');
    } else {
        alert('✅ تم تحديث بيانات المستخدم الإداري بنجاح!\n\nاسم المستخدم: admin\nكلمة المرور: admin123');
    }
    
})();

// Function to reset all data (use with caution)
function resetAllData() {
    if (confirm('⚠️ هل أنت متأكد من إعادة تعيين جميع البيانات؟ سيتم حذف جميع البيانات الموجودة!')) {
        const keys = Object.keys(localStorage).filter(key => key.startsWith('anwar_bakery_'));
        keys.forEach(key => localStorage.removeItem(key));
        
        // Re-run the initialization
        setTimeout(() => {
            location.reload();
        }, 1000);
        
        alert('✅ تم إعادة تعيين جميع البيانات. سيتم إعادة تحميل الصفحة...');
    }
}

// Function to backup all data
function backupAllData() {
    const backup = {};
    const keys = Object.keys(localStorage).filter(key => key.startsWith('anwar_bakery_'));
    
    keys.forEach(key => {
        backup[key] = localStorage.getItem(key);
    });
    
    const dataStr = JSON.stringify(backup, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `anwar_bakery_backup_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    console.log('✅ تم إنشاء نسخة احتياطية من البيانات');
}

// Function to restore data from backup
function restoreFromBackup(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const backup = JSON.parse(e.target.result);
            
            Object.keys(backup).forEach(key => {
                localStorage.setItem(key, backup[key]);
            });
            
            alert('✅ تم استعادة البيانات بنجاح! سيتم إعادة تحميل الصفحة...');
            setTimeout(() => {
                location.reload();
            }, 1000);
        } catch (error) {
            alert('❌ خطأ في استعادة البيانات: ' + error.message);
        }
    };
    reader.readAsText(file);
}

// Export functions for global use
window.resetAllData = resetAllData;
window.backupAllData = backupAllData;
window.restoreFromBackup = restoreFromBackup;
