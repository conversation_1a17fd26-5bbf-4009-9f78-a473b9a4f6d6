<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بيانات المنشأة - مخبز أنور</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="app-settings.js"></script>
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .logo-preview {
            width: 120px;
            height: 120px;
            border: 2px dashed #d1d5db;
            display: flex;
            align-items: center;
            justify-content: center;
            background-size: cover;
            background-position: center;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Mobile menu overlay -->
    <div id="mobileOverlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 lg:hidden hidden"></div>

    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform translate-x-full lg:translate-x-0 lg:static lg:inset-0 sidebar-transition">
            <div class="h-16 px-4 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between">
                <h1 id="sidebarCompanyName" class="text-white text-lg font-bold">مخبز أنور</h1>
                <button onclick="toggleSidebar()" class="lg:hidden text-white">
                    <span class="text-xl">✕</span>
                </button>
            </div>

            <nav class="mt-5 px-2 space-y-1 h-full overflow-y-auto pb-20">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    لوحة التحكم
                </a>
                <a href="users-management.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👥</span>
                    إدارة المستخدمين
                </a>
                <a href="company-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">
                    <span class="ml-3">🏢</span>
                    بيانات المنشأة
                </a>
                <a href="system-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">⚙️</span>
                    إعدادات النظام
                </a>
                <a href="system-admin.html" class="flex items-center px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 hover:text-red-700 rounded-md border border-red-200">
                    <span class="ml-3">🔧</span>
                    إدارة النظام المتقدمة
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة المنتجات
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🚚</span>
                    الموردين
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="inventory.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📊</span>
                    المخزون
                </a>
                <a href="reports.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📈</span>
                    التقارير
                </a>

                <!-- User Info & Logout -->
                <div class="absolute bottom-4 left-2 right-2">
                    <div class="bg-gray-50 rounded-lg p-3 mb-2">
                        <p id="userFullName" class="text-sm font-medium text-gray-900">مدير النظام</p>
                        <p id="userRole" class="text-xs text-gray-500">admin</p>
                    </div>
                    <button onclick="logout()" class="w-full text-right px-3 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50 flex items-center">
                        <span class="ml-3">🚪</span>
                        تسجيل الخروج
                    </button>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 overflow-hidden">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <button onclick="toggleSidebar()" class="lg:hidden text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">☰</span>
                            </button>
                            <span class="text-lg font-semibold text-gray-900">بيانات المنشأة</span>
                        </div>
                        <div class="flex items-center">
                            <button onclick="saveCompanyData()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center">
                                <span class="ml-2">💾</span>
                                حفظ البيانات
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2 flex items-center">
                        <span class="ml-3">🏢</span>
                        إعدادات بيانات المنشأة
                    </h1>
                    <p class="text-gray-600">
                        قم بتحديث معلومات المنشأة الأساسية التي ستظهر في جميع أنحاء النظام
                    </p>
                </div>

                <!-- Success/Error Messages -->
                <div id="messageContainer" class="mb-6"></div>

                <!-- Company Settings Form -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                    <form id="companyForm" class="p-6 space-y-8">

                        <!-- Basic Information -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <span class="ml-2">📝</span>
                                المعلومات الأساسية
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم المنشأة (عربي) *</label>
                                    <input type="text" id="companyNameAr" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="مخبز أنور">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم المنشأة (إنجليزي)</label>
                                    <input type="text" id="companyNameEn"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="Anwar Bakery" dir="ltr">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">الشعار أو الوصف</label>
                                    <input type="text" id="companySlogan"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="نظام إدارة المخبز المتكامل">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">نوع النشاط</label>
                                    <select id="businessType"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="bakery">مخبز</option>
                                        <option value="pastry">حلويات</option>
                                        <option value="cafe">مقهى</option>
                                        <option value="restaurant">مطعم</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <span class="ml-2">📞</span>
                                معلومات التواصل
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف *</label>
                                    <input type="tel" id="phone" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="+966501234567" dir="ltr">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                                    <input type="email" id="email"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="<EMAIL>" dir="ltr">
                                </div>
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">العنوان *</label>
                                    <textarea id="address" required rows="3"
                                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                              placeholder="الرياض، المملكة العربية السعودية"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Business Information -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <span class="ml-2">🏪</span>
                                المعلومات التجارية
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">السجل التجاري</label>
                                    <input type="text" id="commercialRegister"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="1234567890" dir="ltr">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">الرقم الضريبي</label>
                                    <input type="text" id="taxNumber"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="123456789012345" dir="ltr">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">ساعات العمل</label>
                                    <input type="text" id="workingHours"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="6:00 ص - 12:00 م">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">أيام العمل</label>
                                    <input type="text" id="workingDays"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           placeholder="السبت - الخميس">
                                </div>
                            </div>
                        </div>

                        <!-- Logo Upload -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <span class="ml-2">🖼️</span>
                                شعار المنشأة
                            </h3>
                            <div class="flex items-start space-x-6">
                                <div>
                                    <div id="logoPreview" class="logo-preview rounded-lg mb-4">
                                        <span class="text-4xl text-gray-400">🏪</span>
                                    </div>
                                    <input type="file" id="logoFile" accept="image/*" class="hidden" onchange="handleLogoUpload(event)">
                                    <button type="button" onclick="document.getElementById('logoFile').click()"
                                            class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 text-sm">
                                        اختيار شعار
                                    </button>
                                    <button type="button" onclick="removeLogo()"
                                            class="mr-2 bg-red-100 text-red-700 px-4 py-2 rounded-lg hover:bg-red-200 text-sm">
                                        إزالة
                                    </button>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm text-gray-600 mb-2">متطلبات الشعار:</p>
                                    <ul class="text-xs text-gray-500 space-y-1">
                                        <li>• الحد الأقصى: 2 ميجابايت</li>
                                        <li>• الصيغ المدعومة: JPG, PNG, GIF</li>
                                        <li>• الحجم المفضل: 200x200 بكسل</li>
                                        <li>• سيتم تغيير حجم الصورة تلقائياً</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Settings -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <span class="ml-2">📝</span>
                                ملاحظات إضافية
                            </h3>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات إضافية</label>
                                <textarea id="notes" rows="3"
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                          placeholder="أي ملاحظات أو معلومات إضافية عن المنشأة..."></textarea>
                            </div>
                        </div>

                    </form>
                </div>

                <!-- Action Buttons -->
                <div class="mt-8 flex justify-end space-x-4">
                    <button onclick="resetForm()" class="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 font-medium">
                        إعادة تعيين
                    </button>
                    <button onclick="saveCompanyData()" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-medium flex items-center">
                        <span class="ml-2">💾</span>
                        حفظ البيانات
                    </button>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Default company data
        const defaultCompanyData = {
            companyNameAr: 'مخبز أنور',
            companyNameEn: 'Anwar Bakery',
            companySlogan: 'نظام إدارة المخبز المتكامل',
            businessType: 'bakery',
            phone: '+966501234567',
            email: '<EMAIL>',
            address: 'الرياض، المملكة العربية السعودية',
            commercialRegister: '',
            taxNumber: '',
            workingHours: '6:00 ص - 12:00 م',
            workingDays: 'السبت - الخميس',
            notes: '',
            logo: null
        };

        // Check authentication
        function checkAuth() {
            const session = localStorage.getItem('anwar_bakery_session') ||
                           sessionStorage.getItem('anwar_bakery_session');

            if (!session) {
                window.location.href = 'login.html';
                return null;
            }

            return JSON.parse(session);
        }

        // Load user info
        function loadUserInfo() {
            const session = checkAuth();
            if (session) {
                document.getElementById('userFullName').textContent = session.fullName;
                document.getElementById('userRole').textContent = session.role;
            }
        }

        // Toggle sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobileOverlay');

            if (sidebar.classList.contains('translate-x-full')) {
                sidebar.classList.remove('translate-x-full');
                overlay.classList.remove('hidden');
            } else {
                sidebar.classList.add('translate-x-full');
                overlay.classList.add('hidden');
            }
        }

        // Logout function
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('anwar_bakery_session');
                sessionStorage.removeItem('anwar_bakery_session');
                window.location.href = 'login.html';
            }
        }

        // Load company data
        function loadCompanyData() {
            const savedData = localStorage.getItem('anwar_bakery_company');
            const companyData = savedData ? JSON.parse(savedData) : defaultCompanyData;

            // Fill form fields
            Object.keys(companyData).forEach(key => {
                const element = document.getElementById(key);
                if (element && key !== 'logo') {
                    element.value = companyData[key];
                }
            });

            // Load logo if exists
            if (companyData.logo) {
                const logoPreview = document.getElementById('logoPreview');
                logoPreview.style.backgroundImage = `url(${companyData.logo})`;
                logoPreview.innerHTML = '';
            }

            // Update sidebar company name
            updateSidebarCompanyName(companyData.companyNameAr);
        }

        // Update sidebar company name
        function updateSidebarCompanyName(name) {
            document.getElementById('sidebarCompanyName').textContent = name || 'مخبز أنور';
        }

        // Handle logo upload
        function handleLogoUpload(event) {
            const file = event.target.files[0];
            if (file) {
                if (file.size > 2 * 1024 * 1024) {
                    showMessage('حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت.', 'error');
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    const logoPreview = document.getElementById('logoPreview');
                    logoPreview.style.backgroundImage = `url(${e.target.result})`;
                    logoPreview.innerHTML = '';
                };
                reader.readAsDataURL(file);
            }
        }

        // Remove logo
        function removeLogo() {
            const logoPreview = document.getElementById('logoPreview');
            logoPreview.style.backgroundImage = '';
            logoPreview.innerHTML = '<span class="text-4xl text-gray-400">🏪</span>';
            document.getElementById('logoFile').value = '';
        }

        // Save company data
        function saveCompanyData() {
            const form = document.getElementById('companyForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const companyData = {};

            // Get form data
            const formElements = form.querySelectorAll('input, select, textarea');
            formElements.forEach(element => {
                if (element.id && element.id !== 'logoFile') {
                    companyData[element.id] = element.value;
                }
            });

            // Get logo data
            const logoPreview = document.getElementById('logoPreview');
            if (logoPreview.style.backgroundImage) {
                const logoUrl = logoPreview.style.backgroundImage.slice(5, -2);
                if (logoUrl && logoUrl !== '') {
                    companyData.logo = logoUrl;
                }
            }

            // Save to localStorage (old format for compatibility)
            localStorage.setItem('anwar_bakery_company', JSON.stringify(companyData));

            // Update new settings system
            if (window.appSettings) {
                // Update company settings
                window.appSettings.setCategory('company', {
                    companyNameAr: companyData.companyNameAr,
                    companyNameEn: companyData.companyNameEn,
                    logo: companyData.logo,
                    phone: companyData.phone,
                    email: companyData.email,
                    address: companyData.address,
                    city: companyData.city || 'الرياض',
                    country: 'السعودية',
                    taxNumber: companyData.taxNumber,
                    commercialRegister: companyData.commercialRegister
                });

                // Financial and system settings will be managed separately
                // This keeps company data focused on business information only
            }

            // Update sidebar
            updateSidebarCompanyName(companyData.companyNameAr);

            // Show success message
            showMessage('تم حفظ بيانات المنشأة وتطبيقها على كامل النظام بنجاح!', 'success');

            // Trigger update event for other pages
            window.dispatchEvent(new CustomEvent('companyDataUpdated', { detail: companyData }));
        }

        // Get currency info
        function getCurrencyInfo(currencyCode) {
            const currencies = {
                'SAR': { symbol: 'ر.س', name: 'ريال سعودي' },
                'YER': { symbol: 'ر.ي', name: 'ريال يمني' },
                'USD': { symbol: '$', name: 'دولار أمريكي' },
                'EUR': { symbol: '€', name: 'يورو' },
                'AED': { symbol: 'د.إ', name: 'درهم إماراتي' },
                'KWD': { symbol: 'د.ك', name: 'دينار كويتي' },
                'QAR': { symbol: 'ر.ق', name: 'ريال قطري' }
            };
            return currencies[currencyCode] || currencies['SAR'];
        }

        // Reset form
        function resetForm() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
                loadCompanyData();
                showMessage('تم إعادة تعيين البيانات.', 'info');
            }
        }

        // Show message
        function showMessage(message, type) {
            const container = document.getElementById('messageContainer');
            const alertClass = {
                'success': 'bg-green-50 border-green-200 text-green-700',
                'error': 'bg-red-50 border-red-200 text-red-700',
                'info': 'bg-blue-50 border-blue-200 text-blue-700'
            };

            container.innerHTML = `
                <div class="border rounded-lg p-4 ${alertClass[type]}">
                    <div class="flex items-center">
                        <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                        ${message}
                    </div>
                </div>
            `;

            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadUserInfo();
            loadCompanyData();
        });

        // Close sidebar when clicking overlay
        document.getElementById('mobileOverlay').addEventListener('click', toggleSidebar);

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 1024) {
                document.getElementById('sidebar').classList.remove('translate-x-full');
                document.getElementById('mobileOverlay').classList.add('hidden');
            }
        });
    </script>
</body>
</html>
