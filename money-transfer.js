// Money Transfer Management System
let transfers = [];
let cashRegisters = [];
let banks = [];

// Check authentication
function checkAuth() {
    const session = localStorage.getItem('anwar_bakery_session') ||
                   sessionStorage.getItem('anwar_bakery_session');

    if (!session) {
        window.location.href = 'login.html';
        return null;
    }

    return JSON.parse(session);
}

// Load user info
function loadUserInfo() {
    const session = checkAuth();
    if (session) {
        const userFullNameElement = document.getElementById('userFullName');
        const userRoleElement = document.getElementById('userRole');
        
        if (userFullNameElement) userFullNameElement.textContent = session.fullName;
        if (userRoleElement) userRoleElement.textContent = session.role;
    }
}

// Load company info
function loadCompanyInfo() {
    const savedData = localStorage.getItem('anwar_bakery_company');
    if (savedData) {
        const companyData = JSON.parse(savedData);
        const sidebarElement = document.getElementById('sidebarCompanyName');
        if (sidebarElement && companyData.companyNameAr) {
            sidebarElement.textContent = companyData.companyNameAr;
        }
    }
}

// Load cash registers
function loadCashRegisters() {
    const savedData = localStorage.getItem('anwar_bakery_cash_registers');
    if (savedData) {
        cashRegisters = JSON.parse(savedData);
    } else {
        // Default cash registers
        cashRegisters = [
            { id: 1, name: 'الصندوق الرئيسي', balance: 5000.00, isActive: true },
            { id: 2, name: 'صندوق الفرع الثاني', balance: 2500.00, isActive: true },
            { id: 3, name: 'صندوق المبيعات', balance: 1800.00, isActive: true }
        ];
        localStorage.setItem('anwar_bakery_cash_registers', JSON.stringify(cashRegisters));
    }
    renderCashRegistersOverview();
}

// Load banks
function loadBanks() {
    const savedData = localStorage.getItem('anwar_bakery_banks');
    if (savedData) {
        banks = JSON.parse(savedData);
    } else {
        // Default banks
        banks = [
            { id: 1, bankName: 'البنك الأهلي السعودي', accountNumber: '*********', balance: 50000.00, isActive: true },
            { id: 2, bankName: 'بنك الراجحي', accountNumber: '*********', balance: 30000.00, isActive: true }
        ];
        localStorage.setItem('anwar_bakery_banks', JSON.stringify(banks));
    }
}

// Load transfers
function loadTransfers() {
    const savedData = localStorage.getItem('anwar_bakery_money_transfers');
    if (savedData) {
        transfers = JSON.parse(savedData);
    } else {
        // Sample transfers
        transfers = [
            {
                id: 1,
                transferNumber: 'TRF-001',
                date: new Date().toISOString().split('T')[0],
                type: 'cash_to_cash',
                fromType: 'cash',
                fromId: 1,
                fromName: 'الصندوق الرئيسي',
                toType: 'cash',
                toId: 2,
                toName: 'صندوق الفرع الثاني',
                amount: 1000.00,
                notes: 'تحويل لتعزيز رصيد الفرع',
                status: 'completed',
                createdBy: 'مدير النظام',
                createdAt: new Date().toISOString()
            }
        ];
        localStorage.setItem('anwar_bakery_money_transfers', JSON.stringify(transfers));
    }
    renderRecentTransfers();
}

// Render cash registers overview
function renderCashRegistersOverview() {
    const container = document.getElementById('cashRegistersOverview');
    if (!container) return;

    const currencySymbol = getCurrencySymbol();
    
    container.innerHTML = cashRegisters.filter(cr => cr.isActive).map(register => `
        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="font-medium text-gray-900">${register.name}</h4>
                    <p class="text-2xl font-bold text-green-600">${register.balance.toFixed(2)} ${currencySymbol}</p>
                </div>
                <div class="text-2xl">💰</div>
            </div>
        </div>
    `).join('');
}

// Render recent transfers
function renderRecentTransfers() {
    const tableBody = document.getElementById('recentTransfersTable');
    if (!tableBody) return;

    const recentTransfers = transfers.slice(-10).reverse();
    const currencySymbol = getCurrencySymbol();

    if (recentTransfers.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="px-6 py-12 text-center text-gray-500">
                    <div class="flex flex-col items-center">
                        <span class="text-4xl mb-4">🔄</span>
                        <p class="text-lg mb-2">لا توجد تحويلات حتى الآن</p>
                        <p class="text-sm">ابدأ بإنشاء تحويل جديد</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = recentTransfers.map(transfer => `
        <tr class="hover:bg-gray-50">
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">${transfer.transferNumber}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">${new Date(transfer.date).toLocaleDateString('ar-SA')}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">${transfer.fromName}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">${transfer.toName}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-blue-600">${transfer.amount.toFixed(2)} ${currencySymbol}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <span class="px-2 py-1 text-xs rounded-full ${getTransferTypeBadge(transfer.type)}">
                    ${getTransferTypeText(transfer.type)}
                </span>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <span class="px-2 py-1 text-xs rounded-full ${getStatusBadge(transfer.status)}">
                    ${getStatusText(transfer.status)}
                </span>
            </td>
        </tr>
    `).join('');
}

// Get transfer type badge class
function getTransferTypeBadge(type) {
    const badges = {
        'cash_to_cash': 'bg-green-100 text-green-800',
        'cash_to_bank': 'bg-blue-100 text-blue-800',
        'bank_to_cash': 'bg-purple-100 text-purple-800'
    };
    return badges[type] || 'bg-gray-100 text-gray-800';
}

// Get transfer type text
function getTransferTypeText(type) {
    const texts = {
        'cash_to_cash': 'صندوق لصندوق',
        'cash_to_bank': 'صندوق لبنك',
        'bank_to_cash': 'بنك لصندوق'
    };
    return texts[type] || 'غير محدد';
}

// Get status badge class
function getStatusBadge(status) {
    const badges = {
        'completed': 'bg-green-100 text-green-800',
        'pending': 'bg-yellow-100 text-yellow-800',
        'cancelled': 'bg-red-100 text-red-800'
    };
    return badges[status] || 'bg-gray-100 text-gray-800';
}

// Get status text
function getStatusText(status) {
    const texts = {
        'completed': 'مكتمل',
        'pending': 'معلق',
        'cancelled': 'ملغي'
    };
    return texts[status] || 'غير محدد';
}

// Open transfer modal
function openTransferModal() {
    document.getElementById('transferModal').classList.add('active');
    document.getElementById('transferModal').style.display = 'flex';
    resetTransferForm();
}

// Close transfer modal
function closeTransferModal() {
    document.getElementById('transferModal').classList.remove('active');
    document.getElementById('transferModal').style.display = 'none';
    resetTransferForm();
}

// Reset transfer form
function resetTransferForm() {
    document.getElementById('transferForm').reset();
    document.getElementById('transferFrom').innerHTML = '<option value="">اختر المصدر</option>';
    document.getElementById('transferTo').innerHTML = '<option value="">اختر الوجهة</option>';
}

// Update transfer fields based on type
function updateTransferFields() {
    const type = document.getElementById('transferType').value;
    const fromSelect = document.getElementById('transferFrom');
    const toSelect = document.getElementById('transferTo');

    fromSelect.innerHTML = '<option value="">اختر المصدر</option>';
    toSelect.innerHTML = '<option value="">اختر الوجهة</option>';

    if (type === 'cash_to_cash') {
        // Populate both with cash registers
        cashRegisters.filter(cr => cr.isActive).forEach(register => {
            const fromOption = document.createElement('option');
            fromOption.value = `cash_${register.id}`;
            fromOption.textContent = register.name;
            fromSelect.appendChild(fromOption);

            const toOption = document.createElement('option');
            toOption.value = `cash_${register.id}`;
            toOption.textContent = register.name;
            toSelect.appendChild(toOption);
        });
    } else if (type === 'cash_to_bank') {
        // From: cash registers, To: banks
        cashRegisters.filter(cr => cr.isActive).forEach(register => {
            const option = document.createElement('option');
            option.value = `cash_${register.id}`;
            option.textContent = register.name;
            fromSelect.appendChild(option);
        });

        banks.filter(bank => bank.isActive).forEach(bank => {
            const option = document.createElement('option');
            option.value = `bank_${bank.id}`;
            option.textContent = bank.bankName;
            toSelect.appendChild(option);
        });
    } else if (type === 'bank_to_cash') {
        // From: banks, To: cash registers
        banks.filter(bank => bank.isActive).forEach(bank => {
            const option = document.createElement('option');
            option.value = `bank_${bank.id}`;
            option.textContent = bank.bankName;
            fromSelect.appendChild(option);
        });

        cashRegisters.filter(cr => cr.isActive).forEach(register => {
            const option = document.createElement('option');
            option.value = `cash_${register.id}`;
            option.textContent = register.name;
            toSelect.appendChild(option);
        });
    }
}

// Process transfer
function processTransfer(event) {
    event.preventDefault();

    const type = document.getElementById('transferType').value;
    const fromValue = document.getElementById('transferFrom').value;
    const toValue = document.getElementById('transferTo').value;
    const amount = parseFloat(document.getElementById('transferAmount').value);
    const notes = document.getElementById('transferNotes').value;

    if (!type || !fromValue || !toValue || !amount) {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    if (fromValue === toValue) {
        showMessage('لا يمكن التحويل من نفس المصدر إلى نفس الوجهة', 'error');
        return;
    }

    // Parse from and to values
    const [fromType, fromId] = fromValue.split('_');
    const [toType, toId] = toValue.split('_');

    // Get source and destination names
    let fromName, toName;
    if (fromType === 'cash') {
        const cashRegister = cashRegisters.find(cr => cr.id == fromId);
        fromName = cashRegister ? cashRegister.name : 'غير معروف';
        
        // Check balance
        if (cashRegister && cashRegister.balance < amount) {
            showMessage('الرصيد غير كافي في الصندوق المصدر', 'error');
            return;
        }
    } else {
        const bank = banks.find(b => b.id == fromId);
        fromName = bank ? bank.bankName : 'غير معروف';
        
        // Check balance
        if (bank && bank.balance < amount) {
            showMessage('الرصيد غير كافي في البنك المصدر', 'error');
            return;
        }
    }

    if (toType === 'cash') {
        const cashRegister = cashRegisters.find(cr => cr.id == toId);
        toName = cashRegister ? cashRegister.name : 'غير معروف';
    } else {
        const bank = banks.find(b => b.id == toId);
        toName = bank ? bank.bankName : 'غير معروف';
    }

    // Create transfer record
    const transfer = {
        id: Math.max(...transfers.map(t => t.id), 0) + 1,
        transferNumber: `TRF-${(transfers.length + 1).toString().padStart(3, '0')}`,
        date: new Date().toISOString().split('T')[0],
        type: type,
        fromType: fromType,
        fromId: parseInt(fromId),
        fromName: fromName,
        toType: toType,
        toId: parseInt(toId),
        toName: toName,
        amount: amount,
        notes: notes,
        status: 'completed',
        createdBy: document.getElementById('userFullName').textContent,
        createdAt: new Date().toISOString()
    };

    // Update balances
    if (fromType === 'cash') {
        const cashRegister = cashRegisters.find(cr => cr.id == fromId);
        if (cashRegister) {
            cashRegister.balance -= amount;
        }
    } else {
        const bank = banks.find(b => b.id == fromId);
        if (bank) {
            bank.balance -= amount;
        }
    }

    if (toType === 'cash') {
        const cashRegister = cashRegisters.find(cr => cr.id == toId);
        if (cashRegister) {
            cashRegister.balance += amount;
        }
    } else {
        const bank = banks.find(b => b.id == toId);
        if (bank) {
            bank.balance += amount;
        }
    }

    // Save data
    transfers.push(transfer);
    localStorage.setItem('anwar_bakery_money_transfers', JSON.stringify(transfers));
    localStorage.setItem('anwar_bakery_cash_registers', JSON.stringify(cashRegisters));
    localStorage.setItem('anwar_bakery_banks', JSON.stringify(banks));

    // Update UI
    renderCashRegistersOverview();
    renderRecentTransfers();
    closeTransferModal();

    const currencySymbol = getCurrencySymbol();
    showMessage(`تم تنفيذ التحويل بنجاح! المبلغ: ${amount.toFixed(2)} ${currencySymbol}`, 'success');
}

// Quick transfer functions
function openQuickTransferModal() {
    openTransferModal();
    document.getElementById('transferType').value = 'cash_to_cash';
    updateTransferFields();
}

function openBankDepositModal() {
    openTransferModal();
    document.getElementById('transferType').value = 'cash_to_bank';
    updateTransferFields();
}

function openBankWithdrawModal() {
    openTransferModal();
    document.getElementById('transferType').value = 'bank_to_cash';
    updateTransferFields();
}

// View all transfers
function viewAllTransfers() {
    showMessage('صفحة عرض جميع التحويلات قيد التطوير...', 'info');
}

// Get currency symbol
function getCurrencySymbol() {
    const companyData = localStorage.getItem('anwar_bakery_company');
    if (companyData) {
        const company = JSON.parse(companyData);
        return company.currencySymbol || 'ر.س';
    }
    return 'ر.س';
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (sidebar && overlay) {
        if (sidebar.classList.contains('translate-x-full')) {
            sidebar.classList.remove('translate-x-full');
            overlay.classList.remove('hidden');
        } else {
            sidebar.classList.add('translate-x-full');
            overlay.classList.add('hidden');
        }
    }
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_session');
        sessionStorage.removeItem('anwar_bakery_session');
        window.location.href = 'login.html';
    }
}

// Update current date time
function updateDateTime() {
    const now = new Date();
    const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    const dateTimeElement = document.getElementById('currentDateTime');
    if (dateTimeElement) {
        dateTimeElement.textContent = now.toLocaleDateString('ar-SA', options);
    }
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    if (!container) return;

    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700',
        'warning': 'bg-yellow-50 border-yellow-200 text-yellow-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadUserInfo();
    loadCompanyInfo();
    loadCashRegisters();
    loadBanks();
    loadTransfers();
    updateDateTime();
    setInterval(updateDateTime, 60000);
    
    // Close sidebar when clicking overlay
    const overlay = document.getElementById('mobileOverlay');
    if (overlay) {
        overlay.addEventListener('click', toggleSidebar);
    }
});
