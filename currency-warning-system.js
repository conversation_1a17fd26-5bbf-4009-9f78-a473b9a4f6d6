// نظام تحذير الكارثة المحاسبية
(function() {
    'use strict';
    
    // فحص التناسق المحاسبي
    function checkAccountingConsistency() {
        const currentCurrency = getCurrentCurrency();
        const storedCurrency = getStoredCurrency();
        
        if (currentCurrency !== storedCurrency) {
            showCurrencyWarning(storedCurrency, currentCurrency);
        }
    }
    
    // عرض تحذير الكارثة المحاسبية
    function showCurrencyWarning(storedCurrency, currentCurrency) {
        const warningModal = document.createElement('div');
        warningModal.className = 'fixed inset-0 bg-red-900 bg-opacity-75 z-50 flex items-center justify-center';
        warningModal.innerHTML = `
            <div class="bg-white rounded-lg p-8 max-w-2xl mx-4 border-4 border-red-500">
                <div class="text-center mb-6">
                    <div class="text-6xl mb-4">🚨</div>
                    <h2 class="text-2xl font-bold text-red-600 mb-2">تحذير: كارثة محاسبية محتملة!</h2>
                    <p class="text-lg text-gray-700">تم اكتشاف عدم تطابق في العملة المحاسبية</p>
                </div>
                
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <h3 class="font-bold text-red-800 mb-2">المشكلة المكتشفة:</h3>
                    <ul class="text-red-700 space-y-1">
                        <li>• البيانات المالية مخزنة بعملة: <strong>${getCurrencyName(storedCurrency)}</strong></li>
                        <li>• العملة المختارة حالياً: <strong>${getCurrencyName(currentCurrency)}</strong></li>
                        <li>• هذا يعني أن جميع المبالغ معروضة بالعملة الخاطئة!</li>
                    </ul>
                </div>
                
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <h3 class="font-bold text-yellow-800 mb-2">مثال على الخطر:</h3>
                    <p class="text-yellow-700">
                        راتب موظف: 50,000 ${getCurrencySymbol(storedCurrency)}<br>
                        إذا عُرض كـ: 50,000 ${getCurrencySymbol(currentCurrency)}<br>
                        <strong>الفرق: ${calculateDifference(50000, storedCurrency, currentCurrency)} ${getCurrencySymbol(currentCurrency)}</strong>
                    </p>
                </div>
                
                <div class="flex flex-col space-y-3">
                    <button onclick="convertCurrencyData('${storedCurrency}', '${currentCurrency}')" 
                            class="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 font-bold">
                        ✅ تحويل جميع البيانات إلى ${getCurrencyName(currentCurrency)}
                    </button>
                    
                    <button onclick="revertCurrency('${storedCurrency}')" 
                            class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700">
                        🔄 العودة إلى ${getCurrencyName(storedCurrency)}
                    </button>
                    
                    <button onclick="dismissWarning()" 
                            class="w-full bg-gray-600 text-white py-3 px-4 rounded-lg hover:bg-gray-700">
                        ⚠️ تجاهل التحذير (خطر!)
                    </button>
                </div>
                
                <div class="mt-4 text-xs text-gray-500 text-center">
                    تحذير: تجاهل هذا التحذير قد يؤدي إلى أخطاء محاسبية جسيمة
                </div>
            </div>
        `;
        
        document.body.appendChild(warningModal);
        
        // منع إغلاق النافذة بالخطأ
        warningModal.addEventListener('click', function(e) {
            if (e.target === warningModal) {
                e.preventDefault();
                e.stopPropagation();
            }
        });
    }
    
    // حساب الفرق في القيمة
    function calculateDifference(amount, fromCurrency, toCurrency) {
        const exchangeRates = {
            'SAR': 1,
            'YER': 663.75,
            'USD': 0.267,
            'EUR': 0.244,
            'AED': 0.98,
            'KWD': 0.082,
            'QAR': 0.97
        };
        
        const fromRate = exchangeRates[fromCurrency] || 1;
        const toRate = exchangeRates[toCurrency] || 1;
        const convertedAmount = Math.round(amount * (toRate / fromRate));
        
        return Math.abs(convertedAmount - amount).toLocaleString();
    }
    
    // العودة للعملة السابقة
    function revertCurrency(currency) {
        const companyData = JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
        companyData.currency = currency;
        companyData.currencySymbol = getCurrencySymbol(currency);
        localStorage.setItem('anwar_bakery_company', JSON.stringify(companyData));
        
        dismissWarning();
        window.location.reload();
    }
    
    // تجاهل التحذير
    function dismissWarning() {
        const modal = document.querySelector('.fixed.inset-0.bg-red-900');
        if (modal) {
            modal.remove();
        }
    }
    
    // الحصول على العملة الحالية
    function getCurrentCurrency() {
        const companyData = JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
        return companyData.currency || 'SAR';
    }
    
    // الحصول على العملة المخزنة
    function getStoredCurrency() {
        return localStorage.getItem('anwar_bakery_stored_currency') || 'SAR';
    }
    
    // الحصول على اسم العملة
    function getCurrencyName(currency) {
        const names = {
            'SAR': 'الريال السعودي',
            'YER': 'الريال اليمني',
            'USD': 'الدولار الأمريكي',
            'EUR': 'اليورو',
            'AED': 'الدرهم الإماراتي',
            'KWD': 'الدينار الكويتي',
            'QAR': 'الريال القطري'
        };
        return names[currency] || currency;
    }
    
    // الحصول على رمز العملة
    function getCurrencySymbol(currency) {
        const symbols = {
            'SAR': 'ر.س',
            'YER': 'ر.ي',
            'USD': '$',
            'EUR': '€',
            'AED': 'د.إ',
            'KWD': 'د.ك',
            'QAR': 'ر.ق'
        };
        return symbols[currency] || currency;
    }
    
    // إضافة الوظائف للنطاق العام
    window.revertCurrency = revertCurrency;
    window.dismissWarning = dismissWarning;
    
    // فحص التناسق عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(checkAccountingConsistency, 1000);
    });
    
    console.log('🚨 Currency Warning System loaded');
    
})();
