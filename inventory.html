<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المخزون - مخبز أنور</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Mobile menu overlay -->
    <div id="mobileOverlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 lg:hidden hidden"></div>

    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform translate-x-full lg:translate-x-0 lg:static lg:inset-0 sidebar-transition">
            <div class="h-16 px-4 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between">
                <h1 id="sidebarCompanyName" class="text-white text-lg font-bold">مخبز أنور</h1>
                <button onclick="toggleSidebar()" class="lg:hidden text-white">
                    <span class="text-xl">✕</span>
                </button>
            </div>

            <nav class="mt-5 px-2 space-y-1 h-full overflow-y-auto pb-20">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    لوحة التحكم
                </a>
                <a href="users-management.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👥</span>
                    إدارة المستخدمين
                </a>
                <a href="company-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏢</span>
                    بيانات المنشأة
                </a>
                <a href="branches-warehouses.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏪</span>
                    الفروع والمخازن
                </a>
                <a href="units.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📏</span>
                    وحدات القياس
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة المنتجات
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🚚</span>
                    الموردين
                </a>
                <a href="chart-of-accounts.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📊</span>
                    دليل الحسابات
                </a>
                <a href="journal-entry.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📝</span>
                    القيود اليومية
                </a>
                <a href="vouchers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    السندات
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="banks.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏦</span>
                    البنوك
                </a>
                <a href="owners.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👑</span>
                    الملاك
                </a>
                <a href="inventory.html" class="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">
                    <span class="ml-3">📊</span>
                    المخزون
                </a>
                <a href="reports.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📈</span>
                    التقارير
                </a>
                <a href="system-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">⚙️</span>
                    إعدادات النظام
                </a>

                <!-- User Info & Logout -->
                <div class="absolute bottom-4 left-2 right-2">
                    <div class="bg-gray-50 rounded-lg p-3 mb-2">
                        <p id="userFullName" class="text-sm font-medium text-gray-900">مدير النظام</p>
                        <p id="userRole" class="text-xs text-gray-500">admin</p>
                        <p id="loginTime" class="text-xs text-gray-400"></p>
                    </div>
                    <button onclick="logout()" class="w-full text-right px-3 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50 flex items-center">
                        <span class="ml-3">🚪</span>
                        تسجيل الخروج
                    </button>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 overflow-hidden">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <button onclick="toggleSidebar()" class="lg:hidden text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">☰</span>
                            </button>
                            <span class="text-lg font-semibold text-gray-900">إدارة المخزون</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-500" id="currentDateTime"></span>
                            <div class="flex items-center space-x-2">
                                <span id="userDisplayName" class="text-sm font-medium text-gray-700">مدير النظام</span>
                                <button onclick="logout()" class="text-red-600 hover:text-red-800 text-sm">
                                    خروج
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Header -->
                <div class="mb-6">
                    <div class="flex justify-between items-center">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900">إدارة المخزون</h1>
                            <p class="text-gray-600 mt-1">متابعة حالة المخزون وحركة الأصناف</p>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="refreshInventory()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center">
                                <span class="ml-2">🔄</span>
                                تحديث المخزون
                            </button>
                            <button onclick="exportInventoryReport()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center">
                                <span class="ml-2">📊</span>
                                تقرير المخزون
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Message Container -->
                <div id="messageContainer" class="mb-4"></div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-blue-100 rounded-lg">
                                <span class="text-blue-600 text-xl">📦</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">إجمالي الأصناف</p>
                                <p class="text-2xl font-bold text-gray-900" id="totalItems">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-green-100 rounded-lg">
                                <span class="text-green-600 text-xl">✅</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">أصناف متوفرة</p>
                                <p class="text-2xl font-bold text-gray-900" id="availableItems">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-red-100 rounded-lg">
                                <span class="text-red-600 text-xl">⚠️</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">نفاد المخزون</p>
                                <p class="text-2xl font-bold text-gray-900" id="lowStockItems">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-purple-100 rounded-lg">
                                <span class="text-purple-600 text-xl">💰</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">قيمة المخزون</p>
                                <p class="text-2xl font-bold text-gray-900" id="inventoryValue">0.00</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="bg-white rounded-lg shadow mb-6 p-4">
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <input type="text" id="searchInput" placeholder="البحث بالاسم أو الكود..."
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   onkeyup="filterInventory()">
                        </div>
                        <div>
                            <select id="branchFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    onchange="filterInventory()">
                                <option value="">جميع الفروع</option>
                            </select>
                        </div>
                        <div>
                            <select id="warehouseFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    onchange="filterInventory()">
                                <option value="">جميع المخازن</option>
                            </select>
                        </div>
                        <div>
                            <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    onchange="filterInventory()">
                                <option value="">جميع الحالات</option>
                                <option value="available">متوفر</option>
                                <option value="low">مخزون منخفض</option>
                                <option value="out">نفد المخزون</option>
                            </select>
                        </div>
                        <div>
                            <select id="typeFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    onchange="filterInventory()">
                                <option value="">جميع الأنواع</option>
                                <option value="raw_material">خامة</option>
                                <option value="finished_product">منتج نهائي</option>
                                <option value="semi_finished">نصف مصنع</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Inventory Table -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">كود الصنف</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم الصنف</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الفرع</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المخزن</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية الحالية</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحد الأدنى</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحد الأقصى</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">قيمة المخزون</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="inventoryTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- Inventory items will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="inventory.js"></script>
</body>
</html>
