// محدث إعدادات الشركة مع تطبيق فوري للعملة
class CompanySettingsUpdater {
    constructor() {
        this.init();
    }

    init() {
        // الاستماع لتحديث إعدادات الشركة
        this.listenForCompanyUpdates();
        
        // تطبيق الإعدادات الحالية
        this.applyCurrentSettings();
    }

    // الاستماع لتحديث إعدادات الشركة
    listenForCompanyUpdates() {
        // الاستماع لأحداث تحديث النماذج
        document.addEventListener('submit', (event) => {
            const form = event.target;
            if (form.id === 'companyForm' || form.classList.contains('company-settings-form')) {
                setTimeout(() => {
                    this.handleCompanyUpdate();
                }, 100);
            }
        });

        // الاستماع لتغييرات حقول العملة
        document.addEventListener('change', (event) => {
            const target = event.target;
            if (target.name === 'currency' || target.id === 'currency' || target.id === 'baseCurrency') {
                this.handleCurrencyChange(target.value);
            }
        });

        // الاستماع لتغييرات localStorage
        window.addEventListener('storage', (event) => {
            if (event.key === 'anwar_bakery_company') {
                this.handleCompanyUpdate();
            }
        });
    }

    // تطبيق الإعدادات الحالية
    applyCurrentSettings() {
        const companyData = this.getCompanyData();
        if (companyData.currency) {
            this.applyCurrencySettings(companyData.currency, companyData.currencySymbol);
        }
    }

    // معالجة تحديث إعدادات الشركة
    handleCompanyUpdate() {
        console.log('🔄 Company settings updated, applying changes...');
        
        const companyData = this.getCompanyData();
        
        // تطبيق إعدادات العملة
        if (companyData.currency) {
            this.applyCurrencySettings(companyData.currency, companyData.currencySymbol);
        }

        // إشعار النظام بالتحديث
        this.notifySystemUpdate(companyData);
        
        console.log('✅ Company settings applied successfully');
    }

    // معالجة تغيير العملة
    handleCurrencyChange(newCurrency) {
        console.log('💱 Currency changed to:', newCurrency);
        
        // تحديث بيانات الشركة
        const companyData = this.getCompanyData();
        companyData.currency = newCurrency;
        
        // تحديث رمز العملة
        const currencySymbols = {
            'SAR': 'ر.س',
            'YER': '﷼',
            'USD': '$',
            'EUR': '€',
            'AED': 'د.إ',
            'KWD': 'د.ك',
            'QAR': 'ر.ق'
        };
        
        companyData.currencySymbol = currencySymbols[newCurrency] || newCurrency;
        companyData.updatedAt = new Date().toISOString();
        
        // حفظ التحديث
        this.saveCompanyData(companyData);
        
        // تطبيق التغيير فوراً
        this.applyCurrencySettings(newCurrency, companyData.currencySymbol);
        
        // إشعار النظام
        this.notifySystemUpdate(companyData);
    }

    // تطبيق إعدادات العملة
    applyCurrencySettings(currency, symbol) {
        // تحديث مدير العملة
        if (window.currencyManager) {
            window.currencyManager.updateCurrency(currency, symbol);
        }

        // تحديث محدث العملة العام
        if (window.globalCurrencyUpdater) {
            window.globalCurrencyUpdater.updateAllCurrencies();
        }

        // تحديث النماذج الحالية
        this.updateCurrentForms(currency, symbol);
        
        // تحديث العرض
        this.updateDisplayElements(symbol);
    }

    // تحديث النماذج الحالية
    updateCurrentForms(currency, symbol) {
        // تحديث خيارات العملة في النماذج
        document.querySelectorAll('select[name="currency"], #currency, #baseCurrency').forEach(select => {
            if (select.value !== currency) {
                select.value = currency;
            }
        });

        // تحديث حقول رمز العملة
        document.querySelectorAll('input[name="currencySymbol"], #currencySymbol').forEach(input => {
            if (input.value !== symbol) {
                input.value = symbol;
            }
        });

        // تحديث عرض العملة في النماذج
        document.querySelectorAll('.currency-preview, .currency-display').forEach(el => {
            el.textContent = symbol;
        });
    }

    // تحديث عناصر العرض
    updateDisplayElements(symbol) {
        // تحديث رموز العملة في الصفحة
        document.querySelectorAll('.currency-symbol').forEach(el => {
            el.textContent = symbol;
        });

        // تحديث عناصر المبالغ
        document.querySelectorAll('.amount-display, .balance-display').forEach(el => {
            const amount = parseFloat(el.dataset.amount || el.textContent.replace(/[^\d.-]/g, '')) || 0;
            if (amount) {
                el.textContent = this.formatAmount(amount, symbol);
            }
        });
    }

    // تنسيق المبلغ
    formatAmount(amount, symbol) {
        if (window.currencyManager) {
            return window.currencyManager.formatCurrency(amount);
        }
        return `${parseFloat(amount).toFixed(2)} ${symbol}`;
    }

    // إشعار النظام بالتحديث
    notifySystemUpdate(companyData) {
        // إرسال حدث مخصص
        window.dispatchEvent(new CustomEvent('companyDataUpdated', {
            detail: companyData
        }));

        // إرسال حدث تغيير العملة
        if (companyData.currency) {
            window.dispatchEvent(new CustomEvent('currencyChanged', {
                detail: {
                    code: companyData.currency,
                    symbol: companyData.currencySymbol
                }
            }));
        }

        // تحديث الصفحات الأخرى عبر localStorage
        const storageEvent = new StorageEvent('storage', {
            key: 'anwar_bakery_company',
            newValue: JSON.stringify(companyData)
        });
        window.dispatchEvent(storageEvent);
    }

    // الحصول على بيانات الشركة
    getCompanyData() {
        try {
            const data = localStorage.getItem('anwar_bakery_company');
            return data ? JSON.parse(data) : {};
        } catch (error) {
            console.error('Error parsing company data:', error);
            return {};
        }
    }

    // حفظ بيانات الشركة
    saveCompanyData(data) {
        try {
            localStorage.setItem('anwar_bakery_company', JSON.stringify(data));
            console.log('💾 Company data saved:', data);
        } catch (error) {
            console.error('Error saving company data:', error);
        }
    }

    // تحديث إعدادات محددة
    updateSetting(key, value) {
        const companyData = this.getCompanyData();
        companyData[key] = value;
        companyData.updatedAt = new Date().toISOString();
        
        this.saveCompanyData(companyData);
        this.notifySystemUpdate(companyData);
        
        // إذا كان التحديث متعلق بالعملة
        if (key === 'currency' || key === 'currencySymbol') {
            this.applyCurrencySettings(companyData.currency, companyData.currencySymbol);
        }
    }

    // تحديث عدة إعدادات
    updateSettings(settings) {
        const companyData = this.getCompanyData();
        Object.assign(companyData, settings);
        companyData.updatedAt = new Date().toISOString();
        
        this.saveCompanyData(companyData);
        this.notifySystemUpdate(companyData);
        
        // إذا كان التحديث متعلق بالعملة
        if (settings.currency || settings.currencySymbol) {
            this.applyCurrencySettings(companyData.currency, companyData.currencySymbol);
        }
    }

    // إعادة تعيين الإعدادات
    resetSettings() {
        const defaultSettings = {
            currency: 'SAR',
            currencySymbol: 'ر.س',
            updatedAt: new Date().toISOString()
        };
        
        this.saveCompanyData(defaultSettings);
        this.applyCurrencySettings(defaultSettings.currency, defaultSettings.currencySymbol);
        this.notifySystemUpdate(defaultSettings);
    }
}

// إنشاء مثيل عام لمحدث إعدادات الشركة
window.companySettingsUpdater = new CompanySettingsUpdater();

// وظائف مساعدة عامة
window.updateCompanySetting = (key, value) => window.companySettingsUpdater.updateSetting(key, value);
window.updateCompanySettings = (settings) => window.companySettingsUpdater.updateSettings(settings);
window.resetCompanySettings = () => window.companySettingsUpdater.resetSettings();

console.log('✅ Company Settings Updater loaded successfully');
