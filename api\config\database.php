<?php
/**
 * Database Configuration for Anwar Bakery Management System
 * إعدادات قاعدة البيانات لنظام إدارة مخبز أنوار الحي
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'anwar_bakery';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    private $conn;

    /**
     * Get database connection
     * الحصول على اتصال قاعدة البيانات
     */
    public function getConnection() {
        $this->conn = null;

        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];

            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch(PDOException $exception) {
            error_log("Connection error: " . $exception->getMessage());
            throw new Exception("Database connection failed");
        }

        return $this->conn;
    }

    /**
     * Test database connection
     * اختبار اتصال قاعدة البيانات
     */
    public function testConnection() {
        try {
            $conn = $this->getConnection();
            $stmt = $conn->query("SELECT 1");
            return true;
        } catch(Exception $e) {
            return false;
        }
    }

    /**
     * Get database configuration from environment or config file
     * الحصول على إعدادات قاعدة البيانات من متغيرات البيئة أو ملف الإعدادات
     */
    public function loadConfig() {
        // Try to load from environment variables first
        if (getenv('DB_HOST')) {
            $this->host = getenv('DB_HOST');
        }
        if (getenv('DB_NAME')) {
            $this->db_name = getenv('DB_NAME');
        }
        if (getenv('DB_USER')) {
            $this->username = getenv('DB_USER');
        }
        if (getenv('DB_PASS')) {
            $this->password = getenv('DB_PASS');
        }

        // Try to load from config file
        $configFile = __DIR__ . '/../../config.json';
        if (file_exists($configFile)) {
            $config = json_decode(file_get_contents($configFile), true);
            if (isset($config['database'])) {
                $dbConfig = $config['database'];
                $this->host = $dbConfig['host'] ?? $this->host;
                $this->db_name = $dbConfig['name'] ?? $this->db_name;
                $this->username = $dbConfig['username'] ?? $this->username;
                $this->password = $dbConfig['password'] ?? $this->password;
            }
        }
    }

    /**
     * Execute SQL file
     * تنفيذ ملف SQL
     */
    public function executeSQLFile($filePath) {
        try {
            if (!file_exists($filePath)) {
                throw new Exception("SQL file not found: " . $filePath);
            }

            $sql = file_get_contents($filePath);
            $conn = $this->getConnection();
            
            // Split SQL into individual statements
            $statements = array_filter(
                array_map('trim', explode(';', $sql)),
                function($stmt) {
                    return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
                }
            );

            $conn->beginTransaction();
            
            foreach ($statements as $statement) {
                if (!empty(trim($statement))) {
                    $conn->exec($statement);
                }
            }
            
            $conn->commit();
            return true;
            
        } catch(Exception $e) {
            if (isset($conn)) {
                $conn->rollback();
            }
            error_log("SQL execution error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Initialize database with schema
     * تهيئة قاعدة البيانات بالهيكل
     */
    public function initializeDatabase() {
        try {
            $schemaFile = __DIR__ . '/../../database-schema.sql';
            return $this->executeSQLFile($schemaFile);
        } catch(Exception $e) {
            error_log("Database initialization error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if database exists and has tables
     * التحقق من وجود قاعدة البيانات والجداول
     */
    public function isDatabaseInitialized() {
        try {
            $conn = $this->getConnection();
            $stmt = $conn->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // Check for essential tables
            $requiredTables = [
                'company_settings',
                'users',
                'chart_of_accounts',
                'products',
                'customers',
                'suppliers',
                'invoices',
                'vouchers'
            ];
            
            foreach ($requiredTables as $table) {
                if (!in_array($table, $tables)) {
                    return false;
                }
            }
            
            return true;
            
        } catch(Exception $e) {
            return false;
        }
    }

    /**
     * Get database statistics
     * الحصول على إحصائيات قاعدة البيانات
     */
    public function getDatabaseStats() {
        try {
            $conn = $this->getConnection();
            $stats = [];
            
            // Get table counts
            $tables = [
                'users' => 'المستخدمين',
                'products' => 'المنتجات',
                'customers' => 'العملاء',
                'suppliers' => 'الموردين',
                'invoices' => 'الفواتير',
                'vouchers' => 'السندات',
                'journal_entries' => 'القيود اليومية'
            ];
            
            foreach ($tables as $table => $label) {
                $stmt = $conn->query("SELECT COUNT(*) as count FROM {$table}");
                $result = $stmt->fetch();
                $stats[$table] = [
                    'label' => $label,
                    'count' => $result['count']
                ];
            }
            
            // Get database size
            $stmt = $conn->query("
                SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = '{$this->db_name}'
            ");
            $result = $stmt->fetch();
            $stats['database_size'] = $result['size_mb'] . ' MB';
            
            return $stats;
            
        } catch(Exception $e) {
            error_log("Database stats error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Backup database
     * نسخ احتياطي لقاعدة البيانات
     */
    public function backupDatabase($backupPath = null) {
        try {
            if (!$backupPath) {
                $backupPath = __DIR__ . '/../../backups/backup_' . date('Y-m-d_H-i-s') . '.sql';
            }
            
            // Create backup directory if it doesn't exist
            $backupDir = dirname($backupPath);
            if (!is_dir($backupDir)) {
                mkdir($backupDir, 0755, true);
            }
            
            // Use mysqldump command
            $command = sprintf(
                'mysqldump -h%s -u%s -p%s %s > %s',
                escapeshellarg($this->host),
                escapeshellarg($this->username),
                escapeshellarg($this->password),
                escapeshellarg($this->db_name),
                escapeshellarg($backupPath)
            );
            
            exec($command, $output, $returnCode);
            
            if ($returnCode === 0 && file_exists($backupPath)) {
                return $backupPath;
            } else {
                throw new Exception("Backup command failed");
            }
            
        } catch(Exception $e) {
            error_log("Database backup error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Restore database from backup
     * استعادة قاعدة البيانات من نسخة احتياطية
     */
    public function restoreDatabase($backupPath) {
        try {
            if (!file_exists($backupPath)) {
                throw new Exception("Backup file not found: " . $backupPath);
            }
            
            // Use mysql command to restore
            $command = sprintf(
                'mysql -h%s -u%s -p%s %s < %s',
                escapeshellarg($this->host),
                escapeshellarg($this->username),
                escapeshellarg($this->password),
                escapeshellarg($this->db_name),
                escapeshellarg($backupPath)
            );
            
            exec($command, $output, $returnCode);
            
            return $returnCode === 0;
            
        } catch(Exception $e) {
            error_log("Database restore error: " . $e->getMessage());
            return false;
        }
    }
}

// Global database instance
$database = new Database();
$database->loadConfig();

// Test connection on include
if (!$database->testConnection()) {
    error_log("Warning: Database connection failed during initialization");
}
?>
