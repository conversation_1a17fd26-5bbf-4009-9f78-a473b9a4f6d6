// Advanced Print System - نظام الطباعة المتقدم
// Professional printing system for invoices and reports

class AdvancedPrint {
    constructor() {
        this.companySettings = this.getCompanySettings();
        this.printSettings = this.getPrintSettings();
    }

    /**
     * Print invoice with professional layout
     * طباعة فاتورة بتخطيط احترافي
     */
    printInvoice(invoice, options = {}) {
        const defaultOptions = {
            showLogo: true,
            showHeader: true,
            showFooter: true,
            copies: 1,
            paperSize: 'A4',
            orientation: 'portrait',
            margins: '10mm',
            fontSize: '12px',
            language: 'ar'
        };

        const printOptions = { ...defaultOptions, ...options };
        
        // Generate invoice HTML
        const invoiceHTML = this.generateInvoiceHTML(invoice, printOptions);
        
        // Create print window
        this.createPrintWindow(invoiceHTML, printOptions);
    }

    /**
     * Generate professional invoice HTML
     * إنشاء HTML احترافي للفاتورة
     */
    generateInvoiceHTML(invoice, options) {
        const company = this.companySettings;
        const customer = this.getCustomerById(invoice.customer_id);
        const supplier = this.getSupplierById(invoice.supplier_id);
        
        return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>فاتورة ${invoice.number}</title>
            <style>
                ${this.getInvoicePrintCSS(options)}
            </style>
        </head>
        <body>
            <div class="invoice-container">
                ${options.showHeader ? this.generateInvoiceHeader(company, options) : ''}
                
                <div class="invoice-info">
                    <div class="invoice-details">
                        <h2 class="invoice-title">${this.getInvoiceTitle(invoice.type)}</h2>
                        <div class="invoice-meta">
                            <div class="meta-row">
                                <span class="label">رقم الفاتورة:</span>
                                <span class="value">${invoice.number}</span>
                            </div>
                            <div class="meta-row">
                                <span class="label">التاريخ:</span>
                                <span class="value">${this.formatDate(invoice.date)}</span>
                            </div>
                            <div class="meta-row">
                                <span class="label">الوقت:</span>
                                <span class="value">${this.formatTime(invoice.time || new Date())}</span>
                            </div>
                        </div>
                    </div>
                    
                    ${this.generateClientInfo(invoice, customer, supplier)}
                </div>

                <div class="invoice-items">
                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>م</th>
                                <th>الصنف</th>
                                <th>الكمية</th>
                                <th>الوحدة</th>
                                <th>السعر</th>
                                <th>الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${this.generateInvoiceItems(invoice.items)}
                        </tbody>
                    </table>
                </div>

                <div class="invoice-totals">
                    ${this.generateInvoiceTotals(invoice)}
                </div>

                <div class="invoice-notes">
                    ${invoice.notes ? `<p><strong>ملاحظات:</strong> ${invoice.notes}</p>` : ''}
                </div>

                ${options.showFooter ? this.generateInvoiceFooter(company, options) : ''}
            </div>

            <script>
                // Auto print when page loads
                window.onload = function() {
                    setTimeout(function() {
                        window.print();
                    }, 500);
                };
            </script>
        </body>
        </html>
        `;
    }

    /**
     * Generate invoice CSS for printing
     * إنشاء CSS للطباعة
     */
    getInvoicePrintCSS(options) {
        return `
            @page {
                size: ${options.paperSize} ${options.orientation};
                margin: ${options.margins};
            }
            
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: 'Arial', 'Tahoma', sans-serif;
                font-size: ${options.fontSize};
                line-height: 1.4;
                color: #333;
                direction: rtl;
                text-align: right;
            }
            
            .invoice-container {
                max-width: 100%;
                margin: 0 auto;
                padding: 20px;
            }
            
            .company-header {
                text-align: center;
                border-bottom: 2px solid #333;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }
            
            .company-logo {
                max-width: 150px;
                max-height: 80px;
                margin-bottom: 10px;
            }
            
            .company-name {
                font-size: 24px;
                font-weight: bold;
                color: #2563eb;
                margin-bottom: 5px;
            }
            
            .company-info {
                font-size: 12px;
                color: #666;
                line-height: 1.6;
            }
            
            .invoice-info {
                display: flex;
                justify-content: space-between;
                margin-bottom: 30px;
                gap: 30px;
            }
            
            .invoice-details {
                flex: 1;
            }
            
            .invoice-title {
                font-size: 20px;
                font-weight: bold;
                color: #dc2626;
                margin-bottom: 15px;
                text-align: center;
                padding: 10px;
                border: 2px solid #dc2626;
                border-radius: 5px;
            }
            
            .invoice-meta {
                background: #f8f9fa;
                padding: 15px;
                border-radius: 5px;
            }
            
            .meta-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;
            }
            
            .meta-row:last-child {
                margin-bottom: 0;
            }
            
            .label {
                font-weight: bold;
                color: #374151;
            }
            
            .value {
                color: #1f2937;
            }
            
            .client-info {
                flex: 1;
                background: #f1f5f9;
                padding: 15px;
                border-radius: 5px;
            }
            
            .client-title {
                font-weight: bold;
                font-size: 14px;
                color: #1e40af;
                margin-bottom: 10px;
                border-bottom: 1px solid #cbd5e1;
                padding-bottom: 5px;
            }
            
            .items-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 30px;
                font-size: 11px;
            }
            
            .items-table th,
            .items-table td {
                border: 1px solid #d1d5db;
                padding: 8px;
                text-align: center;
            }
            
            .items-table th {
                background: #374151;
                color: white;
                font-weight: bold;
            }
            
            .items-table tbody tr:nth-child(even) {
                background: #f9fafb;
            }
            
            .items-table .item-name {
                text-align: right;
                font-weight: 500;
            }
            
            .invoice-totals {
                margin-left: auto;
                width: 300px;
                border: 2px solid #374151;
                border-radius: 5px;
                overflow: hidden;
            }
            
            .total-row {
                display: flex;
                justify-content: space-between;
                padding: 8px 15px;
                border-bottom: 1px solid #d1d5db;
            }
            
            .total-row:last-child {
                border-bottom: none;
                background: #374151;
                color: white;
                font-weight: bold;
                font-size: 14px;
            }
            
            .total-label {
                font-weight: 500;
            }
            
            .total-value {
                font-weight: bold;
            }
            
            .invoice-notes {
                margin-top: 30px;
                padding: 15px;
                background: #fef3c7;
                border-left: 4px solid #f59e0b;
                border-radius: 0 5px 5px 0;
            }
            
            .invoice-footer {
                margin-top: 40px;
                padding-top: 20px;
                border-top: 1px solid #d1d5db;
                text-align: center;
                font-size: 10px;
                color: #6b7280;
            }
            
            .signature-section {
                display: flex;
                justify-content: space-between;
                margin-top: 50px;
                padding-top: 20px;
            }
            
            .signature-box {
                text-align: center;
                width: 200px;
            }
            
            .signature-line {
                border-top: 1px solid #374151;
                margin-top: 40px;
                padding-top: 5px;
                font-size: 10px;
                color: #6b7280;
            }
            
            @media print {
                .invoice-container {
                    padding: 0;
                }
                
                .no-print {
                    display: none !important;
                }
                
                .page-break {
                    page-break-before: always;
                }
            }
        `;
    }

    /**
     * Generate invoice header
     * إنشاء رأس الفاتورة
     */
    generateInvoiceHeader(company, options) {
        return `
            <div class="company-header">
                ${options.showLogo && company.logo ? `<img src="${company.logo}" alt="شعار الشركة" class="company-logo">` : ''}
                <div class="company-name">${company.companyNameAr || 'مخبز أنوار الحي'}</div>
                <div class="company-info">
                    ${company.address ? `<div>العنوان: ${company.address}</div>` : ''}
                    ${company.phone ? `<div>الهاتف: ${company.phone}</div>` : ''}
                    ${company.email ? `<div>البريد الإلكتروني: ${company.email}</div>` : ''}
                    ${company.taxNumber ? `<div>الرقم الضريبي: ${company.taxNumber}</div>` : ''}
                    ${company.commercialRegister ? `<div>السجل التجاري: ${company.commercialRegister}</div>` : ''}
                </div>
            </div>
        `;
    }

    /**
     * Generate client information section
     * إنشاء قسم معلومات العميل
     */
    generateClientInfo(invoice, customer, supplier) {
        const client = invoice.type === 'sales' || invoice.type === 'sales_return' ? customer : supplier;
        const clientType = invoice.type === 'sales' || invoice.type === 'sales_return' ? 'العميل' : 'المورد';
        
        if (!client) {
            return `
                <div class="client-info">
                    <div class="client-title">بيانات ${clientType}</div>
                    <div>عميل نقدي</div>
                </div>
            `;
        }

        return `
            <div class="client-info">
                <div class="client-title">بيانات ${clientType}</div>
                <div><strong>الاسم:</strong> ${client.name}</div>
                ${client.phone ? `<div><strong>الهاتف:</strong> ${client.phone}</div>` : ''}
                ${client.email ? `<div><strong>البريد:</strong> ${client.email}</div>` : ''}
                ${client.address ? `<div><strong>العنوان:</strong> ${client.address}</div>` : ''}
                ${client.taxNumber ? `<div><strong>الرقم الضريبي:</strong> ${client.taxNumber}</div>` : ''}
            </div>
        `;
    }

    /**
     * Generate invoice items table
     * إنشاء جدول أصناف الفاتورة
     */
    generateInvoiceItems(items) {
        return items.map((item, index) => `
            <tr>
                <td>${index + 1}</td>
                <td class="item-name">${item.name}</td>
                <td>${this.formatNumber(item.quantity)}</td>
                <td>${item.unit || 'قطعة'}</td>
                <td>${this.formatCurrency(item.price)}</td>
                <td>${this.formatCurrency(item.total)}</td>
            </tr>
        `).join('');
    }

    /**
     * Generate invoice totals section
     * إنشاء قسم إجماليات الفاتورة
     */
    generateInvoiceTotals(invoice) {
        const subtotal = invoice.subtotal || 0;
        const discount = invoice.discount || 0;
        const tax = invoice.tax || 0;
        const total = invoice.total || 0;

        return `
            <div class="invoice-totals">
                <div class="total-row">
                    <span class="total-label">المجموع الفرعي:</span>
                    <span class="total-value">${this.formatCurrency(subtotal)}</span>
                </div>
                ${discount > 0 ? `
                <div class="total-row">
                    <span class="total-label">الخصم:</span>
                    <span class="total-value">${this.formatCurrency(discount)}</span>
                </div>
                ` : ''}
                ${tax > 0 ? `
                <div class="total-row">
                    <span class="total-label">ضريبة القيمة المضافة (${invoice.taxRate || 15}%):</span>
                    <span class="total-value">${this.formatCurrency(tax)}</span>
                </div>
                ` : ''}
                <div class="total-row">
                    <span class="total-label">الإجمالي النهائي:</span>
                    <span class="total-value">${this.formatCurrency(total)}</span>
                </div>
            </div>
        `;
    }

    /**
     * Generate invoice footer
     * إنشاء تذييل الفاتورة
     */
    generateInvoiceFooter(company, options) {
        return `
            <div class="signature-section">
                <div class="signature-box">
                    <div class="signature-line">توقيع العميل</div>
                </div>
                <div class="signature-box">
                    <div class="signature-line">توقيع المحاسب</div>
                </div>
            </div>
            
            <div class="invoice-footer">
                <div>شكراً لتعاملكم معنا</div>
                <div>تم إنشاء هذه الفاتورة بواسطة نظام إدارة ${company.companyNameAr || 'مخبز أنوار الحي'}</div>
                <div>تاريخ الطباعة: ${this.formatDateTime(new Date())}</div>
            </div>
        `;
    }

    /**
     * Create print window
     * إنشاء نافذة الطباعة
     */
    createPrintWindow(html, options) {
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        
        if (!printWindow) {
            alert('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة لهذا الموقع.');
            return;
        }

        printWindow.document.write(html);
        printWindow.document.close();

        // Wait for content to load then focus and print
        printWindow.onload = function() {
            printWindow.focus();
            
            // Print multiple copies if specified
            for (let i = 0; i < options.copies; i++) {
                setTimeout(() => {
                    printWindow.print();
                }, i * 1000);
            }
        };
    }

    /**
     * Print receipt (thermal printer format)
     * طباعة إيصال (تنسيق الطابعة الحرارية)
     */
    printReceipt(invoice, options = {}) {
        const receiptOptions = {
            width: '80mm',
            fontSize: '12px',
            showLogo: false,
            ...options
        };

        const receiptHTML = this.generateReceiptHTML(invoice, receiptOptions);
        this.createPrintWindow(receiptHTML, receiptOptions);
    }

    /**
     * Generate thermal receipt HTML
     * إنشاء HTML للإيصال الحراري
     */
    generateReceiptHTML(invoice, options) {
        const company = this.companySettings;
        
        return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>إيصال ${invoice.number}</title>
            <style>
                @page { size: ${options.width} auto; margin: 0; }
                body { 
                    font-family: 'Courier New', monospace; 
                    font-size: ${options.fontSize}; 
                    margin: 0; 
                    padding: 10px; 
                    width: ${options.width};
                    direction: rtl;
                }
                .center { text-align: center; }
                .bold { font-weight: bold; }
                .line { border-top: 1px dashed #000; margin: 5px 0; }
                .item { display: flex; justify-content: space-between; }
                .total { font-size: 14px; font-weight: bold; }
            </style>
        </head>
        <body>
            <div class="center bold">${company.companyNameAr || 'مخبز أنوار الحي'}</div>
            <div class="center">${company.phone || ''}</div>
            <div class="line"></div>
            <div>رقم الفاتورة: ${invoice.number}</div>
            <div>التاريخ: ${this.formatDateTime(new Date())}</div>
            <div class="line"></div>
            ${invoice.items.map(item => `
                <div class="item">
                    <span>${item.name}</span>
                    <span>${this.formatCurrency(item.total)}</span>
                </div>
                <div style="font-size: 10px; color: #666;">
                    ${item.quantity} × ${this.formatCurrency(item.price)}
                </div>
            `).join('')}
            <div class="line"></div>
            <div class="item total">
                <span>الإجمالي:</span>
                <span>${this.formatCurrency(invoice.total)}</span>
            </div>
            <div class="line"></div>
            <div class="center">شكراً لزيارتكم</div>
            <script>window.onload = () => { setTimeout(() => window.print(), 500); };</script>
        </body>
        </html>
        `;
    }

    // Helper methods
    getCompanySettings() {
        return JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
    }

    getPrintSettings() {
        return JSON.parse(localStorage.getItem('anwar_bakery_print_settings') || '{}');
    }

    getCustomerById(id) {
        const customers = JSON.parse(localStorage.getItem('anwar_bakery_customers') || '[]');
        return customers.find(c => c.id === id);
    }

    getSupplierById(id) {
        const suppliers = JSON.parse(localStorage.getItem('anwar_bakery_suppliers') || '[]');
        return suppliers.find(s => s.id === id);
    }

    getInvoiceTitle(type) {
        const titles = {
            'sales': 'فاتورة مبيعات',
            'purchase': 'فاتورة مشتريات',
            'sales_return': 'فاتورة مرتجع مبيعات',
            'purchase_return': 'فاتورة مرتجع مشتريات'
        };
        return titles[type] || 'فاتورة';
    }

    formatDate(date) {
        return new Date(date).toLocaleDateString('ar-SA');
    }

    formatTime(time) {
        return new Date(time).toLocaleTimeString('ar-SA');
    }

    formatDateTime(datetime) {
        return new Date(datetime).toLocaleString('ar-SA');
    }

    formatNumber(number) {
        return parseFloat(number).toLocaleString('ar-SA');
    }

    formatCurrency(amount) {
        const currency = this.companySettings.currency || 'ر.س';
        return `${parseFloat(amount).toLocaleString('ar-SA', { minimumFractionDigits: 2 })} ${currency}`;
    }
}

// Create global print instance
window.advancedPrint = new AdvancedPrint();

// Export functions for global use
window.printInvoice = (invoice, options) => window.advancedPrint.printInvoice(invoice, options);
window.printReceipt = (invoice, options) => window.advancedPrint.printReceipt(invoice, options);
