<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المخازن - نظام إدارة مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- الشريط الجانبي -->
    <div class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out" id="sidebar">
        <div class="flex items-center justify-center h-16 px-4 gradient-bg">
            <h1 class="text-xl font-bold text-white">🏪 مخبز أنوار الحي</h1>
        </div>
        
        <nav class="flex-1 overflow-y-auto px-2 py-4 space-y-1">
            <a href="dashboard.html" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                <span class="mr-3">📊</span>
                لوحة التحكم
            </a>
            <a href="products.html" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                <span class="mr-3">📦</span>
                المنتجات
            </a>
            <a href="warehouses.html" class="flex items-center px-4 py-2 text-white bg-blue-600 rounded-lg">
                <span class="mr-3">🏬</span>
                المخازن
            </a>
            <a href="customers.html" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                <span class="mr-3">👥</span>
                العملاء
            </a>
            <a href="suppliers.html" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                <span class="mr-3">🏭</span>
                الموردين
            </a>
            <a href="invoices.html" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                <span class="mr-3">📄</span>
                الفواتير
            </a>
            <a href="cash-registers.html" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                <span class="mr-3">💰</span>
                الصناديق
            </a>
            <a href="reports.html" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                <span class="mr-3">📈</span>
                التقارير
            </a>
        </nav>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="mr-64 min-h-screen">
        <!-- الشريط العلوي -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">إدارة المخازن</h1>
                        <p class="text-gray-600">إدارة وتنظيم المخازن والمواقع</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button onclick="openAddWarehouseModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                            <span class="ml-2">➕</span>
                            إضافة مخزن جديد
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- الإحصائيات -->
        <div class="px-6 py-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg">
                            <span class="text-2xl">🏬</span>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">إجمالي المخازن</p>
                            <p class="text-2xl font-bold text-gray-900" id="totalWarehouses">0</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <span class="text-2xl">✅</span>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">المخازن النشطة</p>
                            <p class="text-2xl font-bold text-gray-900" id="activeWarehouses">0</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-100 rounded-lg">
                            <span class="text-2xl">📊</span>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">السعة الإجمالية</p>
                            <p class="text-2xl font-bold text-gray-900" id="totalCapacity">0</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-yellow-100 rounded-lg">
                            <span class="text-2xl">📈</span>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">نسبة الاستخدام</p>
                            <p class="text-2xl font-bold text-gray-900" id="utilizationPercentage">0%</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أدوات البحث والفلترة -->
            <div class="bg-white rounded-lg shadow mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">البحث والفلترة</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                            <input type="text" id="searchInput" placeholder="ابحث عن مخزن..." 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                            <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button onclick="filterWarehouses()" class="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md">
                                تطبيق الفلتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول المخازن -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">قائمة المخازن</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم المخزن</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الوصف والموقع</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المسؤول</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">السعة</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاستخدام</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="warehousesTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- سيتم ملء البيانات بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل مخزن -->
    <div id="warehouseModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="flex items-center justify-between mb-4">
                <h3 id="warehouseModalTitle" class="text-lg font-medium text-gray-900">إضافة مخزن جديد</h3>
                <button onclick="closeWarehouseModal()" class="text-gray-400 hover:text-gray-600">
                    <span class="text-2xl">&times;</span>
                </button>
            </div>
            
            <form id="warehouseForm" onsubmit="event.preventDefault(); saveWarehouse();">
                <input type="hidden" id="warehouseId">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم المخزن *</label>
                        <input type="text" id="name" name="name" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
                        <input type="text" id="location" name="location" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                    <textarea id="description" name="description" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">المسؤول</label>
                        <input type="text" id="manager" name="manager" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                        <input type="tel" id="phone" name="phone" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">السعة الإجمالية</label>
                        <input type="number" id="capacity" name="capacity" min="0" step="0.01" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الاستخدام الحالي</label>
                        <input type="number" id="currentUtilization" name="currentUtilization" min="0" step="0.01" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                
                <div class="mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" id="isActive" name="isActive" checked 
                               class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="mr-2 text-sm text-gray-700">مخزن نشط</span>
                    </label>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeWarehouseModal()" 
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        إلغاء
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="warehouses.js"></script>
</body>
</html>
