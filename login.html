<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - مخبز أنور</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .login-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="login-bg min-h-screen flex items-center justify-center p-4">
    <div class="max-w-md w-full space-y-8">
        <!-- Logo and Title -->
        <div class="text-center">
            <div class="mx-auto h-24 w-24 bg-white rounded-full flex items-center justify-center mb-6 shadow-lg">
                <span class="text-blue-600 text-3xl font-bold">أ</span>
            </div>
            <h2 class="text-4xl font-bold text-white mb-2">
                مخبز أنور
            </h2>
            <p class="text-blue-100 text-lg">
                نظام إدارة المخبز المتكامل
            </p>
        </div>

        <!-- Login Form -->
        <div class="bg-white rounded-2xl shadow-2xl p-8">
            <div class="mb-6">
                <h3 class="text-2xl font-bold text-gray-900 text-center">تسجيل الدخول</h3>
                <p class="text-gray-600 text-center mt-2">أدخل بياناتك للوصول للنظام</p>
            </div>

            <form id="loginForm" class="space-y-6">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                        اسم المستخدم
                    </label>
                    <input
                        id="username"
                        name="username"
                        type="text"
                        required
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                        placeholder="أدخل اسم المستخدم"
                        autocomplete="username"
                    />
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        كلمة المرور
                    </label>
                    <div class="relative">
                        <input
                            id="password"
                            name="password"
                            type="password"
                            required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            placeholder="أدخل كلمة المرور"
                            autocomplete="current-password"
                        />
                        <button
                            type="button"
                            onclick="togglePassword()"
                            class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        >
                            <span id="eyeIcon">👁️</span>
                        </button>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input
                            id="remember"
                            name="remember"
                            type="checkbox"
                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label for="remember" class="mr-2 block text-sm text-gray-900">
                            تذكرني
                        </label>
                    </div>
                    <div class="text-sm">
                        <a href="#" class="font-medium text-blue-600 hover:text-blue-500">
                            نسيت كلمة المرور؟
                        </a>
                    </div>
                </div>

                <div id="errorMessage" class="hidden bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm">
                </div>

                <div>
                    <button
                        type="submit"
                        class="w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all transform hover:scale-105"
                    >
                        <span id="loginText">تسجيل الدخول</span>
                        <span id="loadingText" class="hidden">جاري التحقق...</span>
                    </button>
                </div>
            </form>

            <!-- Demo Users -->
            <div class="mt-8 p-4 bg-gray-50 rounded-lg">
                <h4 class="text-sm font-medium text-gray-700 mb-3">المستخدمين التجريبيين:</h4>
                <div class="space-y-2 text-xs text-gray-600">
                    <div class="flex justify-between">
                        <span>مدير النظام:</span>
                        <span>admin / admin123</span>
                    </div>
                    <div class="flex justify-between">
                        <span>مدير المخبز:</span>
                        <span>manager / manager123</span>
                    </div>
                    <div class="flex justify-between">
                        <span>كاشير:</span>
                        <span>cashier / cashier123</span>
                    </div>
                </div>
                <button onclick="quickLogin('admin', 'admin123')" class="mt-3 w-full text-xs bg-blue-100 text-blue-700 py-2 rounded hover:bg-blue-200 transition-colors">
                    دخول سريع كمدير
                </button>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center text-blue-100 text-sm">
            <p>&copy; 2024 مخبز أنور. جميع الحقوق محفوظة.</p>
            <p class="mt-1">نسخة 1.0.0</p>
        </div>
    </div>

    <script>
        // Default users for demo
        const defaultUsers = [
            {
                id: 1,
                username: 'admin',
                password: 'admin123',
                fullName: 'مدير النظام',
                email: '<EMAIL>',
                role: 'admin',
                permissions: {
                    dashboard: true,
                    products: true,
                    customers: true,
                    suppliers: true,
                    employees: true,
                    invoices: true,
                    vouchers: true,
                    reports: true,
                    settings: true,
                    users: true,
                    branches: true,
                    cashRegisters: true,
                    chartOfAccounts: true
                },
                isActive: true,
                branchId: 1,
                branchName: 'الفرع الرئيسي',
                createdAt: new Date().toISOString().split('T')[0],
                lastLogin: null,
                loginAttempts: 0,
                isLocked: false
            },
            {
                id: 2,
                username: 'manager',
                password: 'manager123',
                fullName: 'مدير المخبز',
                email: '<EMAIL>',
                role: 'manager',
                permissions: {
                    dashboard: true,
                    products: true,
                    customers: true,
                    suppliers: true,
                    employees: true,
                    invoices: true,
                    vouchers: true,
                    reports: true,
                    settings: false,
                    users: false,
                    branches: false,
                    cashRegisters: true,
                    chartOfAccounts: true
                },
                isActive: true,
                branchId: 1,
                branchName: 'الفرع الرئيسي',
                createdAt: new Date().toISOString().split('T')[0],
                lastLogin: null,
                loginAttempts: 0,
                isLocked: false
            },
            {
                id: 3,
                username: 'cashier',
                password: 'cashier123',
                fullName: 'كاشير المخبز',
                email: '<EMAIL>',
                role: 'cashier',
                permissions: {
                    dashboard: true,
                    products: true,
                    customers: true,
                    invoices: true,
                    vouchers: true,
                    reports: false,
                    settings: false,
                    users: false,
                    branches: false,
                    cashRegisters: true,
                    chartOfAccounts: false
                },
                isActive: true,
                branchId: 1,
                branchName: 'الفرع الرئيسي',
                createdAt: new Date().toISOString().split('T')[0],
                lastLogin: null,
                loginAttempts: 0,
                isLocked: false
            }
        ];

        // Initialize default users if not exists
        function initializeUsers() {
            let existingUsers = localStorage.getItem('anwar_bakery_users');
            let users = [];

            if (existingUsers) {
                users = JSON.parse(existingUsers);
            }

            // Always ensure admin user exists with correct credentials
            const adminIndex = users.findIndex(u => u.username === 'admin');
            if (adminIndex === -1) {
                // Add admin user if not exists
                users.unshift(defaultUsers[0]);
                console.log('✅ تم إنشاء المستخدم الإداري');
            } else {
                // Update admin user credentials
                users[adminIndex] = {
                    ...users[adminIndex],
                    username: 'admin',
                    password: 'admin123',
                    fullName: 'مدير النظام',
                    role: 'admin',
                    isActive: true,
                    permissions: defaultUsers[0].permissions
                };
                console.log('✅ تم تحديث بيانات المستخدم الإداري');
            }

            // Add other default users if they don't exist
            defaultUsers.slice(1).forEach(defaultUser => {
                const userExists = users.find(u => u.username === defaultUser.username);
                if (!userExists) {
                    users.push(defaultUser);
                }
            });

            localStorage.setItem('anwar_bakery_users', JSON.stringify(users));
            console.log('📋 المستخدمين المتاحين:', users.map(u => u.username));
        }

        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const eyeIcon = document.getElementById('eyeIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                eyeIcon.textContent = '👁️';
            }
        }

        // Quick login function
        function quickLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            handleLogin();
        }

        // Show error message
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.classList.remove('hidden');

            setTimeout(() => {
                errorDiv.classList.add('hidden');
            }, 5000);
        }

        // Handle login
        function handleLogin() {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const remember = document.getElementById('remember').checked;

            if (!username || !password) {
                showError('يرجى إدخال اسم المستخدم وكلمة المرور');
                return;
            }

            // Show loading
            document.getElementById('loginText').classList.add('hidden');
            document.getElementById('loadingText').classList.remove('hidden');

            // Simulate loading delay
            setTimeout(() => {
                const users = JSON.parse(localStorage.getItem('anwar_bakery_users') || '[]');
                const user = users.find(u => u.username === username && u.password === password);

                if (user) {
                    if (!user.isActive) {
                        showError('هذا المستخدم غير مفعل. يرجى التواصل مع المدير.');
                        resetLoginButton();
                        return;
                    }

                    // Save session
                    const sessionData = {
                        userId: user.id,
                        username: user.username,
                        fullName: user.fullName,
                        role: user.role,
                        loginTime: new Date().toISOString(),
                        remember: remember
                    };

                    if (remember) {
                        localStorage.setItem('anwar_bakery_session', JSON.stringify(sessionData));
                    } else {
                        sessionStorage.setItem('anwar_bakery_session', JSON.stringify(sessionData));
                    }

                    // Success message
                    showSuccess('تم تسجيل الدخول بنجاح! جاري التحويل...');

                    // Redirect to dashboard
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1500);

                } else {
                    showError('اسم المستخدم أو كلمة المرور غير صحيحة');
                    resetLoginButton();
                }
            }, 1000);
        }

        // Show success message
        function showSuccess(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.className = 'bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-lg text-sm';
        }

        // Reset login button
        function resetLoginButton() {
            document.getElementById('loginText').classList.remove('hidden');
            document.getElementById('loadingText').classList.add('hidden');
        }

        // Form submit handler
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            handleLogin();
        });

        // Check if already logged in
        function checkExistingSession() {
            const session = localStorage.getItem('anwar_bakery_session') ||
                           sessionStorage.getItem('anwar_bakery_session');

            if (session) {
                const sessionData = JSON.parse(session);
                // Check if session is still valid (less than 24 hours for localStorage, session for sessionStorage)
                const loginTime = new Date(sessionData.loginTime);
                const now = new Date();
                const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

                if (sessionData.remember && hoursDiff < 24) {
                    window.location.href = 'dashboard.html';
                } else if (!sessionData.remember) {
                    window.location.href = 'dashboard.html';
                }
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeUsers();
            checkExistingSession();

            // Focus on username field
            document.getElementById('username').focus();
        });

        // Enter key handling
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleLogin();
            }
        });
    </script>
</body>
</html>
