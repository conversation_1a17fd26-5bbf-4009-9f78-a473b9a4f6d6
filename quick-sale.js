// Quick Sale System
let cart = [];
let products = [];
let calculatorValue = '';
let currentTotal = 0;

// Load products from localStorage or create empty array
let sampleProducts = [];

// Check authentication
function checkAuth() {
    const session = localStorage.getItem('anwar_bakery_session') ||
                   sessionStorage.getItem('anwar_bakery_session');

    if (!session) {
        window.location.href = 'login.html';
        return null;
    }

    return JSON.parse(session);
}

// Load user info
function loadUserInfo() {
    const session = checkAuth();
    if (session) {
        const userFullNameElement = document.getElementById('userFullName');
        const userRoleElement = document.getElementById('userRole');

        if (userFullNameElement) userFullNameElement.textContent = session.fullName;
        if (userRoleElement) userRoleElement.textContent = session.role;
    }
}

// Load company info
function loadCompanyInfo() {
    const savedData = localStorage.getItem('anwar_bakery_company');
    if (savedData) {
        const companyData = JSON.parse(savedData);
        const sidebarElement = document.getElementById('sidebarCompanyName');
        if (sidebarElement && companyData.companyNameAr) {
            sidebarElement.textContent = companyData.companyNameAr;
        }
    }
}

// Load products - FRESH START
function loadProducts() {
    try {
        // Clear any existing products first
        products = [];

        // Force reload from localStorage
        const savedProducts = localStorage.getItem('anwar_bakery_products');

        if (!savedProducts) {
            // No products in database at all
            products = [];
            renderProducts();
            showMessage('لا توجد منتجات في قاعدة البيانات. يرجى إضافة منتجات من صفحة إدارة الأصناف.', 'warning');
            return;
        }

        let parsedProducts;
        try {
            parsedProducts = JSON.parse(savedProducts);
        } catch (parseError) {
            console.error('خطأ في قراءة بيانات المنتجات:', parseError);
            products = [];
            renderProducts();
            showMessage('خطأ في قراءة بيانات المنتجات', 'error');
            return;
        }

        if (!Array.isArray(parsedProducts) || parsedProducts.length === 0) {
            // Empty or invalid products array
            products = [];
            renderProducts();
            showMessage('لا توجد منتجات في قاعدة البيانات.', 'info');
            return;
        }

        // Filter and map products with strict validation
        products = parsedProducts
            .filter(product => {
                // Must have valid ID and name
                if (!product.id || !product.itemName) {
                    return false;
                }

                // Must be active (default to true if not specified)
                if (product.isActive === false) {
                    return false;
                }

                // Must have stock > 0
                const stock = parseInt(product.currentStock) || 0;
                if (stock <= 0) {
                    return false;
                }

                return true;
            })
            .map(product => {
                const sellingPrice = parseFloat(product.sellingPrice) || 0;
                const costPrice = parseFloat(product.costPrice) || 0;
                const finalPrice = sellingPrice > 0 ? sellingPrice : costPrice;

                return {
                    id: product.id,
                    name: product.itemName.trim(),
                    price: finalPrice,
                    category: product.itemType || product.category || 'عام',
                    image: getProductIcon(product.itemType || product.category),
                    stock: parseInt(product.currentStock) || 0,
                    code: product.itemCode || '',
                    unit: getProductUnit(product)
                };
            });

        // Render products
        renderProducts();

        if (products.length === 0) {
            showMessage('لا توجد منتجات متاحة للبيع (لا يوجد مخزون أو منتجات غير نشطة).', 'warning');
        } else {
            console.log(`تم تحميل ${products.length} منتج للبيع`);
        }

    } catch (error) {
        console.error('خطأ في تحميل المنتجات:', error);
        products = [];
        renderProducts();
        showMessage('حدث خطأ في تحميل المنتجات', 'error');
    }
}

// Get product unit
function getProductUnit(product) {
    if (product.units && Array.isArray(product.units) && product.units.length > 0) {
        return product.units[0].name || product.units[0].unitName || 'قطعة';
    }
    return product.unit || 'قطعة';
}

// Get product icon based on type/category
function getProductIcon(type) {
    if (!type) return '📦';

    const typeStr = type.toString().toLowerCase();
    const icons = {
        // English categories
        'raw_materials': '🌾',
        'products': '🍞',
        'bread': '🥖',
        'pastry': '🥐',
        'cake': '🍰',
        'drinks': '🧃',
        'beverages': '☕',
        'services': '🔧',
        'supplies': '📦',
        'dairy': '🥛',
        'meat': '🥩',
        'vegetables': '🥬',
        'fruits': '🍎',
        'sweets': '🍭',
        'chocolate': '🍫',
        'cookies': '🍪',
        'pizza': '🍕',
        'sandwich': '🥪',
        'flour': '🌾',
        'sugar': '🍯',
        'oil': '🫒',
        'spices': '🌶️',

        // Arabic categories
        'خبز': '🥖',
        'معجنات': '🥐',
        'كيك': '🍰',
        'مشروبات': '🧃',
        'خامات': '🌾',
        'منتجات': '🍞',
        'خدمات': '🔧',
        'لوازم': '📦',
        'ألبان': '🥛',
        'لحوم': '🥩',
        'خضروات': '🥬',
        'فواكه': '🍎',
        'حلويات': '🍭',
        'شوكولاتة': '🍫',
        'بسكويت': '🍪',
        'بيتزا': '🍕',
        'ساندويش': '🥪',
        'دقيق': '🌾',
        'سكر': '🍯',
        'زيت': '🫒',
        'بهارات': '🌶️',
        'عام': '📦',
        'أخرى': '📦'
    };

    // Try exact match first
    if (icons[typeStr]) {
        return icons[typeStr];
    }

    // Try partial matches
    for (const [key, icon] of Object.entries(icons)) {
        if (typeStr.includes(key) || key.includes(typeStr)) {
            return icon;
        }
    }

    return '📦';
}

// Render products grid
function renderProducts(filteredProducts = products) {
    const grid = document.getElementById('productsGrid');
    if (!grid) return;

    grid.innerHTML = '';

    if (filteredProducts.length === 0) {
        grid.innerHTML = `
            <div class="col-span-full text-center py-8 text-gray-500">
                <div class="text-6xl mb-4">📦</div>
                <p class="text-lg font-medium">لا توجد منتجات متاحة</p>
                <p class="text-sm">يرجى إضافة منتجات من صفحة إدارة الأصناف</p>
            </div>
        `;
        return;
    }

    filteredProducts.forEach(product => {
        const productCard = document.createElement('div');
        productCard.className = 'product-card bg-white rounded-lg p-3 cursor-pointer hover:shadow-lg transition-all duration-200 border border-gray-200 hover:border-blue-400 hover:bg-blue-50';
        productCard.onclick = () => addToCart(product);

        // Ensure we have valid data
        const productName = product.name || 'منتج غير محدد';
        const productPrice = parseFloat(product.price) || 0;
        const productStock = parseInt(product.stock) || 0;
        const productImage = product.image || '📦';

        productCard.innerHTML = `
            <div class="text-center">
                <div class="text-2xl mb-2 flex justify-center items-center h-12">${productImage}</div>
                <h4 class="font-semibold text-gray-900 mb-1 text-sm leading-tight min-h-[2.5rem] flex items-center justify-center">${productName}</h4>
                <p class="text-blue-600 font-bold text-lg mb-1">${formatCurrency(productPrice)}</p>
                <p class="text-xs text-gray-500 bg-gray-100 rounded px-2 py-1">متوفر: ${productStock}</p>
                ${productStock <= 5 ? '<p class="text-xs text-red-500 mt-1">⚠️ مخزون منخفض</p>' : ''}
            </div>
        `;

        grid.appendChild(productCard);
    });
}

// Add product to cart
function addToCart(product) {
    if (product.stock <= 0) {
        showMessage('هذا المنتج غير متوفر في المخزون', 'error');
        return;
    }

    const existingItem = cart.find(item => item.id === product.id);

    if (existingItem) {
        if (existingItem.quantity < product.stock) {
            existingItem.quantity++;
        } else {
            showMessage('لا يمكن إضافة المزيد من هذا المنتج', 'warning');
            return;
        }
    } else {
        cart.push({
            id: product.id,
            name: product.name,
            price: product.price,
            quantity: 1,
            image: product.image,
            code: product.code,
            unit: product.unit
        });
    }

    renderCart();
    calculateTotal();
    showMessage(`تم إضافة ${product.name} إلى السلة`, 'success');
}

// Remove from cart
function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    renderCart();
    calculateTotal();
}

// Update quantity in cart
function updateQuantity(productId, newQuantity) {
    const item = cart.find(item => item.id === productId);
    const product = products.find(p => p.id === productId);

    if (item && product) {
        if (newQuantity <= 0) {
            removeFromCart(productId);
        } else if (newQuantity <= product.stock) {
            item.quantity = newQuantity;
            renderCart();
            calculateTotal();
        } else {
            showMessage('الكمية المطلوبة غير متوفرة في المخزون', 'warning');
        }
    }
}

// Render cart
function renderCart() {
    const cartContainer = document.getElementById('cartItems');
    if (!cartContainer) return;

    if (cart.length === 0) {
        cartContainer.innerHTML = `
            <div class="text-center py-8 text-gray-500">
                <div class="text-3xl mb-2">🛒</div>
                <p class="text-sm">السلة فارغة</p>
                <p class="text-xs">أضف منتجات لبدء البيع</p>
            </div>
        `;
        return;
    }

    cartContainer.innerHTML = '';

    cart.forEach(item => {
        const cartItem = document.createElement('div');
        cartItem.className = 'cart-item bg-white rounded-lg p-3 flex items-center justify-between border border-gray-200 mb-2';

        // Ensure we have valid data
        const itemName = item.name || 'منتج غير محدد';
        const itemPrice = parseFloat(item.price) || 0;
        const itemQuantity = parseInt(item.quantity) || 1;
        const itemImage = item.image || '📦';
        const itemTotal = itemPrice * itemQuantity;

        cartItem.innerHTML = `
            <div class="flex items-center flex-1">
                <span class="text-lg ml-3 w-8 text-center">${itemImage}</span>
                <div class="flex-1">
                    <h5 class="font-medium text-gray-900 text-sm">${itemName}</h5>
                    <p class="text-xs text-gray-600">${formatCurrency(itemPrice)} × ${itemQuantity}</p>
                    <p class="text-sm font-semibold text-blue-600">${formatCurrency(itemTotal)}</p>
                </div>
            </div>
            <div class="flex items-center space-x-1">
                <button onclick="updateQuantity(${item.id}, ${itemQuantity - 1})"
                        class="w-7 h-7 bg-red-500 text-white rounded-full hover:bg-red-600 flex items-center justify-center text-sm">
                    -
                </button>
                <span class="w-8 text-center font-semibold text-sm">${itemQuantity}</span>
                <button onclick="updateQuantity(${item.id}, ${itemQuantity + 1})"
                        class="w-7 h-7 bg-green-500 text-white rounded-full hover:bg-green-600 flex items-center justify-center text-sm">
                    +
                </button>
                <button onclick="removeFromCart(${item.id})"
                        class="w-7 h-7 bg-gray-500 text-white rounded-full hover:bg-gray-600 flex items-center justify-center text-sm">
                    ×
                </button>
            </div>
        `;

        cartContainer.appendChild(cartItem);
    });
}

// Calculate total
function calculateTotal() {
    const subtotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);
    const tax = subtotal * 0.15; // 15% VAT
    currentTotal = subtotal; // Keep currentTotal as subtotal for compatibility

    const totalElement = document.getElementById('totalAmount');
    if (totalElement) {
        // Get company settings for currency
        const company = getCompanyInfo();
        const currency = company.currency || 'ر.س';

        // Display total with tax
        const totalWithTax = subtotal + tax;
        totalElement.textContent = `${totalWithTax.toFixed(2)} ${currency}`;
    }

    // Update any subtotal displays if they exist
    const subtotalElement = document.getElementById('subtotalAmount');
    if (subtotalElement) {
        const company = getCompanyInfo();
        const currency = company.currency || 'ر.س';
        subtotalElement.textContent = `${subtotal.toFixed(2)} ${currency}`;
    }

    const taxElement = document.getElementById('taxAmount');
    if (taxElement) {
        const company = getCompanyInfo();
        const currency = company.currency || 'ر.س';
        taxElement.textContent = `${tax.toFixed(2)} ${currency}`;
    }
}

// Get company info helper
function getCompanyInfo() {
    try {
        return JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
    } catch {
        return {};
    }
}

// Clear cart
function clearCart() {
    if (cart.length === 0) return;

    if (confirm('هل أنت متأكد من مسح جميع العناصر من السلة؟')) {
        cart = [];
        renderCart();
        calculateTotal();
        showMessage('تم مسح السلة', 'info');
    }
}

// Calculator functions
function addToCalculator(value) {
    calculatorValue += value;
    document.getElementById('calculatorDisplay').value = calculatorValue;
}

function clearCalculator() {
    calculatorValue = '';
    document.getElementById('calculatorDisplay').value = '';
}

function addCustomAmount() {
    const amount = parseFloat(calculatorValue);
    if (amount && amount > 0) {
        const customItem = {
            id: Date.now(),
            name: 'مبلغ مخصص',
            price: amount,
            quantity: 1,
            image: '💰'
        };

        cart.push(customItem);
        renderCart();
        calculateTotal();
        clearCalculator();
        showMessage(`تم إضافة مبلغ ${formatCurrency(amount)}`, 'success');
    } else {
        showMessage('يرجى إدخال مبلغ صحيح', 'error');
    }
}

// Reload products manually
function reloadProducts() {
    console.log('🔄 Manual reload requested...');

    // Clear current products
    products = [];

    // Clear the grid
    const grid = document.getElementById('productsGrid');
    if (grid) {
        grid.innerHTML = '<div class="col-span-full text-center py-8"><div class="text-4xl mb-2">⏳</div><p>جاري إعادة تحميل المنتجات...</p></div>';
    }

    // Reload after a short delay to show loading
    setTimeout(() => {
        loadProducts();
        showMessage('تم إعادة تحميل المنتجات', 'success');
    }, 500);
}

// Search products
function searchProducts() {
    const searchTerm = document.getElementById('productSearch').value.toLowerCase();
    const categoryFilter = document.getElementById('categoryFilter').value;

    let filtered = products.filter(product =>
        product.name.toLowerCase().includes(searchTerm)
    );

    if (categoryFilter) {
        filtered = filtered.filter(product => product.category === categoryFilter);
    }

    renderProducts(filtered);
}

// Filter by category
function filterByCategory() {
    searchProducts();
}

// Create sample bank if none exist
function createSampleBankIfNeeded() {
    try {
        const banks = JSON.parse(localStorage.getItem('anwar_bakery_banks') || '[]');
        if (banks.length === 0) {
            console.log('No banks found, creating sample bank...');
            const sampleBank = {
                id: Date.now(),
                bankName: 'البنك الأهلي السعودي',
                accountNumber: '*********',
                accountName: 'مخبز أنوار الحي',
                currentBalance: 0,
                isActive: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            banks.push(sampleBank);
            localStorage.setItem('anwar_bakery_banks', JSON.stringify(banks));
            console.log('Sample bank created:', sampleBank.bankName);
        }
    } catch (error) {
        console.error('Error creating sample bank:', error);
    }
}

// Initialize cash payment on page load
function initializeCashPayment() {
    console.log('Initializing cash payment...');
    loadCashRegisters();
}

// Load banks from localStorage
function loadBanks() {
    try {
        const banks = JSON.parse(localStorage.getItem('anwar_bakery_banks') || '[]');
        const bankSelect = document.getElementById('bankSelect');

        console.log('Loading banks:', banks.length, 'banks found');

        if (!bankSelect) {
            console.error('Bank select element not found');
            return;
        }

        // Get current branch to filter banks
        const branches = JSON.parse(localStorage.getItem('anwar_bakery_branches') || '[]');
        const currentBranch = branches.find(branch => branch.isMain) || branches[0];
        const currentBranchId = currentBranch ? currentBranch.id : null;

        console.log('Current branch ID:', currentBranchId);

        // Filter banks by current branch
        const branchBanks = banks.filter(bank => {
            // If bank has no branch specified, show it for all branches
            // If bank has branch specified, only show for matching branch
            return !bank.branchId || bank.branchId === currentBranchId;
        });

        console.log('Banks for current branch:', branchBanks.length);

        // Always show the dropdown, even if empty
        bankSelect.innerHTML = '<option value="">اختر البنك</option>';

        if (branchBanks.length === 0) {
            console.warn('No banks found for current branch');
            // Add a message option but keep the dropdown functional
            const option = document.createElement('option');
            option.value = '';
            option.textContent = 'لا توجد بنوك لهذا الفرع - يرجى إضافة بنك من إدارة البنوك';
            option.disabled = true;
            option.style.color = '#999';
            bankSelect.appendChild(option);
        } else {
            branchBanks.forEach(bank => {
                const option = document.createElement('option');
                option.value = bank.id;
                option.textContent = bank.bankName || bank.name || `بنك ${bank.id}`;
                bankSelect.appendChild(option);
                console.log('Added bank for branch:', bank.bankName || bank.name);
            });
        }

        // Add change listener
        bankSelect.onchange = validatePaymentSelection;
        console.log('Banks loaded successfully');
    } catch (error) {
        console.error('Error loading banks:', error);
        const bankSelect = document.getElementById('bankSelect');
        if (bankSelect) {
            bankSelect.innerHTML = '<option value="">خطأ في تحميل البنوك</option>';
        }
    }
}

// Load cash registers from localStorage filtered by current branch
function loadCashRegisters() {
    try {
        const cashRegisters = JSON.parse(localStorage.getItem('anwar_bakery_cash_registers') || '[]');
        const branches = JSON.parse(localStorage.getItem('anwar_bakery_branches') || '[]');
        const cashRegisterSelect = document.getElementById('cashRegisterSelect');

        console.log('Loading cash registers:', cashRegisters.length, 'total registers found');

        // Get current branch to filter cash registers
        const currentBranch = branches.find(branch => branch.isMain) || branches[0];
        const currentBranchId = currentBranch ? currentBranch.id : null;

        console.log('Current branch ID for cash registers:', currentBranchId);

        // Filter cash registers by current branch
        const branchCashRegisters = cashRegisters.filter(register => {
            // If register has no branch specified, show it for all branches
            // If register has branch specified, only show for matching branch
            return !register.branchId || register.branchId === currentBranchId;
        });

        console.log('Cash registers for current branch:', branchCashRegisters.length);

        cashRegisterSelect.innerHTML = '<option value="">اختر الصندوق</option>';

        if (branchCashRegisters.length === 0) {
            console.warn('No cash registers found for current branch');
            const option = document.createElement('option');
            option.value = '';
            option.textContent = 'لا توجد صناديق لهذا الفرع - يرجى إضافة صندوق من إدارة الصناديق';
            option.disabled = true;
            option.style.color = '#999';
            cashRegisterSelect.appendChild(option);
        } else {
            branchCashRegisters.forEach(register => {
                const option = document.createElement('option');
                option.value = register.id;
                option.textContent = register.registerName || `صندوق ${register.id}`;
                cashRegisterSelect.appendChild(option);
                console.log('Added cash register for branch:', register.registerName);
            });
        }

        // Add change listener
        cashRegisterSelect.onchange = validateCashRegisterSelection;

        // Auto-select main register if available in current branch
        const mainRegister = branchCashRegisters.find(reg => reg.isMain);
        if (mainRegister) {
            cashRegisterSelect.value = mainRegister.id;
            validateCashRegisterSelection();
            console.log('Auto-selected main register:', mainRegister.registerName);
        }
    } catch (error) {
        console.error('Error loading cash registers:', error);
    }
}

// Validate cash register selection
function validateCashRegisterSelection() {
    const cashRegisterId = document.getElementById('cashRegisterSelect').value;
    const processBtn = document.getElementById('processPaymentBtn');

    if (cashRegisterId !== '') {
        processBtn.disabled = false;
        processBtn.className = 'w-full bg-green-600 hover:bg-green-700 text-white py-4 rounded-lg font-semibold flex items-center justify-center';
    } else {
        processBtn.disabled = true;
        processBtn.className = 'w-full bg-gray-400 text-white py-4 rounded-lg font-semibold flex items-center justify-center cursor-not-allowed';
    }
}

// Process cash payment
function processPayment() {
    if (cart.length === 0) {
        showMessage('السلة فارغة! يرجى إضافة منتجات أولاً', 'error');
        return;
    }

    const cashRegisterId = document.getElementById('cashRegisterSelect').value;

    if (!cashRegisterId) {
        showMessage('يرجى اختيار الصندوق', 'error');
        return;
    }

    const subtotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);
    const tax = subtotal * 0.15;
    const total = subtotal + tax;

    if (confirm(`تأكيد الدفع النقدي بمبلغ ${formatCurrency(total)}؟`)) {
        // Save sale with cash payment details
        const saleData = saveSale({
            method: 'cash',
            cashRegisterId: cashRegisterId
        });

        if (saleData) {
            // Clear cart after saving
            const cartCopy = [...cart];
            cart = [];
            renderCart();
            calculateTotal();

            showMessage('تم إتمام البيع النقدي بنجاح!', 'success');

            // Show print options
            showPrintOptions(saleData, cartCopy);
        }
    }
}

// Show print options dialog
function showPrintOptions(saleData, cartItems) {
    const printDialog = document.createElement('div');
    printDialog.className = 'fixed inset-0 bg-gray-600 bg-opacity-75 z-50 flex items-center justify-center';
    printDialog.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <span class="ml-2">🖨️</span>
                خيارات الطباعة
            </h3>
            <div class="space-y-3">
                <button onclick="printThermalReceipt(${JSON.stringify(saleData).replace(/"/g, '&quot;')}, ${JSON.stringify(cartItems).replace(/"/g, '&quot;')}); closePrintDialog();"
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-semibold flex items-center justify-center">
                    <span class="ml-2">🧾</span>
                    طباعة حرارية (إيصال)
                </button>
                <button onclick="printStandardReceipt(${JSON.stringify(saleData).replace(/"/g, '&quot;')}, ${JSON.stringify(cartItems).replace(/"/g, '&quot;')}); closePrintDialog();"
                        class="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold flex items-center justify-center">
                    <span class="ml-2">🖨️</span>
                    طباعة عادية (A4)
                </button>
                <button onclick="closePrintDialog();"
                        class="w-full bg-gray-600 hover:bg-gray-700 text-white py-3 rounded-lg font-semibold">
                    تخطي الطباعة
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(printDialog);
}

// Close print dialog
function closePrintDialog() {
    const dialog = document.querySelector('.fixed.inset-0.bg-gray-600');
    if (dialog) {
        document.body.removeChild(dialog);
    }
}

// Save sale
function saveSale(paymentDetails) {
    try {
        const saleNumber = generateSaleNumber();
        const subtotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);
        const tax = subtotal * 0.15; // 15% VAT
        const total = subtotal + tax;

        // Get branch and cash register info
        const branches = JSON.parse(localStorage.getItem('anwar_bakery_branches') || '[]');
        const cashRegisters = JSON.parse(localStorage.getItem('anwar_bakery_cash_registers') || '[]');
        const banks = JSON.parse(localStorage.getItem('anwar_bakery_banks') || '[]');

        const currentBranch = branches.find(branch => branch.isMain) || branches[0];
        const selectedCashRegister = cashRegisters.find(reg => reg.id == paymentDetails.cashRegisterId);
        const selectedBank = banks.find(bank => bank.id == paymentDetails.bankId);

        const sale = {
            id: Date.now(),
            number: saleNumber,
            type: 'quick_sale',
            items: cart.map(item => ({
                id: item.id,
                name: item.name,
                code: item.code || '',
                quantity: item.quantity,
                unit: item.unit || 'قطعة',
                price: item.price,
                total: item.price * item.quantity
            })),
            subtotal: subtotal,
            tax: tax,
            total: total,
            paymentMethod: paymentDetails.method,
            bankId: paymentDetails.bankId || null,
            bankName: selectedBank ? selectedBank.bankName : null,
            cashRegisterId: paymentDetails.cashRegisterId || null,
            cashRegisterName: selectedCashRegister ? selectedCashRegister.registerName : null,
            branchId: currentBranch ? currentBranch.id : null,
            branchName: currentBranch ? currentBranch.branchName : 'الفرع الرئيسي',
            date: new Date().toISOString().split('T')[0],
            time: new Date().toISOString(),
            cashier: document.getElementById('userFullName').textContent,
            status: 'completed',
            createdAt: new Date().toISOString()
        };

        // Save to quick sales
        const quickSales = JSON.parse(localStorage.getItem('anwar_bakery_quick_sales') || '[]');
        quickSales.push(sale);
        localStorage.setItem('anwar_bakery_quick_sales', JSON.stringify(quickSales));

        // Also save to main sales invoices
        const salesInvoices = JSON.parse(localStorage.getItem('anwar_bakery_sales_invoices') || '[]');
        salesInvoices.push(sale);
        localStorage.setItem('anwar_bakery_sales_invoices', JSON.stringify(salesInvoices));

        // Update product stock
        updateProductStock();

        // Create accounting entries
        createQuickSaleAccountingEntries(sale);

        // Update cash register balance
        updateCashRegisterBalance(sale);

        // Update sale counter
        updateSaleCounter();

        // Update cash register display
        updateCashRegisterDisplay();

        console.log('Quick sale saved successfully:', sale);
        return sale;
    } catch (error) {
        console.error('Error saving sale:', error);
        showMessage('حدث خطأ في حفظ البيع', 'error');
        return null;
    }
}

// Generate sale number
function generateSaleNumber() {
    const counter = parseInt(localStorage.getItem('anwar_bakery_quick_sale_counter') || '0') + 1;
    return `QS-${new Date().getFullYear()}-${counter.toString().padStart(4, '0')}`;
}

// Update sale counter
function updateSaleCounter() {
    const currentCounter = parseInt(localStorage.getItem('anwar_bakery_quick_sale_counter') || '0');
    localStorage.setItem('anwar_bakery_quick_sale_counter', (currentCounter + 1).toString());
}

// Update cash register balance for cash sale
function updateCashRegisterBalance(saleData) {
    try {
        // Only cash payments are supported now
        updateCashRegister(saleData);
    } catch (error) {
        console.error('Error updating cash register balance:', error);
    }
}

// Update cash register balance
function updateCashRegister(saleData) {
    try {
        if (!saleData.cashRegisterId) {
            console.warn('No cash register ID provided');
            return;
        }

        const cashRegisters = JSON.parse(localStorage.getItem('anwar_bakery_cash_registers') || '[]');
        const registerIndex = cashRegisters.findIndex(reg => reg.id == saleData.cashRegisterId);

        if (registerIndex !== -1) {
            // Add sale amount to cash register (cash in)
            cashRegisters[registerIndex].currentBalance += saleData.total;
            cashRegisters[registerIndex].updatedAt = new Date().toISOString();

            // Save updated cash registers
            localStorage.setItem('anwar_bakery_cash_registers', JSON.stringify(cashRegisters));

            console.log(`Cash register balance updated: +${saleData.total.toFixed(2)}, New balance: ${cashRegisters[registerIndex].currentBalance.toFixed(2)}`);
        } else {
            console.warn('Cash register not found:', saleData.cashRegisterId);
        }
    } catch (error) {
        console.error('Error updating cash register balance:', error);
    }
}





// Create accounting entries for quick sale
function createQuickSaleAccountingEntries(saleData) {
    try {
        const journalEntry = {
            id: Date.now(),
            date: saleData.date,
            reference: saleData.number,
            description: `بيع سريع رقم ${saleData.number}`,
            type: 'quick_sale',
            entries: [
                {
                    account: 'الصندوق',
                    debit: saleData.total,
                    credit: 0
                },
                {
                    account: 'المبيعات',
                    debit: 0,
                    credit: saleData.subtotal
                },
                {
                    account: 'ضريبة القيمة المضافة',
                    debit: 0,
                    credit: saleData.tax
                }
            ],
            createdAt: saleData.createdAt
        };

        const journalEntries = JSON.parse(localStorage.getItem('anwar_bakery_journal_entries') || '[]');
        journalEntries.push(journalEntry);
        localStorage.setItem('anwar_bakery_journal_entries', JSON.stringify(journalEntries));

        console.log('Quick sale accounting entries created');
    } catch (error) {
        console.error('Error creating accounting entries:', error);
    }
}

// Update product stock
function updateProductStock() {
    try {
        // Get the original products from localStorage
        const savedProducts = JSON.parse(localStorage.getItem('anwar_bakery_products') || '[]');

        cart.forEach(cartItem => {
            // Find the original product in saved products
            const productIndex = savedProducts.findIndex(p => p.id === cartItem.id);
            if (productIndex !== -1) {
                // Update the current stock
                savedProducts[productIndex].currentStock -= cartItem.quantity;
                savedProducts[productIndex].lastSaleDate = new Date().toISOString().split('T')[0];
                savedProducts[productIndex].lastSalePrice = cartItem.price;
                savedProducts[productIndex].updatedAt = new Date().toISOString();

                // Ensure stock doesn't go negative
                if (savedProducts[productIndex].currentStock < 0) {
                    console.warn(`Warning: Negative stock for ${cartItem.name}. Stock: ${savedProducts[productIndex].currentStock}`);
                    savedProducts[productIndex].currentStock = 0;
                }

                console.log(`Updated stock for ${cartItem.name}: ${savedProducts[productIndex].currentStock}`);
            }

            // Also update the local products array for immediate UI update
            const localProduct = products.find(p => p.id === cartItem.id);
            if (localProduct) {
                localProduct.stock -= cartItem.quantity;
                if (localProduct.stock < 0) localProduct.stock = 0;
            }
        });

        // Save updated products back to localStorage
        localStorage.setItem('anwar_bakery_products', JSON.stringify(savedProducts));

        // Re-render products with updated stock
        renderProducts();

        console.log('Product stock updated successfully');
    } catch (error) {
        console.error('Error updating product stock:', error);
        showMessage('حدث خطأ في تحديث المخزون', 'error');
    }
}

// Print thermal receipt (80mm)
function printThermalReceipt(saleData, cartItems) {
    try {
        const companyData = JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
        const currency = companyData.currency || 'ر.س';

        const receiptWindow = window.open('', '_blank', 'width=400,height=600');
        if (!receiptWindow) {
            showMessage('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة.', 'error');
            return;
        }

        receiptWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>إيصال حراري - ${saleData.number}</title>
                <style>
                    @page { size: 80mm auto; margin: 0; }
                    body {
                        font-family: 'Courier New', monospace;
                        font-size: 12px;
                        margin: 0;
                        padding: 10px;
                        width: 80mm;
                        direction: rtl;
                        text-align: center;
                    }
                    .center { text-align: center; }
                    .bold { font-weight: bold; }
                    .line { border-top: 1px dashed #000; margin: 5px 0; }
                    .item {
                        display: flex;
                        justify-content: space-between;
                        margin: 2px 0;
                        text-align: right;
                    }
                    .total { font-size: 14px; font-weight: bold; }
                    .small { font-size: 10px; color: #666; }
                    .header { margin-bottom: 10px; }
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="center bold" style="font-size: 16px;">${companyData.companyNameAr || 'مخبز أنوار الحي'}</div>
                    ${companyData.address ? `<div class="center small">${companyData.address}</div>` : ''}
                    ${companyData.phone ? `<div class="center small">هاتف: ${companyData.phone}</div>` : ''}
                    ${companyData.taxNumber ? `<div class="center small">ض.ب: ${companyData.taxNumber}</div>` : ''}
                </div>

                <div class="line"></div>

                <div style="text-align: right;">
                    <div>رقم الإيصال: ${saleData.number}</div>
                    <div>التاريخ: ${new Date(saleData.time).toLocaleDateString('ar-SA')}</div>
                    <div>الوقت: ${new Date(saleData.time).toLocaleTimeString('ar-SA')}</div>
                    <div>الكاشير: ${saleData.cashier}</div>
                    <div>الفرع: ${saleData.branchName || 'الفرع الرئيسي'}</div>
                    ${saleData.cashRegisterName ? `<div>الصندوق: ${saleData.cashRegisterName}</div>` : ''}
                    ${saleData.bankName ? `<div>البنك: ${saleData.bankName}</div>` : ''}
                </div>

                <div class="line"></div>

                ${cartItems.map(item => `
                    <div class="item">
                        <span>${item.name}</span>
                        <span>${(item.price * item.quantity).toFixed(2)} ${currency}</span>
                    </div>
                    <div class="small" style="text-align: right; margin-right: 10px;">
                        ${item.quantity} × ${item.price.toFixed(2)} ${currency}
                    </div>
                `).join('')}

                <div class="line"></div>

                <div class="item">
                    <span>المجموع الفرعي:</span>
                    <span>${saleData.subtotal.toFixed(2)} ${currency}</span>
                </div>
                <div class="item">
                    <span>ضريبة القيمة المضافة (15%):</span>
                    <span>${saleData.tax.toFixed(2)} ${currency}</span>
                </div>

                <div class="line"></div>

                <div class="item total">
                    <span>الإجمالي النهائي:</span>
                    <span>${saleData.total.toFixed(2)} ${currency}</span>
                </div>

                <div class="line"></div>

                <div class="center">
                    <div>طريقة الدفع: ${getPaymentMethodName(saleData.paymentMethod)}</div>
                    <div style="margin-top: 10px;">شكراً لزيارتكم</div>
                    <div class="small">نتطلع لخدمتكم مرة أخرى</div>
                </div>

                <script>
                    window.onload = function() {
                        setTimeout(function() {
                            window.print();
                        }, 500);
                    };
                </script>
            </body>
            </html>
        `);

        receiptWindow.document.close();
        showMessage('تم إرسال الإيصال للطباعة الحرارية', 'success');
    } catch (error) {
        console.error('Error printing thermal receipt:', error);
        showMessage('حدث خطأ في طباعة الإيصال الحراري', 'error');
    }
}

// Print standard receipt (A4)
function printStandardReceipt(saleData, cartItems) {
    try {
        const companyData = JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
        const currency = companyData.currency || 'ر.س';

        const receiptWindow = window.open('', '_blank', 'width=800,height=600');
        if (!receiptWindow) {
            showMessage('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة.', 'error');
            return;
        }

        receiptWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة بيع سريع - ${saleData.number}</title>
                <style>
                    @page { size: A4; margin: 20mm; }
                    body {
                        font-family: 'Arial', 'Tahoma', sans-serif;
                        font-size: 12px;
                        line-height: 1.4;
                        direction: rtl;
                        text-align: right;
                        margin: 0;
                        padding: 0;
                    }
                    .header {
                        text-align: center;
                        border-bottom: 2px solid #333;
                        padding-bottom: 20px;
                        margin-bottom: 30px;
                    }
                    .company-name {
                        font-size: 24px;
                        font-weight: bold;
                        color: #2563eb;
                        margin-bottom: 10px;
                    }
                    .invoice-title {
                        font-size: 20px;
                        font-weight: bold;
                        color: #dc2626;
                        text-align: center;
                        margin: 20px 0;
                        padding: 10px;
                        border: 2px solid #dc2626;
                        border-radius: 5px;
                    }
                    .invoice-info {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 30px;
                    }
                    .info-section { flex: 1; }
                    .info-row { margin-bottom: 8px; }
                    .label { font-weight: bold; }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 20px 0;
                    }
                    th, td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: center;
                    }
                    th {
                        background: #f5f5f5;
                        font-weight: bold;
                    }
                    .totals {
                        margin-left: auto;
                        width: 300px;
                        margin-top: 20px;
                        border: 2px solid #374151;
                        border-radius: 5px;
                        overflow: hidden;
                    }
                    .total-row {
                        display: flex;
                        justify-content: space-between;
                        padding: 8px 15px;
                        border-bottom: 1px solid #ddd;
                    }
                    .final-total {
                        background: #374151;
                        color: white;
                        font-weight: bold;
                        font-size: 14px;
                    }
                    .footer {
                        margin-top: 50px;
                        text-align: center;
                        font-size: 10px;
                        color: #666;
                    }
                    @media print { body { margin: 0; } }
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="company-name">${companyData.companyNameAr || 'مخبز أنوار الحي'}</div>
                    <div>${companyData.address || ''}</div>
                    <div>${companyData.phone || ''}</div>
                    ${companyData.taxNumber ? `<div>الرقم الضريبي: ${companyData.taxNumber}</div>` : ''}
                </div>

                <div class="invoice-title">فاتورة بيع سريع</div>

                <div class="invoice-info">
                    <div class="info-section">
                        <div class="info-row"><span class="label">رقم الفاتورة:</span> ${saleData.number}</div>
                        <div class="info-row"><span class="label">التاريخ:</span> ${new Date(saleData.time).toLocaleDateString('ar-SA')}</div>
                        <div class="info-row"><span class="label">الوقت:</span> ${new Date(saleData.time).toLocaleTimeString('ar-SA')}</div>
                        <div class="info-row"><span class="label">الفرع:</span> ${saleData.branchName || 'الفرع الرئيسي'}</div>
                    </div>
                    <div class="info-section">
                        <div class="info-row"><span class="label">الكاشير:</span> ${saleData.cashier}</div>
                        <div class="info-row"><span class="label">طريقة الدفع:</span> ${getPaymentMethodName(saleData.paymentMethod)}</div>
                        ${saleData.cashRegisterName ? `<div class="info-row"><span class="label">الصندوق:</span> ${saleData.cashRegisterName}</div>` : ''}
                        ${saleData.bankName ? `<div class="info-row"><span class="label">البنك:</span> ${saleData.bankName}</div>` : ''}
                        <div class="info-row"><span class="label">العميل:</span> عميل نقدي</div>
                    </div>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>م</th>
                            <th>الصنف</th>
                            <th>الكمية</th>
                            <th>الوحدة</th>
                            <th>السعر</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${cartItems.map((item, index) => `
                            <tr>
                                <td>${index + 1}</td>
                                <td style="text-align: right;">${item.name}</td>
                                <td>${item.quantity}</td>
                                <td>${item.unit || 'قطعة'}</td>
                                <td>${item.price.toFixed(2)} ${currency}</td>
                                <td>${(item.price * item.quantity).toFixed(2)} ${currency}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>

                <div class="totals">
                    <div class="total-row">
                        <span>المجموع الفرعي:</span>
                        <span>${saleData.subtotal.toFixed(2)} ${currency}</span>
                    </div>
                    <div class="total-row">
                        <span>ضريبة القيمة المضافة (15%):</span>
                        <span>${saleData.tax.toFixed(2)} ${currency}</span>
                    </div>
                    <div class="total-row final-total">
                        <span>الإجمالي النهائي:</span>
                        <span>${saleData.total.toFixed(2)} ${currency}</span>
                    </div>
                </div>

                <div class="footer">
                    <div>شكراً لتعاملكم معنا</div>
                    <div>تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</div>
                </div>

                <script>
                    window.onload = function() {
                        setTimeout(function() {
                            window.print();
                        }, 500);
                    };
                </script>
            </body>
            </html>
        `);

        receiptWindow.document.close();
        showMessage('تم إرسال الفاتورة للطباعة العادية', 'success');
    } catch (error) {
        console.error('Error printing standard receipt:', error);
        showMessage('حدث خطأ في طباعة الفاتورة', 'error');
    }
}

// Get payment method name in Arabic
function getPaymentMethodName(method) {
    const methods = {
        'cash': 'نقدي',
        'card': 'بطاقة ائتمان',
        'split': 'دفع مختلط'
    };
    return methods[method] || 'نقدي';
}

// Format currency using currency manager
function formatCurrency(amount) {
    if (window.currencyManager) {
        return window.currencyManager.formatCurrency(amount);
    }
    // Fallback
    const company = getCompanyInfo();
    const currency = company.currencySymbol || company.currency || 'ر.س';
    return `${parseFloat(amount).toFixed(2)} ${currency}`;
}

// Open cash register
function openCashRegister() {
    try {
        // Get cash registers from localStorage
        const cashRegisters = JSON.parse(localStorage.getItem('anwar_bakery_cash_registers') || '[]');

        if (cashRegisters.length === 0) {
            showMessage('لا توجد صناديق مسجلة في النظام', 'warning');
            return;
        }

        // Find the main cash register or first available
        const mainRegister = cashRegisters.find(reg => reg.isMain) || cashRegisters[0];

        if (mainRegister) {
            const company = getCompanyInfo();
            const currency = company.currency || 'ر.س';

            showMessage(
                `تم فتح ${mainRegister.registerName} - الرصيد الحالي: ${mainRegister.currentBalance.toFixed(2)} ${currency}`,
                'success'
            );

            // Log the cash register opening
            console.log('Cash register opened:', mainRegister);
        } else {
            showMessage('لا يمكن فتح الصندوق', 'error');
        }
    } catch (error) {
        console.error('Error opening cash register:', error);
        showMessage('حدث خطأ في فتح الصندوق', 'error');
    }
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (sidebar && overlay) {
        if (sidebar.classList.contains('translate-x-full')) {
            sidebar.classList.remove('translate-x-full');
            overlay.classList.remove('hidden');
        } else {
            sidebar.classList.add('translate-x-full');
            overlay.classList.add('hidden');
        }
    }
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_session');
        sessionStorage.removeItem('anwar_bakery_session');
        window.location.href = 'login.html';
    }
}

// Update current date time
function updateDateTime() {
    const now = new Date();
    const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    const dateTimeElement = document.getElementById('currentDateTime');
    if (dateTimeElement) {
        dateTimeElement.textContent = now.toLocaleDateString('ar-SA', options);
    }
}

// Show message function
function showMessage(message, type = 'info') {
    const messageContainer = document.getElementById('messageContainer');
    if (!messageContainer) {
        // Fallback to old method if container not found
        const messageDiv = document.createElement('div');
        messageDiv.className = `fixed top-4 left-1/2 transform -translate-x-1/2 z-50 px-6 py-3 rounded-lg shadow-lg ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'warning' ? 'bg-yellow-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        messageDiv.textContent = message;
        document.body.appendChild(messageDiv);
        setTimeout(() => {
            if (messageDiv.parentNode) {
                document.body.removeChild(messageDiv);
            }
        }, 3000);
        return;
    }

    const messageDiv = document.createElement('div');

    const bgColor = type === 'success' ? 'bg-green-100 border-green-400 text-green-700' :
                   type === 'error' ? 'bg-red-100 border-red-400 text-red-700' :
                   type === 'warning' ? 'bg-yellow-100 border-yellow-400 text-yellow-700' :
                   'bg-blue-100 border-blue-400 text-blue-700';

    messageDiv.className = `border-l-4 p-4 mb-4 rounded-md shadow-md ${bgColor}`;
    messageDiv.innerHTML = `
        <div class="flex">
            <div class="flex-shrink-0">
                <span class="text-lg">${type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'}</span>
            </div>
            <div class="mr-3 flex-1">
                <p class="text-sm font-medium">${message}</p>
            </div>
            <div class="mr-auto">
                <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-gray-400 hover:text-gray-600">
                    <span class="text-lg">×</span>
                </button>
            </div>
        </div>
    `;

    messageContainer.appendChild(messageDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 5000);
}

// Load branch and cash register info
function loadBranchAndCashRegisterInfo() {
    try {
        // Load branch info
        const branches = JSON.parse(localStorage.getItem('anwar_bakery_branches') || '[]');
        const mainBranch = branches.find(branch => branch.isMain) || branches[0];

        if (mainBranch) {
            document.getElementById('currentBranch').textContent = mainBranch.branchName || 'الفرع الرئيسي';
        }

        // Load cash register info
        const cashRegisters = JSON.parse(localStorage.getItem('anwar_bakery_cash_registers') || '[]');
        const mainRegister = cashRegisters.find(reg => reg.isMain) || cashRegisters[0];

        if (mainRegister) {
            document.getElementById('activeCashRegister').textContent = mainRegister.registerName || 'الصندوق الرئيسي';

            // Display current balance
            const company = getCompanyInfo();
            const currency = company.currency || 'ر.س';
            const balance = parseFloat(mainRegister.currentBalance || 0);
            document.getElementById('currentBalance').textContent = `${balance.toFixed(2)} ${currency}`;

            // Store active register ID for later use
            window.activeCashRegisterId = mainRegister.id;
        } else {
            document.getElementById('activeCashRegister').textContent = 'لا يوجد صندوق نشط';
            document.getElementById('currentBalance').textContent = '0.00 ر.س';
        }

        console.log('Branch and cash register info loaded');
    } catch (error) {
        console.error('Error loading branch and cash register info:', error);
        document.getElementById('currentBranch').textContent = 'غير محدد';
        document.getElementById('activeCashRegister').textContent = 'غير محدد';
        document.getElementById('currentBalance').textContent = '0.00 ر.س';
    }
}

// Update cash register balance display
function updateCashRegisterDisplay() {
    try {
        const cashRegisters = JSON.parse(localStorage.getItem('anwar_bakery_cash_registers') || '[]');
        const activeRegister = cashRegisters.find(reg => reg.id == window.activeCashRegisterId);

        if (activeRegister) {
            const company = getCompanyInfo();
            const currency = company.currency || 'ر.س';
            const balance = parseFloat(activeRegister.currentBalance || 0);
            document.getElementById('currentBalance').textContent = `${balance.toFixed(2)} ${currency}`;
        }
    } catch (error) {
        console.error('Error updating cash register display:', error);
    }
}

// Debug function to check database
function debugDatabase() {
    console.log('🔍 === DATABASE DEBUG ===');

    // Check all localStorage keys
    console.log('📋 All localStorage keys:');
    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.includes('anwar_bakery')) {
            console.log(`  - ${key}`);
        }
    }

    // Check products specifically
    const productsData = localStorage.getItem('anwar_bakery_products');
    if (productsData) {
        try {
            const products = JSON.parse(productsData);
            console.log(`📦 Products in database: ${products.length}`);

            if (products.length > 0) {
                console.log('📊 Product analysis:');
                let activeCount = 0;
                let stockCount = 0;
                let bothCount = 0;

                products.forEach(product => {
                    const isActive = product.isActive !== false;
                    const hasStock = (product.currentStock || 0) > 0;

                    if (isActive) activeCount++;
                    if (hasStock) stockCount++;
                    if (isActive && hasStock) bothCount++;
                });

                console.log(`  - Active products: ${activeCount}`);
                console.log(`  - Products with stock: ${stockCount}`);
                console.log(`  - Active with stock: ${bothCount}`);

                // Show first 5 products details
                console.log('📋 First 5 products details:');
                products.slice(0, 5).forEach((product, index) => {
                    console.log(`Product ${index + 1}:`, {
                        id: product.id,
                        itemName: product.itemName,
                        isActive: product.isActive,
                        currentStock: product.currentStock,
                        sellingPrice: product.sellingPrice,
                        costPrice: product.costPrice,
                        itemType: product.itemType,
                        category: product.category
                    });
                });
            }
        } catch (error) {
            console.error('❌ Error parsing products data:', error);
        }
    } else {
        console.log('❌ No products data found in localStorage');
    }

    console.log('🔍 === END DEBUG ===');
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Initializing Quick Sale page...');

    loadUserInfo();
    loadCompanyInfo();
    loadBranchAndCashRegisterInfo();
    loadProducts();
    initializeCashPayment();
    updateDateTime();
    setInterval(updateDateTime, 60000);

    // Close sidebar when clicking overlay
    const overlay = document.getElementById('mobileOverlay');
    if (overlay) {
        overlay.addEventListener('click', toggleSidebar);
    }

    console.log('✅ Quick Sale page initialized successfully');
});
