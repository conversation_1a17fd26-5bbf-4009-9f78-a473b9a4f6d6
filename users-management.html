<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .modal {
            display: none;
            z-index: 9999 !important;
        }
        .modal.active {
            display: flex !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Mobile menu overlay -->
    <div id="mobileOverlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 lg:hidden hidden"></div>

    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform translate-x-full lg:translate-x-0 lg:static lg:inset-0 sidebar-transition">
            <div class="h-16 px-4 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between">
                <h1 id="sidebarCompanyName" class="text-white text-lg font-bold">مخبز أنوار الحي</h1>
                <button onclick="toggleSidebar()" class="lg:hidden text-white">
                    <span class="text-xl">✕</span>
                </button>
            </div>

            <nav class="mt-5 px-2 space-y-1 h-full overflow-y-auto pb-20">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    لوحة التحكم
                </a>
                <a href="users-management.html" class="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">
                    <span class="ml-3">👥</span>
                    إدارة المستخدمين
                </a>
                <a href="company-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏢</span>
                    بيانات المنشأة
                </a>
                <a href="system-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">⚙️</span>
                    إعدادات النظام
                </a>
                <a href="system-admin.html" class="flex items-center px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 hover:text-red-700 rounded-md border border-red-200">
                    <span class="ml-3">🔧</span>
                    إدارة النظام المتقدمة
                </a>
                <a href="branches-warehouses.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏪</span>
                    الفروع والمخازن
                </a>
                <a href="units.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📏</span>
                    وحدات القياس
                </a>
                <a href="chart-of-accounts.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🌳</span>
                    شجرة الحسابات
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="damage-entry.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🗑️</span>
                    قيد التالف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🚚</span>
                    الموردين
                </a>
                <a href="employees.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👨‍💼</span>
                    الموظفين
                </a>
                <a href="cash-registers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">💰</span>
                    الصناديق
                </a>
                <a href="banks.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏦</span>
                    البنوك
                </a>
                <a href="owners.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👑</span>
                    الملاك
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="vouchers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📋</span>
                    السندات
                </a>
                <a href="journal-entry.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📝</span>
                    القيود اليومية
                </a>
                <a href="inventory.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📊</span>
                    المخزون
                </a>
                <a href="reports.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📈</span>
                    التقارير
                </a>

                <!-- User Info & Logout -->
                <div class="absolute bottom-4 left-2 right-2">
                    <div class="bg-gray-50 rounded-lg p-3 mb-2">
                        <p id="userFullName" class="text-sm font-medium text-gray-900">مدير النظام</p>
                        <p id="userRole" class="text-xs text-gray-500">admin</p>
                    </div>
                    <button onclick="logout()" class="w-full text-right px-3 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50 flex items-center">
                        <span class="ml-3">🚪</span>
                        تسجيل الخروج
                    </button>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 overflow-hidden">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <button onclick="toggleSidebar()" class="lg:hidden text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">☰</span>
                            </button>
                            <span class="text-lg font-semibold text-gray-900">إدارة المستخدمين</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-500" id="currentDateTime"></span>
                            <div class="flex items-center space-x-2">
                                <span id="userDisplayName" class="text-sm font-medium text-gray-700">مدير النظام</span>
                                <button onclick="logout()" class="text-red-600 hover:text-red-800 text-sm">
                                    خروج
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex justify-between items-center">
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 mb-2 flex items-center">
                                <span class="ml-3">👥</span>
                                إدارة المستخدمين والصلاحيات
                            </h1>
                            <p class="text-gray-600">
                                إدارة حسابات المستخدمين وتحديد صلاحياتهم في النظام
                            </p>
                        </div>
                        <button onclick="openAddModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center">
                            <span class="ml-2">👤</span>
                            إضافة مستخدم جديد
                        </button>
                    </div>
                </div>

                <!-- Success/Error Messages -->
                <div id="messageContainer" class="mb-6"></div>

                <!-- Search and Filter -->
                <div class="bg-white rounded-lg shadow-md p-4 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <input type="text" id="searchInput" placeholder="البحث بالاسم أو اسم المستخدم..."
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   onkeyup="filterUsers()">
                        </div>
                        <div>
                            <select id="roleFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="filterUsers()">
                                <option value="">جميع الصلاحيات</option>
                                <option value="admin">مدير النظام</option>
                                <option value="manager">مدير المخبز</option>
                                <option value="cashier">كاشير</option>
                                <option value="viewer">مشاهد</option>
                            </select>
                        </div>
                        <div>
                            <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="filterUsers()">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Users Table -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاسم الكامل</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم المستخدم</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">البريد الإلكتروني</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الصلاحية</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- Users will be populated here -->
                        </tbody>
                    </table>
                </div>
            </main>
        </div>
    </div>

    <!-- Add/Edit User Modal -->
    <div id="userModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-sm w-full mx-4">
            <div class="px-4 py-3 border-b border-gray-200">
                <h3 id="modalTitle" class="text-lg font-medium text-gray-900">إضافة مستخدم جديد</h3>
            </div>
            <form id="userForm" class="px-4 py-3 space-y-3">
                <input type="hidden" id="userId" value="">

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">الاسم الكامل *</label>
                    <input type="text" id="fullName" required placeholder="أدخل الاسم الكامل" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">اسم المستخدم *</label>
                    <input type="text" id="username" required placeholder="أدخل اسم المستخدم" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">كلمة المرور *</label>
                    <input type="password" id="password" required placeholder="أدخل كلمة المرور" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">نوع الصلاحية *</label>
                    <select id="role" required class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">اختر الصلاحية</option>
                        <option value="admin">مدير النظام</option>
                        <option value="manager">مدير المخبز</option>
                        <option value="cashier">كاشير</option>
                        <option value="viewer">مشاهد</option>
                    </select>
                </div>

                <!-- Optional fields (collapsible) -->
                <div class="border-t pt-3">
                    <button type="button" onclick="toggleOptionalFields()" class="text-sm text-blue-600 hover:text-blue-800 mb-2">
                        <span id="toggleText">+ إضافة معلومات إضافية</span>
                    </button>
                    <div id="optionalFields" class="hidden space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                            <input type="email" id="email" placeholder="<EMAIL>" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف</label>
                            <input type="tel" id="phone" placeholder="05xxxxxxxx" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="isActive" checked class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="isActive" class="mr-2 block text-sm text-gray-900">المستخدم نشط</label>
                        </div>
                    </div>
                </div>
            </form>

            <div class="px-4 py-3 border-t border-gray-200 flex justify-end space-x-2">
                <button onclick="closeModal()" class="px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">إلغاء</button>
                <button onclick="saveUser()" class="px-3 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">حفظ</button>
            </div>
        </div>
    </div>

    <script>
        // Users data - starts with admin user only
        let users = [
            {
                id: 1,
                fullName: 'مدير النظام',
                username: 'admin',
                email: '',
                phone: '',
                role: 'admin',
                isActive: true,
                createdAt: new Date().toISOString().split('T')[0]
            }
        ];

        // Load users from localStorage if available
        function loadUsers() {
            try {
                const savedUsers = localStorage.getItem('anwar_bakery_users');
                if (savedUsers) {
                    const parsedUsers = JSON.parse(savedUsers);
                    if (Array.isArray(parsedUsers)) {
                        users = parsedUsers;
                    } else {
                        console.warn('Invalid users data format, using default');
                        users = [
                            {
                                id: 1,
                                fullName: 'مدير النظام',
                                username: 'admin',
                                email: '',
                                phone: '',
                                role: 'admin',
                                isActive: true,
                                createdAt: new Date().toISOString().split('T')[0]
                            }
                        ];
                        saveUsers();
                    }
                } else {
                    // Initialize with default admin user
                    saveUsers();
                }
                renderUsers();
                console.log('Users loaded successfully:', users.length, 'users');
            } catch (error) {
                console.error('Error loading users:', error);
                showMessage('حدث خطأ في تحميل بيانات المستخدمين', 'error');
            }
        }

        // Save users to localStorage
        function saveUsers() {
            try {
                localStorage.setItem('anwar_bakery_users', JSON.stringify(users));
                console.log('Users saved successfully to localStorage');
            } catch (error) {
                console.error('Error saving users:', error);
                showMessage('حدث خطأ في حفظ بيانات المستخدمين', 'error');
            }
        }

        // Render users table
        function renderUsers(filteredUsers = users) {
            const tbody = document.getElementById('usersTableBody');
            tbody.innerHTML = '';

            filteredUsers.forEach(user => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';

                const roleNames = {
                    'admin': 'مدير النظام',
                    'manager': 'مدير المخبز',
                    'cashier': 'كاشير',
                    'viewer': 'مشاهد'
                };

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.fullName}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.username}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.email || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${getRoleBadgeClass(user.role)}">
                            ${roleNames[user.role]}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${user.isActive ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button onclick="editUser(${user.id})" class="text-blue-600 hover:text-blue-900">تعديل</button>
                        <button onclick="toggleUserStatus(${user.id})" class="text-yellow-600 hover:text-yellow-900">
                            ${user.isActive ? 'إلغاء تفعيل' : 'تفعيل'}
                        </button>
                        <button onclick="deleteUser(${user.id})" class="text-red-600 hover:text-red-900">حذف</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Get role badge class
        function getRoleBadgeClass(role) {
            const classes = {
                'admin': 'bg-purple-100 text-purple-800',
                'manager': 'bg-blue-100 text-blue-800',
                'cashier': 'bg-green-100 text-green-800',
                'viewer': 'bg-gray-100 text-gray-800'
            };
            return classes[role] || 'bg-gray-100 text-gray-800';
        }

        // Filter users
        function filterUsers() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const roleFilter = document.getElementById('roleFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            const filtered = users.filter(user => {
                const matchesSearch = user.fullName.toLowerCase().includes(searchTerm) ||
                                    user.username.toLowerCase().includes(searchTerm);
                const matchesRole = !roleFilter || user.role === roleFilter;
                const matchesStatus = !statusFilter ||
                                    (statusFilter === 'active' && user.isActive) ||
                                    (statusFilter === 'inactive' && !user.isActive);

                return matchesSearch && matchesRole && matchesStatus;
            });

            renderUsers(filtered);
        }

        // Toggle optional fields
        function toggleOptionalFields() {
            const optionalFields = document.getElementById('optionalFields');
            const toggleText = document.getElementById('toggleText');

            if (optionalFields.classList.contains('hidden')) {
                optionalFields.classList.remove('hidden');
                toggleText.textContent = '- إخفاء المعلومات الإضافية';
            } else {
                optionalFields.classList.add('hidden');
                toggleText.textContent = '+ إضافة معلومات إضافية';
            }
        }

        // Modal functions
        function openAddModal() {
            document.getElementById('modalTitle').textContent = 'إضافة مستخدم جديد';
            document.getElementById('userForm').reset();
            document.getElementById('userId').value = '';
            document.getElementById('isActive').checked = true;

            // Reset optional fields to hidden
            document.getElementById('optionalFields').classList.add('hidden');
            document.getElementById('toggleText').textContent = '+ إضافة معلومات إضافية';

            document.getElementById('userModal').classList.add('active');
        }

        function editUser(id) {
            try {
                const user = users.find(u => u.id === id);
                if (!user) {
                    showMessage('المستخدم غير موجود', 'error');
                    return;
                }

                // Reset form first
                document.getElementById('userForm').reset();

                // Set modal title and form values
                document.getElementById('modalTitle').textContent = 'تعديل المستخدم';
                document.getElementById('userId').value = user.id;
                document.getElementById('fullName').value = user.fullName || '';
                document.getElementById('username').value = user.username || '';
                document.getElementById('email').value = user.email || '';
                document.getElementById('phone').value = user.phone || '';
                document.getElementById('role').value = user.role || '';
                document.getElementById('isActive').checked = user.isActive !== false;

                // Make password optional for editing
                const passwordField = document.getElementById('password');
                passwordField.required = false;
                passwordField.value = '';
                passwordField.placeholder = 'اتركه فارغاً للاحتفاظ بكلمة المرور الحالية';

                // Show optional fields if user has email or phone
                const optionalFields = document.getElementById('optionalFields');
                const toggleText = document.getElementById('toggleText');

                if (user.email || user.phone) {
                    optionalFields.classList.remove('hidden');
                    toggleText.textContent = '- إخفاء المعلومات الإضافية';
                } else {
                    optionalFields.classList.add('hidden');
                    toggleText.textContent = '+ إضافة معلومات إضافية';
                }

                // Show modal
                document.getElementById('userModal').classList.add('active');

                console.log('Editing user:', user);
            } catch (error) {
                console.error('Error editing user:', error);
                showMessage('حدث خطأ أثناء تحميل بيانات المستخدم', 'error');
            }
        }

        function closeModal() {
            try {
                // Hide modal
                document.getElementById('userModal').classList.remove('active');

                // Reset form
                document.getElementById('userForm').reset();
                document.getElementById('userId').value = '';

                // Reset password field
                const passwordField = document.getElementById('password');
                passwordField.required = true;
                passwordField.placeholder = 'أدخل كلمة المرور';

                // Hide optional fields
                document.getElementById('optionalFields').classList.add('hidden');
                document.getElementById('toggleText').textContent = '+ إضافة معلومات إضافية';

                console.log('Modal closed and form reset');
            } catch (error) {
                console.error('Error closing modal:', error);
            }
        }

        function saveUser() {
            try {
                const form = document.getElementById('userForm');
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                const userId = document.getElementById('userId').value;
                const fullName = document.getElementById('fullName').value.trim();
                const username = document.getElementById('username').value.trim();
                const email = document.getElementById('email').value.trim();
                const phone = document.getElementById('phone').value.trim();
                const role = document.getElementById('role').value;
                const password = document.getElementById('password').value;
                const isActive = document.getElementById('isActive').checked;

                // Validation
                if (!fullName) {
                    showMessage('يرجى إدخال الاسم الكامل', 'error');
                    return;
                }

                if (!username) {
                    showMessage('يرجى إدخال اسم المستخدم', 'error');
                    return;
                }

                if (!role) {
                    showMessage('يرجى اختيار نوع الصلاحية', 'error');
                    return;
                }

                // Check if username already exists (for new users or different user)
                const existingUser = users.find(u => u.username === username && u.id != userId);
                if (existingUser) {
                    showMessage('اسم المستخدم موجود مسبقاً!', 'error');
                    return;
                }

                // For new users, password is required
                if (!userId && !password) {
                    showMessage('يرجى إدخال كلمة المرور', 'error');
                    return;
                }

                const userData = {
                    fullName: fullName,
                    username: username,
                    email: email,
                    phone: phone,
                    role: role,
                    isActive: isActive
                };

                if (userId) {
                    // Edit existing user
                    const userIndex = users.findIndex(u => u.id == userId);
                    if (userIndex !== -1) {
                        users[userIndex] = { ...users[userIndex], ...userData };
                        if (password) {
                            users[userIndex].password = password;
                        }
                        users[userIndex].updatedAt = new Date().toISOString().split('T')[0];
                        showMessage('تم تحديث المستخدم بنجاح!', 'success');
                    }
                } else {
                    // Add new user
                    const newUser = {
                        id: Math.max(...users.map(u => u.id), 0) + 1,
                        ...userData,
                        password: password,
                        createdAt: new Date().toISOString().split('T')[0],
                        updatedAt: new Date().toISOString().split('T')[0]
                    };
                    users.push(newUser);
                    showMessage('تم إضافة المستخدم بنجاح!', 'success');
                }

                // Save to localStorage
                saveUsers();

                // Update display
                renderUsers();

                // Close modal
                closeModal();

                console.log('User saved successfully:', userData);

            } catch (error) {
                console.error('Error saving user:', error);
                showMessage('حدث خطأ أثناء حفظ المستخدم: ' + error.message, 'error');
            }
        }

        function toggleUserStatus(id) {
            try {
                const user = users.find(u => u.id === id);
                if (user) {
                    // Prevent deactivating the last admin user
                    if (user.role === 'admin' && user.isActive) {
                        const activeAdmins = users.filter(u => u.role === 'admin' && u.isActive);
                        if (activeAdmins.length === 1) {
                            showMessage('لا يمكن إلغاء تفعيل آخر مدير نظام نشط', 'error');
                            return;
                        }
                    }

                    user.isActive = !user.isActive;
                    user.updatedAt = new Date().toISOString().split('T')[0];
                    saveUsers();
                    renderUsers();
                    showMessage(`تم ${user.isActive ? 'تفعيل' : 'إلغاء تفعيل'} المستخدم بنجاح!`, 'success');
                }
            } catch (error) {
                console.error('Error toggling user status:', error);
                showMessage('حدث خطأ أثناء تغيير حالة المستخدم', 'error');
            }
        }

        function deleteUser(id) {
            try {
                const user = users.find(u => u.id === id);
                if (!user) {
                    showMessage('المستخدم غير موجود', 'error');
                    return;
                }

                // Prevent deleting the last admin user
                if (user.role === 'admin') {
                    const adminUsers = users.filter(u => u.role === 'admin');
                    if (adminUsers.length === 1) {
                        showMessage('لا يمكن حذف آخر مدير نظام', 'error');
                        return;
                    }
                }

                if (confirm(`هل أنت متأكد من حذف المستخدم "${user.fullName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                    users = users.filter(u => u.id !== id);
                    saveUsers();
                    renderUsers();
                    showMessage('تم حذف المستخدم بنجاح!', 'success');
                }
            } catch (error) {
                console.error('Error deleting user:', error);
                showMessage('حدث خطأ أثناء حذف المستخدم', 'error');
            }
        }

        // Load company info
        function loadCompanyInfo() {
            const savedData = localStorage.getItem('anwar_bakery_company');
            if (savedData) {
                const companyData = JSON.parse(savedData);

                // Update company name in sidebar
                if (companyData.companyNameAr) {
                    document.getElementById('sidebarCompanyName').textContent = companyData.companyNameAr;
                }

                // Update page title
                if (companyData.companyNameAr) {
                    document.title = `إدارة المستخدمين - ${companyData.companyNameAr}`;
                }
            }
        }

        // Toggle sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobileOverlay');

            if (sidebar && overlay) {
                if (sidebar.classList.contains('translate-x-full')) {
                    sidebar.classList.remove('translate-x-full');
                    overlay.classList.remove('hidden');
                } else {
                    sidebar.classList.add('translate-x-full');
                    overlay.classList.add('hidden');
                }
            }
        }

        // Logout function
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('anwar_bakery_session');
                sessionStorage.removeItem('anwar_bakery_session');
                window.location.href = 'login.html';
            }
        }

        // Update current date time
        function updateDateTime() {
            const now = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            const dateTimeElement = document.getElementById('currentDateTime');
            if (dateTimeElement) {
                dateTimeElement.textContent = now.toLocaleDateString('ar-SA', options);
            }
        }

        // Show message
        function showMessage(message, type) {
            const container = document.getElementById('messageContainer');
            if (!container) return;

            const alertClass = {
                'success': 'bg-green-50 border-green-200 text-green-700',
                'error': 'bg-red-50 border-red-200 text-red-700',
                'info': 'bg-blue-50 border-blue-200 text-blue-700',
                'warning': 'bg-yellow-50 border-yellow-200 text-yellow-700'
            };

            container.innerHTML = `
                <div class="border rounded-lg p-4 ${alertClass[type]}">
                    <div class="flex items-center">
                        <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'}</span>
                        ${message}
                    </div>
                </div>
            `;

            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            try {
                console.log('Initializing Users Management page...');
                loadUsers();
                loadCompanyInfo();
                updateDateTime();
                setInterval(updateDateTime, 60000);

                // Add form submit event listener
                document.getElementById('userForm').addEventListener('submit', function(e) {
                    e.preventDefault();
                    saveUser();
                });

                console.log('Users Management page initialized successfully');
            } catch (error) {
                console.error('Error initializing page:', error);
                showMessage('حدث خطأ في تحميل الصفحة', 'error');
            }
        });

        // Listen for company data updates
        window.addEventListener('companyDataUpdated', function(event) {
            loadCompanyInfo();
        });

        // Close modal when clicking outside
        document.getElementById('userModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // Close sidebar when clicking overlay
        document.getElementById('mobileOverlay').addEventListener('click', toggleSidebar);
    </script>
</body>
</html>
