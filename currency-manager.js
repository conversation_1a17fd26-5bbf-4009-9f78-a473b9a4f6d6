// نظام إدارة العملة الموحد
class CurrencyManager {
    constructor() {
        this.currencies = {
            'SAR': { symbol: 'ر.س', name: 'ريال سعودي', position: 'after' },
            'YER': { symbol: '﷼', name: 'ريال يمني', position: 'after' },
            'USD': { symbol: '$', name: 'دولار أمريكي', position: 'before' },
            'EUR': { symbol: '€', name: 'يورو', position: 'before' },
            'AED': { symbol: 'د.إ', name: 'درهم إماراتي', position: 'after' },
            'KWD': { symbol: 'د.ك', name: 'دينار كويتي', position: 'after' },
            'QAR': { symbol: 'ر.ق', name: 'ريال قطري', position: 'after' }
        };
        
        this.defaultCurrency = 'YER';
        this.init();
    }

    // تهيئة النظام
    init() {
        // التأكد من وجود إعدادات العملة
        this.ensureCurrencySettings();
        
        // تطبيق العملة على الصفحة الحالية
        this.applyCurrencyToPage();
        
        // الاستماع لتغييرات العملة
        this.listenForCurrencyChanges();
    }

    // التأكد من وجود إعدادات العملة
    ensureCurrencySettings() {
        const companyData = this.getCompanyData();
        
        // إذا لم تكن العملة محددة، استخدم الافتراضية
        if (!companyData.currency) {
            companyData.currency = this.defaultCurrency;
            companyData.currencySymbol = this.currencies[this.defaultCurrency].symbol;
            this.saveCompanyData(companyData);
        }
    }

    // الحصول على بيانات الشركة
    getCompanyData() {
        try {
            const data = localStorage.getItem('anwar_bakery_company');
            return data ? JSON.parse(data) : {};
        } catch (error) {
            console.error('Error parsing company data:', error);
            return {};
        }
    }

    // حفظ بيانات الشركة
    saveCompanyData(data) {
        try {
            localStorage.setItem('anwar_bakery_company', JSON.stringify(data));
        } catch (error) {
            console.error('Error saving company data:', error);
        }
    }

    // الحصول على العملة الحالية
    getCurrentCurrency() {
        const companyData = this.getCompanyData();
        return companyData.currency || this.defaultCurrency;
    }

    // الحصول على رمز العملة الحالية
    getCurrentCurrencySymbol() {
        const companyData = this.getCompanyData();
        const currency = companyData.currency || this.defaultCurrency;
        
        // إذا كان هناك رمز محفوظ، استخدمه
        if (companyData.currencySymbol) {
            return companyData.currencySymbol;
        }
        
        // وإلا استخدم الرمز الافتراضي للعملة
        return this.currencies[currency]?.symbol || this.currencies[this.defaultCurrency].symbol;
    }

    // الحصول على معلومات العملة الكاملة
    getCurrentCurrencyInfo() {
        const currency = this.getCurrentCurrency();
        const symbol = this.getCurrentCurrencySymbol();
        const currencyInfo = this.currencies[currency] || this.currencies[this.defaultCurrency];
        
        return {
            code: currency,
            symbol: symbol,
            name: currencyInfo.name,
            position: currencyInfo.position
        };
    }

    // تنسيق المبلغ بالعملة
    formatCurrency(amount, options = {}) {
        const currencyInfo = this.getCurrentCurrencyInfo();
        const {
            showSymbol = true,
            decimalPlaces = 2,
            thousandsSeparator = ',',
            decimalSeparator = '.'
        } = options;

        // تنسيق الرقم
        let formattedAmount = parseFloat(amount || 0).toFixed(decimalPlaces);
        
        // إضافة فاصل الآلاف
        if (thousandsSeparator) {
            formattedAmount = formattedAmount.replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator);
        }
        
        // تغيير فاصل العشرية
        if (decimalSeparator !== '.') {
            formattedAmount = formattedAmount.replace('.', decimalSeparator);
        }

        // إضافة رمز العملة
        if (showSymbol) {
            if (currencyInfo.position === 'before') {
                return `${currencyInfo.symbol} ${formattedAmount}`;
            } else {
                return `${formattedAmount} ${currencyInfo.symbol}`;
            }
        }

        return formattedAmount;
    }

    // تحديث العملة
    updateCurrency(currencyCode, currencySymbol = null) {
        if (!this.currencies[currencyCode]) {
            console.error('Currency not supported:', currencyCode);
            return false;
        }

        const companyData = this.getCompanyData();
        companyData.currency = currencyCode;
        companyData.currencySymbol = currencySymbol || this.currencies[currencyCode].symbol;
        companyData.updatedAt = new Date().toISOString();
        
        this.saveCompanyData(companyData);
        
        // تطبيق التغيير على الصفحة الحالية
        this.applyCurrencyToPage();
        
        // إشعار الصفحات الأخرى بالتغيير
        this.notifyOtherPages();
        
        return true;
    }

    // تطبيق العملة على الصفحة الحالية
    applyCurrencyToPage() {
        const currencyInfo = this.getCurrentCurrencyInfo();
        
        // تحديث جميع عناصر العملة
        this.updateCurrencyElements();
        
        // تحديث النماذج والجداول
        this.updateFormsAndTables();
        
        // تحديث الإحصائيات
        this.updateStatistics();
    }

    // تحديث عناصر العملة في الصفحة
    updateCurrencyElements() {
        const currencyInfo = this.getCurrentCurrencyInfo();
        
        // تحديث عناصر العملة المباشرة
        document.querySelectorAll('.currency-symbol').forEach(el => {
            el.textContent = currencyInfo.symbol;
        });
        
        // تحديث عناصر المبالغ
        document.querySelectorAll('.currency-amount').forEach(el => {
            const amount = el.dataset.amount || el.textContent.replace(/[^\d.-]/g, '');
            if (amount) {
                el.textContent = this.formatCurrency(amount);
            }
        });
        
        // تحديث عناصر العملة المخصصة
        document.querySelectorAll('[data-currency]').forEach(el => {
            const amount = el.dataset.currency;
            if (amount) {
                el.textContent = this.formatCurrency(amount);
            }
        });
    }

    // تحديث النماذج والجداول
    updateFormsAndTables() {
        // تحديث خيارات العملة في النماذج
        document.querySelectorAll('select[name="currency"], #currency, #baseCurrency').forEach(select => {
            const currentCurrency = this.getCurrentCurrency();
            if (select.value !== currentCurrency) {
                select.value = currentCurrency;
            }
        });
        
        // تحديث رموز العملة في الحقول
        document.querySelectorAll('input[data-currency-field]').forEach(input => {
            const wrapper = input.parentElement;
            let symbolSpan = wrapper.querySelector('.currency-symbol-input');
            
            if (!symbolSpan) {
                symbolSpan = document.createElement('span');
                symbolSpan.className = 'currency-symbol-input absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500';
                wrapper.style.position = 'relative';
                wrapper.appendChild(symbolSpan);
                input.style.paddingLeft = '2.5rem';
            }
            
            symbolSpan.textContent = this.getCurrentCurrencySymbol();
        });
    }

    // تحديث الإحصائيات
    updateStatistics() {
        // إعادة حساب وعرض الإحصائيات بالعملة الجديدة
        if (typeof updateStats === 'function') {
            updateStats();
        }
        
        if (typeof updateRegisterStats === 'function') {
            updateRegisterStats();
        }
        
        if (typeof calculateTotal === 'function') {
            calculateTotal();
        }
    }

    // الاستماع لتغييرات العملة
    listenForCurrencyChanges() {
        // الاستماع لأحداث تحديث بيانات الشركة
        window.addEventListener('companyDataUpdated', (event) => {
            if (event.detail && event.detail.currency) {
                this.applyCurrencyToPage();
            }
        });
        
        // الاستماع لتغييرات localStorage
        window.addEventListener('storage', (event) => {
            if (event.key === 'anwar_bakery_company') {
                this.applyCurrencyToPage();
            }
        });
    }

    // إشعار الصفحات الأخرى بالتغيير
    notifyOtherPages() {
        const currencyInfo = this.getCurrentCurrencyInfo();
        
        // إرسال حدث مخصص
        window.dispatchEvent(new CustomEvent('currencyChanged', {
            detail: currencyInfo
        }));
        
        // تحديث localStorage لإشعار الصفحات الأخرى
        const event = new StorageEvent('storage', {
            key: 'anwar_bakery_company',
            newValue: JSON.stringify(this.getCompanyData())
        });
        window.dispatchEvent(event);
    }

    // الحصول على قائمة العملات المدعومة
    getSupportedCurrencies() {
        return Object.keys(this.currencies).map(code => ({
            code,
            ...this.currencies[code]
        }));
    }
}

// إنشاء مثيل عام لمدير العملة
window.currencyManager = new CurrencyManager();

// وظائف مساعدة عامة
window.getCurrencySymbol = () => window.currencyManager.getCurrentCurrencySymbol();
window.getCurrentCurrency = () => window.currencyManager.getCurrentCurrency();
window.formatCurrency = (amount, options) => window.currencyManager.formatCurrency(amount, options);
window.updateCurrency = (code, symbol) => window.currencyManager.updateCurrency(code, symbol);

// تطبيق العملة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    if (window.currencyManager) {
        window.currencyManager.applyCurrencyToPage();
    }
});

console.log('✅ Currency Manager loaded successfully');
