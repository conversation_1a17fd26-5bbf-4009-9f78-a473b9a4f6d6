<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصناديق - مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .custom-scroll {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 #f7fafc;
        }

        .custom-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scroll::-webkit-scrollbar-track {
            background: #f7fafc;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        .modal {
            display: none;
        }

        .modal.active {
            display: flex;
        }

        .cash-register-card {
            transition: all 0.3s ease;
        }

        .cash-register-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="bg-white w-64 shadow-lg flex flex-col">
            <div class="p-4 flex-shrink-0">
                <div class="flex items-center mb-8">
                    <span class="text-2xl ml-3">🍞</span>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800" id="companyName">مخبز أنوار الحي</h1>
                        <p class="text-sm text-gray-600" id="companySlogan">جودة تستحق الثقة</p>
                    </div>
                </div>
            </div>

            <nav class="flex-1 overflow-y-auto px-4 pb-4 custom-scroll">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    الرئيسية
                </a>
                <a href="employees.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👨‍💼</span>
                    إدارة الموظفين
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏭</span>
                    الموردين
                </a>
                <a href="cash-registers.html" class="flex items-center px-3 py-2 text-sm font-medium text-green-600 bg-green-50 rounded-md">
                    <span class="ml-3">💰</span>
                    الصناديق
                </a>

                <!-- User Info & Logout -->
                <div class="mt-auto p-4 border-t border-gray-200">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                            أ
                        </div>
                        <div class="mr-3 flex-1">
                            <p id="userFullName" class="text-sm font-medium text-gray-900">أحمد محمد</p>
                            <p id="userRole" class="text-xs text-gray-500">مدير النظام</p>
                        </div>
                        <button onclick="logout()" class="text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50" title="تسجيل الخروج">
                            <span class="text-lg">🚪</span>
                        </button>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col min-h-0">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <h2 class="text-xl font-semibold text-gray-900">إدارة الصناديق</h2>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="debugCashRegisters()" class="bg-red-600 text-white px-3 py-2 rounded-md hover:bg-red-700 flex items-center text-sm">
                                <span class="ml-1">🔍</span>
                                فحص البيانات
                            </button>
                            <button onclick="addSampleRegistersIfEmpty()" class="bg-yellow-600 text-white px-3 py-2 rounded-md hover:bg-yellow-700 flex items-center text-sm">
                                <span class="ml-1">📝</span>
                                بيانات تجريبية
                            </button>
                            <button onclick="exportToExcel()" class="bg-emerald-600 text-white px-3 py-2 rounded-md hover:bg-emerald-700 flex items-center text-sm">
                                <span class="ml-1">📊</span>
                                تصدير Excel
                            </button>
                            <button onclick="printCashRegistersList()" class="bg-indigo-600 text-white px-3 py-2 rounded-md hover:bg-indigo-700 flex items-center text-sm">
                                <span class="ml-1">🖨️</span>
                                طباعة
                            </button>
                            <button onclick="openTransferModal()" class="bg-purple-600 text-white px-4 py-2 rounded text-sm hover:bg-purple-700">
                                🔄 تحويل مبلغ
                            </button>
                            <button onclick="openAddModal()" class="bg-green-600 text-white px-4 py-2 rounded text-sm hover:bg-green-700">
                                ➕ إضافة صندوق
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6 custom-scroll">
                <!-- Success/Error Messages -->
                <div id="messageContainer" class="mb-6"></div>

                <!-- Statistics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <span class="text-2xl">💰</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">إجمالي الصناديق</p>
                                <p class="text-2xl font-bold text-gray-900" id="totalRegisters">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <span class="text-2xl">✅</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">الصناديق النشطة</p>
                                <p class="text-2xl font-bold text-gray-900" id="activeRegisters">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                <span class="text-2xl">🔒</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">الصناديق المغلقة</p>
                                <p class="text-2xl font-bold text-gray-900" id="closedRegisters">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                                <span class="text-2xl">💵</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">إجمالي الأرصدة</p>
                                <p class="text-2xl font-bold text-gray-900" id="totalBalance">0</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">البحث والتصفية</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <input type="text" id="searchInput" placeholder="البحث بالاسم أو الكود..."
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-sm"
                                   onkeyup="filterRegisters()">
                        </div>
                        <div>
                            <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-sm" onchange="filterRegisters()">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="closed">مغلق</option>
                                <option value="maintenance">صيانة</option>
                            </select>
                        </div>
                        <div>
                            <select id="branchFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-sm" onchange="filterRegisters()">
                                <option value="">جميع الفروع</option>
                            </select>
                        </div>
                        <div>
                            <select id="typeFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-sm" onchange="filterRegisters()">
                                <option value="">جميع الأنواع</option>
                                <option value="main">رئيسي</option>
                                <option value="pos">نقطة بيع</option>
                                <option value="petty">مصروفات صغيرة</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Cash Registers Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="registersGrid">
                    <!-- Cash registers will be populated here -->
                </div>

                <!-- Transfer Entries Section -->
                <div class="bg-white rounded-xl shadow-lg p-6 mt-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-bold text-gray-800">📋 قيود تحويل المبالغ</h2>
                        <div class="flex space-x-2">
                            <button onclick="refreshTransferEntries()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm">
                                🔄 تحديث
                            </button>
                            <button onclick="exportTransferEntries()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 text-sm">
                                📊 تصدير
                            </button>
                        </div>
                    </div>

                    <!-- Transfer Entries Table -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white border border-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b">رقم القيد</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b">التاريخ</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b">من صندوق</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b">إلى صندوق</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b">المبلغ</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b">البيان</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b">الحالة</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="transferEntriesTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- Transfer entries will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Empty State -->
                    <div id="transferEntriesEmpty" class="text-center py-8 hidden">
                        <div class="text-gray-400 text-lg mb-2">📋</div>
                        <p class="text-gray-500">لا توجد قيود تحويل</p>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Cash Register Modal -->
    <div id="addRegisterModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">إضافة صندوق جديد</h3>
                    <button onclick="closeAddModal()" class="text-gray-400 hover:text-gray-600">
                        <span class="text-xl">✕</span>
                    </button>
                </div>

                <form id="addRegisterForm" onsubmit="addRegister(event)">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <!-- Register Code -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">كود الصندوق *</label>
                            <input type="text" id="registerCode" required readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                                   placeholder="سيتم إنشاؤه تلقائياً">
                        </div>

                        <!-- Register Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">اسم الصندوق *</label>
                            <input type="text" id="registerName" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                                   placeholder="اسم الصندوق">
                        </div>

                        <!-- Register Type -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">نوع الصندوق *</label>
                            <select id="registerType" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                                <option value="">اختر النوع</option>
                                <option value="main">صندوق رئيسي</option>
                                <option value="pos">نقطة بيع</option>
                                <option value="petty">مصروفات صغيرة</option>
                            </select>
                        </div>

                        <!-- Branch -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الفرع *</label>
                            <select id="registerBranch" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                                <option value="">اختر الفرع</option>
                                <option value="main">الفرع الرئيسي</option>
                                <option value="branch1">فرع الملك فهد</option>
                                <option value="branch2">فرع العليا</option>
                            </select>
                        </div>

                        <!-- Responsible Employee -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الموظف المسؤول</label>
                            <select id="responsibleEmployee"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                                <option value="">اختر الموظف</option>
                            </select>
                        </div>

                        <!-- Opening Balance -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الرصيد الافتتاحي</label>
                            <input type="number" id="openingBalance" step="0.01" min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                                   placeholder="0.00">
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
                        <textarea id="registerDescription" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                                  placeholder="وصف الصندوق واستخدامه"></textarea>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeAddModal()"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                            إلغاء
                        </button>
                        <button type="submit"
                                class="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700">
                            حفظ الصندوق
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Transfer Money Modal -->
    <div id="transferModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">تحويل مبلغ بين الصناديق</h3>
                    <button onclick="closeTransferModal()" class="text-gray-400 hover:text-gray-600">
                        <span class="text-xl">✕</span>
                    </button>
                </div>

                <form id="transferForm" onsubmit="transferMoney(event)">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <!-- From Register -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">من الصندوق *</label>
                            <select id="fromRegister" required onchange="updateFromBalance()"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500">
                                <option value="">اختر الصندوق المرسل</option>
                            </select>
                            <div id="fromBalance" class="text-sm text-gray-600 mt-1"></div>
                        </div>

                        <!-- To Register -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">إلى الصندوق *</label>
                            <select id="toRegister" required onchange="updateToBalance()"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500">
                                <option value="">اختر الصندوق المستقبل</option>
                            </select>
                            <div id="toBalance" class="text-sm text-gray-600 mt-1"></div>
                        </div>

                        <!-- Transfer Amount -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">المبلغ المحول *</label>
                            <input type="number" id="transferAmount" required step="0.01" min="0.01"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                                   placeholder="0.00">
                        </div>

                        <!-- Transfer Date -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ التحويل *</label>
                            <input type="date" id="transferDate" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500">
                        </div>
                    </div>

                    <!-- Transfer Reference -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">رقم المرجع</label>
                        <input type="text" id="transferReference"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                               placeholder="رقم المرجع أو الإشارة">
                    </div>

                    <!-- Transfer Notes -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-1">ملاحظات التحويل</label>
                        <textarea id="transferNotes" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                                  placeholder="ملاحظات حول عملية التحويل..."></textarea>
                    </div>

                    <!-- Transfer Summary -->
                    <div id="transferSummary" class="bg-purple-50 p-4 rounded-lg mb-4 hidden">
                        <h4 class="text-md font-semibold text-purple-800 mb-2">ملخص التحويل</h4>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">من:</span>
                                <span id="summaryFrom" class="font-medium"></span>
                            </div>
                            <div>
                                <span class="text-gray-600">إلى:</span>
                                <span id="summaryTo" class="font-medium"></span>
                            </div>
                            <div>
                                <span class="text-gray-600">المبلغ:</span>
                                <span id="summaryAmount" class="font-bold text-purple-600"></span>
                            </div>
                            <div>
                                <span class="text-gray-600">التاريخ:</span>
                                <span id="summaryDate" class="font-medium"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeTransferModal()"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                            إلغاء
                        </button>
                        <button type="submit"
                                class="px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700">
                            تنفيذ التحويل
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Transfer Entry Modal -->
    <div id="editTransferModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">تعديل قيد التحويل</h3>
                    <button onclick="closeEditTransferModal()" class="text-gray-400 hover:text-gray-600">
                        <span class="text-xl">✕</span>
                    </button>
                </div>

                <form id="editTransferForm" onsubmit="updateTransferEntry(event)">
                    <input type="hidden" id="editTransferId">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <!-- Transfer Number -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">رقم القيد</label>
                            <input type="text" id="editTransferNumber" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50">
                        </div>

                        <!-- Transfer Date -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ التحويل *</label>
                            <input type="date" id="editTransferDate" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <!-- From Register -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">من الصندوق *</label>
                            <select id="editFromRegister" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">اختر الصندوق المرسل</option>
                            </select>
                        </div>

                        <!-- To Register -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">إلى الصندوق *</label>
                            <select id="editToRegister" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">اختر الصندوق المستقبل</option>
                            </select>
                        </div>

                        <!-- Transfer Amount -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">المبلغ المحول *</label>
                            <input type="number" id="editTransferAmount" required step="0.01" min="0.01"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <!-- Status -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                            <select id="editTransferStatus"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="completed">مكتمل</option>
                                <option value="pending">معلق</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                        </div>
                    </div>

                    <!-- Transfer Reference -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">رقم المرجع</label>
                        <input type="text" id="editTransferReference"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <!-- Transfer Notes -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-1">ملاحظات التحويل</label>
                        <textarea id="editTransferNotes" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeEditTransferModal()"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                            إلغاء
                        </button>
                        <button type="submit"
                                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                            حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Essential scripts only -->
    <script>
        // Basic currency function
        function getCurrencySymbol() {
            try {
                const settings = JSON.parse(localStorage.getItem('anwar_bakery_settings') || '{}');
                return settings.currencySymbol || 'ر.ي';
            } catch {
                return 'ر.ي';
            }
        }

        // Basic message function
        function showMessage(message, type) {
            const container = document.getElementById('messageContainer');
            const colors = {
                success: 'bg-green-100 border-green-400 text-green-700',
                error: 'bg-red-100 border-red-400 text-red-700',
                info: 'bg-blue-100 border-blue-400 text-blue-700'
            };

            container.innerHTML = `
                <div class="border px-4 py-3 rounded ${colors[type] || colors.info}">
                    ${message}
                </div>
            `;

            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        // Basic user info functions
        function loadUserInfo() {
            try {
                const user = JSON.parse(localStorage.getItem('anwar_bakery_current_user') || '{}');
                if (user.fullName) {
                    document.getElementById('userFullName').textContent = user.fullName;
                    document.getElementById('userRole').textContent = user.role || 'مستخدم';
                }
            } catch (error) {
                console.log('Could not load user info');
            }
        }

        function loadCompanyInfo() {
            try {
                const company = JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
                if (company.companyName) {
                    document.getElementById('companyName').textContent = company.companyName;
                    document.getElementById('companySlogan').textContent = company.slogan || '';
                }
            } catch (error) {
                console.log('Could not load company info');
            }
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('anwar_bakery_current_user');
                window.location.href = 'login.html';
            }
        }

        // Placeholder functions for missing features
        function exportToExcel() {
            showMessage('ميزة التصدير قيد التطوير', 'info');
        }

        function printCashRegistersList() {
            window.print();
        }
    </script>

    <script src="cash-registers.js"></script>
</body>
</html>
