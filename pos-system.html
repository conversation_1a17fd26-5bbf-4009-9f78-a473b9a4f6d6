<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نقطة البيع السريع - مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .pos-product-card {
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .pos-product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .pos-category-btn.active {
            background-color: #7c3aed !important;
            color: white !important;
        }

        .pos-payment-btn.active {
            border: 2px solid #7c3aed;
            background-color: #ede9fe;
            color: #7c3aed;
        }

        .cart-item {
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .checkout-btn {
            background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
            transition: all 0.3s ease;
        }

        .checkout-btn:hover {
            background: linear-gradient(135deg, #6d28d9 0%, #9333ea 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(124, 58, 237, 0.4);
        }

        .amount-btn {
            transition: all 0.2s ease;
        }

        .amount-btn:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="bg-white w-64 shadow-lg flex flex-col">
            <div class="p-4 flex-shrink-0">
                <div class="flex items-center mb-8">
                    <span class="text-2xl ml-3">🍞</span>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800" id="companyName">مخبز أنوار الحي</h1>
                        <p class="text-sm text-gray-600" id="companySlogan">نقطة البيع السريع</p>
                    </div>
                </div>
            </div>

            <nav class="flex-1 overflow-y-auto px-4 pb-4">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    الرئيسية
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="#" class="flex items-center px-3 py-2 text-sm font-medium text-purple-600 bg-purple-50 rounded-md">
                    <span class="ml-3">⚡</span>
                    نقطة البيع
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>

                <!-- User Info & Logout -->
                <div class="mt-auto p-4 border-t border-gray-200">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                            أ
                        </div>
                        <div class="mr-3 flex-1">
                            <p id="userFullName" class="text-sm font-medium text-gray-900">أحمد محمد</p>
                            <p id="userRole" class="text-xs text-gray-500">كاشير</p>
                        </div>
                        <button onclick="logout()" class="text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50" title="تسجيل الخروج">
                            <span class="text-lg">🚪</span>
                        </button>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col min-h-0">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <a href="invoices.html" class="text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">←</span>
                            </a>
                            <h2 class="text-xl font-semibold text-gray-900">نقطة البيع السريع</h2>
                            <div class="mr-4 flex items-center space-x-4">
                                <div class="text-sm text-gray-600">
                                    <span class="font-medium">الفرع:</span>
                                    <span id="currentBranch" class="text-purple-600">الفرع الرئيسي</span>
                                </div>
                                <div class="text-sm text-gray-600">
                                    <span class="font-medium">الصندوق:</span>
                                    <span id="currentCashRegister" class="text-purple-600">صندوق 1</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="openShiftReport()" class="bg-purple-100 text-purple-700 px-3 py-2 rounded text-sm hover:bg-purple-200">
                                📊 تقرير الوردية
                            </button>
                            <button onclick="window.history.back()" class="text-gray-600 hover:text-gray-800 px-2 py-2 rounded text-sm">
                                ← رجوع
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-hidden p-4">
                <!-- Success/Error Messages -->
                <div id="messageContainer" class="mb-4"></div>

                <!-- POS Interface -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
                    <!-- Product Selection & Search -->
                    <div class="lg:col-span-2 flex flex-col">
                        <!-- Product Search -->
                        <div class="bg-white rounded-xl shadow-lg p-4 mb-4">
                            <div class="relative mb-4">
                                <input type="text" id="posSearchInput" placeholder="ابحث بالاسم، الكود، أو الباركود..."
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-lg"
                                       oninput="searchPOSProducts()" onkeydown="handlePOSSearch(event)">
                                <div class="absolute left-3 top-3.5 text-gray-400">
                                    <span class="text-xl">🔍</span>
                                </div>
                            </div>

                            <!-- Quick Categories -->
                            <div class="flex flex-wrap gap-2">
                                <button onclick="filterPOSProducts('all')" class="pos-category-btn active bg-purple-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-purple-700">
                                    🛍️ الكل
                                </button>
                                <button onclick="filterPOSProducts('bread')" class="pos-category-btn bg-yellow-100 text-yellow-800 px-4 py-2 rounded-lg text-sm hover:bg-yellow-200">
                                    🍞 خبز
                                </button>
                                <button onclick="filterPOSProducts('pastry')" class="pos-category-btn bg-orange-100 text-orange-800 px-4 py-2 rounded-lg text-sm hover:bg-orange-200">
                                    🥐 معجنات
                                </button>
                                <button onclick="filterPOSProducts('cake')" class="pos-category-btn bg-pink-100 text-pink-800 px-4 py-2 rounded-lg text-sm hover:bg-pink-200">
                                    🎂 كيك وحلويات
                                </button>
                                <button onclick="filterPOSProducts('drinks')" class="pos-category-btn bg-blue-100 text-blue-800 px-4 py-2 rounded-lg text-sm hover:bg-blue-200">
                                    🥤 مشروبات
                                </button>
                            </div>
                        </div>

                        <!-- Product Grid -->
                        <div class="bg-white rounded-xl shadow-lg p-4 flex-1 overflow-hidden">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <span class="ml-2">🛍️</span>
                                المنتجات المتاحة
                            </h3>
                            <div id="posProductsGrid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 overflow-y-auto h-full">
                                <!-- Products will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- Shopping Cart & Checkout -->
                    <div class="lg:col-span-1 flex flex-col">
                        <!-- Current Sale -->
                        <div class="bg-white rounded-xl shadow-lg p-4 mb-4">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                    <span class="ml-2">🛒</span>
                                    سلة التسوق
                                </h3>
                                <button onclick="clearPOSCart()" class="text-red-600 hover:text-red-800 text-sm">
                                    🗑️ مسح الكل
                                </button>
                            </div>

                            <!-- Cart Items -->
                            <div id="posCartItems" class="space-y-3 mb-4 max-h-64 overflow-y-auto custom-scroll">
                                <!-- Cart items will be populated here -->
                            </div>

                            <!-- Cart Summary -->
                            <div class="border-t border-gray-200 pt-4">
                                <div class="space-y-2">
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">عدد الأصناف:</span>
                                        <span id="posItemsCount" class="font-medium">0</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">المجموع الفرعي:</span>
                                        <span id="posSubtotal" class="font-medium">0.00</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">الضريبة (15%):</span>
                                        <span id="posTax" class="font-medium">0.00</span>
                                    </div>
                                    <div class="flex justify-between text-lg font-bold border-t border-gray-200 pt-2">
                                        <span>الإجمالي:</span>
                                        <span id="posTotal" class="text-purple-600">0.00</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Info -->
                        <div class="bg-white rounded-xl shadow-lg p-4 mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <span class="ml-2">👤</span>
                                معلومات العميل
                            </h3>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">نوع العميل</label>
                                    <div class="flex space-x-2">
                                        <label class="flex items-center text-sm">
                                            <input type="radio" name="posCustomerType" value="cash" checked onchange="togglePOSCustomer()"
                                                   class="ml-1 text-purple-600 focus:ring-purple-500">
                                            نقدي
                                        </label>
                                        <label class="flex items-center text-sm">
                                            <input type="radio" name="posCustomerType" value="registered" onchange="togglePOSCustomer()"
                                                   class="ml-1 text-purple-600 focus:ring-purple-500">
                                            عميل مسجل
                                        </label>
                                    </div>
                                </div>
                                <div id="posCustomerSelect" style="display: none;">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">اختر العميل</label>
                                    <select id="posCustomerId" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm">
                                        <option value="">اختر العميل</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Payment & Checkout -->
                        <div class="bg-white rounded-xl shadow-lg p-4 flex-1">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <span class="ml-2">💳</span>
                                الدفع والإتمام
                            </h3>
                            <div class="space-y-4">
                                <!-- Payment Method -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">طريقة الدفع</label>
                                    <div class="grid grid-cols-2 gap-2">
                                        <button onclick="selectPOSPayment('cash')" class="pos-payment-btn active bg-green-100 text-green-800 px-3 py-2 rounded text-sm hover:bg-green-200 flex items-center justify-center">
                                            💵 نقدي
                                        </button>
                                        <button onclick="selectPOSPayment('card')" class="pos-payment-btn bg-blue-100 text-blue-800 px-3 py-2 rounded text-sm hover:bg-blue-200 flex items-center justify-center">
                                            💳 بطاقة
                                        </button>
                                    </div>
                                </div>

                                <!-- Cash Payment Details -->
                                <div id="posCashPayment">
                                    <div class="space-y-3">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">المبلغ المدفوع</label>
                                            <input type="number" id="posPaidAmount" step="0.01" min="0" placeholder="المبلغ المدفوع (﷼)" data-currency-field
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-lg font-bold text-center"
                                                   onchange="calculatePOSChange()">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">الباقي</label>
                                            <div id="posChange" class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-lg font-bold text-center text-green-600">
                                                0.00
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Quick Amount Buttons -->
                                <div class="grid grid-cols-3 gap-2">
                                    <button onclick="setPOSAmount(50)" class="amount-btn bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm hover:bg-gray-200">50</button>
                                    <button onclick="setPOSAmount(100)" class="amount-btn bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm hover:bg-gray-200">100</button>
                                    <button onclick="setPOSAmount(200)" class="amount-btn bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm hover:bg-gray-200">200</button>
                                    <button onclick="setPOSAmount(500)" class="amount-btn bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm hover:bg-gray-200">500</button>
                                    <button onclick="setPOSExactAmount()" class="amount-btn bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm hover:bg-purple-200">بالضبط</button>
                                    <button onclick="clearPOSAmount()" class="amount-btn bg-red-100 text-red-800 px-2 py-1 rounded text-sm hover:bg-red-200">مسح</button>
                                </div>

                                <!-- Checkout Buttons -->
                                <div class="space-y-2 pt-4 border-t border-gray-200">
                                    <button onclick="completePOSSale()" class="checkout-btn w-full text-white py-3 rounded-lg font-semibold text-lg">
                                        ✅ إتمام البيع
                                    </button>
                                    <button onclick="holdPOSSale()" class="w-full bg-yellow-600 text-white py-2 rounded-lg hover:bg-yellow-700 text-sm">
                                        ⏸️ تعليق البيع
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Sample products data
        const posProducts = [
            { id: 1, name: 'خبز أبيض', code: 'BR001', price: 2.50, category: 'bread', icon: '🍞', stock: 50 },
            { id: 2, name: 'خبز أسمر', code: 'BR002', price: 3.00, category: 'bread', icon: '🍞', stock: 30 },
            { id: 3, name: 'كرواسان', code: 'PS001', price: 5.00, category: 'pastry', icon: '🥐', stock: 25 },
            { id: 4, name: 'دونات', code: 'PS002', price: 4.50, category: 'pastry', icon: '🍩', stock: 20 },
            { id: 5, name: 'كيك شوكولاتة', code: 'CK001', price: 25.00, category: 'cake', icon: '🎂', stock: 10 },
            { id: 6, name: 'عصير برتقال', code: 'DR001', price: 6.00, category: 'drinks', icon: '🥤', stock: 40 }
        ];

        let posCart = [];
        let currentCategory = 'all';

        // Initialize POS
        function initializePOS() {
            loadPOSProducts();
            updateCartDisplay();
        }

        // Load products
        function loadPOSProducts() {
            const grid = document.getElementById('posProductsGrid');
            const filteredProducts = currentCategory === 'all'
                ? posProducts
                : posProducts.filter(p => p.category === currentCategory);

            grid.innerHTML = filteredProducts.map(product => `
                <div class="pos-product-card bg-white border border-gray-200 rounded-lg p-4 text-center" onclick="addToPOSCart(${product.id})">
                    <div class="text-3xl mb-2">${product.icon}</div>
                    <h4 class="font-medium text-gray-900 text-sm mb-1">${product.name}</h4>
                    <p class="text-xs text-gray-500 mb-2">${product.code}</p>
                    <p class="text-lg font-bold text-purple-600">${formatCurrency(product.price)}</p>
                    <p class="text-xs text-gray-500">المخزون: ${product.stock}</p>
                </div>
            `).join('');
        }

        // Filter products by category
        function filterPOSProducts(category) {
            currentCategory = category;

            // Update active button
            document.querySelectorAll('.pos-category-btn').forEach(btn => {
                btn.classList.remove('active', 'bg-purple-600', 'text-white');
                btn.classList.add('bg-gray-100', 'text-gray-800');
            });

            event.target.classList.add('active', 'bg-purple-600', 'text-white');
            event.target.classList.remove('bg-gray-100', 'text-gray-800');

            loadPOSProducts();
        }

        // Add to cart
        function addToPOSCart(productId) {
            const product = posProducts.find(p => p.id === productId);
            if (!product || product.stock <= 0) return;

            const existingItem = posCart.find(item => item.id === productId);
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                posCart.push({
                    ...product,
                    quantity: 1
                });
            }

            updateCartDisplay();
        }

        // Update cart display
        function updateCartDisplay() {
            const cartItems = document.getElementById('posCartItems');
            const itemsCount = document.getElementById('posItemsCount');
            const subtotal = document.getElementById('posSubtotal');
            const tax = document.getElementById('posTax');
            const total = document.getElementById('posTotal');

            if (posCart.length === 0) {
                cartItems.innerHTML = '<p class="text-gray-500 text-center py-4">السلة فارغة</p>';
                itemsCount.textContent = '0';
                subtotal.textContent = '0.00';
                tax.textContent = '0.00';
                total.textContent = '0.00';
                return;
            }

            cartItems.innerHTML = posCart.map(item => `
                <div class="cart-item flex items-center justify-between bg-gray-50 p-3 rounded">
                    <div class="flex items-center">
                        <span class="text-lg ml-2">${item.icon}</span>
                        <div>
                            <div class="font-medium text-sm">${item.name}</div>
                            <div class="text-xs text-gray-500">${formatCurrency(item.price)} × ${item.quantity}</div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button onclick="updatePOSQuantity(${item.id}, -1)" class="bg-red-100 text-red-600 w-6 h-6 rounded text-xs">-</button>
                        <span class="font-medium">${item.quantity}</span>
                        <button onclick="updatePOSQuantity(${item.id}, 1)" class="bg-green-100 text-green-600 w-6 h-6 rounded text-xs">+</button>
                        <button onclick="removeFromPOSCart(${item.id})" class="text-red-600 text-xs">🗑️</button>
                    </div>
                </div>
            `).join('');

            const subtotalAmount = posCart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const taxAmount = subtotalAmount * 0.15;
            const totalAmount = subtotalAmount + taxAmount;

            itemsCount.textContent = posCart.reduce((sum, item) => sum + item.quantity, 0);
            subtotal.textContent = subtotalAmount.toFixed(2);
            tax.textContent = taxAmount.toFixed(2);
            total.textContent = totalAmount.toFixed(2);
        }

        // Update quantity
        function updatePOSQuantity(productId, change) {
            const item = posCart.find(item => item.id === productId);
            if (item) {
                item.quantity += change;
                if (item.quantity <= 0) {
                    removeFromPOSCart(productId);
                } else {
                    updateCartDisplay();
                }
            }
        }

        // Remove from cart
        function removeFromPOSCart(productId) {
            posCart = posCart.filter(item => item.id !== productId);
            updateCartDisplay();
        }

        // Clear cart
        function clearPOSCart() {
            posCart = [];
            updateCartDisplay();
        }

        // Toggle customer type
        function togglePOSCustomer() {
            const customerType = document.querySelector('input[name="posCustomerType"]:checked').value;
            const customerSelect = document.getElementById('posCustomerSelect');

            if (customerType === 'registered') {
                customerSelect.style.display = 'block';
            } else {
                customerSelect.style.display = 'none';
            }
        }

        // Select payment method
        function selectPOSPayment(method) {
            document.querySelectorAll('.pos-payment-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // Set amount
        function setPOSAmount(amount) {
            document.getElementById('posPaidAmount').value = amount;
            calculatePOSChange();
        }

        // Set exact amount
        function setPOSExactAmount() {
            const total = parseFloat(document.getElementById('posTotal').textContent);
            document.getElementById('posPaidAmount').value = total.toFixed(2);
            calculatePOSChange();
        }

        // Clear amount
        function clearPOSAmount() {
            document.getElementById('posPaidAmount').value = '';
            document.getElementById('posChange').textContent = '0.00';
        }

        // Calculate change
        function calculatePOSChange() {
            const total = parseFloat(document.getElementById('posTotal').textContent);
            const paid = parseFloat(document.getElementById('posPaidAmount').value) || 0;
            const change = paid - total;

            const changeElement = document.getElementById('posChange');
            changeElement.textContent = change.toFixed(2);
            changeElement.className = change >= 0 ?
                'w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-lg font-bold text-center text-green-600' :
                'w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-lg font-bold text-center text-red-600';
        }

        // Complete sale
        function completePOSSale() {
            if (posCart.length === 0) {
                alert('السلة فارغة!');
                return;
            }

            const total = parseFloat(document.getElementById('posTotal').textContent);
            const paid = parseFloat(document.getElementById('posPaidAmount').value) || 0;

            if (paid < total) {
                alert('المبلغ المدفوع أقل من الإجمالي!');
                return;
            }

            // Save sale
            const sale = {
                id: Date.now(),
                items: [...posCart],
                subtotal: parseFloat(document.getElementById('posSubtotal').textContent),
                tax: parseFloat(document.getElementById('posTax').textContent),
                total: total,
                paid: paid,
                change: paid - total,
                paymentMethod: document.querySelector('.pos-payment-btn.active').textContent.trim(),
                customerType: document.querySelector('input[name="posCustomerType"]:checked').value,
                customerId: document.getElementById('posCustomerId').value || null,
                cashier: document.getElementById('userFullName').textContent,
                date: new Date().toISOString()
            };

            // Save to localStorage
            const sales = JSON.parse(localStorage.getItem('anwar_bakery_pos_sales') || '[]');
            sales.push(sale);
            localStorage.setItem('anwar_bakery_pos_sales', JSON.stringify(sales));

            // Update stock
            updatePOSStock();

            // Clear cart
            clearPOSCart();
            clearPOSAmount();

            alert(`تم إتمام البيع بنجاح!\nالإجمالي: ${formatCurrency(total)}\nالمدفوع: ${formatCurrency(paid)}\nالباقي: ${formatCurrency(paid - total)}`);

            // Print receipt option
            if (confirm('هل تريد طباعة الإيصال؟')) {
                printPOSReceipt(sale);
            }
        }

        // Update stock
        function updatePOSStock() {
            posCart.forEach(cartItem => {
                const product = posProducts.find(p => p.id === cartItem.id);
                if (product) {
                    product.stock -= cartItem.quantity;
                }
            });
            loadPOSProducts();
        }

        // Hold sale
        function holdPOSSale() {
            if (posCart.length === 0) {
                alert('السلة فارغة!');
                return;
            }

            const heldSales = JSON.parse(localStorage.getItem('anwar_bakery_held_sales') || '[]');
            heldSales.push({
                id: Date.now(),
                items: [...posCart],
                date: new Date().toISOString()
            });
            localStorage.setItem('anwar_bakery_held_sales', JSON.stringify(heldSales));

            clearPOSCart();
            alert('تم تعليق البيع بنجاح!');
        }

        // Print receipt
        function printPOSReceipt(sale) {
            const receiptWindow = window.open('', '_blank');
            const companyData = JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');

            receiptWindow.document.write(`
                <html dir="rtl">
                <head>
                    <title>إيصال البيع</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; margin: 20px; }
                        .receipt { max-width: 300px; margin: 0 auto; }
                        .header { border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 10px; }
                        .items { text-align: right; margin: 10px 0; }
                        .item { display: flex; justify-content: space-between; margin-bottom: 5px; }
                        .totals { border-top: 2px solid #000; padding-top: 10px; margin-top: 10px; }
                        .total-line { display: flex; justify-content: space-between; margin-bottom: 3px; }
                        .grand-total { font-weight: bold; font-size: 1.2em; border-top: 1px solid #000; padding-top: 5px; }
                    </style>
                </head>
                <body>
                    <div class="receipt">
                        <div class="header">
                            <h2>${companyData.companyNameAr || 'مخبز أنوار الحي'}</h2>
                            <p>نقطة البيع السريع</p>
                            <p>${new Date(sale.date).toLocaleString('ar-SA')}</p>
                            <p>الكاشير: ${sale.cashier}</p>
                        </div>
                        <div class="items">
                            ${sale.items.map(item => `
                                <div class="item">
                                    <span>${item.name} × ${item.quantity}</span>
                                    <span>${formatCurrency(item.price * item.quantity)}</span>
                                </div>
                            `).join('')}
                        </div>
                        <div class="totals">
                            <div class="total-line">
                                <span>المجموع الفرعي:</span>
                                <span>${formatCurrency(sale.subtotal)}</span>
                            </div>
                            <div class="total-line">
                                <span>الضريبة (15%):</span>
                                <span>${formatCurrency(sale.tax)}</span>
                            </div>
                            <div class="total-line grand-total">
                                <span>الإجمالي:</span>
                                <span>${formatCurrency(sale.total)}</span>
                            </div>
                            <div class="total-line">
                                <span>المدفوع:</span>
                                <span>${formatCurrency(sale.paid)}</span>
                            </div>
                            <div class="total-line">
                                <span>الباقي:</span>
                                <span>${formatCurrency(sale.change)}</span>
                            </div>
                        </div>
                        <p style="margin-top: 20px;">شكراً لزيارتكم</p>
                    </div>
                </body>
                </html>
            `);

            receiptWindow.document.close();
            receiptWindow.print();
        }

        // Search products
        function searchPOSProducts() {
            const searchTerm = document.getElementById('posSearchInput').value.toLowerCase();
            const grid = document.getElementById('posProductsGrid');

            const filteredProducts = posProducts.filter(product =>
                product.name.toLowerCase().includes(searchTerm) ||
                product.code.toLowerCase().includes(searchTerm)
            );

            grid.innerHTML = filteredProducts.map(product => `
                <div class="pos-product-card bg-white border border-gray-200 rounded-lg p-4 text-center" onclick="addToPOSCart(${product.id})">
                    <div class="text-3xl mb-2">${product.icon}</div>
                    <h4 class="font-medium text-gray-900 text-sm mb-1">${product.name}</h4>
                    <p class="text-xs text-gray-500 mb-2">${product.code}</p>
                    <p class="text-lg font-bold text-purple-600">${formatCurrency(product.price)}</p>
                    <p class="text-xs text-gray-500">المخزون: ${product.stock}</p>
                </div>
            `).join('');
        }

        // Handle search with Enter key
        function handlePOSSearch(event) {
            if (event.key === 'Enter') {
                const searchTerm = event.target.value.toLowerCase();
                const product = posProducts.find(p =>
                    p.code.toLowerCase() === searchTerm ||
                    p.name.toLowerCase() === searchTerm
                );

                if (product) {
                    addToPOSCart(product.id);
                    event.target.value = '';
                }
            }
        }

        // Open shift report
        function openShiftReport() {
            alert('تقرير الوردية قيد التطوير...');
        }

        // Logout function
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('anwar_bakery_session');
                sessionStorage.removeItem('anwar_bakery_session');
                window.location.href = 'login.html';
            }
        }

        // Currency format function
        function formatCurrency(amount) {
            return `${amount.toFixed(2)} ر.س`;
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializePOS();
        });
    </script>

</body>
</html>