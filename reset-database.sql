-- Reset all data except chart_of_accounts
SET FOREIGN_KEY_CHECKS = 0;

-- Empty all tables except chart_of_accounts
TRUNCATE TABLE users;
TRUNCATE TABLE branches;
TRUNCATE TABLE units;
TRUNCATE TABLE product_categories;
TRUNCATE TABLE products;
TRUNCATE TABLE inventory_movements;
TRUNCATE TABLE customers;
TRUNCATE TABLE suppliers;
TRUNCATE TABLE journal_entries;
TRUNCATE TABLE journal_entry_details;
TRUNCATE TABLE company_settings;
-- Add more TRUNCATE TABLE statements for any other tables as needed

-- Reset balances in chart_of_accounts
UPDATE chart_of_accounts SET opening_balance = 0, current_balance = 0;

SET FOREIGN_KEY_CHECKS = 1;

-- Now the system is clean except for the chart of accounts structure
