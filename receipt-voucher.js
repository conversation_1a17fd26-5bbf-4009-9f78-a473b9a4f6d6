// Receipt Voucher Management System
let currentVoucher = {
    type: 'receipt',
    journalEntries: []
};

let voucherCounter = 1;
let accounts = [];
let cashRegisters = [];

// Load initial data
function loadInitialData() {
    loadUserInfo();
    loadCompanyInfo();
    generateVoucherNumber();
    setCurrentDate();
    loadBranches();
    loadAccounts();
    loadBanks();
}

// Generate voucher number
function generateVoucherNumber() {
    const savedCounter = localStorage.getItem('anwar_bakery_receipt_counter');
    if (savedCounter) {
        voucherCounter = parseInt(savedCounter) + 1;
    }

    const voucherNumber = `REC-${new Date().getFullYear()}-${voucherCounter.toString().padStart(3, '0')}`;
    document.getElementById('voucherNumber').value = voucherNumber;
}

// Set current date
function setCurrentDate() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('voucherDate').value = today;
}

// Load branches
function loadBranches() {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    const branchSelect = document.getElementById('branchId');

    branchSelect.innerHTML = '<option value="">اختر الفرع</option>';

    if (savedBranches) {
        const branches = JSON.parse(savedBranches);
        branches.filter(branch => branch.isActive).forEach(branch => {
            const option = document.createElement('option');
            option.value = branch.id;
            option.textContent = branch.branchName;
            branchSelect.appendChild(option);
        });
    }
}

// Load accounts from chart of accounts
function loadAccounts() {
    const savedAccounts = localStorage.getItem('anwar_bakery_accounts');
    if (savedAccounts) {
        accounts = JSON.parse(savedAccounts);

        // Populate account dropdown
        const accountSelect = document.getElementById('accountId');
        accountSelect.innerHTML = '<option value="">اختيار تلقائي</option>';

        accounts.forEach(account => {
            const option = document.createElement('option');
            option.value = account.id;
            option.textContent = `${account.name} (${account.code})`;
            option.dataset.code = account.code;
            option.dataset.type = account.type;
            accountSelect.appendChild(option);
        });
    }
}

// Load banks
function loadBanks() {
    // Create sample banks if not exist
    let banks = [];
    const savedBanks = localStorage.getItem('anwar_bakery_banks');
    if (savedBanks) {
        banks = JSON.parse(savedBanks);
    } else {
        banks = [
            { id: 1, bankName: 'البنك الأهلي السعودي', accountNumber: '*********', isActive: true },
            { id: 2, bankName: 'بنك الرياض', accountNumber: '*********', isActive: true },
            { id: 3, bankName: 'البنك السعودي للاستثمار', accountNumber: '*********', isActive: true },
            { id: 4, bankName: 'بنك ساب', accountNumber: '*********', isActive: true }
        ];
        localStorage.setItem('anwar_bakery_banks', JSON.stringify(banks));
    }

    // Populate bank dropdown
    const bankSelect = document.getElementById('bankId');
    bankSelect.innerHTML = '<option value="">اختر البنك</option>';

    banks.filter(bank => bank.isActive).forEach(bank => {
        const option = document.createElement('option');
        option.value = bank.id;
        option.textContent = `${bank.bankName} (${bank.accountNumber})`;
        bankSelect.appendChild(option);
    });
}

// Toggle payment method fields
function togglePaymentMethod() {
    const paymentMethod = document.getElementById('paymentMethod').value;
    const bankField = document.getElementById('bankField');
    const referenceLabel = document.getElementById('referenceLabel');
    const referenceInput = document.getElementById('referenceNumber');

    if (paymentMethod === 'cash') {
        bankField.style.display = 'none';
        referenceLabel.textContent = 'رقم المرجع';
        referenceInput.placeholder = 'رقم مرجعي (اختياري)';
        referenceInput.required = false;
    } else {
        bankField.style.display = 'block';

        switch(paymentMethod) {
            case 'check':
                referenceLabel.textContent = 'رقم الشيك *';
                referenceInput.placeholder = 'رقم الشيك';
                referenceInput.required = true;
                break;
            case 'bank':
                referenceLabel.textContent = 'رقم التحويل *';
                referenceInput.placeholder = 'رقم التحويل البنكي';
                referenceInput.required = true;
                break;
            case 'card':
                referenceLabel.textContent = 'رقم العملية *';
                referenceInput.placeholder = 'رقم عملية البطاقة';
                referenceInput.required = true;
                break;
        }
    }

    updateJournalPreview();
}

// Load branch data when branch is selected
function loadBranchData() {
    const branchId = document.getElementById('branchId').value;

    if (branchId) {
        loadCashRegisters(branchId);
        loadPayers(branchId);
    } else {
        clearDependentDropdowns();
    }
}

// Load cash registers for selected branch
function loadCashRegisters(branchId) {
    const savedCashRegisters = localStorage.getItem('anwar_bakery_cash_registers');
    const cashRegisterSelect = document.getElementById('cashRegisterId');

    cashRegisterSelect.innerHTML = '<option value="">اختر الصندوق</option>';

    if (savedCashRegisters) {
        cashRegisters = JSON.parse(savedCashRegisters);
        const branchCashRegisters = cashRegisters.filter(register =>
            register.branchId == branchId && register.isActive
        );

        branchCashRegisters.forEach(register => {
            const option = document.createElement('option');
            option.value = register.id;
            option.textContent = `${register.registerName} (${register.currentBalance.toLocaleString()})`;
            option.dataset.accountId = register.accountId;

            // Mark default cash register
            if (register.isDefault) {
                option.selected = true;
                option.textContent += ' - افتراضي';
            }

            cashRegisterSelect.appendChild(option);
        });
    }
}

// Toggle payer type (customer/supplier/employee/other)
function togglePayerType() {
    const payerType = document.querySelector('input[name="payerType"]:checked').value;
    const payerLabel = document.getElementById('payerLabel');
    const branchId = document.getElementById('branchId').value;

    switch(payerType) {
        case 'customer':
            payerLabel.textContent = 'العميل *';
            break;
        case 'supplier':
            payerLabel.textContent = 'المورد *';
            break;
        case 'employee':
            payerLabel.textContent = 'الموظف *';
            break;
        case 'other':
            payerLabel.textContent = 'الحساب *';
            break;
    }

    // Reload payers based on type and branch
    if (branchId) {
        loadPayers(branchId);
    }
}

// Load payers based on selected type and branch
function loadPayers(branchId) {
    const payerType = document.querySelector('input[name="payerType"]:checked').value;
    const payerSelect = document.getElementById('payerId');

    payerSelect.innerHTML = '<option value="">اختر...</option>';

    if (payerType === 'customer') {
        loadCustomers(branchId);
    } else if (payerType === 'supplier') {
        loadSuppliers(branchId);
    } else if (payerType === 'employee') {
        loadEmployees(branchId);
    } else if (payerType === 'other') {
        loadOtherAccounts();
    }
}

// Load customers for the branch
function loadCustomers(branchId) {
    const savedCustomers = localStorage.getItem('anwar_bakery_customers');
    const payerSelect = document.getElementById('payerId');

    if (savedCustomers) {
        const customers = JSON.parse(savedCustomers);
        const branchCustomers = customers.filter(customer =>
            customer.isActive && (
                !customer.branchId ||
                customer.branchId == branchId
            )
        );

        branchCustomers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.id;
            option.textContent = customer.customerName;
            option.dataset.type = 'customer';
            option.dataset.accountId = customer.accountId;
            payerSelect.appendChild(option);
        });
    }
}

// Load suppliers for the branch
function loadSuppliers(branchId) {
    const savedSuppliers = localStorage.getItem('anwar_bakery_suppliers');
    const payerSelect = document.getElementById('payerId');

    if (savedSuppliers) {
        const suppliers = JSON.parse(savedSuppliers);
        const branchSuppliers = suppliers.filter(supplier =>
            supplier.isActive && (
                !supplier.branchId ||
                supplier.branchId == branchId
            )
        );

        branchSuppliers.forEach(supplier => {
            const option = document.createElement('option');
            option.value = supplier.id;
            option.textContent = supplier.supplierName;
            option.dataset.type = 'supplier';
            option.dataset.accountId = supplier.accountId;
            payerSelect.appendChild(option);
        });
    }
}

// Load employees for the branch
function loadEmployees(branchId) {
    const savedEmployees = localStorage.getItem('anwar_bakery_employees');
    const payerSelect = document.getElementById('payerId');

    if (savedEmployees) {
        const employees = JSON.parse(savedEmployees);
        const branchEmployees = employees.filter(employee =>
            employee.status === 'active' && employee.branchId == branchId
        );

        branchEmployees.forEach(employee => {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = `${employee.fullName} (${employee.employeeNumber})`;
            option.dataset.type = 'employee';
            // Create employee account if not exists
            option.dataset.accountId = getOrCreateEmployeeAccount(employee);
            payerSelect.appendChild(option);
        });
    }
}

// Get or create employee account
function getOrCreateEmployeeAccount(employee) {
    // Look for existing employee account
    let employeeAccount = accounts.find(acc =>
        acc.code === `1140${employee.id.toString().padStart(3, '0')}`
    );

    if (!employeeAccount) {
        // Create new employee account
        employeeAccount = {
            id: Math.max(...accounts.map(acc => acc.id), 0) + 1,
            code: `1140${employee.id.toString().padStart(3, '0')}`,
            name: `سلف الموظفين - ${employee.fullName}`,
            type: 'asset',
            parentId: accounts.find(acc => acc.code === '1140')?.id || null,
            level: 2,
            balance: 0,
            debitBalance: 0,
            creditBalance: 0,
            isDefault: false,
            description: `حساب سلف الموظف ${employee.fullName}`,
            createdAt: new Date().toISOString()
        };

        accounts.push(employeeAccount);
        localStorage.setItem('anwar_bakery_accounts', JSON.stringify(accounts));
    }

    return employeeAccount.id;
}

// Load other accounts (for miscellaneous receipts)
function loadOtherAccounts() {
    const payerSelect = document.getElementById('payerId');

    // Add common receipt accounts
    const commonAccounts = accounts.filter(account =>
        account.type === 'revenue' ||
        account.type === 'liability' ||
        account.code.startsWith('4') || // Revenue accounts
        account.code.startsWith('2')    // Liability accounts
    );

    commonAccounts.forEach(account => {
        const option = document.createElement('option');
        option.value = account.id;
        option.textContent = `${account.name} (${account.code})`;
        option.dataset.type = 'account';
        option.dataset.accountId = account.id;
        payerSelect.appendChild(option);
    });
}

// Load payer data when payer is selected
function loadPayerData() {
    updateJournalPreview();
}

// Update journal entry preview
function updateJournalPreview() {
    const amount = parseFloat(document.getElementById('amount').value) || 0;
    const cashRegisterId = document.getElementById('cashRegisterId').value;
    const payerId = document.getElementById('payerId').value;
    const accountId = document.getElementById('accountId').value;

    if (amount <= 0) {
        hideJournalPreview();
        return;
    }

    // Get cash register account
    let cashAccount = null;
    if (cashRegisterId) {
        const cashRegister = cashRegisters.find(reg => reg.id == cashRegisterId);
        if (cashRegister) {
            cashAccount = accounts.find(acc => acc.id == cashRegister.accountId);
        }
    }

    // Get payer account
    let payerAccount = null;
    if (accountId) {
        // Manual account selection
        payerAccount = accounts.find(acc => acc.id == accountId);
    } else if (payerId) {
        // Auto-detect based on payer type
        const payerSelect = document.getElementById('payerId');
        const selectedOption = payerSelect.options[payerSelect.selectedIndex];
        if (selectedOption && selectedOption.dataset.accountId) {
            payerAccount = accounts.find(acc => acc.id == selectedOption.dataset.accountId);
        }
    }

    if (!cashAccount || !payerAccount) {
        hideJournalPreview();
        return;
    }

    // Create journal entries
    const journalEntries = [
        {
            account: cashAccount,
            debit: amount,
            credit: 0
        },
        {
            account: payerAccount,
            debit: 0,
            credit: amount
        }
    ];

    renderJournalPreview(journalEntries);
}

// Render journal preview
function renderJournalPreview(entries) {
    const journalEntriesDiv = document.getElementById('journalEntries');
    const totalDebitSpan = document.getElementById('totalDebit');
    const totalCreditSpan = document.getElementById('totalCredit');
    const currencySymbol = getCurrencySymbol();

    let totalDebit = 0;
    let totalCredit = 0;

    journalEntriesDiv.innerHTML = entries.map(entry => {
        totalDebit += entry.debit;
        totalCredit += entry.credit;

        return `
            <div class="grid grid-cols-3 gap-4 text-xs py-1">
                <div class="text-gray-900">${entry.account.name} (${entry.account.code})</div>
                <div class="text-center ${entry.debit > 0 ? 'font-semibold text-green-600' : 'text-gray-400'}">
                    ${entry.debit > 0 ? entry.debit.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '') : '-'}
                </div>
                <div class="text-center ${entry.credit > 0 ? 'font-semibold text-red-600' : 'text-gray-400'}">
                    ${entry.credit > 0 ? entry.credit.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '') : '-'}
                </div>
            </div>
        `;
    }).join('');

    totalDebitSpan.textContent = totalDebit.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '');
    totalCreditSpan.textContent = totalCredit.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '');

    // Show preview
    const preview = document.getElementById('journalPreview');
    preview.classList.remove('hide');
    preview.classList.add('show');

    // Store entries for saving
    currentVoucher.journalEntries = entries;
}

// Hide journal preview
function hideJournalPreview() {
    const preview = document.getElementById('journalPreview');
    preview.classList.remove('show');
    preview.classList.add('hide');
    currentVoucher.journalEntries = [];
}

// Clear dependent dropdowns
function clearDependentDropdowns() {
    document.getElementById('cashRegisterId').innerHTML = '<option value="">اختر الصندوق</option>';
    document.getElementById('payerId').innerHTML = '<option value="">اختر...</option>';
    hideJournalPreview();
}

// Get currency symbol from settings
function getCurrencySymbol() {
    const savedSettings = localStorage.getItem('anwar_bakery_settings');
    if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        return settings.currency || '';
    }
    return '';
}

// Load user info
function loadUserInfo() {
    const savedUser = localStorage.getItem('anwar_bakery_current_user');
    if (savedUser) {
        const user = JSON.parse(savedUser);
        const userFullNameElement = document.getElementById('userFullName');
        const userRoleElement = document.getElementById('userRole');

        if (userFullNameElement) {
            userFullNameElement.textContent = user.fullName || 'أحمد محمد';
        }
        if (userRoleElement) {
            userRoleElement.textContent = user.role === 'admin' ? 'مدير النظام' : (user.role || 'مدير النظام');
        }
    }
}

// Load company info
function loadCompanyInfo() {
    const savedCompany = localStorage.getItem('anwar_bakery_company');
    if (savedCompany) {
        const company = JSON.parse(savedCompany);
        const companyNameElement = document.getElementById('companyName');
        const companySloganElement = document.getElementById('companySlogan');

        if (companyNameElement) {
            companyNameElement.textContent = company.companyName || 'مخبز أنوار الحي';
        }
        if (companySloganElement) {
            companySloganElement.textContent = company.slogan || 'جودة تستحق الثقة';
        }
    }
}

// Save draft
function saveDraft() {
    if (validateVoucher()) {
        const voucherData = collectVoucherData();
        voucherData.status = 'draft';
        saveVoucher(voucherData);
        showMessage('تم حفظ مسودة السند بنجاح', 'success');
    }
}

// Preview voucher
function previewVoucher() {
    if (validateVoucher()) {
        const voucherData = collectVoucherData();
        populatePrintTemplate(voucherData, 'regular');

        // Open preview in new window
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>معاينة سند القبض</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${document.getElementById('regularPrintTemplate').innerHTML}
                    <div style="text-align: center; margin-top: 20px; page-break-inside: avoid;">
                        <button onclick="window.print()" style="padding: 10px 20px; font-size: 16px;">طباعة</button>
                        <button onclick="window.close()" style="padding: 10px 20px; font-size: 16px; margin-right: 10px;">إغلاق</button>
                    </div>
                </body>
            </html>
        `);
        printWindow.document.close();
    }
}

// Save and print thermal
function saveAndPrintThermal() {
    if (validateVoucher()) {
        const voucherData = collectVoucherData();
        voucherData.status = 'posted';
        saveVoucher(voucherData);

        populatePrintTemplate(voucherData, 'thermal');

        // Open thermal print
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>سند قبض - طباعة حرارية</title>
                    <style>
                        body { font-family: monospace; margin: 0; padding: 5px; }
                        @media print {
                            body { margin: 0; padding: 0; }
                            @page { size: 80mm auto; margin: 0; }
                        }
                    </style>
                </head>
                <body>
                    ${document.getElementById('thermalPrintTemplate').innerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();

        showMessage('تم حفظ السند وإرساله للطباعة الحرارية', 'success');

        // Redirect after short delay
        setTimeout(() => {
            window.location.href = 'vouchers.html';
        }, 2000);
    }
}

// Save and print regular
function saveAndPrintRegular() {
    if (validateVoucher()) {
        const voucherData = collectVoucherData();
        voucherData.status = 'posted';
        saveVoucher(voucherData);

        populatePrintTemplate(voucherData, 'regular');

        // Open regular print
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>سند قبض - طباعة عادية</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 0; padding: 0; }
                        @media print {
                            body { margin: 0; }
                            @page { size: A4; margin: 1cm; }
                        }
                    </style>
                </head>
                <body>
                    ${document.getElementById('regularPrintTemplate').innerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();

        showMessage('تم حفظ السند وإرساله للطباعة العادية', 'success');

        // Redirect after short delay
        setTimeout(() => {
            window.location.href = 'vouchers.html';
        }, 2000);
    }
}

// Populate print template
function populatePrintTemplate(voucherData, templateType) {
    const savedUser = localStorage.getItem('anwar_bakery_current_user');
    const savedCompany = localStorage.getItem('anwar_bakery_company');
    const savedBranches = localStorage.getItem('anwar_bakery_branches');

    let currentUser = { fullName: 'غير محدد' };
    let company = { companyName: 'مخبز أنوار الحي', slogan: 'جودة تستحق الثقة' };
    let branchName = 'غير محدد';

    if (savedUser) currentUser = JSON.parse(savedUser);
    if (savedCompany) company = JSON.parse(savedCompany);
    if (savedBranches) {
        const branches = JSON.parse(savedBranches);
        const branch = branches.find(b => b.id == voucherData.branchId);
        if (branch) branchName = branch.branchName;
    }

    const currencySymbol = getCurrencySymbol();
    const paymentMethodNames = {
        'cash': 'نقدي',
        'bank': 'تحويل بنكي',
        'check': 'شيك',
        'card': 'بطاقة ائتمان'
    };

    if (templateType === 'regular') {
        // Populate regular template
        document.getElementById('printCompanyName').textContent = company.companyName;
        document.getElementById('printCompanySlogan').textContent = company.slogan;
        document.getElementById('printVoucherNumber').textContent = voucherData.voucherNumber;
        document.getElementById('printDate').textContent = new Date(voucherData.date).toLocaleDateString('ar-SA');
        document.getElementById('printBranch').textContent = branchName;
        document.getElementById('printPayer').textContent = getPayerName(voucherData);
        document.getElementById('printPaymentMethod').textContent = paymentMethodNames[voucherData.paymentMethod];
        document.getElementById('printAmount').textContent = voucherData.amount.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '');
        document.getElementById('printReference').textContent = voucherData.referenceNumber || '-';
        document.getElementById('printDescription').textContent = voucherData.description;
        document.getElementById('printCreatedBy').textContent = currentUser.fullName;
        document.getElementById('printCreatedAt').textContent = new Date().toLocaleString('ar-SA');

        // Populate journal entries
        const journalEntriesHtml = voucherData.journalEntries.map(entry => `
            <tr>
                <td style="padding: 8px; border: 1px solid #ddd;">${entry.account.name} (${entry.account.code})</td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: center; ${entry.debit > 0 ? 'font-weight: bold; color: #059669;' : ''}">${entry.debit > 0 ? entry.debit.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '') : '-'}</td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: center; ${entry.credit > 0 ? 'font-weight: bold; color: #dc2626;' : ''}">${entry.credit > 0 ? entry.credit.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '') : '-'}</td>
            </tr>
        `).join('');
        document.getElementById('printJournalEntries').innerHTML = journalEntriesHtml;

    } else if (templateType === 'thermal') {
        // Populate thermal template
        document.getElementById('thermalCompanyName').textContent = company.companyName;
        document.getElementById('thermalCompanySlogan').textContent = company.slogan;
        document.getElementById('thermalVoucherNumber').textContent = voucherData.voucherNumber;
        document.getElementById('thermalDate').textContent = new Date(voucherData.date).toLocaleDateString('ar-SA');
        document.getElementById('thermalBranch').textContent = branchName;
        document.getElementById('thermalPayer').textContent = getPayerName(voucherData);
        document.getElementById('thermalAmount').textContent = voucherData.amount.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '');
        document.getElementById('thermalDescription').textContent = voucherData.description;
        document.getElementById('thermalPaymentMethod').textContent = paymentMethodNames[voucherData.paymentMethod];
        document.getElementById('thermalCreatedBy').textContent = currentUser.fullName;
        document.getElementById('thermalCreatedAt').textContent = new Date().toLocaleString('ar-SA');

        if (voucherData.referenceNumber) {
            document.getElementById('thermalReferenceDiv').style.display = 'block';
            document.getElementById('thermalReference').textContent = voucherData.referenceNumber;
        }
    }
}

// Get payer name for printing
function getPayerName(voucherData) {
    const payerSelect = document.getElementById('payerId');
    const selectedOption = payerSelect.options[payerSelect.selectedIndex];
    return selectedOption ? selectedOption.textContent : 'غير محدد';
}

// Validate voucher data
function validateVoucher() {
    const requiredFields = ['voucherDate', 'branchId', 'cashRegisterId', 'payerId', 'amount', 'description'];
    let isValid = true;

    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (!field.value.trim()) {
            field.classList.add('required-field');
            isValid = false;
        } else {
            field.classList.remove('required-field');
        }
    });

    if (!isValid) {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
        return false;
    }

    if (currentVoucher.journalEntries.length === 0) {
        showMessage('لا يمكن حفظ السند بدون قيد محاسبي صحيح', 'error');
        return false;
    }

    return true;
}

// Collect voucher data
function collectVoucherData() {
    return {
        id: Date.now(),
        voucherNumber: document.getElementById('voucherNumber').value,
        type: 'receipt',
        date: document.getElementById('voucherDate').value,
        branchId: parseInt(document.getElementById('branchId').value),
        cashRegisterId: parseInt(document.getElementById('cashRegisterId').value),
        payerId: document.getElementById('payerId').value,
        payerType: document.querySelector('input[name="payerType"]:checked').value,
        amount: parseFloat(document.getElementById('amount').value),
        paymentMethod: document.getElementById('paymentMethod').value,
        referenceNumber: document.getElementById('referenceNumber').value,
        description: document.getElementById('description').value,
        journalEntries: currentVoucher.journalEntries,
        createdBy: 'current_user', // TODO: Get from session
        createdAt: new Date().toISOString()
    };
}

// Save voucher with full implementation
function saveVoucher(voucherData) {
    try {
        // Save to vouchers
        let vouchers = [];
        const savedVouchers = localStorage.getItem('anwar_bakery_vouchers');
        if (savedVouchers) {
            vouchers = JSON.parse(savedVouchers);
        }
        vouchers.push(voucherData);
        localStorage.setItem('anwar_bakery_vouchers', JSON.stringify(vouchers));

        // Update counter
        localStorage.setItem('anwar_bakery_receipt_counter', voucherCounter.toString());

        // Update cash register balance
        updateCashRegisterBalance(voucherData);

        // Update account balances
        updateAccountBalances(voucherData);

        // Create journal entry record
        createJournalEntry(voucherData);

        console.log('Receipt voucher saved successfully:', voucherData);
        return { success: true };
    } catch (error) {
        console.error('Error saving voucher:', error);
        return { success: false, error: error.message };
    }
}

// Update cash register balance
function updateCashRegisterBalance(voucherData) {
    try {
        if (!voucherData.cashRegisterId) return;

        const cashRegisters = JSON.parse(localStorage.getItem('anwar_bakery_cash_registers') || '[]');
        const registerIndex = cashRegisters.findIndex(reg => reg.id == voucherData.cashRegisterId);

        if (registerIndex !== -1) {
            // Add amount to cash register (receipt = money in)
            cashRegisters[registerIndex].currentBalance += voucherData.amount;
            cashRegisters[registerIndex].updatedAt = new Date().toISOString();

            localStorage.setItem('anwar_bakery_cash_registers', JSON.stringify(cashRegisters));
            console.log('Cash register balance updated');
        }
    } catch (error) {
        console.error('Error updating cash register balance:', error);
    }
}

// Update account balances
function updateAccountBalances(voucherData) {
    try {
        const accounts = JSON.parse(localStorage.getItem('anwar_bakery_accounts') || '[]');

        voucherData.journalEntries.forEach(entry => {
            const accountIndex = accounts.findIndex(acc => acc.id == entry.account.id);
            if (accountIndex !== -1) {
                accounts[accountIndex].balance += (entry.debit - entry.credit);
                accounts[accountIndex].updatedAt = new Date().toISOString();
            }
        });

        localStorage.setItem('anwar_bakery_accounts', JSON.stringify(accounts));
        console.log('Account balances updated');
    } catch (error) {
        console.error('Error updating account balances:', error);
    }
}

// Create journal entry record
function createJournalEntry(voucherData) {
    try {
        const journalEntry = {
            id: Date.now(),
            date: voucherData.date,
            reference: voucherData.voucherNumber,
            description: voucherData.description,
            type: 'receipt_voucher',
            entries: voucherData.journalEntries.map(entry => ({
                accountId: entry.account.id,
                accountName: entry.account.name,
                debit: entry.debit,
                credit: entry.credit
            })),
            createdBy: voucherData.createdBy,
            createdAt: voucherData.createdAt
        };

        const journalEntries = JSON.parse(localStorage.getItem('anwar_bakery_journal_entries') || '[]');
        journalEntries.push(journalEntry);
        localStorage.setItem('anwar_bakery_journal_entries', JSON.stringify(journalEntries));

        console.log('Journal entry created:', journalEntry);
    } catch (error) {
        console.error('Error creating journal entry:', error);
    }
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    if (!container) return;

    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_current_user');
        window.location.href = 'login.html';
    }
}

// Load invoice data if coming from invoice
function loadInvoiceData() {
    const urlParams = new URLSearchParams(window.location.search);
    const fromInvoice = urlParams.get('from') === 'invoice';

    if (fromInvoice) {
        const voucherData = localStorage.getItem('anwar_bakery_voucher_data');
        if (voucherData) {
            const data = JSON.parse(voucherData);

            // Set form fields
            if (data.branchId) {
                document.getElementById('branchId').value = data.branchId;
                loadBranchData();
            }

            if (data.amount) {
                document.getElementById('amount').value = data.amount;
            }

            if (data.description) {
                document.getElementById('description').value = data.description;
            }

            if (data.date) {
                document.getElementById('voucherDate').value = data.date;
            }

            // Set customer if available
            if (data.customerId) {
                setTimeout(() => {
                    const payerSelect = document.getElementById('payerId');
                    if (payerSelect) {
                        payerSelect.value = data.customerId;
                        updateJournalPreview();
                    }
                }, 1000);
            }

            // Store linked invoice data
            currentVoucher.linkedInvoiceId = data.linkedInvoiceId;
            currentVoucher.invoiceNumber = data.invoiceNumber;

            // Clear the stored data
            localStorage.removeItem('anwar_bakery_voucher_data');

            // Show success message
            showMessage(`تم تحضير سند القبض للفاتورة رقم ${data.invoiceNumber}`, 'success');
        }
    }
}

// Update save voucher function to include linked invoice
function saveVoucher(voucherData) {
    // Add linked invoice data if available
    if (currentVoucher.linkedInvoiceId) {
        voucherData.linkedInvoiceId = currentVoucher.linkedInvoiceId;
        voucherData.invoiceNumber = currentVoucher.invoiceNumber;

        // Update invoice payment status
        updateInvoicePayment(currentVoucher.linkedInvoiceId, voucherData.amount);
    }

    // Save to vouchers
    let vouchers = [];
    const savedVouchers = localStorage.getItem('anwar_bakery_vouchers');
    if (savedVouchers) {
        vouchers = JSON.parse(savedVouchers);
    }
    vouchers.push(voucherData);
    localStorage.setItem('anwar_bakery_vouchers', JSON.stringify(vouchers));

    // Update counter
    localStorage.setItem('anwar_bakery_receipt_counter', voucherCounter.toString());

    // TODO: Update cash register/bank balance
    // TODO: Update account balances
    // TODO: Create journal entry record
}

// Update invoice payment
function updateInvoicePayment(invoiceId, paidAmount) {
    const savedInvoices = localStorage.getItem('anwar_bakery_invoices');
    if (savedInvoices) {
        let invoices = JSON.parse(savedInvoices);
        const invoiceIndex = invoices.findIndex(inv => inv.id === invoiceId);

        if (invoiceIndex !== -1) {
            const invoice = invoices[invoiceIndex];
            invoice.paidAmount = (invoice.paidAmount || 0) + paidAmount;

            // Update status based on payment
            const totalAmount = invoice.totalAmount || 0;
            const totalPaid = invoice.paidAmount || 0;

            if (totalPaid >= totalAmount) {
                invoice.status = 'paid';
            } else if (totalPaid > 0) {
                invoice.status = 'partial';
            } else {
                invoice.status = 'pending';
            }

            // Save updated invoices
            localStorage.setItem('anwar_bakery_invoices', JSON.stringify(invoices));
        }
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadInitialData();

    // Load invoice data if coming from invoice
    setTimeout(() => {
        loadInvoiceData();
    }, 500);
});
