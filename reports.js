// Reports Management System

// Check authentication
function checkAuth() {
    const session = localStorage.getItem('anwar_bakery_session') ||
                   sessionStorage.getItem('anwar_bakery_session');

    if (!session) {
        window.location.href = 'login.html';
        return null;
    }

    return JSON.parse(session);
}

// Load user info
function loadUserInfo() {
    const session = checkAuth();
    if (session) {
        document.getElementById('userFullName').textContent = session.fullName;
        document.getElementById('userDisplayName').textContent = session.fullName;
        document.getElementById('userRole').textContent = session.role;

        const loginTime = new Date(session.loginTime);
        document.getElementById('loginTime').textContent =
            'آخر دخول: ' + loginTime.toLocaleString('ar-SA');
    }
}

// Load company info
function loadCompanyInfo() {
    const savedData = localStorage.getItem('anwar_bakery_company');
    if (savedData) {
        const companyData = JSON.parse(savedData);
        if (companyData.companyNameAr) {
            document.getElementById('sidebarCompanyName').textContent = companyData.companyNameAr;
            document.title = `التقارير - ${companyData.companyNameAr}`;
        }
    }
}

// Load branches for filter
function loadBranches() {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    if (savedBranches) {
        const branches = JSON.parse(savedBranches);
        const branchFilter = document.getElementById('branchFilter');

        branches.forEach(branch => {
            if (branch.isActive) {
                const option = document.createElement('option');
                option.value = branch.id;
                option.textContent = branch.branchName;
                branchFilter.appendChild(option);
            }
        });
    }
}

// Set default dates
function setDefaultDates() {
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    document.getElementById('fromDate').value = firstDayOfMonth.toISOString().split('T')[0];
    document.getElementById('toDate').value = today.toISOString().split('T')[0];
}

// Generate specific report
function generateReport(reportType) {
    showMessage('جاري إنشاء التقرير...', 'info');

    // Simulate report generation
    setTimeout(() => {
        const reportNames = {
            'profit-loss': 'قائمة الدخل',
            'balance-sheet': 'الميزانية العمومية',
            'cash-flow': 'التدفق النقدي',
            'sales-summary': 'ملخص المبيعات',
            'top-products': 'أكثر المنتجات مبيعاً',
            'customer-sales': 'مبيعات العملاء',
            'inventory-status': 'حالة المخزون',
            'inventory-valuation': 'تقييم المخزون',
            'stock-movement': 'حركة المخزون',
            'trial-balance': 'ميزان المراجعة',
            'journal-entries': 'دفتر اليومية',
            'account-statement': 'كشف حساب',
            'cash-transfers': 'تقرير تحويل المبالغ'
        };

        const reportName = reportNames[reportType] || 'تقرير غير معروف';

        // In a real application, this would generate and download the actual report
        showMessage(`تم إنشاء تقرير "${reportName}" بنجاح! سيتم تحميله قريباً...`, 'success');

        // Simulate opening report in new window
        setTimeout(() => {
            generateSampleReport(reportType, reportName);
        }, 1000);

    }, 2000);
}

// Generate sample report content
function generateSampleReport(reportType, reportName) {
    const companyData = JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
    const companyName = companyData.companyNameAr || 'مخبز أنور';

    let reportContent = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${reportName} - ${companyName}</title>
            <style>
                body { font-family: 'Arial', sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .company-name { font-size: 24px; font-weight: bold; color: #2563eb; }
                .report-title { font-size: 20px; margin: 10px 0; }
                .report-date { font-size: 14px; color: #666; }
                .content { margin: 20px 0; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f5f5f5; font-weight: bold; }
                .total-row { background-color: #e3f2fd; font-weight: bold; }
                .print-btn { background: #2563eb; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px; }
                @media print { .no-print { display: none; } }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="company-name">${companyName}</div>
                <div class="report-title">${reportName}</div>
                <div class="report-date">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</div>
            </div>

            <div class="no-print">
                <button class="print-btn" onclick="window.print()">طباعة التقرير</button>
                <button class="print-btn" onclick="window.close()">إغلاق</button>
            </div>

            <div class="content">
                ${generateReportData(reportType)}
            </div>
        </body>
        </html>
    `;

    const newWindow = window.open('', '_blank');
    newWindow.document.write(reportContent);
    newWindow.document.close();
}

// Generate sample report data based on type
function generateReportData(reportType) {
    switch (reportType) {
        case 'profit-loss':
            return `
                <table>
                    <thead>
                        <tr><th colspan="2">قائمة الدخل</th></tr>
                    </thead>
                    <tbody>
                        <tr><td>الإيرادات</td><td></td></tr>
                        <tr><td>&nbsp;&nbsp;مبيعات المنتجات</td><td>150,000.00</td></tr>
                        <tr><td>&nbsp;&nbsp;إيرادات أخرى</td><td>5,000.00</td></tr>
                        <tr class="total-row"><td>إجمالي الإيرادات</td><td>155,000.00</td></tr>
                        <tr><td>المصروفات</td><td></td></tr>
                        <tr><td>&nbsp;&nbsp;تكلفة البضاعة المباعة</td><td>90,000.00</td></tr>
                        <tr><td>&nbsp;&nbsp;مصروفات التشغيل</td><td>25,000.00</td></tr>
                        <tr><td>&nbsp;&nbsp;مصروفات إدارية</td><td>15,000.00</td></tr>
                        <tr class="total-row"><td>إجمالي المصروفات</td><td>130,000.00</td></tr>
                        <tr class="total-row"><td>صافي الربح</td><td>25,000.00</td></tr>
                    </tbody>
                </table>
            `;

        case 'sales-summary':
            return `
                <table>
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>عدد الفواتير</th>
                            <th>إجمالي المبيعات</th>
                            <th>الضريبة</th>
                            <th>الصافي</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>2024-01-01</td><td>25</td><td>12,500.00</td><td>1,875.00</td><td>10,625.00</td></tr>
                        <tr><td>2024-01-02</td><td>30</td><td>15,000.00</td><td>2,250.00</td><td>12,750.00</td></tr>
                        <tr><td>2024-01-03</td><td>28</td><td>14,000.00</td><td>2,100.00</td><td>11,900.00</td></tr>
                        <tr class="total-row"><td>الإجمالي</td><td>83</td><td>41,500.00</td><td>6,225.00</td><td>35,275.00</td></tr>
                    </tbody>
                </table>
            `;

        case 'inventory-status':
            return `
                <table>
                    <thead>
                        <tr>
                            <th>كود الصنف</th>
                            <th>اسم الصنف</th>
                            <th>الكمية الحالية</th>
                            <th>الحد الأدنى</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>P001</td><td>دقيق أبيض</td><td>150</td><td>100</td><td>متوفر</td></tr>
                        <tr><td>P002</td><td>سكر</td><td>80</td><td>50</td><td>متوفر</td></tr>
                        <tr><td>P003</td><td>زيت نباتي</td><td>25</td><td>30</td><td style="color: red;">مخزون منخفض</td></tr>
                        <tr><td>P004</td><td>خميرة</td><td>0</td><td>20</td><td style="color: red;">نفد المخزون</td></tr>
                    </tbody>
                </table>
            `;

        case 'cash-transfers':
            return generateCashTransfersReport();

        default:
            return `
                <div style="text-align: center; padding: 50px;">
                    <h3>تقرير تجريبي</h3>
                    <p>هذا تقرير تجريبي لعرض الشكل العام للتقارير.</p>
                    <p>في النظام الفعلي، سيتم عرض البيانات الحقيقية هنا.</p>
                </div>
            `;
    }
}

// Generate custom report
function generateCustomReport() {
    const fromDate = document.getElementById('fromDate').value;
    const toDate = document.getElementById('toDate').value;
    const branchId = document.getElementById('branchFilter').value;

    if (!fromDate || !toDate) {
        showMessage('يرجى تحديد تاريخ البداية والنهاية', 'error');
        return;
    }

    if (new Date(fromDate) > new Date(toDate)) {
        showMessage('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'error');
        return;
    }

    showMessage('جاري إنشاء التقرير المخصص...', 'info');

    setTimeout(() => {
        const branchName = branchId ? getBranchName(branchId) : 'جميع الفروع';
        const reportTitle = `تقرير مخصص - ${branchName} (${fromDate} إلى ${toDate})`;

        generateSampleReport('custom', reportTitle);
        showMessage('تم إنشاء التقرير المخصص بنجاح!', 'success');
    }, 2000);
}

// Get branch name by ID
function getBranchName(branchId) {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    if (savedBranches) {
        const branches = JSON.parse(savedBranches);
        const branch = branches.find(b => b.id == branchId);
        return branch ? branch.branchName : 'غير محدد';
    }
    return 'غير محدد';
}

// Get currency symbol from settings - FIXED FOR REPORTS
function getCurrencySymbol() {
    try {
        // First try to get from window.appSettings (the correct way)
        if (window.appSettings) {
            const financial = window.appSettings.get('financial');
            if (financial && financial.currencySymbol) {
                return financial.currencySymbol;
            }
            if (financial && financial.baseCurrency) {
                const currencySymbols = {
                    'SAR': 'ر.س',
                    'YER': 'ر.ي',
                    'USD': '$',
                    'EUR': '€',
                    'AED': 'د.إ',
                    'KWD': 'د.ك',
                    'QAR': 'ر.ق'
                };
                return currencySymbols[financial.baseCurrency] || 'ر.ي';
            }
        }

        // Fallback: try to get from localStorage directly
        const savedSettings = localStorage.getItem('anwar_bakery_settings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);
            if (settings.financial && settings.financial.currencySymbol) {
                return settings.financial.currencySymbol;
            }
            if (settings.financial && settings.financial.baseCurrency) {
                const currencySymbols = {
                    'SAR': 'ر.س',
                    'YER': 'ر.ي',
                    'USD': '$',
                    'EUR': '€',
                    'AED': 'د.إ',
                    'KWD': 'د.ك',
                    'QAR': 'ر.ق'
                };
                return currencySymbols[settings.financial.baseCurrency] || 'ر.ي';
            }
        }

        // Last fallback to company data
        const companyData = localStorage.getItem('anwar_bakery_company');
        if (companyData) {
            const company = JSON.parse(companyData);
            if (company.currencySymbol) {
                return company.currencySymbol;
            }
            if (company.currency) {
                const currencySymbols = {
                    'SAR': 'ر.س',
                    'YER': 'ر.ي',
                    'USD': '$',
                    'EUR': '€',
                    'AED': 'د.إ',
                    'KWD': 'د.ك',
                    'QAR': 'ر.ق'
                };
                return currencySymbols[company.currency] || 'ر.ي';
            }
        }
    } catch (error) {
        console.error('Error getting currency symbol:', error);
    }

    return 'ر.ي'; // Default currency symbol
}

// Generate cash transfers report
function generateCashTransfersReport() {
    const storedTransfers = localStorage.getItem('anwar_bakery_cash_transfers');
    let transfers = [];

    if (storedTransfers) {
        try {
            transfers = JSON.parse(storedTransfers);
        } catch (error) {
            console.error('Error parsing transfers data:', error);
        }
    }

    if (transfers.length === 0) {
        return `
            <div style="text-align: center; padding: 50px;">
                <h3>تقرير تحويل المبالغ</h3>
                <p>لا توجد عمليات تحويل مسجلة في النظام</p>
            </div>
        `;
    }

    // Calculate totals
    const totalTransfers = transfers.length;
    const totalAmount = transfers.reduce((sum, transfer) => sum + (transfer.amount || 0), 0);
    const completedTransfers = transfers.filter(t => t.status === 'completed').length;
    const pendingTransfers = transfers.filter(t => t.status === 'pending').length;
    const cancelledTransfers = transfers.filter(t => t.status === 'cancelled').length;

    // Get currency symbol
    const currencySymbol = getCurrencySymbol();

    // Generate table rows
    const transferRows = transfers.map(transfer => {
        const statusText = {
            'completed': 'مكتمل',
            'pending': 'معلق',
            'cancelled': 'ملغي'
        }[transfer.status] || 'غير محدد';

        const statusColor = {
            'completed': 'color: green;',
            'pending': 'color: orange;',
            'cancelled': 'color: red;'
        }[transfer.status] || '';

        return `
            <tr>
                <td>${transfer.transferNumber || transfer.id}</td>
                <td>${formatReportDate(transfer.date || transfer.createdAt)}</td>
                <td>${transfer.fromRegisterName || 'غير محدد'}</td>
                <td>${transfer.toRegisterName || 'غير محدد'}</td>
                <td style="text-align: left;">${(transfer.amount || 0).toLocaleString()} ${currencySymbol}</td>
                <td>${transfer.reference || '-'}</td>
                <td style="${statusColor}">${statusText}</td>
                <td>${transfer.notes || '-'}</td>
            </tr>
        `;
    }).join('');

    return `
        <div style="margin-bottom: 30px;">
            <h3 style="color: #4F46E5; margin-bottom: 20px;">ملخص تحويل المبالغ</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px;">
                <div style="background: #F0F9FF; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #0369A1;">${totalTransfers}</div>
                    <div style="color: #0369A1;">إجمالي التحويلات</div>
                </div>
                <div style="background: #F0FDF4; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 20px; font-weight: bold; color: #059669;">${totalAmount.toLocaleString()} ${currencySymbol}</div>
                    <div style="color: #059669;">إجمالي المبلغ المحول</div>
                </div>
                <div style="background: #ECFDF5; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #10B981;">${completedTransfers}</div>
                    <div style="color: #10B981;">تحويلات مكتملة</div>
                </div>
                <div style="background: #FFFBEB; padding: 15px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #D97706;">${pendingTransfers}</div>
                    <div style="color: #D97706;">تحويلات معلقة</div>
                </div>
            </div>
        </div>

        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <thead>
                <tr style="background-color: #4F46E5; color: white;">
                    <th style="border: 1px solid #ddd; padding: 12px; text-align: right;">رقم القيد</th>
                    <th style="border: 1px solid #ddd; padding: 12px; text-align: right;">التاريخ</th>
                    <th style="border: 1px solid #ddd; padding: 12px; text-align: right;">من صندوق</th>
                    <th style="border: 1px solid #ddd; padding: 12px; text-align: right;">إلى صندوق</th>
                    <th style="border: 1px solid #ddd; padding: 12px; text-align: right;">المبلغ</th>
                    <th style="border: 1px solid #ddd; padding: 12px; text-align: right;">المرجع</th>
                    <th style="border: 1px solid #ddd; padding: 12px; text-align: right;">الحالة</th>
                    <th style="border: 1px solid #ddd; padding: 12px; text-align: right;">الملاحظات</th>
                </tr>
            </thead>
            <tbody>
                ${transferRows}
            </tbody>
            <tfoot>
                <tr style="background-color: #F3F4F6; font-weight: bold;">
                    <td colspan="4" style="border: 1px solid #ddd; padding: 12px; text-align: right;">الإجمالي</td>
                    <td style="border: 1px solid #ddd; padding: 12px; text-align: left;">${totalAmount.toLocaleString()} ${currencySymbol}</td>
                    <td colspan="3" style="border: 1px solid #ddd; padding: 12px; text-align: right;">${totalTransfers} عملية تحويل</td>
                </tr>
            </tfoot>
        </table>

        <div style="margin-top: 30px; padding: 20px; background: #F9FAFB; border-radius: 8px;">
            <h4 style="color: #374151; margin-bottom: 15px;">إحصائيات إضافية:</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                <div>
                    <strong>متوسط قيمة التحويل:</strong> ${totalTransfers > 0 ? (totalAmount / totalTransfers).toLocaleString() : 0} ${currencySymbol}
                </div>
                <div>
                    <strong>نسبة التحويلات المكتملة:</strong> ${totalTransfers > 0 ? ((completedTransfers / totalTransfers) * 100).toFixed(1) : 0}%
                </div>
                <div>
                    <strong>التحويلات الملغية:</strong> ${cancelledTransfers} عملية
                </div>
                <div>
                    <strong>تاريخ التقرير:</strong> ${new Date().toLocaleDateString('ar-SA')}
                </div>
            </div>
        </div>
    `;
}

// Format date for reports
function formatReportDate(dateString) {
    if (!dateString) return 'غير محدد';
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    } catch (error) {
        return dateString;
    }
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Update current date time
function updateDateTime() {
    const now = new Date();
    const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    document.getElementById('currentDateTime').textContent =
        now.toLocaleDateString('ar-SA', options);
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (sidebar.classList.contains('translate-x-full')) {
        sidebar.classList.remove('translate-x-full');
        overlay.classList.remove('hidden');
    } else {
        sidebar.classList.add('translate-x-full');
        overlay.classList.add('hidden');
    }
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_session');
        sessionStorage.removeItem('anwar_bakery_session');
        window.location.href = 'login.html';
    }
}

// Advanced Export All Reports to Excel
function exportAllReportsToExcel() {
    showMessage('🔄 جاري تصدير جميع التقارير إلى Excel...', 'info');

    if (typeof window.advancedExcel !== 'undefined') {
        try {
            // Collect all reports data
            const allReportsData = {
                summary: generateSummaryData(),
                financial: generateFinancialData(),
                operations: generateOperationsData(),
                kpis: generateKPIData()
            };

            const result = window.advancedExcel.exportBusinessDashboard(allReportsData);

            if (result.success) {
                showMessage('✅ تم تصدير جميع التقارير بنجاح!', 'success');
            } else {
                showMessage('❌ فشل في تصدير التقارير: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('Export error:', error);
            showMessage('❌ خطأ في تصدير التقارير: ' + error.message, 'error');
        }
    } else {
        // Fallback to basic export
        const basicData = generateBasicReportData();
        exportToExcel(basicData, 'جميع_التقارير', 'التقارير');
    }
}

// Print All Reports
function printAllReports() {
    showMessage('🔄 جاري تحضير التقارير للطباعة...', 'info');

    if (typeof window.advancedPrint !== 'undefined') {
        try {
            const reportsData = {
                number: 'RPT-' + Date.now(),
                date: new Date().toISOString().split('T')[0],
                time: new Date().toISOString(),
                type: 'comprehensive_report',
                items: generateReportItems(),
                subtotal: 0,
                tax: 0,
                total: 0,
                notes: 'تقرير شامل لجميع العمليات'
            };

            window.advancedPrint.printInvoice(reportsData, {
                showLogo: true,
                showHeader: true,
                showFooter: true,
                copies: 1,
                paperSize: 'A4'
            });

            showMessage('✅ تم إرسال التقارير للطباعة!', 'success');
        } catch (error) {
            console.error('Print error:', error);
            showMessage('❌ خطأ في طباعة التقارير: ' + error.message, 'error');
        }
    } else {
        // Fallback to basic print
        window.print();
        showMessage('✅ تم إرسال الصفحة للطباعة!', 'success');
    }
}

// Generate summary data for dashboard
function generateSummaryData() {
    const invoices = JSON.parse(localStorage.getItem('anwar_bakery_invoices') || '[]');
    const products = JSON.parse(localStorage.getItem('anwar_bakery_products') || '[]');
    const customers = JSON.parse(localStorage.getItem('anwar_bakery_customers') || '[]');

    const salesInvoices = invoices.filter(inv => inv.type === 'sales');
    const purchaseInvoices = invoices.filter(inv => inv.type === 'purchase');

    return {
        totalSales: salesInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0),
        totalPurchases: purchaseInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0),
        totalProducts: products.length,
        totalCustomers: customers.length,
        totalInvoices: invoices.length
    };
}

// Generate financial data
function generateFinancialData() {
    const journalEntries = JSON.parse(localStorage.getItem('anwar_bakery_journal_entries') || '[]');
    const vouchers = JSON.parse(localStorage.getItem('anwar_bakery_vouchers') || '[]');

    let totalDebit = 0;
    let totalCredit = 0;

    journalEntries.forEach(entry => {
        if (entry.entries) {
            entry.entries.forEach(line => {
                totalDebit += line.debit || 0;
                totalCredit += line.credit || 0;
            });
        }
    });

    return {
        totalDebit,
        totalCredit,
        totalVouchers: vouchers.length,
        totalJournalEntries: journalEntries.length
    };
}

// Generate operations data
function generateOperationsData() {
    const today = new Date().toISOString().split('T')[0];
    const invoices = JSON.parse(localStorage.getItem('anwar_bakery_invoices') || '[]');

    const todayInvoices = invoices.filter(inv => inv.date === today);
    const thisMonthInvoices = invoices.filter(inv => {
        const invDate = new Date(inv.date);
        const now = new Date();
        return invDate.getMonth() === now.getMonth() && invDate.getFullYear() === now.getFullYear();
    });

    return {
        dailyTransactions: todayInvoices.length,
        monthlyTransactions: thisMonthInvoices.length,
        dailySales: todayInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0),
        monthlySales: thisMonthInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0)
    };
}

// Generate KPI data
function generateKPIData() {
    const summary = generateSummaryData();
    const financial = generateFinancialData();
    const operations = generateOperationsData();

    const currencySymbol = getCurrencySymbol();

    return [
        { metric: 'إجمالي المبيعات', value: summary.totalSales, unit: currencySymbol },
        { metric: 'إجمالي المشتريات', value: summary.totalPurchases, unit: currencySymbol },
        { metric: 'عدد المنتجات', value: summary.totalProducts, unit: 'منتج' },
        { metric: 'عدد العملاء', value: summary.totalCustomers, unit: 'عميل' },
        { metric: 'المعاملات اليومية', value: operations.dailyTransactions, unit: 'معاملة' },
        { metric: 'المبيعات اليومية', value: operations.dailySales, unit: currencySymbol }
    ];
}

// Generate basic report data for fallback
function generateBasicReportData() {
    const summary = generateSummaryData();
    const financial = generateFinancialData();
    const operations = generateOperationsData();
    const currencySymbol = getCurrencySymbol();

    return [
        { 'نوع التقرير': 'ملخص المبيعات', 'القيمة': summary.totalSales, 'الوحدة': currencySymbol },
        { 'نوع التقرير': 'إجمالي المشتريات', 'القيمة': summary.totalPurchases, 'الوحدة': currencySymbol },
        { 'نوع التقرير': 'عدد المنتجات', 'القيمة': summary.totalProducts, 'الوحدة': 'منتج' },
        { 'نوع التقرير': 'عدد العملاء', 'القيمة': summary.totalCustomers, 'الوحدة': 'عميل' },
        { 'نوع التقرير': 'المعاملات اليومية', 'القيمة': operations.dailyTransactions, 'الوحدة': 'معاملة' }
    ];
}

// Generate report items for printing
function generateReportItems() {
    const kpis = generateKPIData();

    return kpis.map(kpi => ({
        name: kpi.metric,
        quantity: 1,
        unit: kpi.unit,
        price: kpi.value,
        total: kpi.value
    }));
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    checkAuth();
    loadUserInfo();
    loadCompanyInfo();
    loadBranches();
    setDefaultDates();
    updateDateTime();
    setInterval(updateDateTime, 60000);
});

// Close sidebar when clicking overlay
document.getElementById('mobileOverlay').addEventListener('click', toggleSidebar);

// Advanced Export Functions
function exportToExcel(data, filename, sheetName = 'Sheet1') {
    showMessage('🔄 جاري تصدير البيانات إلى Excel...', 'info');

    setTimeout(() => {
        try {
            // Create CSV content as fallback
            if (!data || data.length === 0) {
                showMessage('❌ لا توجد بيانات للتصدير!', 'error');
                return;
            }

            const headers = Object.keys(data[0]);
            const csvContent = [
                headers.join(','),
                ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
            ].join('\n');

            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `${filename}_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            showMessage('✅ تم تصدير البيانات بنجاح!', 'success');
        } catch (error) {
            console.error('Export error:', error);
            showMessage('❌ خطأ في تصدير البيانات!', 'error');
        }
    }, 1000);
}

// Print Report Function
function printReport(reportHtml, title) {
    const printWindow = window.open('', '_blank');
    const companyData = JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');

    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>${title}</title>
            <style>
                body { font-family: 'Arial', sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
                .company-name { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                .report-title { font-size: 20px; color: #666; margin-bottom: 10px; }
                .report-date { font-size: 14px; color: #888; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f5f5f5; font-weight: bold; }
                .total-row { background-color: #f0f8ff; font-weight: bold; }
                .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
                @media print { body { margin: 0; } .no-print { display: none; } }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="company-name">${companyData.companyNameAr || 'مخبز أنوار الحي'}</div>
                <div class="report-title">${title}</div>
                <div class="report-date">تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</div>
            </div>
            ${reportHtml}
            <div class="footer">
                <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المخبز</p>
                <p>تاريخ الطباعة: ${new Date().toLocaleString('ar-SA')}</p>
            </div>
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.focus();

    setTimeout(() => {
        printWindow.print();
        printWindow.close();
    }, 500);
}

// Advanced Report Generation Functions
function generateTrialBalance() {
    showMessage('🔄 جاري إنشاء ميزان المراجعة...', 'info');

    setTimeout(() => {
        const accounts = JSON.parse(localStorage.getItem('anwar_bakery_accounts') || '[]');
        const journalEntries = JSON.parse(localStorage.getItem('anwar_bakery_journal_entries') || '[]');

        // Calculate account balances
        const accountBalances = {};
        accounts.forEach(account => {
            accountBalances[account.id] = {
                name: account.name,
                type: account.type,
                debit: 0,
                credit: 0,
                balance: account.openingBalance || 0
            };
        });

        // Process journal entries
        journalEntries.forEach(entry => {
            if (entry.entries) {
                entry.entries.forEach(line => {
                    if (accountBalances[line.accountId]) {
                        accountBalances[line.accountId].debit += line.debit || 0;
                        accountBalances[line.accountId].credit += line.credit || 0;
                    }
                });
            }
        });

        // Calculate final balances
        const trialBalanceData = [];
        let totalDebit = 0, totalCredit = 0;

        Object.values(accountBalances).forEach(account => {
            const finalBalance = account.balance + account.debit - account.credit;
            const debitBalance = finalBalance > 0 ? finalBalance : 0;
            const creditBalance = finalBalance < 0 ? Math.abs(finalBalance) : 0;

            trialBalanceData.push({
                'اسم الحساب': account.name,
                'نوع الحساب': account.type,
                'مدين': debitBalance.toLocaleString(),
                'دائن': creditBalance.toLocaleString()
            });

            totalDebit += debitBalance;
            totalCredit += creditBalance;
        });

        // Add totals row
        trialBalanceData.push({
            'اسم الحساب': 'الإجمالي',
            'نوع الحساب': '',
            'مدين': totalDebit.toLocaleString(),
            'دائن': totalCredit.toLocaleString()
        });

        // Generate HTML table
        const tableHtml = generateTableHtml(trialBalanceData, 'ميزان المراجعة');

        // Show options
        showReportOptions(trialBalanceData, tableHtml, 'ميزان المراجعة', 'trial_balance');

        showMessage('✅ تم إنشاء ميزان المراجعة بنجاح!', 'success');
    }, 2000);
}

function generateIncomeStatement() {
    showMessage('🔄 جاري إنشاء قائمة الدخل...', 'info');

    setTimeout(() => {
        const accounts = JSON.parse(localStorage.getItem('anwar_bakery_accounts') || '[]');
        const journalEntries = JSON.parse(localStorage.getItem('anwar_bakery_journal_entries') || '[]');

        const revenueAccounts = accounts.filter(acc => acc.type === 'revenue');
        const expenseAccounts = accounts.filter(acc => acc.type === 'expense');

        let totalRevenue = 0;
        let totalExpenses = 0;
        const incomeData = [];

        // Calculate revenue
        incomeData.push({ 'البيان': 'الإيرادات', 'المبلغ': '', 'النوع': 'header' });
        revenueAccounts.forEach(account => {
            let accountTotal = 0;
            journalEntries.forEach(entry => {
                if (entry.entries) {
                    entry.entries.forEach(line => {
                        if (line.accountId === account.id) {
                            accountTotal += (line.credit || 0) - (line.debit || 0);
                        }
                    });
                }
            });

            if (accountTotal !== 0) {
                incomeData.push({
                    'البيان': account.name,
                    'المبلغ': accountTotal.toLocaleString(),
                    'النوع': 'revenue'
                });
                totalRevenue += accountTotal;
            }
        });

        incomeData.push({
            'البيان': 'إجمالي الإيرادات',
            'المبلغ': totalRevenue.toLocaleString(),
            'النوع': 'total'
        });

        // Calculate expenses
        incomeData.push({ 'البيان': 'المصروفات', 'المبلغ': '', 'النوع': 'header' });
        expenseAccounts.forEach(account => {
            let accountTotal = 0;
            journalEntries.forEach(entry => {
                if (entry.entries) {
                    entry.entries.forEach(line => {
                        if (line.accountId === account.id) {
                            accountTotal += (line.debit || 0) - (line.credit || 0);
                        }
                    });
                }
            });

            if (accountTotal !== 0) {
                incomeData.push({
                    'البيان': account.name,
                    'المبلغ': accountTotal.toLocaleString(),
                    'النوع': 'expense'
                });
                totalExpenses += accountTotal;
            }
        });

        incomeData.push({
            'البيان': 'إجمالي المصروفات',
            'المبلغ': totalExpenses.toLocaleString(),
            'النوع': 'total'
        });

        // Net income
        const netIncome = totalRevenue - totalExpenses;
        incomeData.push({
            'البيان': 'صافي الدخل',
            'المبلغ': netIncome.toLocaleString(),
            'النوع': 'net'
        });

        const tableHtml = generateTableHtml(incomeData, 'قائمة الدخل');
        showReportOptions(incomeData, tableHtml, 'قائمة الدخل', 'income_statement');

        showMessage('✅ تم إنشاء قائمة الدخل بنجاح!', 'success');
    }, 2000);
}

// Helper Functions
function generateTableHtml(data, title) {
    if (!data || data.length === 0) return '<p>لا توجد بيانات</p>';

    const headers = Object.keys(data[0]);
    let html = `<table class="w-full border-collapse border border-gray-300">`;

    // Table header
    html += '<thead><tr>';
    headers.forEach(header => {
        html += `<th class="border border-gray-300 px-4 py-2 bg-gray-100">${header}</th>`;
    });
    html += '</tr></thead>';

    // Table body
    html += '<tbody>';
    data.forEach((row, index) => {
        const isTotal = row['النوع'] === 'total' || row['النوع'] === 'net' || row['اسم الحساب'] === 'الإجمالي';
        html += `<tr class="${isTotal ? 'total-row bg-blue-50 font-bold' : ''}">`;
        headers.forEach(header => {
            html += `<td class="border border-gray-300 px-4 py-2">${row[header] || ''}</td>`;
        });
        html += '</tr>';
    });
    html += '</tbody></table>';

    return html;
}

function showReportOptions(data, tableHtml, title, filename) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold">${title}</h3>
                    <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700">
                        <span class="text-2xl">×</span>
                    </button>
                </div>

                <div class="mb-4 flex space-x-2">
                    <button onclick="exportToExcel(${JSON.stringify(data).replace(/"/g, '&quot;')}, '${filename}')"
                            class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                        📊 تصدير Excel
                    </button>
                    <button onclick="printReport('${tableHtml.replace(/'/g, '\\\'').replace(/"/g, '&quot;')}', '${title}')"
                            class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                        🖨️ طباعة
                    </button>
                    <button onclick="navigator.share ? navigator.share({title: '${title}', text: 'تقرير ${title}'}) : alert('المشاركة غير متاحة')"
                            class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                        📤 مشاركة
                    </button>
                </div>

                <div class="overflow-x-auto">
                    ${tableHtml}
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

// Balance Sheet Generation
function generateBalanceSheet() {
    showMessage('🔄 جاري إنشاء الميزانية العمومية...', 'info');

    setTimeout(() => {
        const accounts = JSON.parse(localStorage.getItem('anwar_bakery_accounts') || '[]');
        const journalEntries = JSON.parse(localStorage.getItem('anwar_bakery_journal_entries') || '[]');

        const assetAccounts = accounts.filter(acc => acc.type === 'assets');
        const liabilityAccounts = accounts.filter(acc => acc.type === 'liabilities');
        const equityAccounts = accounts.filter(acc => acc.type === 'equity');

        let totalAssets = 0;
        let totalLiabilities = 0;
        let totalEquity = 0;
        const balanceSheetData = [];

        // Assets section
        balanceSheetData.push({ 'البيان': 'الأصول', 'المبلغ': '', 'النوع': 'header' });
        assetAccounts.forEach(account => {
            let accountBalance = account.openingBalance || 0;
            journalEntries.forEach(entry => {
                if (entry.entries) {
                    entry.entries.forEach(line => {
                        if (line.accountId === account.id) {
                            accountBalance += (line.debit || 0) - (line.credit || 0);
                        }
                    });
                }
            });

            if (accountBalance !== 0) {
                balanceSheetData.push({
                    'البيان': account.name,
                    'المبلغ': accountBalance.toLocaleString(),
                    'النوع': 'asset'
                });
                totalAssets += accountBalance;
            }
        });

        balanceSheetData.push({
            'البيان': 'إجمالي الأصول',
            'المبلغ': totalAssets.toLocaleString(),
            'النوع': 'total'
        });

        // Liabilities section
        balanceSheetData.push({ 'البيان': 'الخصوم', 'المبلغ': '', 'النوع': 'header' });
        liabilityAccounts.forEach(account => {
            let accountBalance = account.openingBalance || 0;
            journalEntries.forEach(entry => {
                if (entry.entries) {
                    entry.entries.forEach(line => {
                        if (line.accountId === account.id) {
                            accountBalance += (line.credit || 0) - (line.debit || 0);
                        }
                    });
                }
            });

            if (accountBalance !== 0) {
                balanceSheetData.push({
                    'البيان': account.name,
                    'المبلغ': accountBalance.toLocaleString(),
                    'النوع': 'liability'
                });
                totalLiabilities += accountBalance;
            }
        });

        balanceSheetData.push({
            'البيان': 'إجمالي الخصوم',
            'المبلغ': totalLiabilities.toLocaleString(),
            'النوع': 'total'
        });

        // Equity section
        balanceSheetData.push({ 'البيان': 'حقوق الملكية', 'المبلغ': '', 'النوع': 'header' });
        equityAccounts.forEach(account => {
            let accountBalance = account.openingBalance || 0;
            journalEntries.forEach(entry => {
                if (entry.entries) {
                    entry.entries.forEach(line => {
                        if (line.accountId === account.id) {
                            accountBalance += (line.credit || 0) - (line.debit || 0);
                        }
                    });
                }
            });

            if (accountBalance !== 0) {
                balanceSheetData.push({
                    'البيان': account.name,
                    'المبلغ': accountBalance.toLocaleString(),
                    'النوع': 'equity'
                });
                totalEquity += accountBalance;
            }
        });

        balanceSheetData.push({
            'البيان': 'إجمالي حقوق الملكية',
            'المبلغ': totalEquity.toLocaleString(),
            'النوع': 'total'
        });

        balanceSheetData.push({
            'البيان': 'إجمالي الخصوم وحقوق الملكية',
            'المبلغ': (totalLiabilities + totalEquity).toLocaleString(),
            'النوع': 'total'
        });

        const tableHtml = generateTableHtml(balanceSheetData, 'الميزانية العمومية');
        showReportOptions(balanceSheetData, tableHtml, 'الميزانية العمومية', 'balance_sheet');

        showMessage('✅ تم إنشاء الميزانية العمومية بنجاح!', 'success');
    }, 2000);
}

// Sales Report Generation
function generateSalesReport() {
    showMessage('🔄 جاري إنشاء تقرير المبيعات...', 'info');

    setTimeout(() => {
        const invoices = JSON.parse(localStorage.getItem('anwar_bakery_invoices') || '[]');
        const salesInvoices = invoices.filter(inv => inv.type === 'sales');

        const salesData = [];
        let totalSales = 0;
        let totalTax = 0;

        salesInvoices.forEach(invoice => {
            const invoiceTotal = invoice.total || 0;
            const invoiceTax = invoice.taxAmount || 0;

            salesData.push({
                'رقم الفاتورة': invoice.invoiceNumber || invoice.id,
                'التاريخ': invoice.date || '',
                'العميل': invoice.customerName || 'عميل نقدي',
                'المبلغ الإجمالي': invoiceTotal.toLocaleString(),
                'الضريبة': invoiceTax.toLocaleString(),
                'صافي المبلغ': (invoiceTotal - invoiceTax).toLocaleString()
            });

            totalSales += invoiceTotal;
            totalTax += invoiceTax;
        });

        // Add summary row
        salesData.push({
            'رقم الفاتورة': 'الإجمالي',
            'التاريخ': '',
            'العميل': '',
            'المبلغ الإجمالي': totalSales.toLocaleString(),
            'الضريبة': totalTax.toLocaleString(),
            'صافي المبلغ': (totalSales - totalTax).toLocaleString()
        });

        const tableHtml = generateTableHtml(salesData, 'تقرير المبيعات');
        showReportOptions(salesData, tableHtml, 'تقرير المبيعات', 'sales_report');

        showMessage('✅ تم إنشاء تقرير المبيعات بنجاح!', 'success');
    }, 2000);
}

// Inventory Report Generation
function generateInventoryReport() {
    showMessage('🔄 جاري إنشاء تقرير المخزون...', 'info');

    setTimeout(() => {
        const products = JSON.parse(localStorage.getItem('anwar_bakery_products') || '[]');

        const inventoryData = [];
        let totalValue = 0;

        products.forEach(product => {
            const quantity = product.quantity || 0;
            const cost = product.cost || 0;
            const value = quantity * cost;

            inventoryData.push({
                'كود المنتج': product.code || product.id,
                'اسم المنتج': product.name || '',
                'الوحدة': product.unit || '',
                'الكمية': quantity.toLocaleString(),
                'التكلفة': cost.toLocaleString(),
                'القيمة الإجمالية': value.toLocaleString(),
                'الحد الأدنى': product.minStock || 0,
                'الحالة': quantity <= (product.minStock || 0) ? 'منخفض' : 'طبيعي'
            });

            totalValue += value;
        });

        // Add summary row
        inventoryData.push({
            'كود المنتج': 'الإجمالي',
            'اسم المنتج': '',
            'الوحدة': '',
            'الكمية': '',
            'التكلفة': '',
            'القيمة الإجمالية': totalValue.toLocaleString(),
            'الحد الأدنى': '',
            'الحالة': ''
        });

        const tableHtml = generateTableHtml(inventoryData, 'تقرير المخزون');
        showReportOptions(inventoryData, tableHtml, 'تقرير المخزون', 'inventory_report');

        showMessage('✅ تم إنشاء تقرير المخزون بنجاح!', 'success');
    }, 2000);
}
