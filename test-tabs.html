<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التبويبات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .modal-tab-button {
            transition: all 0.2s ease;
            cursor: pointer;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">اختبار التبويبات</h1>
        
        <div class="bg-white rounded-lg shadow-lg p-6">
            <!-- Tabs -->
            <div class="mb-4">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-6">
                        <button type="button" onclick="switchTab('basic', event)" class="modal-tab-button py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
                            المعلومات الأساسية
                        </button>
                        <button type="button" onclick="switchTab('pricing', event)" class="modal-tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                            الأسعار والتكاليف
                        </button>
                        <button type="button" onclick="switchTab('inventory', event)" class="modal-tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                            المخزون والوحدات
                        </button>
                        <button type="button" onclick="switchTab('opening', event)" class="modal-tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                            الرصيد الافتتاحي
                        </button>
                    </nav>
                </div>
            </div>

            <!-- Tab Contents -->
            <div id="basicTab" class="tab-content active">
                <h3 class="text-lg font-semibold mb-4">المعلومات الأساسية</h3>
                <p>هذا محتوى تبويب المعلومات الأساسية</p>
                <div class="grid grid-cols-2 gap-4 mt-4">
                    <input type="text" placeholder="كود الصنف" class="border rounded px-3 py-2">
                    <input type="text" placeholder="اسم الصنف" class="border rounded px-3 py-2">
                </div>
            </div>

            <div id="pricingTab" class="tab-content">
                <h3 class="text-lg font-semibold mb-4">الأسعار والتكاليف</h3>
                <p>هذا محتوى تبويب الأسعار والتكاليف</p>
                <div class="grid grid-cols-2 gap-4 mt-4">
                    <input type="number" placeholder="سعر التكلفة" class="border rounded px-3 py-2">
                    <input type="number" placeholder="سعر البيع" class="border rounded px-3 py-2">
                </div>
            </div>

            <div id="inventoryTab" class="tab-content">
                <h3 class="text-lg font-semibold mb-4">المخزون والوحدات</h3>
                <p>هذا محتوى تبويب المخزون والوحدات</p>
                <div class="grid grid-cols-2 gap-4 mt-4">
                    <input type="number" placeholder="الحد الأدنى" class="border rounded px-3 py-2">
                    <input type="number" placeholder="الحد الأقصى" class="border rounded px-3 py-2">
                </div>
            </div>

            <div id="openingTab" class="tab-content">
                <h3 class="text-lg font-semibold mb-4">الرصيد الافتتاحي</h3>
                <p>هذا محتوى تبويب الرصيد الافتتاحي</p>
                <div class="grid grid-cols-2 gap-4 mt-4">
                    <input type="number" placeholder="الكمية الافتتاحية" class="border rounded px-3 py-2">
                    <input type="number" placeholder="القيمة الافتتاحية" class="border rounded px-3 py-2">
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab switching function
        function switchTab(tabName, event) {
            try {
                // Prevent default if event exists
                if (event) {
                    event.preventDefault();
                }

                console.log('Switching to tab:', tabName);

                // Hide all modal tab contents
                document.querySelectorAll('.tab-content').forEach(tab => {
                    tab.classList.remove('active');
                });

                // Remove active class from all modal tab buttons
                document.querySelectorAll('.modal-tab-button').forEach(button => {
                    button.classList.remove('border-blue-500', 'text-blue-600');
                    button.classList.add('border-transparent', 'text-gray-500');
                });

                // Show selected tab content
                const targetTab = document.getElementById(tabName + 'Tab');
                if (targetTab) {
                    targetTab.classList.add('active');
                    console.log('Tab content shown:', tabName + 'Tab');
                } else {
                    console.warn(`Tab content with ID '${tabName}Tab' not found`);
                }

                // Activate selected tab button
                if (event && event.target) {
                    event.target.classList.remove('border-transparent', 'text-gray-500');
                    event.target.classList.add('border-blue-500', 'text-blue-600');
                    console.log('Tab button activated');
                } else {
                    // Find the button by onclick attribute if event is not available
                    const tabButton = document.querySelector(`button[onclick*="switchTab('${tabName}')"]`);
                    if (tabButton) {
                        tabButton.classList.remove('border-transparent', 'text-gray-500');
                        tabButton.classList.add('border-blue-500', 'text-blue-600');
                        console.log('Tab button found and activated');
                    }
                }

                console.log(`Successfully switched to tab: ${tabName}`);
            } catch (error) {
                console.error('Error switching tabs:', error);
            }
        }

        // Test function to switch tabs programmatically
        function testTabs() {
            console.log('Testing tabs...');
            setTimeout(() => switchTab('pricing'), 1000);
            setTimeout(() => switchTab('inventory'), 2000);
            setTimeout(() => switchTab('opening'), 3000);
            setTimeout(() => switchTab('basic'), 4000);
        }

        // Auto-test on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded, tabs should be working');
            // Uncomment the line below to auto-test
            // testTabs();
        });
    </script>
</body>
</html>
