// Customer Management System
let customers = [];
let filteredCustomers = [];

// Sample customers data
const sampleCustomers = [
    {
        id: 1,
        code: 'CUS-001',
        name: 'مطعم الأصالة',
        type: 'company',
        phone: '**********',
        email: '<EMAIL>',
        city: 'الرياض',
        address: 'حي الملك فهد، شارع الملك عبدالعزيز',
        branchId: 1,
        branchName: 'الفرع الرئيسي',
        creditLimit: 10000,
        openingBalance: 2000,
        balance: 2500,
        status: 'active',
        taxNumber: '************',
        commercialReg: '**********',
        notes: 'عميل مميز - دفع منتظم',
        createdAt: '2024-01-15',
        accountCode: '1201001' // حساب العميل في شجرة الحسابات
    },
    {
        id: 2,
        code: 'CUS-002',
        name: '<PERSON><PERSON>م<PERSON> محمد السالم',
        type: 'individual',
        phone: '**********',
        email: '<EMAIL>',
        city: 'جدة',
        address: 'حي النزهة، شارع الأمير سلطان',
        branchId: 2,
        branchName: 'فرع الملك فهد',
        creditLimit: 5000,
        openingBalance: 0,
        balance: 0,
        status: 'active',
        notes: 'عميل منتظم',
        createdAt: '2024-01-20',
        accountCode: '1201002'
    },
    {
        id: 3,
        code: 'CUS-003',
        name: 'فندق الضيافة الذهبية',
        type: 'vip',
        phone: '**********',
        email: '<EMAIL>',
        city: 'الدمام',
        address: 'الكورنيش الشرقي، برج التجارة',
        branchId: 1,
        branchName: 'الفرع الرئيسي',
        creditLimit: 25000,
        openingBalance: 8000,
        balance: 8750,
        status: 'active',
        taxNumber: '************',
        commercialReg: '**********',
        notes: 'عميل VIP - أولوية في التوريد',
        createdAt: '2024-01-10',
        accountCode: '1201003'
    }
];

// Initialize customers
function initializeCustomers() {
    const storedCustomers = localStorage.getItem('customers');
    if (storedCustomers) {
        customers = JSON.parse(storedCustomers);
    } else {
        customers = [...sampleCustomers];
        saveCustomers();
    }
    filteredCustomers = [...customers];
}

// Check authentication
function checkAuth() {
    const session = localStorage.getItem('anwar_bakery_session') ||
                   sessionStorage.getItem('anwar_bakery_session');

    if (!session) {
        window.location.href = 'login.html';
        return null;
    }

    return JSON.parse(session);
}

// Load user info
function loadUserInfo() {
    const session = checkAuth();
    if (session) {
        document.getElementById('userFullName').textContent = session.fullName;
        document.getElementById('userRole').textContent = session.role;
    }
}

// Load company info
function loadCompanyInfo() {
    // Use new settings system if available
    if (window.appSettings) {
        const companyName = window.appSettings.get('company', 'companyNameAr');
        if (companyName) {
            document.getElementById('sidebarCompanyName').textContent = companyName;
            document.title = `إدارة العملاء - ${companyName}`;
        }
    } else {
        // Fallback to old system
        const savedData = localStorage.getItem('anwar_bakery_company');
        if (savedData) {
            const companyData = JSON.parse(savedData);

            if (companyData.companyNameAr) {
                document.getElementById('sidebarCompanyName').textContent = companyData.companyNameAr;
                document.title = `إدارة العملاء - ${companyData.companyNameAr}`;
            }
        }
    }
}

// Load customers from localStorage
function loadCustomers() {
    const savedCustomers = localStorage.getItem('anwar_bakery_customers');
    if (savedCustomers) {
        customers = JSON.parse(savedCustomers);
    }
    renderCustomers();
    updateStats();
    populateBranchFilters();
}

// Save customers to localStorage
function saveCustomers() {
    localStorage.setItem('anwar_bakery_customers', JSON.stringify(customers));
}

// Load customers and display
function loadCustomers() {
    initializeCustomers();
    displayCustomers();
    updateStats();
}

// Display customers in table
function displayCustomers() {
    const tbody = document.getElementById('customersTableBody');

    if (filteredCustomers.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="px-6 py-4 text-center text-gray-500">
                    لا توجد عملاء مطابقة للبحث
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = filteredCustomers.map(customer => `
        <tr class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                ${customer.code}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-8 w-8">
                        <div class="h-8 w-8 rounded-full bg-${getCustomerTypeColor(customer.type)}-100 flex items-center justify-center">
                            <span class="text-${getCustomerTypeColor(customer.type)}-600 text-sm font-medium">
                                ${getCustomerTypeIcon(customer.type)}
                            </span>
                        </div>
                    </div>
                    <div class="mr-4">
                        <div class="text-sm font-medium text-gray-900">${customer.name}</div>
                        <div class="text-sm text-gray-500">${customer.email || 'لا يوجد بريد إلكتروني'}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${getCustomerTypeColor(customer.type)}-100 text-${getCustomerTypeColor(customer.type)}-800">
                    ${getCustomerTypeText(customer.type)}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${customer.branchName}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${customer.phone}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${customer.city}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <span class="text-${customer.balance >= 0 ? 'green' : 'red'}-600">
                    ${customer.balance.toFixed(2)} ${getCurrencySymbol()}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${customer.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                    ${customer.status === 'active' ? 'نشط' : 'غير نشط'}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                    <button onclick="editCustomer(${customer.id})" class="text-blue-600 hover:text-blue-900" title="تعديل">
                        ✏️
                    </button>
                    <button onclick="viewCustomerAccount(${customer.id})" class="text-green-600 hover:text-green-900" title="كشف حساب">
                        📊
                    </button>
                    <button onclick="toggleCustomerStatus(${customer.id})" class="text-orange-600 hover:text-orange-900" title="تغيير الحالة">
                        🔄
                    </button>
                    <button onclick="deleteCustomer(${customer.id})" class="text-red-600 hover:text-red-900" title="حذف">
                        🗑️
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// Update statistics
function updateStats() {
    const totalCustomers = customers.length;
    const activeCustomers = customers.filter(customer => customer.isActive).length;
    const vipCustomers = customers.filter(customer => customer.isVip).length;

    // Calculate new customers this month
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format
    const newCustomers = customers.filter(customer =>
        customer.createdAt && customer.createdAt.startsWith(currentMonth)
    ).length;

    document.getElementById('totalCustomers').textContent = totalCustomers;
    document.getElementById('activeCustomers').textContent = activeCustomers;
    document.getElementById('vipCustomers').textContent = vipCustomers;
    document.getElementById('newCustomers').textContent = newCustomers;
}

// Render customers table
function renderCustomers(filteredCustomers = customers) {
    const tbody = document.getElementById('customersTableBody');
    tbody.innerHTML = '';

    filteredCustomers.forEach(customer => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';

        const typeNames = {
            'individual': 'فردي',
            'company': 'شركة',
            'vip': 'VIP'
        };

        const statusClass = customer.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
        const statusText = customer.isActive ? 'نشط' : 'غير نشط';

        const balanceClass = customer.balance >= 0 ? 'text-green-600' : 'text-red-600';
        const balanceText = customer.balance >= 0 ? `+${formatCurrency(customer.balance)}` : formatCurrency(customer.balance);

        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${customer.customerCode}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div class="flex items-center">
                    ${customer.isVip ? '<span class="text-yellow-500 ml-2">⭐</span>' : ''}
                    ${customer.customerName}
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${typeNames[customer.customerType]}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${customer.preferredBranchName || '-'}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${customer.phone || '-'}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${customer.city || '-'}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold ${balanceClass}">${balanceText}</td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                    ${statusText}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button onclick="editCustomer(${customer.id})" class="text-blue-600 hover:text-blue-900">تعديل</button>
                <button onclick="viewCustomerDetails(${customer.id})" class="text-green-600 hover:text-green-900">عرض</button>
                <button onclick="toggleCustomerStatus(${customer.id})" class="text-yellow-600 hover:text-yellow-900">
                    ${customer.isActive ? 'إلغاء تفعيل' : 'تفعيل'}
                </button>
                <button onclick="deleteCustomer(${customer.id})" class="text-red-600 hover:text-red-900">حذف</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Filter customers
function filterCustomers() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const branchFilter = document.getElementById('branchFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;

    filteredCustomers = customers.filter(customer => {
        const matchesSearch = customer.name.toLowerCase().includes(searchTerm) ||
                            customer.code.toLowerCase().includes(searchTerm) ||
                            (customer.phone && customer.phone.includes(searchTerm));
        const matchesBranch = !branchFilter || customer.branchId == branchFilter;
        const matchesType = !typeFilter || customer.type === typeFilter;
        const matchesStatus = !statusFilter || customer.status === statusFilter;

        return matchesSearch && matchesBranch && matchesType && matchesStatus;
    });

    displayCustomers();
}

// Load branches for filters
function loadBranches() {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    let branches = [];

    if (savedBranches) {
        branches = JSON.parse(savedBranches);
    } else {
        // Default branches if not found
        branches = [
            { id: 1, branchName: 'الفرع الرئيسي', isActive: true },
            { id: 2, branchName: 'فرع الملك فهد', isActive: true },
            { id: 3, branchName: 'فرع العليا', isActive: true }
        ];
        localStorage.setItem('anwar_bakery_branches', JSON.stringify(branches));
    }

    const branchFilter = document.getElementById('branchFilter');
    if (branchFilter) {
        branchFilter.innerHTML = '<option value="">جميع الفروع</option>';
        branches.filter(branch => branch.isActive).forEach(branch => {
            const option = document.createElement('option');
            option.value = branch.id;
            option.textContent = branch.branchName;
            branchFilter.appendChild(option);
        });
    }
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (sidebar.classList.contains('translate-x-full')) {
        sidebar.classList.remove('translate-x-full');
        overlay.classList.remove('hidden');
    } else {
        sidebar.classList.add('translate-x-full');
        overlay.classList.add('hidden');
    }
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_session');
        sessionStorage.removeItem('anwar_bakery_session');
        window.location.href = 'login.html';
    }
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Open add customer modal
function openAddCustomerModal() {
    document.getElementById('addCustomerModal').classList.remove('hidden');
    document.getElementById('addCustomerModal').style.display = 'block';
    generateCustomerCode();
}

// Close add customer modal
function closeAddCustomerModal() {
    document.getElementById('addCustomerModal').classList.add('hidden');
    document.getElementById('addCustomerModal').style.display = 'none';
    document.getElementById('addCustomerForm').reset();
}

// Generate customer code
function generateCustomerCode() {
    const nextId = Math.max(...customers.map(c => c.id), 0) + 1;
    const code = `CUST${nextId.toString().padStart(4, '0')}`;
    document.getElementById('customerCode').value = code;
}

// Add customer
function addCustomer(event) {
    event.preventDefault();

    const branchId = document.getElementById('customerBranch').value;
    let branchName = '';

    if (branchId) {
        const savedBranches = localStorage.getItem('anwar_bakery_branches');
        if (savedBranches) {
            const branches = JSON.parse(savedBranches);
            const branch = branches.find(b => b.id == branchId);
            branchName = branch ? branch.branchName : '';
        }
    }

    const newCustomer = {
        id: Math.max(...customers.map(c => c.id), 0) + 1,
        code: document.getElementById('customerCode').value,
        name: document.getElementById('customerName').value,
        type: document.getElementById('customerType').value,
        phone: document.getElementById('customerPhone').value,
        email: document.getElementById('customerEmail').value,
        branchId: parseInt(branchId) || null,
        branchName: branchName,
        city: document.getElementById('customerCity').value,
        address: document.getElementById('customerAddress').value,
        creditLimit: parseFloat(document.getElementById('customerCreditLimit').value) || 0,
        openingBalance: 0,
        balance: 0,
        status: 'active',
        taxNumber: document.getElementById('customerTaxNumber').value,
        commercialReg: document.getElementById('customerCommercialReg').value,
        notes: document.getElementById('customerNotes').value,
        createdAt: new Date().toISOString().split('T')[0],
        accountCode: `1201${(Math.max(...customers.map(c => c.id), 0) + 1).toString().padStart(3, '0')}`
    };

    customers.push(newCustomer);
    saveCustomers();
    displayCustomers();
    updateStats();
    closeAddCustomerModal();
    showMessage('تم إضافة العميل بنجاح!', 'success');
}

// Edit customer
function editCustomer(id) {
    const customer = customers.find(c => c.id === id);
    if (customer) {
        document.getElementById('editCustomerId').value = customer.id;
        document.getElementById('editCustomerCode').value = customer.code;
        document.getElementById('editCustomerName').value = customer.name;
        document.getElementById('editCustomerType').value = customer.type;
        document.getElementById('editCustomerPhone').value = customer.phone;
        document.getElementById('editCustomerEmail').value = customer.email || '';
        document.getElementById('editCustomerBranch').value = customer.branchId || '';
        document.getElementById('editCustomerCity').value = customer.city || '';
        document.getElementById('editCustomerAddress').value = customer.address || '';
        document.getElementById('editCustomerCreditLimit').value = customer.creditLimit || 0;
        document.getElementById('editCustomerNotes').value = customer.notes || '';

        document.getElementById('editCustomerModal').classList.remove('hidden');
        document.getElementById('editCustomerModal').style.display = 'block';
    }
}

// Close edit customer modal
function closeEditCustomerModal() {
    document.getElementById('editCustomerModal').classList.add('hidden');
    document.getElementById('editCustomerModal').style.display = 'none';
}

// Update customer
function updateCustomer(event) {
    event.preventDefault();

    const customerId = parseInt(document.getElementById('editCustomerId').value);
    const branchId = document.getElementById('editCustomerBranch').value;
    let branchName = '';

    if (branchId) {
        const savedBranches = localStorage.getItem('anwar_bakery_branches');
        if (savedBranches) {
            const branches = JSON.parse(savedBranches);
            const branch = branches.find(b => b.id == branchId);
            branchName = branch ? branch.branchName : '';
        }
    }

    const customerIndex = customers.findIndex(c => c.id === customerId);
    if (customerIndex !== -1) {
        customers[customerIndex] = {
            ...customers[customerIndex],
            name: document.getElementById('editCustomerName').value,
            type: document.getElementById('editCustomerType').value,
            phone: document.getElementById('editCustomerPhone').value,
            email: document.getElementById('editCustomerEmail').value,
            branchId: parseInt(branchId) || null,
            branchName: branchName,
            city: document.getElementById('editCustomerCity').value,
            address: document.getElementById('editCustomerAddress').value,
            creditLimit: parseFloat(document.getElementById('editCustomerCreditLimit').value) || 0,
            notes: document.getElementById('editCustomerNotes').value
        };

        saveCustomers();
        displayCustomers();
        updateStats();
        closeEditCustomerModal();
        showMessage('تم تحديث بيانات العميل بنجاح!', 'success');
    }
}

// View customer details
function viewCustomerDetails(id) {
    const customer = customers.find(c => c.id === id);
    if (customer) {
        const currencySymbol = getCurrencySymbol();
        let details = `تفاصيل العميل: ${customer.name}\n\n`;
        details += `🆔 الكود: ${customer.code}\n`;
        details += `📱 الهاتف: ${customer.phone}\n`;
        details += `📧 البريد: ${customer.email || 'غير محدد'}\n`;
        details += `🏢 الفرع: ${customer.branchName || 'غير محدد'}\n`;
        details += `🏙️ المدينة: ${customer.city || 'غير محدد'}\n`;
        details += `💰 حد الائتمان: ${customer.creditLimit.toFixed(2)} ${currencySymbol}\n`;
        details += `💵 الرصيد: ${customer.balance.toFixed(2)} ${currencySymbol}\n`;
        details += `📊 الحالة: ${customer.status === 'active' ? 'نشط' : 'غير نشط'}\n`;
        if (customer.notes) {
            details += `📝 ملاحظات: ${customer.notes}\n`;
        }

        alert(details);
    }
}

function toggleCustomerStatus(id) {
    const customer = customers.find(c => c.id === id);
    if (customer) {
        customer.isActive = !customer.isActive;
        saveCustomers();
        renderCustomers();
        updateStats();
        showMessage(`تم ${customer.isActive ? 'تفعيل' : 'إلغاء تفعيل'} العميل بنجاح!`, 'success');
    }
}

function deleteCustomer(id) {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
        customers = customers.filter(c => c.id !== id);
        saveCustomers();
        renderCustomers();
        updateStats();
        showMessage('تم حذف العميل بنجاح!', 'success');
    }
}

// Toggle customer fields based on type
function toggleCustomerFields() {
    const customerType = document.getElementById('customerType').value;
    const companyFields = document.getElementById('companyFields');

    if (customerType === 'company') {
        companyFields.style.display = 'block';
    } else {
        companyFields.style.display = 'none';
    }
}

function exportCustomersToExcel() {
    const currencySymbol = getCurrencySymbol();
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += `الكود,الاسم,النوع,الهاتف,البريد الإلكتروني,الفرع,المدينة,حد الائتمان (${currencySymbol}),الرصيد (${currencySymbol}),الحالة,تاريخ الإنشاء\n`;

    customers.forEach(customer => {
        const typeText = customer.type === 'individual' ? 'فردي' : customer.type === 'company' ? 'شركة' : 'VIP';
        const statusText = customer.status === 'active' ? 'نشط' : 'غير نشط';

        csvContent += `"${customer.code}","${customer.name}","${typeText}","${customer.phone}","${customer.email || ''}","${customer.branchName || ''}","${customer.city || ''}","${customer.creditLimit}","${customer.balance}","${statusText}","${customer.createdAt}"\n`;
    });

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `customers_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showMessage('تم تصدير بيانات العملاء بنجاح!', 'success');
}

// Get currency symbol from company settings
function getCurrencySymbol() {
    const companyData = localStorage.getItem('anwar_bakery_company');
    if (companyData) {
        const company = JSON.parse(companyData);
        return company.currency || 'ر.س';
    }
    return 'ر.س';
}

// Format currency using settings
function formatCurrency(amount) {
    if (window.appSettings) {
        return window.appSettings.formatCurrency(amount);
    } else {
        // Fallback formatting
        return `${amount.toFixed(2)} ${getCurrencySymbol()}`;
    }
}

// Populate branch dropdowns
function populateBranchDropdowns() {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    let branches = [];

    if (savedBranches) {
        branches = JSON.parse(savedBranches);
    } else {
        branches = [
            { id: 1, branchName: 'الفرع الرئيسي', isActive: true },
            { id: 2, branchName: 'فرع الملك فهد', isActive: true },
            { id: 3, branchName: 'فرع العليا', isActive: true }
        ];
        localStorage.setItem('anwar_bakery_branches', JSON.stringify(branches));
    }

    const dropdowns = ['customerBranch', 'editCustomerBranch'];
    dropdowns.forEach(dropdownId => {
        const dropdown = document.getElementById(dropdownId);
        if (dropdown) {
            dropdown.innerHTML = '<option value="">اختر الفرع</option>';
            branches.filter(branch => branch.isActive).forEach(branch => {
                const option = document.createElement('option');
                option.value = branch.id;
                option.textContent = branch.branchName;
                dropdown.appendChild(option);
            });
        }
    });
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadCustomers();
    loadBranches();
    populateBranchDropdowns();

    // Hide modals initially
    document.getElementById('addCustomerModal').style.display = 'none';
    document.getElementById('editCustomerModal').style.display = 'none';
});
