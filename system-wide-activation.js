// System-Wide Advanced Features Activation - تفعيل الميزات المتقدمة على مستوى النظام
// This script ensures all advanced features are activated across the entire system

/**
 * Global system activation
 * تفعيل النظام الشامل
 */
(function() {
    'use strict';
    
    // Configuration
    const SYSTEM_CONFIG = {
        version: '2.0.0',
        features: {
            advancedExcel: true,
            advancedPrint: true,
            globalFeatures: true,
            autoActivation: true,
            keyboardShortcuts: true,
            statusIndicator: true
        },
        debug: false
    };

    /**
     * Main system activation function
     * دالة تفعيل النظام الرئيسية
     */
    function activateSystemWide() {
        console.log('🚀 System-Wide Advanced Features Activation Started...');
        
        // Check if already activated
        if (window.systemActivated) {
            console.log('✅ System already activated');
            return;
        }
        
        try {
            // Step 1: Initialize core features
            initializeCoreFeatures();
            
            // Step 2: Activate page-specific features
            activatePageFeatures();
            
            // Step 3: Setup global event listeners
            setupGlobalEventListeners();
            
            // Step 4: Initialize UI enhancements
            initializeUIEnhancements();
            
            // Step 5: Setup monitoring and diagnostics
            setupMonitoring();
            
            // Step 6: Show activation status
            showSystemStatus();
            
            // Mark as activated
            window.systemActivated = true;
            window.systemConfig = SYSTEM_CONFIG;
            
            console.log('✅ System-Wide Advanced Features Activated Successfully!');
            
            // Dispatch activation event
            window.dispatchEvent(new CustomEvent('systemActivated', {
                detail: { version: SYSTEM_CONFIG.version, timestamp: new Date() }
            }));
            
        } catch (error) {
            console.error('❌ System activation failed:', error);
            showErrorStatus(error);
        }
    }

    /**
     * Initialize core features
     * تهيئة الميزات الأساسية
     */
    function initializeCoreFeatures() {
        // Check library availability
        const libraries = checkLibraryAvailability();
        
        // Initialize Excel features
        if (libraries.advancedExcel) {
            initializeExcelFeatures();
        }
        
        // Initialize Print features
        if (libraries.advancedPrint) {
            initializePrintFeatures();
        }
        
        // Initialize basic Excel utils
        if (libraries.excelUtils) {
            initializeBasicExcel();
        }
        
        console.log('📚 Core features initialized:', libraries);
    }

    /**
     * Check library availability
     * فحص توفر المكتبات
     */
    function checkLibraryAvailability() {
        return {
            advancedExcel: typeof window.advancedExcel !== 'undefined',
            advancedPrint: typeof window.advancedPrint !== 'undefined',
            excelUtils: typeof window.excelUtils !== 'undefined',
            globalFeatures: typeof window.activateAdvancedFeatures !== 'undefined'
        };
    }

    /**
     * Initialize Excel features
     * تهيئة ميزات Excel
     */
    function initializeExcelFeatures() {
        if (!window.globalExportToExcel) {
            window.globalExportToExcel = function(data, filename = 'export', sheetName = 'البيانات') {
                if (typeof window.advancedExcel !== 'undefined') {
                    return window.advancedExcel.exportFinancialReport(data, filename, { date: new Date().toISOString().split('T')[0] });
                } else if (typeof window.excelUtils !== 'undefined') {
                    return window.excelUtils.exportToExcel(data, filename, sheetName);
                } else {
                    console.warn('No Excel export library available');
                    return false;
                }
            };
        }
    }

    /**
     * Initialize Print features
     * تهيئة ميزات الطباعة
     */
    function initializePrintFeatures() {
        if (!window.globalPrint) {
            window.globalPrint = function(data, options = {}) {
                if (typeof window.advancedPrint !== 'undefined') {
                    return window.advancedPrint.printInvoice(data, {
                        showLogo: true,
                        showHeader: true,
                        showFooter: true,
                        copies: 1,
                        paperSize: 'A4',
                        ...options
                    });
                } else {
                    window.print();
                    return true;
                }
            };
        }
        
        if (!window.globalPrintReceipt) {
            window.globalPrintReceipt = function(data, options = {}) {
                if (typeof window.advancedPrint !== 'undefined') {
                    return window.advancedPrint.printReceipt(data, {
                        width: '80mm',
                        fontSize: '12px',
                        ...options
                    });
                } else {
                    alert('نظام الطباعة الحرارية غير متوفر');
                    return false;
                }
            };
        }
    }

    /**
     * Initialize basic Excel
     * تهيئة Excel الأساسي
     */
    function initializeBasicExcel() {
        if (!window.basicExportToExcel) {
            window.basicExportToExcel = function(data, filename = 'export', sheetName = 'البيانات') {
                if (typeof window.excelUtils !== 'undefined') {
                    return window.excelUtils.exportToExcel(data, filename, sheetName);
                } else {
                    console.warn('Basic Excel export not available');
                    return false;
                }
            };
        }
    }

    /**
     * Activate page-specific features
     * تفعيل الميزات الخاصة بالصفحة
     */
    function activatePageFeatures() {
        const currentPage = getCurrentPageType();
        
        // Add export/print buttons to all data management pages
        addUniversalButtons();
        
        // Activate specific features based on page
        switch (currentPage) {
            case 'dashboard':
                activateDashboardFeatures();
                break;
            case 'invoices':
                activateInvoiceFeatures();
                break;
            case 'products':
                activateProductFeatures();
                break;
            case 'customers':
                activateCustomerFeatures();
                break;
            case 'suppliers':
                activateSupplierFeatures();
                break;
            case 'employees':
                activateEmployeeFeatures();
                break;
            case 'reports':
                activateReportFeatures();
                break;
            case 'vouchers':
                activateVoucherFeatures();
                break;
            default:
                activateGeneralFeatures();
        }
        
        console.log(`📄 Page features activated for: ${currentPage}`);
    }

    /**
     * Get current page type
     * الحصول على نوع الصفحة الحالية
     */
    function getCurrentPageType() {
        const path = window.location.pathname;
        const filename = path.split('/').pop().split('.')[0];
        
        if (filename.includes('dashboard')) return 'dashboard';
        if (filename.includes('invoice')) return 'invoices';
        if (filename.includes('product')) return 'products';
        if (filename.includes('customer')) return 'customers';
        if (filename.includes('supplier')) return 'suppliers';
        if (filename.includes('employee')) return 'employees';
        if (filename.includes('report')) return 'reports';
        if (filename.includes('voucher')) return 'vouchers';
        
        return 'general';
    }

    /**
     * Add universal export/print buttons
     * إضافة أزرار التصدير والطباعة الشاملة
     */
    function addUniversalButtons() {
        // Find header sections that might need buttons
        const headers = document.querySelectorAll('.flex.justify-between.items-center, .flex.items-center.justify-between');
        
        headers.forEach(header => {
            if (!header.querySelector('.universal-buttons')) {
                const buttonsContainer = header.querySelector('.flex.space-x-2, .flex.space-x-4') || 
                                       header.querySelector('button')?.parentNode;
                
                if (buttonsContainer && !buttonsContainer.querySelector('.universal-export-btn')) {
                    const universalButtons = document.createElement('div');
                    universalButtons.className = 'universal-buttons flex space-x-2 ml-2';
                    universalButtons.innerHTML = `
                        <button onclick="universalExport()" class="universal-export-btn bg-emerald-600 text-white px-3 py-2 rounded-lg hover:bg-emerald-700 flex items-center text-sm transition-all duration-200">
                            <span class="ml-1">📊</span>
                            تصدير
                        </button>
                        <button onclick="universalPrint()" class="universal-print-btn bg-indigo-600 text-white px-3 py-2 rounded-lg hover:bg-indigo-700 flex items-center text-sm transition-all duration-200">
                            <span class="ml-1">🖨️</span>
                            طباعة
                        </button>
                    `;
                    
                    buttonsContainer.insertBefore(universalButtons, buttonsContainer.firstChild);
                }
            }
        });
    }

    /**
     * Setup global event listeners
     * إعداد مستمعي الأحداث العامة
     */
    function setupGlobalEventListeners() {
        // Global keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+Shift+E for universal export
            if (e.ctrlKey && e.shiftKey && e.key === 'E') {
                e.preventDefault();
                universalExport();
            }
            
            // Ctrl+Shift+P for universal print
            if (e.ctrlKey && e.shiftKey && e.key === 'P') {
                e.preventDefault();
                universalPrint();
            }
            
            // Ctrl+Shift+H for help
            if (e.ctrlKey && e.shiftKey && e.key === 'H') {
                e.preventDefault();
                showKeyboardShortcuts();
            }
        });
        
        // Listen for page changes
        window.addEventListener('popstate', function() {
            setTimeout(activateSystemWide, 100);
        });
        
        console.log('⌨️ Global event listeners setup complete');
    }

    /**
     * Initialize UI enhancements
     * تهيئة تحسينات واجهة المستخدم
     */
    function initializeUIEnhancements() {
        // Add global CSS
        addGlobalCSS();
        
        // Add floating action button
        addFloatingActionButton();
        
        // Add status indicator
        addSystemStatusIndicator();
        
        console.log('🎨 UI enhancements initialized');
    }

    /**
     * Add global CSS styles
     * إضافة أنماط CSS العامة
     */
    function addGlobalCSS() {
        if (document.getElementById('systemWideCSS')) return;
        
        const style = document.createElement('style');
        style.id = 'systemWideCSS';
        style.textContent = `
            .system-activated {
                position: relative;
            }
            
            .universal-buttons button {
                transition: all 0.3s ease;
            }
            
            .universal-buttons button:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            }
            
            .floating-action-btn {
                position: fixed;
                bottom: 20px;
                left: 20px;
                z-index: 1000;
                background: linear-gradient(45deg, #3b82f6, #8b5cf6);
                color: white;
                border: none;
                border-radius: 50%;
                width: 56px;
                height: 56px;
                font-size: 24px;
                cursor: pointer;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            
            .floating-action-btn:hover {
                transform: scale(1.1);
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
            }
            
            .system-status-indicator {
                position: fixed;
                top: 10px;
                left: 10px;
                z-index: 9999;
                background: rgba(34, 197, 94, 0.9);
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                pointer-events: none;
                opacity: 0;
                transition: opacity 0.3s ease;
            }
            
            .system-status-indicator.show {
                opacity: 1;
            }
            
            .keyboard-shortcuts-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 10000;
                display: none;
                align-items: center;
                justify-content: center;
            }
            
            .keyboard-shortcuts-content {
                background: white;
                padding: 20px;
                border-radius: 8px;
                max-width: 500px;
                max-height: 80vh;
                overflow-y: auto;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Add floating action button
     * إضافة زر الإجراء العائم
     */
    function addFloatingActionButton() {
        if (document.getElementById('floatingActionBtn')) return;
        
        const fab = document.createElement('button');
        fab.id = 'floatingActionBtn';
        fab.className = 'floating-action-btn';
        fab.innerHTML = '🚀';
        fab.title = 'الميزات المتقدمة (Ctrl+Shift+H للمساعدة)';
        fab.onclick = showKeyboardShortcuts;
        
        document.body.appendChild(fab);
    }

    /**
     * Add system status indicator
     * إضافة مؤشر حالة النظام
     */
    function addSystemStatusIndicator() {
        if (document.getElementById('systemStatusIndicator')) return;
        
        const indicator = document.createElement('div');
        indicator.id = 'systemStatusIndicator';
        indicator.className = 'system-status-indicator';
        indicator.textContent = '🚀 نشط';
        
        document.body.appendChild(indicator);
        document.body.classList.add('system-activated');
    }

    /**
     * Setup monitoring and diagnostics
     * إعداد المراقبة والتشخيص
     */
    function setupMonitoring() {
        // Monitor for errors
        window.addEventListener('error', function(e) {
            console.error('System error detected:', e.error);
        });
        
        // Monitor performance
        if (window.performance && window.performance.mark) {
            window.performance.mark('system-activation-complete');
        }
        
        console.log('📊 Monitoring and diagnostics setup complete');
    }

    /**
     * Show system status
     * عرض حالة النظام
     */
    function showSystemStatus() {
        const indicator = document.getElementById('systemStatusIndicator');
        if (indicator) {
            indicator.classList.add('show');
            setTimeout(() => {
                indicator.classList.remove('show');
            }, 3000);
        }
        
        // Show console message
        console.log(`
🚀 نظام الميزات المتقدمة نشط
📊 تصدير Excel: ${typeof window.advancedExcel !== 'undefined' ? '✅' : '⚠️'}
🖨️ طباعة متقدمة: ${typeof window.advancedPrint !== 'undefined' ? '✅' : '⚠️'}
⌨️ اختصارات المفاتيح: ✅
🎨 تحسينات الواجهة: ✅

اختصارات المفاتيح:
• Ctrl+Shift+E: تصدير شامل
• Ctrl+Shift+P: طباعة شاملة  
• Ctrl+Shift+H: عرض المساعدة
        `);
    }

    /**
     * Show error status
     * عرض حالة الخطأ
     */
    function showErrorStatus(error) {
        console.error('❌ فشل في تفعيل النظام:', error);
        
        const indicator = document.getElementById('systemStatusIndicator');
        if (indicator) {
            indicator.style.background = 'rgba(239, 68, 68, 0.9)';
            indicator.textContent = '❌ خطأ';
            indicator.classList.add('show');
        }
    }

    // Universal functions
    window.universalExport = function() {
        console.log('🔄 Universal export triggered');
        
        // Try page-specific export first
        if (typeof exportToExcel === 'function') {
            exportToExcel();
        } else if (typeof exportProductsToExcel === 'function') {
            exportProductsToExcel();
        } else if (typeof exportCustomersToExcel === 'function') {
            exportCustomersToExcel();
        } else if (typeof exportAllReportsToExcel === 'function') {
            exportAllReportsToExcel();
        } else {
            // Fallback to global export
            const data = collectPageData();
            window.globalExportToExcel(data, 'تصدير_شامل', 'البيانات');
        }
    };

    window.universalPrint = function() {
        console.log('🔄 Universal print triggered');
        
        // Try page-specific print first
        if (typeof printInvoice === 'function') {
            printInvoice();
        } else if (typeof printProductsList === 'function') {
            printProductsList();
        } else if (typeof printCustomersList === 'function') {
            printCustomersList();
        } else if (typeof printAllReports === 'function') {
            printAllReports();
        } else {
            // Fallback to global print
            window.globalPrint({
                number: 'PRINT-' + Date.now(),
                date: new Date().toISOString().split('T')[0],
                type: 'general_print',
                items: [],
                notes: 'طباعة شاملة للصفحة'
            });
        }
    };

    window.showKeyboardShortcuts = function() {
        // Implementation for showing keyboard shortcuts modal
        alert(`اختصارات المفاتيح:
• Ctrl+Shift+E: تصدير شامل
• Ctrl+Shift+P: طباعة شاملة
• Ctrl+Shift+H: عرض هذه المساعدة

الميزات المتقدمة نشطة ومتاحة في جميع الصفحات!`);
    };

    // Collect page data for export
    function collectPageData() {
        const tables = document.querySelectorAll('table');
        const data = [];
        
        tables.forEach(table => {
            const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim());
            const rows = Array.from(table.querySelectorAll('tbody tr'));
            
            rows.forEach(row => {
                const cells = Array.from(row.querySelectorAll('td'));
                const rowData = {};
                
                cells.forEach((cell, index) => {
                    if (headers[index]) {
                        rowData[headers[index]] = cell.textContent.trim();
                    }
                });
                
                if (Object.keys(rowData).length > 0) {
                    data.push(rowData);
                }
            });
        });
        
        return data.length > 0 ? data : [{ 'البيانات': 'لا توجد بيانات متاحة للتصدير' }];
    }

    // Placeholder activation functions
    function activateDashboardFeatures() { console.log('📊 Dashboard features activated'); }
    function activateInvoiceFeatures() { console.log('🧾 Invoice features activated'); }
    function activateProductFeatures() { console.log('📦 Product features activated'); }
    function activateCustomerFeatures() { console.log('👤 Customer features activated'); }
    function activateSupplierFeatures() { console.log('🚚 Supplier features activated'); }
    function activateEmployeeFeatures() { console.log('👨‍💼 Employee features activated'); }
    function activateReportFeatures() { console.log('📈 Report features activated'); }
    function activateVoucherFeatures() { console.log('📋 Voucher features activated'); }
    function activateGeneralFeatures() { console.log('🌐 General features activated'); }

    // Auto-activate when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(activateSystemWide, 500);
        });
    } else {
        setTimeout(activateSystemWide, 100);
    }

    // Also activate when window loads (fallback)
    window.addEventListener('load', function() {
        if (!window.systemActivated) {
            setTimeout(activateSystemWide, 200);
        }
    });

    // Export for manual activation
    window.activateSystemWide = activateSystemWide;
    
})();
