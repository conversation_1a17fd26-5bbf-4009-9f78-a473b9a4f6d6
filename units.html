<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة وحدات القياس - مخبز أنور</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="app-settings.js"></script>
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .modal { display: none; }
        .modal.active { display: flex; }

        /* تحسين مظهر النماذج */
        .modal .bg-white {
            max-height: 90vh;
        }

        input[type="text"],
        input[type="number"],
        select,
        textarea {
            font-size: 16px !important;
            line-height: 1.5;
        }

        button {
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .modal form {
            max-height: calc(90vh - 120px);
            overflow-y: auto;
        }

        .unit-example {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Mobile menu overlay -->
    <div id="mobileOverlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 lg:hidden hidden"></div>

    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform translate-x-full lg:translate-x-0 lg:static lg:inset-0 sidebar-transition">
            <div class="h-16 px-4 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between">
                <h1 id="sidebarCompanyName" class="text-white text-lg font-bold">مخبز أنور</h1>
                <button onclick="toggleSidebar()" class="lg:hidden text-white">
                    <span class="text-xl">✕</span>
                </button>
            </div>

            <nav class="mt-5 px-2 space-y-1 h-full overflow-y-auto pb-20">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    لوحة التحكم
                </a>
                <a href="users-management.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👥</span>
                    إدارة المستخدمين
                </a>
                <a href="company-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏢</span>
                    بيانات المنشأة
                </a>
                <a href="system-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">⚙️</span>
                    إعدادات النظام
                </a>
                <a href="branches-warehouses.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏪</span>
                    الفروع والمخازن
                </a>
                <a href="units.html" class="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">
                    <span class="ml-3">📏</span>
                    وحدات القياس
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🚚</span>
                    الموردين
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="inventory.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📊</span>
                    المخزون
                </a>
                <a href="reports.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📈</span>
                    التقارير
                </a>

                <!-- User Info & Logout -->
                <div class="absolute bottom-4 left-2 right-2">
                    <div class="bg-gray-50 rounded-lg p-3 mb-2">
                        <p id="userFullName" class="text-sm font-medium text-gray-900">مدير النظام</p>
                        <p id="userRole" class="text-xs text-gray-500">admin</p>
                    </div>
                    <button onclick="logout()" class="w-full text-right px-3 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50 flex items-center">
                        <span class="ml-3">🚪</span>
                        تسجيل الخروج
                    </button>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 overflow-hidden">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <button onclick="toggleSidebar()" class="lg:hidden text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">☰</span>
                            </button>
                            <span class="text-lg font-semibold text-gray-900">إدارة وحدات القياس</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button onclick="openAddUnitModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center">
                                <span class="ml-2">📏</span>
                                إضافة وحدة جديدة
                            </button>
                            <button onclick="showUnitCalculator()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center">
                                <span class="ml-2">🧮</span>
                                حاسبة الوحدات
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2 flex items-center">
                        <span class="ml-3">📏</span>
                        إدارة وحدات القياس
                    </h1>
                    <p class="text-gray-600">
                        إدارة وحدات القياس للمواد الخام والمنتجات مع حساب التكلفة التلقائي
                    </p>
                </div>

                <!-- Success/Error Messages -->
                <div id="messageContainer" class="mb-6"></div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100 text-sm">إجمالي الوحدات</p>
                                <p class="text-3xl font-bold" id="totalUnits">0</p>
                            </div>
                            <div class="text-3xl opacity-80">📏</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-100 text-sm">وحدات الوزن</p>
                                <p class="text-3xl font-bold" id="weightUnits">0</p>
                            </div>
                            <div class="text-3xl opacity-80">⚖️</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl shadow-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-100 text-sm">وحدات الحجم</p>
                                <p class="text-3xl font-bold" id="volumeUnits">0</p>
                            </div>
                            <div class="text-3xl opacity-80">🥤</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl shadow-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-orange-100 text-sm">وحدات العدد</p>
                                <p class="text-3xl font-bold" id="countUnits">0</p>
                            </div>
                            <div class="text-3xl opacity-80">🔢</div>
                        </div>
                    </div>
                </div>

                <!-- Unit Examples -->
                <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <span class="ml-2">💡</span>
                        أمثلة على وحدات القياس
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="unit-example rounded-lg p-4 text-white">
                            <h4 class="font-semibold mb-2">🛍️ كيس دقيق 50كج</h4>
                            <p class="text-sm opacity-90">الوحدة الكبيرة: 1 كيس</p>
                            <p class="text-sm opacity-90">الوحدة الصغيرة: 50000 جرام</p>
                            <p class="text-xs mt-2 opacity-75">للوصفات بالجرام أو بالكيس</p>
                        </div>
                        <div class="unit-example rounded-lg p-4 text-white">
                            <h4 class="font-semibold mb-2">🥛 جالون زيت 20 لتر</h4>
                            <p class="text-sm opacity-90">الوحدة الكبيرة: 1 جالون</p>
                            <p class="text-sm opacity-90">الوحدة الصغيرة: 20000 مل</p>
                            <p class="text-xs mt-2 opacity-75">للوصفات بالمل أو بالجالون</p>
                        </div>
                        <div class="unit-example rounded-lg p-4 text-white">
                            <h4 class="font-semibold mb-2">🥚 كرتون بيض 30 حبة</h4>
                            <p class="text-sm opacity-90">الوحدة الكبيرة: 1 كرتون</p>
                            <p class="text-sm opacity-90">الوحدة الصغيرة: 30 حبة</p>
                            <p class="text-xs mt-2 opacity-75">للوصفات بالحبة أو بالكرتون</p>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <input type="text" id="unitSearchInput" placeholder="البحث في الوحدات..."
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   onkeyup="filterUnits()">
                        </div>
                        <div>
                            <select id="unitTypeFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterUnits()">
                                <option value="">جميع الأنواع</option>
                                <option value="weight">وزن</option>
                                <option value="volume">حجم</option>
                                <option value="count">عدد</option>
                                <option value="length">طول</option>
                                <option value="area">مساحة</option>
                            </select>
                        </div>
                        <div>
                            <select id="unitCategoryFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterUnits()">
                                <option value="">جميع الفئات</option>
                                <option value="raw_materials">مواد خام</option>
                                <option value="packaging">تعبئة وتغليف</option>
                                <option value="finished_products">منتجات نهائية</option>
                            </select>
                        </div>
                        <div>
                            <select id="unitStatusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterUnits()">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Units Table -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">اسم الوحدة</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">النوع</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">الوحدة الكبيرة</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">الوحدة الصغيرة</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">معامل التحويل</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">الفئة</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                                    <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="unitsTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- Units will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add/Edit Unit Modal -->
    <div id="unitModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 id="unitModalTitle" class="text-lg font-medium text-gray-900">إضافة وحدة قياس جديدة</h3>
                <button onclick="closeUnitModal()" class="text-gray-400 hover:text-gray-600">
                    <span class="text-2xl">×</span>
                </button>
            </div>

            <form id="unitForm" class="px-4 py-3">
                <input type="hidden" id="unitId" value="">

                <div class="space-y-4">
                    <!-- Basic Information -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="font-semibold text-blue-900 mb-3 flex items-center">
                            <span class="ml-2">📝</span>
                            المعلومات الأساسية
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">اسم الوحدة *</label>
                                <input type="text" id="unitName" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                       placeholder="مثال: كيس دقيق 50كج">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">نوع الوحدة *</label>
                                <select id="unitType" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                                    <option value="">اختر النوع</option>
                                    <option value="weight">وزن (كيلوجرام، جرام)</option>
                                    <option value="volume">حجم (لتر، مل)</option>
                                    <option value="count">عدد (حبة، قطعة)</option>
                                    <option value="length">طول (متر، سم)</option>
                                    <option value="area">مساحة (متر مربع)</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الفئة *</label>
                                <select id="unitCategory" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                                    <option value="">اختر الفئة</option>
                                    <option value="raw_materials">مواد خام</option>
                                    <option value="packaging">تعبئة وتغليف</option>
                                    <option value="finished_products">منتجات نهائية</option>
                                </select>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="unitIsActive" checked
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="unitIsActive" class="mr-2 block text-sm text-gray-900">الوحدة نشطة</label>
                            </div>
                        </div>
                    </div>

                    <!-- Unit Conversion -->
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <h4 class="font-semibold text-green-900 mb-3 flex items-center">
                            <span class="ml-2">🔄</span>
                            معامل التحويل
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">اسم الوحدة الكبيرة *</label>
                                <input type="text" id="largeUnitName" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                       placeholder="مثال: كيس">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">عدد الوحدة الكبيرة *</label>
                                <input type="number" id="largeUnitCount" required min="1" step="1" value="1"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                       placeholder="1" onchange="calculateConversion()">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">اسم الوحدة الصغيرة *</label>
                                <input type="text" id="smallUnitName" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                       placeholder="مثال: جرام">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">عدد الوحدة الصغيرة *</label>
                                <input type="number" id="smallUnitCount" required min="1" step="0.01"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                       placeholder="50000" onchange="calculateConversion()">
                            </div>
                        </div>

                        <!-- Conversion Display -->
                        <div class="mt-4 p-3 bg-white rounded-lg border">
                            <p class="text-sm text-gray-600 mb-2">معامل التحويل:</p>
                            <p id="conversionDisplay" class="text-lg font-semibold text-green-600">
                                1 كيس = 50000 جرام
                            </p>
                        </div>
                    </div>

                    <!-- Description -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                        <textarea id="unitDescription" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                  placeholder="وصف تفصيلي للوحدة واستخدامها..."></textarea>
                    </div>
                </div>
            </form>

            <div class="px-4 py-3 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="closeUnitModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                    إلغاء
                </button>
                <button onclick="saveUnit()" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700">
                    حفظ الوحدة
                </button>
            </div>
        </div>
    </div>

    <!-- Unit Calculator Modal -->
    <div id="calculatorModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">حاسبة وحدات القياس</h3>
                <button onclick="closeCalculatorModal()" class="text-gray-400 hover:text-gray-600">
                    <span class="text-2xl">×</span>
                </button>
            </div>

            <div class="px-4 py-3">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اختر الوحدة</label>
                        <select id="calculatorUnit"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                onchange="updateCalculator()">
                            <option value="">اختر الوحدة</option>
                        </select>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الكمية</label>
                            <input type="number" id="calculatorQuantity" step="0.01" min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="0" onchange="calculateConversion()">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">من وحدة</label>
                            <select id="calculatorFromUnit"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                    onchange="performCalculation()">
                                <option value="">اختر</option>
                            </select>
                        </div>
                    </div>

                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 mb-2">النتيجة:</h4>
                        <div id="calculatorResult" class="text-lg font-semibold text-blue-600">
                            اختر الوحدة والكمية للحساب
                        </div>
                    </div>
                </div>
            </div>

            <div class="px-4 py-3 border-t border-gray-200 flex justify-end">
                <button onclick="closeCalculatorModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                    إغلاق
                </button>
            </div>
        </div>
    </div>

    <script src="units.js"></script>
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadUserInfo();
            loadCompanyInfo();
            loadUnits();
        });
    </script>

</body>
</html>
