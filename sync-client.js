// Sync Client - نظام المزامنة من جانب العميل
// Client-side synchronization system

class SyncClient {
    constructor() {
        this.apiUrl = 'api/sync-manager.php';
        this.isOnline = navigator.onLine;
        this.syncInProgress = false;
        this.autoSyncEnabled = true;
        this.syncInterval = 5 * 60 * 1000; // 5 minutes
        
        this.initializeSync();
    }

    /**
     * Initialize synchronization system
     * تهيئة نظام المزامنة
     */
    initializeSync() {
        // Check online status
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.showSyncStatus('🌐 متصل بالإنترنت', 'success');
            if (this.autoSyncEnabled) {
                this.autoSync();
            }
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showSyncStatus('📱 وضع عدم الاتصال', 'warning');
        });

        // Auto sync interval
        if (this.autoSyncEnabled) {
            setInterval(() => {
                if (this.isOnline && !this.syncInProgress) {
                    this.autoSync();
                }
            }, this.syncInterval);
        }

        // Check connection on load
        this.checkConnection();
    }

    /**
     * Check database connection
     * فحص اتصال قاعدة البيانات
     */
    async checkConnection() {
        try {
            const response = await fetch(`${this.apiUrl}?action=check-connection`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();
            
            if (result.success) {
                this.showSyncStatus('✅ متصل بقاعدة البيانات', 'success');
                return true;
            } else {
                this.showSyncStatus('❌ فشل الاتصال بقاعدة البيانات', 'error');
                return false;
            }
        } catch (error) {
            this.showSyncStatus('📱 وضع محلي فقط', 'info');
            return false;
        }
    }

    /**
     * Sync localStorage data to server
     * رفع البيانات المحلية إلى الخادم
     */
    async syncToServer() {
        if (!this.isOnline || this.syncInProgress) {
            return false;
        }

        this.syncInProgress = true;
        this.showSyncStatus('⬆️ جاري رفع البيانات...', 'info');

        try {
            // Collect all localStorage data
            const localData = this.collectLocalData();

            const response = await fetch(`${this.apiUrl}?action=sync-to-server`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ data: localData })
            });

            const result = await response.json();

            if (result.success) {
                this.showSyncStatus('✅ تم رفع البيانات بنجاح', 'success');
                this.updateLastSyncTime();
                return true;
            } else {
                this.showSyncStatus('❌ فشل في رفع البيانات', 'error');
                return false;
            }
        } catch (error) {
            this.showSyncStatus('❌ خطأ في الرفع: ' + error.message, 'error');
            return false;
        } finally {
            this.syncInProgress = false;
        }
    }

    /**
     * Sync server data to localStorage
     * تحميل البيانات من الخادم
     */
    async syncFromServer() {
        if (!this.isOnline || this.syncInProgress) {
            return false;
        }

        this.syncInProgress = true;
        this.showSyncStatus('⬇️ جاري تحميل البيانات...', 'info');

        try {
            const response = await fetch(`${this.apiUrl}?action=sync-from-server`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.success) {
                // Update localStorage with server data
                this.updateLocalData(result.data);
                this.showSyncStatus('✅ تم تحميل البيانات بنجاح', 'success');
                this.updateLastSyncTime();
                
                // Refresh current page if needed
                if (typeof refreshCurrentPage === 'function') {
                    refreshCurrentPage();
                }
                
                return true;
            } else {
                this.showSyncStatus('❌ فشل في تحميل البيانات', 'error');
                return false;
            }
        } catch (error) {
            this.showSyncStatus('❌ خطأ في التحميل: ' + error.message, 'error');
            return false;
        } finally {
            this.syncInProgress = false;
        }
    }

    /**
     * Full bidirectional sync
     * مزامنة كاملة ثنائية الاتجاه
     */
    async fullSync() {
        if (!this.isOnline || this.syncInProgress) {
            return false;
        }

        this.syncInProgress = true;
        this.showSyncStatus('🔄 جاري المزامنة الكاملة...', 'info');

        try {
            // First upload local changes
            await this.syncToServer();
            
            // Then download server updates
            await this.syncFromServer();

            this.showSyncStatus('✅ تمت المزامنة الكاملة بنجاح', 'success');
            return true;
        } catch (error) {
            this.showSyncStatus('❌ فشل في المزامنة الكاملة', 'error');
            return false;
        } finally {
            this.syncInProgress = false;
        }
    }

    /**
     * Auto sync (smart sync)
     * مزامنة تلقائية ذكية
     */
    async autoSync() {
        if (!this.isOnline || this.syncInProgress) {
            return;
        }

        // Check if local data has been modified since last sync
        const lastSync = localStorage.getItem('anwar_bakery_last_sync');
        const lastModified = localStorage.getItem('anwar_bakery_last_modified');

        if (!lastModified || (lastSync && new Date(lastModified) <= new Date(lastSync))) {
            // No local changes, just check for server updates
            await this.syncFromServer();
        } else {
            // Local changes exist, do full sync
            await this.fullSync();
        }
    }

    /**
     * Collect all localStorage data
     * جمع جميع البيانات المحلية
     */
    collectLocalData() {
        const data = {};
        const collections = [
            'products', 'customers', 'suppliers', 'employees',
            'invoices', 'vouchers', 'journal_entries', 'accounts',
            'cash_registers', 'banks', 'owners', 'branches', 'units'
        ];

        collections.forEach(collection => {
            const key = `anwar_bakery_${collection}`;
            const value = localStorage.getItem(key);
            if (value) {
                try {
                    data[collection] = JSON.parse(value);
                } catch (error) {
                    console.error(`Error parsing ${collection}:`, error);
                }
            }
        });

        return data;
    }

    /**
     * Update localStorage with server data
     * تحديث البيانات المحلية بالبيانات من الخادم
     */
    updateLocalData(serverData) {
        Object.keys(serverData).forEach(collection => {
            const key = `anwar_bakery_${collection}`;
            localStorage.setItem(key, JSON.stringify(serverData[collection]));
        });

        // Update last sync time
        this.updateLastSyncTime();
    }

    /**
     * Update last sync time
     * تحديث وقت آخر مزامنة
     */
    updateLastSyncTime() {
        const now = new Date().toISOString();
        localStorage.setItem('anwar_bakery_last_sync', now);
    }

    /**
     * Mark data as modified
     * تمييز البيانات كمعدلة
     */
    markDataModified() {
        const now = new Date().toISOString();
        localStorage.setItem('anwar_bakery_last_modified', now);
    }

    /**
     * Show sync status message
     * عرض رسالة حالة المزامنة
     */
    showSyncStatus(message, type = 'info') {
        // Create or update sync status element
        let statusElement = document.getElementById('sync-status');
        
        if (!statusElement) {
            statusElement = document.createElement('div');
            statusElement.id = 'sync-status';
            statusElement.className = 'fixed top-4 left-4 z-50 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300';
            document.body.appendChild(statusElement);
        }

        // Set message and style based on type
        statusElement.textContent = message;
        statusElement.className = 'fixed top-4 left-4 z-50 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300';

        switch (type) {
            case 'success':
                statusElement.className += ' bg-green-100 text-green-800 border border-green-200';
                break;
            case 'error':
                statusElement.className += ' bg-red-100 text-red-800 border border-red-200';
                break;
            case 'warning':
                statusElement.className += ' bg-yellow-100 text-yellow-800 border border-yellow-200';
                break;
            default:
                statusElement.className += ' bg-blue-100 text-blue-800 border border-blue-200';
        }

        // Auto hide after 3 seconds for success/info messages
        if (type === 'success' || type === 'info') {
            setTimeout(() => {
                if (statusElement) {
                    statusElement.style.opacity = '0';
                    setTimeout(() => {
                        if (statusElement && statusElement.parentNode) {
                            statusElement.parentNode.removeChild(statusElement);
                        }
                    }, 300);
                }
            }, 3000);
        }
    }

    /**
     * Get sync statistics
     * الحصول على إحصائيات المزامنة
     */
    getSyncStats() {
        const lastSync = localStorage.getItem('anwar_bakery_last_sync');
        const lastModified = localStorage.getItem('anwar_bakery_last_modified');
        
        return {
            lastSync: lastSync ? new Date(lastSync) : null,
            lastModified: lastModified ? new Date(lastModified) : null,
            isOnline: this.isOnline,
            syncInProgress: this.syncInProgress,
            autoSyncEnabled: this.autoSyncEnabled
        };
    }

    /**
     * Enable/disable auto sync
     * تفعيل/إلغاء المزامنة التلقائية
     */
    setAutoSync(enabled) {
        this.autoSyncEnabled = enabled;
        localStorage.setItem('anwar_bakery_auto_sync', enabled.toString());
        
        if (enabled && this.isOnline) {
            this.showSyncStatus('🔄 تم تفعيل المزامنة التلقائية', 'success');
        } else {
            this.showSyncStatus('⏸️ تم إيقاف المزامنة التلقائية', 'info');
        }
    }
}

// Create global sync client instance
window.syncClient = new SyncClient();

// Override localStorage setItem to mark data as modified
const originalSetItem = localStorage.setItem;
localStorage.setItem = function(key, value) {
    originalSetItem.call(this, key, value);
    
    // Mark as modified if it's anwar_bakery data
    if (key.startsWith('anwar_bakery_') && key !== 'anwar_bakery_last_sync' && key !== 'anwar_bakery_last_modified') {
        window.syncClient.markDataModified();
    }
};

// Export functions for global use
window.syncToServer = () => window.syncClient.syncToServer();
window.syncFromServer = () => window.syncClient.syncFromServer();
window.fullSync = () => window.syncClient.fullSync();
window.checkConnection = () => window.syncClient.checkConnection();
window.getSyncStats = () => window.syncClient.getSyncStats();
