// Inventory Management System
let inventoryItems = [];
let branches = [];
let warehouses = [];

// Check authentication
function checkAuth() {
    const session = localStorage.getItem('anwar_bakery_session') ||
                   sessionStorage.getItem('anwar_bakery_session');

    if (!session) {
        window.location.href = 'login.html';
        return null;
    }

    return JSON.parse(session);
}

// Load user info
function loadUserInfo() {
    const session = checkAuth();
    if (session) {
        document.getElementById('userFullName').textContent = session.fullName;
        document.getElementById('userDisplayName').textContent = session.fullName;
        document.getElementById('userRole').textContent = session.role;

        const loginTime = new Date(session.loginTime);
        document.getElementById('loginTime').textContent =
            'آخر دخول: ' + loginTime.toLocaleString('ar-SA');
    }
}

// Load company info
function loadCompanyInfo() {
    const savedData = localStorage.getItem('anwar_bakery_company');
    if (savedData) {
        const companyData = JSON.parse(savedData);
        if (companyData.companyNameAr) {
            document.getElementById('sidebarCompanyName').textContent = companyData.companyNameAr;
            document.title = `إدارة المخزون - ${companyData.companyNameAr}`;
        }
    }
}

// Load branches and warehouses
function loadBranchesAndWarehouses() {
    // Load branches
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    if (savedBranches) {
        branches = JSON.parse(savedBranches);
    }

    // Load warehouses
    const savedWarehouses = localStorage.getItem('anwar_bakery_warehouses');
    if (savedWarehouses) {
        warehouses = JSON.parse(savedWarehouses);
    }

    // Populate filter dropdowns
    populateFilters();
}

// Populate filter dropdowns
function populateFilters() {
    // Populate branch filter
    const branchFilter = document.getElementById('branchFilter');
    branchFilter.innerHTML = '<option value="">جميع الفروع</option>';
    branches.forEach(branch => {
        if (branch.isActive) {
            const option = document.createElement('option');
            option.value = branch.id;
            option.textContent = branch.branchName;
            branchFilter.appendChild(option);
        }
    });

    // Populate warehouse filter
    const warehouseFilter = document.getElementById('warehouseFilter');
    warehouseFilter.innerHTML = '<option value="">جميع المخازن</option>';
    warehouses.forEach(warehouse => {
        if (warehouse.isActive) {
            const option = document.createElement('option');
            option.value = warehouse.id;
            option.textContent = warehouse.warehouseName;
            warehouseFilter.appendChild(option);
        }
    });
}

// Load inventory from products
function loadInventory() {
    const savedProducts = localStorage.getItem('anwar_bakery_items');
    if (savedProducts) {
        const products = JSON.parse(savedProducts);
        
        // Convert products to inventory items
        inventoryItems = products.filter(product => product.itemType !== 'service').map(product => {
            return {
                id: product.id,
                itemCode: product.itemCode,
                itemName: product.itemName,
                itemType: product.itemType,
                branchId: product.branchId,
                branchName: getBranchName(product.branchId),
                warehouseId: product.warehouseId,
                warehouseName: getWarehouseName(product.warehouseId),
                currentStock: product.currentStock || 0,
                minStock: product.minStock || 0,
                maxStock: product.maxStock || 0,
                costPrice: product.costPrice || 0,
                inventoryValue: (product.currentStock || 0) * (product.costPrice || 0),
                unit: product.unit || 'وحدة',
                isActive: product.isActive,
                lastUpdated: product.lastUpdated || new Date().toISOString()
            };
        });
    }

    renderInventory();
    updateStats();
}

// Get branch name by ID
function getBranchName(branchId) {
    const branch = branches.find(b => b.id == branchId);
    return branch ? branch.branchName : 'غير محدد';
}

// Get warehouse name by ID
function getWarehouseName(warehouseId) {
    const warehouse = warehouses.find(w => w.id == warehouseId);
    return warehouse ? warehouse.warehouseName : 'غير محدد';
}

// Update statistics
function updateStats() {
    const totalItems = inventoryItems.length;
    const availableItems = inventoryItems.filter(item => item.currentStock > item.minStock).length;
    const lowStockItems = inventoryItems.filter(item => item.currentStock <= item.minStock && item.currentStock > 0).length;
    const outOfStockItems = inventoryItems.filter(item => item.currentStock <= 0).length;
    const totalValue = inventoryItems.reduce((sum, item) => sum + item.inventoryValue, 0);

    document.getElementById('totalItems').textContent = totalItems;
    document.getElementById('availableItems').textContent = availableItems;
    document.getElementById('lowStockItems').textContent = lowStockItems + outOfStockItems;
    document.getElementById('inventoryValue').textContent = totalValue.toFixed(2);
}

// Render inventory table
function renderInventory(filteredItems = inventoryItems) {
    const tbody = document.getElementById('inventoryTableBody');
    tbody.innerHTML = '';

    filteredItems.forEach(item => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';

        // Determine stock status
        let stockStatus = 'available';
        let stockClass = 'bg-green-100 text-green-800';
        let stockText = 'متوفر';

        if (item.currentStock <= 0) {
            stockStatus = 'out';
            stockClass = 'bg-red-100 text-red-800';
            stockText = 'نفد المخزون';
        } else if (item.currentStock <= item.minStock) {
            stockStatus = 'low';
            stockClass = 'bg-yellow-100 text-yellow-800';
            stockText = 'مخزون منخفض';
        }

        const typeNames = {
            'raw_material': 'خامة',
            'finished_product': 'منتج نهائي',
            'semi_finished': 'نصف مصنع'
        };

        row.innerHTML = `
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">${item.itemCode}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <div class="font-medium">${item.itemName}</div>
                <div class="text-xs text-gray-500">${typeNames[item.itemType] || 'غير محدد'}</div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">${item.branchName}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">${item.warehouseName}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 font-semibold ${item.currentStock <= item.minStock ? 'text-red-600' : 'text-green-600'}">
                ${item.currentStock} ${item.unit}
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">${item.minStock} ${item.unit}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">${item.maxStock} ${item.unit}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 font-semibold text-purple-600">
                ${item.inventoryValue.toFixed(2)} ر.س
            </td>
            <td class="px-4 py-3 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full ${stockClass}">
                    ${stockText}
                </span>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-center">
                <div class="flex justify-center space-x-1">
                    <button onclick="viewItemHistory(${item.id})" class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50" title="تاريخ الحركة">
                        📋
                    </button>
                    <button onclick="adjustStock(${item.id})" class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50" title="تعديل المخزون">
                        ⚖️
                    </button>
                    <button onclick="transferStock(${item.id})" class="text-purple-600 hover:text-purple-900 p-1 rounded hover:bg-purple-50" title="نقل مخزون">
                        🔄
                    </button>
                    <button onclick="generateBarcode(${item.id})" class="text-orange-600 hover:text-orange-900 p-1 rounded hover:bg-orange-50" title="طباعة باركود">
                        📊
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Filter inventory
function filterInventory() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const branchFilter = document.getElementById('branchFilter').value;
    const warehouseFilter = document.getElementById('warehouseFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;

    const filtered = inventoryItems.filter(item => {
        const matchesSearch = item.itemName.toLowerCase().includes(searchTerm) ||
                            item.itemCode.toLowerCase().includes(searchTerm);
        
        const matchesBranch = !branchFilter || item.branchId == branchFilter;
        const matchesWarehouse = !warehouseFilter || item.warehouseId == warehouseFilter;
        const matchesType = !typeFilter || item.itemType === typeFilter;

        let matchesStatus = true;
        if (statusFilter === 'available') {
            matchesStatus = item.currentStock > item.minStock;
        } else if (statusFilter === 'low') {
            matchesStatus = item.currentStock <= item.minStock && item.currentStock > 0;
        } else if (statusFilter === 'out') {
            matchesStatus = item.currentStock <= 0;
        }

        return matchesSearch && matchesBranch && matchesWarehouse && matchesType && matchesStatus;
    });

    renderInventory(filtered);
}

// Refresh inventory
function refreshInventory() {
    loadInventory();
    showMessage('تم تحديث المخزون بنجاح!', 'success');
}

// Export inventory report
function exportInventoryReport() {
    // This would typically generate an Excel file
    showMessage('سيتم تصدير تقرير المخزون قريباً...', 'info');
}

// View item history (placeholder)
function viewItemHistory(itemId) {
    const item = inventoryItems.find(i => i.id === itemId);
    if (item) {
        alert(`تاريخ حركة الصنف: ${item.itemName}\n\nهذه الميزة قيد التطوير...`);
    }
}

// Adjust stock (placeholder)
function adjustStock(itemId) {
    const item = inventoryItems.find(i => i.id === itemId);
    if (item) {
        const newStock = prompt(`تعديل مخزون: ${item.itemName}\nالكمية الحالية: ${item.currentStock}\n\nأدخل الكمية الجديدة:`);
        if (newStock !== null && !isNaN(newStock)) {
            item.currentStock = parseFloat(newStock);
            item.inventoryValue = item.currentStock * item.costPrice;
            item.lastUpdated = new Date().toISOString();
            
            // Update in products as well
            updateProductStock(item.id, item.currentStock);
            
            renderInventory();
            updateStats();
            showMessage('تم تعديل المخزون بنجاح!', 'success');
        }
    }
}

// Update product stock in localStorage
function updateProductStock(itemId, newStock) {
    const savedProducts = localStorage.getItem('anwar_bakery_items');
    if (savedProducts) {
        const products = JSON.parse(savedProducts);
        const productIndex = products.findIndex(p => p.id === itemId);
        if (productIndex !== -1) {
            products[productIndex].currentStock = newStock;
            products[productIndex].lastUpdated = new Date().toISOString();
            localStorage.setItem('anwar_bakery_items', JSON.stringify(products));
        }
    }
}

// Transfer stock (placeholder)
function transferStock(itemId) {
    const item = inventoryItems.find(i => i.id === itemId);
    if (item) {
        alert(`نقل مخزون: ${item.itemName}\n\nهذه الميزة قيد التطوير...`);
    }
}

// Generate barcode (placeholder)
function generateBarcode(itemId) {
    const item = inventoryItems.find(i => i.id === itemId);
    if (item) {
        alert(`طباعة باركود: ${item.itemName}\nكود الصنف: ${item.itemCode}\n\nهذه الميزة قيد التطوير...`);
    }
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Update current date time
function updateDateTime() {
    const now = new Date();
    const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    document.getElementById('currentDateTime').textContent =
        now.toLocaleDateString('ar-SA', options);
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (sidebar.classList.contains('translate-x-full')) {
        sidebar.classList.remove('translate-x-full');
        overlay.classList.remove('hidden');
    } else {
        sidebar.classList.add('translate-x-full');
        overlay.classList.add('hidden');
    }
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_session');
        sessionStorage.removeItem('anwar_bakery_session');
        window.location.href = 'login.html';
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    checkAuth();
    loadUserInfo();
    loadCompanyInfo();
    loadBranchesAndWarehouses();
    loadInventory();
    updateDateTime();
    setInterval(updateDateTime, 60000);
});

// Close sidebar when clicking overlay
document.getElementById('mobileOverlay').addEventListener('click', toggleSidebar);
