// Global Advanced Features - الميزات المتقدمة الشاملة
// This file enables advanced printing and Excel export across all pages

// Global initialization for advanced features
document.addEventListener('DOMContentLoaded', function() {
    initializeAdvancedFeatures();
});

/**
 * Initialize advanced features globally
 * تهيئة الميزات المتقدمة عالمياً
 */
function initializeAdvancedFeatures() {
    // Add advanced buttons to existing pages
    addAdvancedButtonsToPages();
    
    // Initialize keyboard shortcuts
    initializeKeyboardShortcuts();
    
    // Add global CSS for advanced features
    addAdvancedCSS();
    
    console.log('🚀 Advanced features initialized globally');
}

/**
 * Add advanced buttons to existing pages
 * إضافة أزرار متقدمة للصفحات الموجودة
 */
function addAdvancedButtonsToPages() {
    const currentPage = getCurrentPageType();
    
    switch (currentPage) {
        case 'customers':
            addAdvancedButtonsToCustomers();
            break;
        case 'suppliers':
            addAdvancedButtonsToSuppliers();
            break;
        case 'employees':
            addAdvancedButtonsToEmployees();
            break;
        case 'invoices':
            addAdvancedButtonsToInvoices();
            break;
        case 'vouchers':
            addAdvancedButtonsToVouchers();
            break;
        case 'journal':
            addAdvancedButtonsToJournal();
            break;
        case 'accounts':
            addAdvancedButtonsToAccounts();
            break;
    }
}

/**
 * Get current page type
 * الحصول على نوع الصفحة الحالية
 */
function getCurrentPageType() {
    const path = window.location.pathname;
    const filename = path.split('/').pop().split('.')[0];
    
    if (filename.includes('customer')) return 'customers';
    if (filename.includes('supplier')) return 'suppliers';
    if (filename.includes('employee')) return 'employees';
    if (filename.includes('invoice')) return 'invoices';
    if (filename.includes('voucher')) return 'vouchers';
    if (filename.includes('journal')) return 'journal';
    if (filename.includes('account')) return 'accounts';
    
    return 'unknown';
}

/**
 * Add advanced buttons to customers page
 * إضافة أزرار متقدمة لصفحة العملاء
 */
function addAdvancedButtonsToCustomers() {
    const headerDiv = document.querySelector('.flex.justify-between.items-center');
    if (headerDiv && !document.getElementById('advancedCustomersButtons')) {
        const buttonsContainer = headerDiv.querySelector('.flex.space-x-2') || 
                                headerDiv.querySelector('button').parentNode;
        
        const advancedButtons = document.createElement('div');
        advancedButtons.id = 'advancedCustomersButtons';
        advancedButtons.className = 'flex space-x-2 ml-2';
        advancedButtons.innerHTML = `
            <button onclick="exportCustomersToExcel()" class="bg-emerald-600 text-white px-3 py-2 rounded-lg hover:bg-emerald-700 flex items-center text-sm">
                <span class="ml-1">📊</span>
                تصدير Excel
            </button>
            <button onclick="printCustomersList()" class="bg-indigo-600 text-white px-3 py-2 rounded-lg hover:bg-indigo-700 flex items-center text-sm">
                <span class="ml-1">🖨️</span>
                طباعة
            </button>
        `;
        
        buttonsContainer.insertBefore(advancedButtons, buttonsContainer.firstChild);
    }
}

/**
 * Add advanced buttons to suppliers page
 * إضافة أزرار متقدمة لصفحة الموردين
 */
function addAdvancedButtonsToSuppliers() {
    const headerDiv = document.querySelector('.flex.justify-between.items-center');
    if (headerDiv && !document.getElementById('advancedSuppliersButtons')) {
        const buttonsContainer = headerDiv.querySelector('.flex.space-x-2') || 
                                headerDiv.querySelector('button').parentNode;
        
        const advancedButtons = document.createElement('div');
        advancedButtons.id = 'advancedSuppliersButtons';
        advancedButtons.className = 'flex space-x-2 ml-2';
        advancedButtons.innerHTML = `
            <button onclick="exportSuppliersToExcel()" class="bg-emerald-600 text-white px-3 py-2 rounded-lg hover:bg-emerald-700 flex items-center text-sm">
                <span class="ml-1">📊</span>
                تصدير Excel
            </button>
            <button onclick="printSuppliersList()" class="bg-indigo-600 text-white px-3 py-2 rounded-lg hover:bg-indigo-700 flex items-center text-sm">
                <span class="ml-1">🖨️</span>
                طباعة
            </button>
        `;
        
        buttonsContainer.insertBefore(advancedButtons, buttonsContainer.firstChild);
    }
}

/**
 * Add advanced buttons to employees page
 * إضافة أزرار متقدمة لصفحة الموظفين
 */
function addAdvancedButtonsToEmployees() {
    const headerDiv = document.querySelector('.flex.justify-between.items-center');
    if (headerDiv && !document.getElementById('advancedEmployeesButtons')) {
        const buttonsContainer = headerDiv.querySelector('.flex.space-x-2') || 
                                headerDiv.querySelector('button').parentNode;
        
        const advancedButtons = document.createElement('div');
        advancedButtons.id = 'advancedEmployeesButtons';
        advancedButtons.className = 'flex space-x-2 ml-2';
        advancedButtons.innerHTML = `
            <button onclick="exportEmployeesToExcel()" class="bg-emerald-600 text-white px-3 py-2 rounded-lg hover:bg-emerald-700 flex items-center text-sm">
                <span class="ml-1">📊</span>
                تصدير Excel
            </button>
            <button onclick="printEmployeesList()" class="bg-indigo-600 text-white px-3 py-2 rounded-lg hover:bg-indigo-700 flex items-center text-sm">
                <span class="ml-1">🖨️</span>
                طباعة
            </button>
        `;
        
        buttonsContainer.insertBefore(advancedButtons, buttonsContainer.firstChild);
    }
}

/**
 * Initialize keyboard shortcuts
 * تهيئة اختصارات لوحة المفاتيح
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+P for printing
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            const currentPage = getCurrentPageType();
            switch (currentPage) {
                case 'customers':
                    if (typeof printCustomersList === 'function') printCustomersList();
                    break;
                case 'suppliers':
                    if (typeof printSuppliersList === 'function') printSuppliersList();
                    break;
                case 'employees':
                    if (typeof printEmployeesList === 'function') printEmployeesList();
                    break;
                case 'invoices':
                    if (typeof printInvoice === 'function') printInvoice();
                    break;
                default:
                    window.print();
            }
        }
        
        // Ctrl+E for Excel export
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            const currentPage = getCurrentPageType();
            switch (currentPage) {
                case 'customers':
                    if (typeof exportCustomersToExcel === 'function') exportCustomersToExcel();
                    break;
                case 'suppliers':
                    if (typeof exportSuppliersToExcel === 'function') exportSuppliersToExcel();
                    break;
                case 'employees':
                    if (typeof exportEmployeesToExcel === 'function') exportEmployeesToExcel();
                    break;
                case 'invoices':
                    if (typeof exportToExcel === 'function') exportToExcel();
                    break;
            }
        }
    });
}

/**
 * Add advanced CSS styles
 * إضافة أنماط CSS متقدمة
 */
function addAdvancedCSS() {
    const style = document.createElement('style');
    style.textContent = `
        .advanced-button {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .advanced-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .advanced-button:active {
            transform: translateY(0);
        }
        
        .print-preview {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .print-preview-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 90%;
            max-height: 90%;
            overflow: auto;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            .print-only {
                display: block !important;
            }
        }
    `;
    document.head.appendChild(style);
}

// Global Export Functions
// دوال التصدير الشاملة

/**
 * Export customers to Excel
 * تصدير العملاء إلى Excel
 */
function exportCustomersToExcel() {
    const customers = JSON.parse(localStorage.getItem('anwar_bakery_customers') || '[]');
    
    if (typeof window.advancedExcel !== 'undefined') {
        const reportData = {
            main: customers.map(customer => ({
                'كود العميل': customer.code || '',
                'اسم العميل': customer.name,
                'رقم الهاتف': customer.phone || '',
                'البريد الإلكتروني': customer.email || '',
                'العنوان': customer.address || '',
                'الرصيد الحالي': customer.balance || 0,
                'الحالة': customer.isActive ? 'نشط' : 'غير نشط'
            })),
            summary: {
                totalCustomers: customers.length,
                activeCustomers: customers.filter(c => c.isActive).length,
                totalBalance: customers.reduce((sum, c) => sum + (c.balance || 0), 0)
            }
        };
        
        window.advancedExcel.exportFinancialReport(reportData, 'customers_report', { date: new Date().toISOString().split('T')[0] });
    } else {
        const basicData = customers.map(customer => ({
            'اسم العميل': customer.name,
            'رقم الهاتف': customer.phone || '',
            'الرصيد': customer.balance || 0
        }));
        
        if (typeof window.excelUtils !== 'undefined') {
            window.excelUtils.exportToExcel(basicData, 'العملاء', 'قائمة العملاء');
        }
    }
    
    showMessage('✅ تم تصدير قائمة العملاء بنجاح!', 'success');
}

/**
 * Print customers list
 * طباعة قائمة العملاء
 */
function printCustomersList() {
    const customers = JSON.parse(localStorage.getItem('anwar_bakery_customers') || '[]');
    
    if (typeof window.advancedPrint !== 'undefined') {
        const customersData = {
            number: 'CUST-LIST-' + Date.now(),
            date: new Date().toISOString().split('T')[0],
            type: 'customers_list',
            items: customers.filter(c => c.isActive).map(customer => ({
                name: customer.name,
                quantity: 1,
                unit: 'عميل',
                price: customer.balance || 0,
                total: customer.balance || 0
            })),
            subtotal: customers.reduce((sum, c) => sum + (c.balance || 0), 0),
            tax: 0,
            total: customers.reduce((sum, c) => sum + (c.balance || 0), 0),
            notes: 'قائمة العملاء النشطين'
        };
        
        window.advancedPrint.printInvoice(customersData, {
            showLogo: true,
            showHeader: true,
            showFooter: true,
            copies: 1,
            paperSize: 'A4'
        });
    } else {
        window.print();
    }
    
    showMessage('✅ تم إرسال قائمة العملاء للطباعة!', 'success');
}

/**
 * Export suppliers to Excel
 * تصدير الموردين إلى Excel
 */
function exportSuppliersToExcel() {
    const suppliers = JSON.parse(localStorage.getItem('anwar_bakery_suppliers') || '[]');
    
    const basicData = suppliers.map(supplier => ({
        'اسم المورد': supplier.name,
        'رقم الهاتف': supplier.phone || '',
        'البريد الإلكتروني': supplier.email || '',
        'الرصيد': supplier.balance || 0,
        'الحالة': supplier.isActive ? 'نشط' : 'غير نشط'
    }));
    
    if (typeof window.excelUtils !== 'undefined') {
        window.excelUtils.exportToExcel(basicData, 'الموردين', 'قائمة الموردين');
    }
    
    showMessage('✅ تم تصدير قائمة الموردين بنجاح!', 'success');
}

/**
 * Print suppliers list
 * طباعة قائمة الموردين
 */
function printSuppliersList() {
    window.print();
    showMessage('✅ تم إرسال قائمة الموردين للطباعة!', 'success');
}

/**
 * Export employees to Excel
 * تصدير الموظفين إلى Excel
 */
function exportEmployeesToExcel() {
    const employees = JSON.parse(localStorage.getItem('anwar_bakery_employees') || '[]');
    
    const basicData = employees.map(employee => ({
        'اسم الموظف': employee.name,
        'المنصب': employee.position || '',
        'رقم الهاتف': employee.phone || '',
        'الراتب': employee.salary || 0,
        'الحالة': employee.isActive ? 'نشط' : 'غير نشط'
    }));
    
    if (typeof window.excelUtils !== 'undefined') {
        window.excelUtils.exportToExcel(basicData, 'الموظفين', 'قائمة الموظفين');
    }
    
    showMessage('✅ تم تصدير قائمة الموظفين بنجاح!', 'success');
}

/**
 * Print employees list
 * طباعة قائمة الموظفين
 */
function printEmployeesList() {
    window.print();
    showMessage('✅ تم إرسال قائمة الموظفين للطباعة!', 'success');
}

/**
 * Show message helper function
 * دالة مساعدة لعرض الرسائل
 */
function showMessage(message, type = 'info') {
    // Try to use existing showMessage function, otherwise create our own
    if (typeof window.showMessage === 'function') {
        window.showMessage(message, type);
    } else {
        // Create simple message display
        const messageDiv = document.createElement('div');
        messageDiv.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white text-sm max-w-sm`;
        messageDiv.textContent = message;
        
        switch (type) {
            case 'success':
                messageDiv.className += ' bg-green-600';
                break;
            case 'error':
                messageDiv.className += ' bg-red-600';
                break;
            case 'warning':
                messageDiv.className += ' bg-yellow-600';
                break;
            default:
                messageDiv.className += ' bg-blue-600';
        }
        
        document.body.appendChild(messageDiv);
        
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 3000);
    }
}
