<?php
// api/get-warehouses.php
header('Content-Type: application/json');
require_once __DIR__ . '/../config.php';

$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(array('error' => 'Database connection failed'));
    exit;
}

$branchId = isset($_GET['branchId']) ? intval($_GET['branchId']) : 0;
$where = array();
if ($branchId > 0) {
    $where[] = "branchId = $branchId";
}
$whereSql = $where ? ('WHERE ' . implode(' AND ', $where)) : '';

$sql = "SELECT id, warehouseCode, warehouseName, branchId, type, capacity, area, unit, temperature, isActive, location, notes, createdAt FROM warehouses $whereSql ORDER BY warehouseName ASC";
$result = $conn->query($sql);

$warehouses = array();
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $row['isActive'] = (bool)$row['isActive'];
        $warehouses[] = $row;
    }
}

$conn->close();
echo json_encode(array('warehouses' => $warehouses));
