<?php
/**
 * Setup Units Table for Anwar Bakery Management System
 * إعداد جدول وحدات القياس لنظام إدارة مخبز أنوار الحي
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إعداد جدول وحدات القياس</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".step { margin: 15px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }";
echo ".success { background-color: #d4edda; color: #155724; border-color: #c3e6cb; }";
echo ".error { background-color: #f8d7da; color: #721c24; border-color: #f5c6cb; }";
echo ".info { background-color: #d1ecf1; color: #0c5460; border-color: #bee5eb; }";
echo ".warning { background-color: #fff3cd; color: #856404; border-color: #ffeaa7; }";
echo "pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 إعداد جدول وحدات القياس</h1>";

try {
    require_once 'api/config/database.php';
    $db = new Database();
    $conn = $db->getConnection();
    
    if (!$conn) {
        throw new Exception("فشل الاتصال بقاعدة البيانات");
    }
    
    echo "<div class='step success'>";
    echo "<h3>✅ الخطوة 1: اتصال قاعدة البيانات</h3>";
    echo "<p>تم الاتصال بقاعدة البيانات بنجاح</p>";
    echo "</div>";
    
    // Check if table exists
    echo "<div class='step'>";
    echo "<h3>🔍 الخطوة 2: فحص جدول units</h3>";
    
    $stmt = $conn->query("SHOW TABLES LIKE 'units'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "<div class='warning'>";
        echo "<p>⚠️ جدول units موجود مسبقاً</p>";
        
        // Check table structure
        $stmt = $conn->query("DESCRIBE units");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p><strong>هيكل الجدول الحالي:</strong></p>";
        echo "<pre>";
        foreach ($columns as $column) {
            echo $column['Field'] . " - " . $column['Type'] . "\n";
        }
        echo "</pre>";
        
        // Check if new columns exist
        $columnNames = array_column($columns, 'Field');
        $newColumns = ['large_unit_name', 'large_unit_count', 'small_unit_name', 'small_unit_count', 'category'];
        $missingColumns = array_diff($newColumns, $columnNames);
        
        if (!empty($missingColumns)) {
            echo "<p class='error'>❌ الأعمدة المفقودة: " . implode(', ', $missingColumns) . "</p>";
            echo "<p>سيتم إضافة الأعمدة المفقودة...</p>";
            
            // Add missing columns
            foreach ($missingColumns as $column) {
                try {
                    switch ($column) {
                        case 'large_unit_name':
                            $conn->exec("ALTER TABLE units ADD COLUMN large_unit_name VARCHAR(50) NOT NULL DEFAULT ''");
                            echo "<p class='success'>✅ تم إضافة عمود large_unit_name</p>";
                            break;
                        case 'large_unit_count':
                            $conn->exec("ALTER TABLE units ADD COLUMN large_unit_count DECIMAL(10,2) DEFAULT 1.00");
                            echo "<p class='success'>✅ تم إضافة عمود large_unit_count</p>";
                            break;
                        case 'small_unit_name':
                            $conn->exec("ALTER TABLE units ADD COLUMN small_unit_name VARCHAR(50) NOT NULL DEFAULT ''");
                            echo "<p class='success'>✅ تم إضافة عمود small_unit_name</p>";
                            break;
                        case 'small_unit_count':
                            $conn->exec("ALTER TABLE units ADD COLUMN small_unit_count DECIMAL(10,2) NOT NULL DEFAULT 1.00");
                            echo "<p class='success'>✅ تم إضافة عمود small_unit_count</p>";
                            break;
                        case 'category':
                            $conn->exec("ALTER TABLE units ADD COLUMN category ENUM('raw_materials', 'finished_products', 'packaging', 'general') DEFAULT 'general'");
                            echo "<p class='success'>✅ تم إضافة عمود category</p>";
                            break;
                    }
                } catch (Exception $e) {
                    echo "<p class='error'>❌ فشل إضافة عمود $column: " . $e->getMessage() . "</p>";
                }
            }
        } else {
            echo "<p class='success'>✅ جميع الأعمدة المطلوبة موجودة</p>";
        }
        
        echo "</div>";
    } else {
        echo "<p class='info'>ℹ️ جدول units غير موجود، سيتم إنشاؤه</p>";
        echo "</div>";
        
        // Create table
        echo "<div class='step'>";
        echo "<h3>🔨 الخطوة 3: إنشاء جدول units</h3>";
        
        $createTableSQL = "
        CREATE TABLE units (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            symbol VARCHAR(10) NOT NULL,
            type ENUM('weight', 'volume', 'length', 'piece', 'other') DEFAULT 'piece',
            category ENUM('raw_materials', 'finished_products', 'packaging', 'general') DEFAULT 'general',
            
            -- الوحدة الكبيرة
            large_unit_name VARCHAR(50) NOT NULL,
            large_unit_count DECIMAL(10,2) DEFAULT 1.00,
            
            -- الوحدة الصغيرة
            small_unit_name VARCHAR(50) NOT NULL,
            small_unit_count DECIMAL(10,2) NOT NULL,
            
            -- معامل التحويل (small_unit_count / large_unit_count)
            conversion_factor DECIMAL(10,4) GENERATED ALWAYS AS (small_unit_count / large_unit_count) STORED,
            
            -- الوحدة الأساسية للتحويل
            base_unit_id INT NULL,
            base_conversion_factor DECIMAL(10,4) DEFAULT 1.0000,
            
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            FOREIGN KEY (base_unit_id) REFERENCES units(id) ON DELETE SET NULL,
            INDEX idx_type (type),
            INDEX idx_category (category),
            INDEX idx_active (is_active),
            INDEX idx_name (name)
        )";
        
        try {
            $conn->exec($createTableSQL);
            echo "<p class='success'>✅ تم إنشاء جدول units بنجاح</p>";
        } catch (Exception $e) {
            echo "<p class='error'>❌ فشل إنشاء الجدول: " . $e->getMessage() . "</p>";
            throw $e;
        }
        
        echo "</div>";
    }
    
    // Insert sample data
    echo "<div class='step'>";
    echo "<h3>📊 الخطوة 4: إدراج البيانات الأساسية</h3>";
    
    // Check if we have any data
    $stmt = $conn->query("SELECT COUNT(*) as count FROM units");
    $result = $stmt->fetch();
    
    if ($result['count'] == 0) {
        echo "<p class='info'>ℹ️ لا توجد بيانات، سيتم إدراج البيانات الأساسية</p>";
        
        $sampleUnits = [
            ['كيس دقيق 50كج', 'كيس', 'weight', 'raw_materials', 'كيس', 1, 'جرام', 50000, 'كيس دقيق أبيض 50 كيلوجرام للمخبوزات'],
            ['كيلوجرام', 'كجم', 'weight', 'general', 'كيلوجرام', 1, 'جرام', 1000, 'وحدة الوزن الأساسية'],
            ['جرام', 'جم', 'weight', 'general', 'جرام', 1, 'جرام', 1, 'وحدة الوزن الصغيرة'],
            ['جالون زيت 20 لتر', 'جالون', 'volume', 'raw_materials', 'جالون', 1, 'مل', 20000, 'جالون زيت نباتي 20 لتر للقلي والطبخ'],
            ['لتر', 'لتر', 'volume', 'general', 'لتر', 1, 'مل', 1000, 'وحدة الحجم الأساسية'],
            ['صندوق خبز 50 رغيف', 'صندوق', 'piece', 'finished_products', 'صندوق', 1, 'رغيف', 50, 'صندوق خبز أبيض 50 رغيف'],
            ['قطعة', 'قطعة', 'piece', 'general', 'قطعة', 1, 'قطعة', 1, 'وحدة العدد الأساسية'],
            ['كرتونة 24 قطعة', 'كرتونة', 'piece', 'packaging', 'كرتونة', 1, 'قطعة', 24, 'كرتونة تحتوي على 24 قطعة']
        ];
        
        $stmt = $conn->prepare("
            INSERT INTO units (name, symbol, type, category, large_unit_name, large_unit_count, small_unit_name, small_unit_count, description) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $insertedCount = 0;
        foreach ($sampleUnits as $unit) {
            try {
                $stmt->execute($unit);
                $insertedCount++;
                echo "<p class='success'>✅ تم إدراج: " . $unit[0] . "</p>";
            } catch (Exception $e) {
                echo "<p class='error'>❌ فشل إدراج " . $unit[0] . ": " . $e->getMessage() . "</p>";
            }
        }
        
        echo "<p class='success'><strong>تم إدراج $insertedCount وحدة قياس</strong></p>";
    } else {
        echo "<p class='info'>ℹ️ يوجد " . $result['count'] . " وحدة قياس في الجدول</p>";
    }
    
    echo "</div>";
    
    // Final verification
    echo "<div class='step success'>";
    echo "<h3>✅ الخطوة 5: التحقق النهائي</h3>";
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM units WHERE is_active = 1");
    $result = $stmt->fetch();
    echo "<p>عدد وحدات القياس النشطة: " . $result['count'] . "</p>";
    
    $stmt = $conn->query("SELECT name, large_unit_name, small_unit_count, small_unit_name FROM units WHERE is_active = 1 LIMIT 5");
    $units = $stmt->fetchAll();
    
    echo "<p><strong>أمثلة على الوحدات:</strong></p>";
    echo "<ul>";
    foreach ($units as $unit) {
        echo "<li>" . $unit['name'] . ": " . $unit['large_unit_name'] . " = " . $unit['small_unit_count'] . " " . $unit['small_unit_name'] . "</li>";
    }
    echo "</ul>";
    
    echo "<p class='success'><strong>🎉 تم إعداد نظام وحدات القياس بنجاح!</strong></p>";
    echo "<p>يمكنك الآن:</p>";
    echo "<ul>";
    echo "<li>فتح صفحة units.html لإدارة وحدات القياس</li>";
    echo "<li>فتح test-units-debug.html لاختبار النظام</li>";
    echo "<li>فتح test-api-direct.php للتحقق من API</li>";
    echo "</ul>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='step error'>";
    echo "<h3>❌ خطأ</h3>";
    echo "<p>حدث خطأ أثناء الإعداد: " . $e->getMessage() . "</p>";
    echo "<p><strong>تأكد من:</strong></p>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL</li>";
    echo "<li>صحة إعدادات قاعدة البيانات في api/config/database.php</li>";
    echo "<li>وجود قاعدة البيانات anwar_bakery</li>";
    echo "<li>صلاحيات المستخدم للإنشاء والتعديل</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</body>";
echo "</html>";
?>
