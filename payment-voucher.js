// Payment Voucher Management System
let currentVoucher = {
    type: 'payment',
    journalEntries: []
};

let voucherCounter = 1;
let accounts = [];
let cashRegisters = [];
let banks = [];

// Load initial data
function loadInitialData() {
    loadUserInfo();
    loadCompanyInfo();
    generateVoucherNumber();
    setCurrentDate();
    loadBranches();
    loadAccounts();
    loadBanks();
}

// Generate voucher number
function generateVoucherNumber() {
    const savedCounter = localStorage.getItem('anwar_bakery_payment_counter');
    if (savedCounter) {
        voucherCounter = parseInt(savedCounter) + 1;
    }

    const voucherNumber = `PAY-${new Date().getFullYear()}-${voucherCounter.toString().padStart(3, '0')}`;
    document.getElementById('voucherNumber').value = voucherNumber;
}

// Set current date
function setCurrentDate() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('voucherDate').value = today;
}

// Load branches
function loadBranches() {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    const branchSelect = document.getElementById('branchId');

    branchSelect.innerHTML = '<option value="">اختر الفرع</option>';

    if (savedBranches) {
        const branches = JSON.parse(savedBranches);
        branches.filter(branch => branch.isActive).forEach(branch => {
            const option = document.createElement('option');
            option.value = branch.id;
            option.textContent = branch.branchName;
            branchSelect.appendChild(option);
        });
    }
}

// Load accounts from chart of accounts
function loadAccounts() {
    const savedAccounts = localStorage.getItem('anwar_bakery_accounts');
    if (savedAccounts) {
        accounts = JSON.parse(savedAccounts);

        // Populate account dropdown
        const accountSelect = document.getElementById('accountId');
        accountSelect.innerHTML = '<option value="">اختيار تلقائي</option>';

        accounts.forEach(account => {
            const option = document.createElement('option');
            option.value = account.id;
            option.textContent = `${account.name} (${account.code})`;
            option.dataset.code = account.code;
            option.dataset.type = account.type;
            accountSelect.appendChild(option);
        });
    }
}

// Load banks
function loadBanks() {
    // Create sample banks if not exist
    const savedBanks = localStorage.getItem('anwar_bakery_banks');
    if (savedBanks) {
        banks = JSON.parse(savedBanks);
    } else {
        banks = [
            { id: 1, bankName: 'البنك الأهلي السعودي', accountNumber: '*********', currentBalance: 50000, isActive: true },
            { id: 2, bankName: 'بنك الرياض', accountNumber: '*********', currentBalance: 75000, isActive: true },
            { id: 3, bankName: 'البنك السعودي للاستثمار', accountNumber: '*********', currentBalance: 30000, isActive: true },
            { id: 4, bankName: 'بنك ساب', accountNumber: '*********', currentBalance: 60000, isActive: true }
        ];
        localStorage.setItem('anwar_bakery_banks', JSON.stringify(banks));
    }

    // Populate bank dropdown
    const bankSelect = document.getElementById('bankId');
    bankSelect.innerHTML = '<option value="">اختر البنك</option>';

    banks.filter(bank => bank.isActive).forEach(bank => {
        const option = document.createElement('option');
        option.value = bank.id;
        option.textContent = `${bank.bankName} (${bank.accountNumber})`;
        option.dataset.balance = bank.currentBalance;
        bankSelect.appendChild(option);
    });
}

// Load branch data when branch is selected
function loadBranchData() {
    const branchId = document.getElementById('branchId').value;

    if (branchId) {
        loadCashRegisters(branchId);
        loadPayees(branchId);
    } else {
        clearDependentDropdowns();
    }
}

// Load cash registers for selected branch
function loadCashRegisters(branchId) {
    const savedCashRegisters = localStorage.getItem('anwar_bakery_cash_registers');
    const cashRegisterSelect = document.getElementById('cashRegisterId');

    cashRegisterSelect.innerHTML = '<option value="">اختر الصندوق</option>';

    if (savedCashRegisters) {
        cashRegisters = JSON.parse(savedCashRegisters);
        const branchCashRegisters = cashRegisters.filter(register =>
            register.branchId == branchId && register.isActive
        );

        branchCashRegisters.forEach(register => {
            const option = document.createElement('option');
            option.value = register.id;
            option.textContent = `${register.registerName}`;
            option.dataset.balance = register.currentBalance;
            option.dataset.accountId = register.accountId;

            // Mark default cash register
            if (register.isDefault) {
                option.selected = true;
                option.textContent += ' - افتراضي';
                // Show balance immediately for default
                showCashRegisterBalance(register.currentBalance);
            }

            cashRegisterSelect.appendChild(option);
        });
    }
}

// Toggle payment method fields
function togglePaymentMethod() {
    const paymentMethod = document.getElementById('paymentMethod').value;
    const cashRegisterField = document.getElementById('cashRegisterField');
    const bankField = document.getElementById('bankField');
    const referenceLabel = document.getElementById('referenceLabel');
    const referenceInput = document.getElementById('referenceNumber');

    if (paymentMethod === 'cash') {
        cashRegisterField.style.display = 'block';
        bankField.style.display = 'none';
        referenceLabel.textContent = 'رقم المرجع';
        referenceInput.placeholder = 'رقم مرجعي (اختياري)';
        referenceInput.required = false;
    } else {
        cashRegisterField.style.display = 'none';
        bankField.style.display = 'block';

        switch(paymentMethod) {
            case 'check':
                referenceLabel.textContent = 'رقم الشيك *';
                referenceInput.placeholder = 'رقم الشيك';
                referenceInput.required = true;
                break;
            case 'bank':
                referenceLabel.textContent = 'رقم التحويل *';
                referenceInput.placeholder = 'رقم التحويل البنكي';
                referenceInput.required = true;
                break;
            case 'card':
                referenceLabel.textContent = 'رقم العملية *';
                referenceInput.placeholder = 'رقم عملية البطاقة';
                referenceInput.required = true;
                break;
        }
    }

    checkBalance();
    updateJournalPreview();
}

// Toggle payee type (supplier/customer/employee/other)
function togglePayeeType() {
    const payeeType = document.querySelector('input[name="payeeType"]:checked').value;
    const payeeLabel = document.getElementById('payeeLabel');
    const branchId = document.getElementById('branchId').value;

    switch(payeeType) {
        case 'supplier':
            payeeLabel.textContent = 'المورد *';
            break;
        case 'customer':
            payeeLabel.textContent = 'العميل *';
            break;
        case 'employee':
            payeeLabel.textContent = 'الموظف *';
            break;
        case 'other':
            payeeLabel.textContent = 'الحساب *';
            break;
    }

    // Reload payees based on type and branch
    if (branchId) {
        loadPayees(branchId);
    }
}

// Load payees based on selected type and branch
function loadPayees(branchId) {
    const payeeType = document.querySelector('input[name="payeeType"]:checked').value;
    const payeeSelect = document.getElementById('payeeId');

    payeeSelect.innerHTML = '<option value="">اختر...</option>';

    if (payeeType === 'supplier') {
        loadSuppliers(branchId);
    } else if (payeeType === 'customer') {
        loadCustomers(branchId);
    } else if (payeeType === 'employee') {
        loadEmployees(branchId);
    } else if (payeeType === 'other') {
        loadOtherAccounts();
    }
}

// Load suppliers for the branch
function loadSuppliers(branchId) {
    const savedSuppliers = localStorage.getItem('anwar_bakery_suppliers');
    const payeeSelect = document.getElementById('payeeId');

    if (savedSuppliers) {
        const suppliers = JSON.parse(savedSuppliers);
        const branchSuppliers = suppliers.filter(supplier =>
            supplier.isActive && (
                !supplier.branchId ||
                supplier.branchId == branchId
            )
        );

        branchSuppliers.forEach(supplier => {
            const option = document.createElement('option');
            option.value = supplier.id;
            option.textContent = supplier.supplierName;
            option.dataset.type = 'supplier';
            option.dataset.accountId = supplier.accountId;
            payeeSelect.appendChild(option);
        });
    }
}

// Load customers for the branch
function loadCustomers(branchId) {
    const savedCustomers = localStorage.getItem('anwar_bakery_customers');
    const payeeSelect = document.getElementById('payeeId');

    if (savedCustomers) {
        const customers = JSON.parse(savedCustomers);
        const branchCustomers = customers.filter(customer =>
            customer.isActive && (
                !customer.branchId ||
                customer.branchId == branchId
            )
        );

        branchCustomers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.id;
            option.textContent = customer.customerName;
            option.dataset.type = 'customer';
            option.dataset.accountId = customer.accountId;
            payeeSelect.appendChild(option);
        });
    }
}

// Load employees for the branch
function loadEmployees(branchId) {
    const savedEmployees = localStorage.getItem('anwar_bakery_employees');
    const payeeSelect = document.getElementById('payeeId');

    if (savedEmployees) {
        const employees = JSON.parse(savedEmployees);
        const branchEmployees = employees.filter(employee =>
            employee.status === 'active' && employee.branchId == branchId
        );

        branchEmployees.forEach(employee => {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = `${employee.fullName} (${employee.employeeNumber})`;
            option.dataset.type = 'employee';
            // Create employee account if not exists
            option.dataset.accountId = getOrCreateEmployeeAccount(employee);
            payeeSelect.appendChild(option);
        });
    }
}

// Get or create employee account
function getOrCreateEmployeeAccount(employee) {
    // Look for existing employee account
    let employeeAccount = accounts.find(acc =>
        acc.code === `1140${employee.id.toString().padStart(3, '0')}`
    );

    if (!employeeAccount) {
        // Create new employee account
        employeeAccount = {
            id: Math.max(...accounts.map(acc => acc.id), 0) + 1,
            code: `1140${employee.id.toString().padStart(3, '0')}`,
            name: `سلف الموظفين - ${employee.fullName}`,
            type: 'asset',
            parentId: accounts.find(acc => acc.code === '1140')?.id || null,
            level: 2,
            balance: 0,
            debitBalance: 0,
            creditBalance: 0,
            isDefault: false,
            description: `حساب سلف الموظف ${employee.fullName}`,
            createdAt: new Date().toISOString()
        };

        accounts.push(employeeAccount);
        localStorage.setItem('anwar_bakery_accounts', JSON.stringify(accounts));
    }

    return employeeAccount.id;
}

// Load other accounts (for miscellaneous payments)
function loadOtherAccounts() {
    const payeeSelect = document.getElementById('payeeId');

    // Add common payment accounts
    const commonAccounts = accounts.filter(account =>
        account.type === 'expense' ||
        account.type === 'asset' ||
        account.code.startsWith('5') || // Expense accounts
        account.code.startsWith('1')    // Asset accounts
    );

    commonAccounts.forEach(account => {
        const option = document.createElement('option');
        option.value = account.id;
        option.textContent = `${account.name} (${account.code})`;
        option.dataset.type = 'account';
        option.dataset.accountId = account.id;
        payeeSelect.appendChild(option);
    });
}

// Load payee data when payee is selected
function loadPayeeData() {
    updateJournalPreview();
}

// Check balance before payment
function checkBalance() {
    const amount = parseFloat(document.getElementById('amount').value) || 0;
    const paymentMethod = document.getElementById('paymentMethod').value;

    if (amount <= 0) {
        hideBalanceWarning();
        return;
    }

    let currentBalance = 0;
    let sourceName = '';

    if (paymentMethod === 'cash') {
        const cashRegisterId = document.getElementById('cashRegisterId').value;
        if (cashRegisterId) {
            const cashRegister = cashRegisters.find(reg => reg.id == cashRegisterId);
            if (cashRegister) {
                currentBalance = cashRegister.currentBalance;
                sourceName = cashRegister.registerName;
                showCashRegisterBalance(currentBalance);
            }
        }
    } else {
        const bankId = document.getElementById('bankId').value;
        if (bankId) {
            const bank = banks.find(b => b.id == bankId);
            if (bank) {
                currentBalance = bank.currentBalance;
                sourceName = bank.bankName;
                showBankBalance(currentBalance);
            }
        }
    }

    if (currentBalance > 0) {
        if (amount > currentBalance) {
            showBalanceWarning(`تحذير: المبلغ المطلوب (${amount.toLocaleString()}) أكبر من الرصيد المتاح في ${sourceName} (${currentBalance.toLocaleString()})`);
        } else {
            hideBalanceWarning();
        }
    }
}

// Show cash register balance
function showCashRegisterBalance(balance) {
    const balanceDiv = document.getElementById('cashRegisterBalance');
    const currencySymbol = getCurrencySymbol();

    if (balance >= 0) {
        balanceDiv.innerHTML = `<span class="text-green-600">الرصيد: ${balance.toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}</span>`;
    } else {
        balanceDiv.innerHTML = `<span class="text-red-600">الرصيد: ${balance.toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}</span>`;
    }
}

// Show bank balance
function showBankBalance(balance) {
    const balanceDiv = document.getElementById('bankBalance');
    const currencySymbol = getCurrencySymbol();

    if (balance >= 0) {
        balanceDiv.innerHTML = `<span class="text-green-600">الرصيد: ${balance.toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}</span>`;
    } else {
        balanceDiv.innerHTML = `<span class="text-red-600">الرصيد: ${balance.toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}</span>`;
    }
}

// Show balance warning
function showBalanceWarning(message) {
    const warningDiv = document.getElementById('balanceWarning');
    const warningText = document.getElementById('balanceWarningText');

    warningText.textContent = message;
    warningDiv.style.display = 'block';
}

// Hide balance warning
function hideBalanceWarning() {
    const warningDiv = document.getElementById('balanceWarning');
    warningDiv.style.display = 'none';
}

// Clear dependent dropdowns
function clearDependentDropdowns() {
    document.getElementById('cashRegisterId').innerHTML = '<option value="">اختر الصندوق</option>';
    document.getElementById('payeeId').innerHTML = '<option value="">اختر...</option>';
    document.getElementById('cashRegisterBalance').innerHTML = '';
    document.getElementById('bankBalance').innerHTML = '';
    hideBalanceWarning();
    hideJournalPreview();
}

// Get currency symbol from settings
function getCurrencySymbol() {
    const savedSettings = localStorage.getItem('anwar_bakery_settings');
    if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        return settings.currency || '';
    }
    return '';
}

// Load user info
function loadUserInfo() {
    const savedUser = localStorage.getItem('anwar_bakery_current_user');
    if (savedUser) {
        const user = JSON.parse(savedUser);
        const userFullNameElement = document.getElementById('userFullName');
        const userRoleElement = document.getElementById('userRole');

        if (userFullNameElement) {
            userFullNameElement.textContent = user.fullName || 'أحمد محمد';
        }
        if (userRoleElement) {
            userRoleElement.textContent = user.role === 'admin' ? 'مدير النظام' : (user.role || 'مدير النظام');
        }
    }
}

// Load company info
function loadCompanyInfo() {
    const savedCompany = localStorage.getItem('anwar_bakery_company');
    if (savedCompany) {
        const company = JSON.parse(savedCompany);
        const companyNameElement = document.getElementById('companyName');
        const companySloganElement = document.getElementById('companySlogan');

        if (companyNameElement) {
            companyNameElement.textContent = company.companyName || 'مخبز أنوار الحي';
        }
        if (companySloganElement) {
            companySloganElement.textContent = company.slogan || 'جودة تستحق الثقة';
        }
    }
}

// Update journal entry preview
function updateJournalPreview() {
    const amount = parseFloat(document.getElementById('amount').value) || 0;
    const paymentMethod = document.getElementById('paymentMethod').value;
    const payeeId = document.getElementById('payeeId').value;
    const accountId = document.getElementById('accountId').value;

    if (amount <= 0) {
        hideJournalPreview();
        return;
    }

    // Get payment source account (cash register or bank)
    let sourceAccount = null;
    if (paymentMethod === 'cash') {
        const cashRegisterId = document.getElementById('cashRegisterId').value;
        if (cashRegisterId) {
            const cashRegister = cashRegisters.find(reg => reg.id == cashRegisterId);
            if (cashRegister) {
                sourceAccount = accounts.find(acc => acc.id == cashRegister.accountId);
            }
        }
    } else {
        const bankId = document.getElementById('bankId').value;
        if (bankId) {
            const bank = banks.find(b => b.id == bankId);
            if (bank) {
                // Find bank account in chart of accounts
                sourceAccount = accounts.find(acc => acc.name.includes(bank.bankName));
            }
        }
    }

    // Get payee account
    let payeeAccount = null;
    if (accountId) {
        // Manual account selection
        payeeAccount = accounts.find(acc => acc.id == accountId);
    } else if (payeeId) {
        // Auto-detect based on payee type
        const payeeSelect = document.getElementById('payeeId');
        const selectedOption = payeeSelect.options[payeeSelect.selectedIndex];
        if (selectedOption && selectedOption.dataset.accountId) {
            payeeAccount = accounts.find(acc => acc.id == selectedOption.dataset.accountId);
        }
    }

    if (!sourceAccount || !payeeAccount) {
        hideJournalPreview();
        return;
    }

    // Create journal entries (opposite of receipt voucher)
    const journalEntries = [
        {
            account: payeeAccount,
            debit: amount,
            credit: 0
        },
        {
            account: sourceAccount,
            debit: 0,
            credit: amount
        }
    ];

    renderJournalPreview(journalEntries);
}

// Render journal preview
function renderJournalPreview(entries) {
    const journalEntriesDiv = document.getElementById('journalEntries');
    const totalDebitSpan = document.getElementById('totalDebit');
    const totalCreditSpan = document.getElementById('totalCredit');
    const currencySymbol = getCurrencySymbol();

    let totalDebit = 0;
    let totalCredit = 0;

    journalEntriesDiv.innerHTML = entries.map(entry => {
        totalDebit += entry.debit;
        totalCredit += entry.credit;

        return `
            <div class="grid grid-cols-3 gap-4 text-xs py-1">
                <div class="text-gray-900">${entry.account.name} (${entry.account.code})</div>
                <div class="text-center ${entry.debit > 0 ? 'font-semibold text-green-600' : 'text-gray-400'}">
                    ${entry.debit > 0 ? entry.debit.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '') : '-'}
                </div>
                <div class="text-center ${entry.credit > 0 ? 'font-semibold text-red-600' : 'text-gray-400'}">
                    ${entry.credit > 0 ? entry.credit.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '') : '-'}
                </div>
            </div>
        `;
    }).join('');

    totalDebitSpan.textContent = totalDebit.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '');
    totalCreditSpan.textContent = totalCredit.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '');

    // Show preview
    const preview = document.getElementById('journalPreview');
    preview.classList.remove('hide');
    preview.classList.add('show');

    // Store entries for saving
    currentVoucher.journalEntries = entries;
}

// Hide journal preview
function hideJournalPreview() {
    const preview = document.getElementById('journalPreview');
    preview.classList.remove('show');
    preview.classList.add('hide');
    currentVoucher.journalEntries = [];
}

// Preview voucher
function previewVoucher() {
    if (validateVoucher()) {
        const voucherData = collectVoucherData();
        populatePrintTemplate(voucherData, 'regular');

        // Open preview in new window
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>معاينة سند الصرف</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${document.getElementById('regularPrintTemplate').innerHTML}
                    <div style="text-align: center; margin-top: 20px; page-break-inside: avoid;">
                        <button onclick="window.print()" style="padding: 10px 20px; font-size: 16px;">طباعة</button>
                        <button onclick="window.close()" style="padding: 10px 20px; font-size: 16px; margin-right: 10px;">إغلاق</button>
                    </div>
                </body>
            </html>
        `);
        printWindow.document.close();
    }
}

// Save draft
function saveDraft() {
    if (validateVoucher()) {
        const voucherData = collectVoucherData();
        voucherData.status = 'draft';
        saveVoucher(voucherData);
        showMessage('تم حفظ مسودة السند بنجاح', 'success');
    }
}

// Save and print thermal
function saveAndPrintThermal() {
    if (validateVoucher()) {
        const voucherData = collectVoucherData();
        voucherData.status = 'posted';
        saveVoucher(voucherData);

        populatePrintTemplate(voucherData, 'thermal');

        // Open thermal print
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>سند صرف - طباعة حرارية</title>
                    <style>
                        body { font-family: monospace; margin: 0; padding: 5px; }
                        @media print {
                            body { margin: 0; padding: 0; }
                            @page { size: 80mm auto; margin: 0; }
                        }
                    </style>
                </head>
                <body>
                    ${document.getElementById('thermalPrintTemplate').innerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();

        showMessage('تم حفظ السند وإرساله للطباعة الحرارية', 'success');

        // Redirect after short delay
        setTimeout(() => {
            window.location.href = 'vouchers.html';
        }, 2000);
    }
}

// Save and print regular
function saveAndPrintRegular() {
    if (validateVoucher()) {
        const voucherData = collectVoucherData();
        voucherData.status = 'posted';
        saveVoucher(voucherData);

        populatePrintTemplate(voucherData, 'regular');

        // Open regular print
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>سند صرف - طباعة عادية</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 0; padding: 0; }
                        @media print {
                            body { margin: 0; }
                            @page { size: A4; margin: 1cm; }
                        }
                    </style>
                </head>
                <body>
                    ${document.getElementById('regularPrintTemplate').innerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();

        showMessage('تم حفظ السند وإرساله للطباعة العادية', 'success');

        // Redirect after short delay
        setTimeout(() => {
            window.location.href = 'vouchers.html';
        }, 2000);
    }
}

// Validate voucher data
function validateVoucher() {
    const requiredFields = ['voucherDate', 'branchId', 'payeeId', 'amount', 'description'];
    let isValid = true;

    // Check payment method specific requirements
    const paymentMethod = document.getElementById('paymentMethod').value;
    if (paymentMethod === 'cash') {
        requiredFields.push('cashRegisterId');
    } else {
        requiredFields.push('bankId');
        if (paymentMethod !== 'cash') {
            requiredFields.push('referenceNumber');
        }
    }

    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (!field.value.trim()) {
            field.classList.add('required-field');
            isValid = false;
        } else {
            field.classList.remove('required-field');
        }
    });

    if (!isValid) {
        showMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
        return false;
    }

    if (currentVoucher.journalEntries.length === 0) {
        showMessage('لا يمكن حفظ السند بدون قيد محاسبي صحيح', 'error');
        return false;
    }

    // Check balance
    const amount = parseFloat(document.getElementById('amount').value);
    if (paymentMethod === 'cash') {
        const cashRegisterId = document.getElementById('cashRegisterId').value;
        const cashRegister = cashRegisters.find(reg => reg.id == cashRegisterId);
        if (cashRegister && amount > cashRegister.currentBalance) {
            showMessage('المبلغ المطلوب أكبر من رصيد الصندوق المتاح', 'error');
            return false;
        }
    } else {
        const bankId = document.getElementById('bankId').value;
        const bank = banks.find(b => b.id == bankId);
        if (bank && amount > bank.currentBalance) {
            showMessage('المبلغ المطلوب أكبر من رصيد البنك المتاح', 'error');
            return false;
        }
    }

    return true;
}

// Collect voucher data
function collectVoucherData() {
    return {
        id: Date.now(),
        voucherNumber: document.getElementById('voucherNumber').value,
        type: 'payment',
        date: document.getElementById('voucherDate').value,
        branchId: parseInt(document.getElementById('branchId').value),
        cashRegisterId: document.getElementById('cashRegisterId').value ? parseInt(document.getElementById('cashRegisterId').value) : null,
        bankId: document.getElementById('bankId').value ? parseInt(document.getElementById('bankId').value) : null,
        payeeId: document.getElementById('payeeId').value,
        payeeType: document.querySelector('input[name="payeeType"]:checked').value,
        amount: parseFloat(document.getElementById('amount').value),
        paymentMethod: document.getElementById('paymentMethod').value,
        referenceNumber: document.getElementById('referenceNumber').value,
        description: document.getElementById('description').value,
        journalEntries: currentVoucher.journalEntries,
        createdBy: 'current_user', // TODO: Get from session
        createdAt: new Date().toISOString()
    };
}

// Save voucher with full implementation
function saveVoucher(voucherData) {
    try {
        // Save to vouchers
        let vouchers = [];
        const savedVouchers = localStorage.getItem('anwar_bakery_vouchers');
        if (savedVouchers) {
            vouchers = JSON.parse(savedVouchers);
        }
        vouchers.push(voucherData);
        localStorage.setItem('anwar_bakery_vouchers', JSON.stringify(vouchers));

        // Update counter
        localStorage.setItem('anwar_bakery_payment_counter', voucherCounter.toString());

        // Update cash register/bank balance
        updateCashRegisterOrBankBalance(voucherData);

        // Update account balances
        updateAccountBalances(voucherData);

        // Create journal entry record
        createJournalEntry(voucherData);

        console.log('Payment voucher saved successfully:', voucherData);
        return { success: true };
    } catch (error) {
        console.error('Error saving voucher:', error);
        return { success: false, error: error.message };
    }
}

// Update cash register or bank balance
function updateCashRegisterOrBankBalance(voucherData) {
    try {
        if (voucherData.paymentMethod === 'cash' && voucherData.cashRegisterId) {
            // Update cash register
            const cashRegisters = JSON.parse(localStorage.getItem('anwar_bakery_cash_registers') || '[]');
            const registerIndex = cashRegisters.findIndex(reg => reg.id == voucherData.cashRegisterId);

            if (registerIndex !== -1) {
                // Subtract amount from cash register (payment = money out)
                cashRegisters[registerIndex].currentBalance -= voucherData.amount;
                cashRegisters[registerIndex].updatedAt = new Date().toISOString();

                localStorage.setItem('anwar_bakery_cash_registers', JSON.stringify(cashRegisters));
                console.log('Cash register balance updated');
            }
        } else if (voucherData.bankId) {
            // Update bank balance
            const banks = JSON.parse(localStorage.getItem('anwar_bakery_banks') || '[]');
            const bankIndex = banks.findIndex(bank => bank.id == voucherData.bankId);

            if (bankIndex !== -1) {
                // Subtract amount from bank (payment = money out)
                banks[bankIndex].currentBalance -= voucherData.amount;
                banks[bankIndex].updatedAt = new Date().toISOString();

                localStorage.setItem('anwar_bakery_banks', JSON.stringify(banks));
                console.log('Bank balance updated');
            }
        }
    } catch (error) {
        console.error('Error updating cash register/bank balance:', error);
    }
}

// Update account balances
function updateAccountBalances(voucherData) {
    try {
        const accounts = JSON.parse(localStorage.getItem('anwar_bakery_accounts') || '[]');

        voucherData.journalEntries.forEach(entry => {
            const accountIndex = accounts.findIndex(acc => acc.id == entry.account.id);
            if (accountIndex !== -1) {
                accounts[accountIndex].balance += (entry.debit - entry.credit);
                accounts[accountIndex].updatedAt = new Date().toISOString();
            }
        });

        localStorage.setItem('anwar_bakery_accounts', JSON.stringify(accounts));
        console.log('Account balances updated');
    } catch (error) {
        console.error('Error updating account balances:', error);
    }
}

// Create journal entry record
function createJournalEntry(voucherData) {
    try {
        const journalEntry = {
            id: Date.now(),
            date: voucherData.date,
            reference: voucherData.voucherNumber,
            description: voucherData.description,
            type: 'payment_voucher',
            entries: voucherData.journalEntries.map(entry => ({
                accountId: entry.account.id,
                accountName: entry.account.name,
                debit: entry.debit,
                credit: entry.credit
            })),
            createdBy: voucherData.createdBy,
            createdAt: voucherData.createdAt
        };

        const journalEntries = JSON.parse(localStorage.getItem('anwar_bakery_journal_entries') || '[]');
        journalEntries.push(journalEntry);
        localStorage.setItem('anwar_bakery_journal_entries', JSON.stringify(journalEntries));

        console.log('Journal entry created:', journalEntry);
    } catch (error) {
        console.error('Error creating journal entry:', error);
    }
}

// Populate print template
function populatePrintTemplate(voucherData, templateType) {
    const savedUser = localStorage.getItem('anwar_bakery_current_user');
    const savedCompany = localStorage.getItem('anwar_bakery_company');
    const savedBranches = localStorage.getItem('anwar_bakery_branches');

    let currentUser = { fullName: 'غير محدد' };
    let company = { companyName: 'مخبز أنوار الحي', slogan: 'جودة تستحق الثقة' };
    let branchName = 'غير محدد';

    if (savedUser) currentUser = JSON.parse(savedUser);
    if (savedCompany) company = JSON.parse(savedCompany);
    if (savedBranches) {
        const branches = JSON.parse(savedBranches);
        const branch = branches.find(b => b.id == voucherData.branchId);
        if (branch) branchName = branch.branchName;
    }

    const currencySymbol = getCurrencySymbol();
    const paymentMethodNames = {
        'cash': 'نقدي',
        'bank': 'تحويل بنكي',
        'check': 'شيك',
        'card': 'بطاقة ائتمان'
    };

    if (templateType === 'regular') {
        // Populate regular template
        document.getElementById('printCompanyName').textContent = company.companyName;
        document.getElementById('printCompanySlogan').textContent = company.slogan;
        document.getElementById('printVoucherNumber').textContent = voucherData.voucherNumber;
        document.getElementById('printDate').textContent = new Date(voucherData.date).toLocaleDateString('ar-SA');
        document.getElementById('printBranch').textContent = branchName;
        document.getElementById('printPayee').textContent = getPayeeName(voucherData);
        document.getElementById('printPaymentMethod').textContent = paymentMethodNames[voucherData.paymentMethod];
        document.getElementById('printAmount').textContent = voucherData.amount.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '');
        document.getElementById('printReference').textContent = voucherData.referenceNumber || '-';
        document.getElementById('printDescription').textContent = voucherData.description;
        document.getElementById('printCreatedBy').textContent = currentUser.fullName;
        document.getElementById('printCreatedAt').textContent = new Date().toLocaleString('ar-SA');

        // Populate journal entries
        const journalEntriesHtml = voucherData.journalEntries.map(entry => `
            <tr>
                <td style="padding: 8px; border: 1px solid #ddd;">${entry.account.name} (${entry.account.code})</td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: center; ${entry.debit > 0 ? 'font-weight: bold; color: #059669;' : ''}">${entry.debit > 0 ? entry.debit.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '') : '-'}</td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: center; ${entry.credit > 0 ? 'font-weight: bold; color: #dc2626;' : ''}">${entry.credit > 0 ? entry.credit.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '') : '-'}</td>
            </tr>
        `).join('');
        document.getElementById('printJournalEntries').innerHTML = journalEntriesHtml;

    } else if (templateType === 'thermal') {
        // Populate thermal template
        document.getElementById('thermalCompanyName').textContent = company.companyName;
        document.getElementById('thermalCompanySlogan').textContent = company.slogan;
        document.getElementById('thermalVoucherNumber').textContent = voucherData.voucherNumber;
        document.getElementById('thermalDate').textContent = new Date(voucherData.date).toLocaleDateString('ar-SA');
        document.getElementById('thermalBranch').textContent = branchName;
        document.getElementById('thermalPayee').textContent = getPayeeName(voucherData);
        document.getElementById('thermalAmount').textContent = voucherData.amount.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '');
        document.getElementById('thermalDescription').textContent = voucherData.description;
        document.getElementById('thermalPaymentMethod').textContent = paymentMethodNames[voucherData.paymentMethod];
        document.getElementById('thermalCreatedBy').textContent = currentUser.fullName;
        document.getElementById('thermalCreatedAt').textContent = new Date().toLocaleString('ar-SA');

        if (voucherData.referenceNumber) {
            document.getElementById('thermalReferenceDiv').style.display = 'block';
            document.getElementById('thermalReference').textContent = voucherData.referenceNumber;
        }
    }
}

// Get payee name for printing
function getPayeeName(voucherData) {
    const payeeSelect = document.getElementById('payeeId');
    const selectedOption = payeeSelect.options[payeeSelect.selectedIndex];
    return selectedOption ? selectedOption.textContent : 'غير محدد';
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    if (!container) return;

    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_current_user');
        window.location.href = 'login.html';
    }
}

// Load invoice data if coming from invoice
function loadInvoiceData() {
    const urlParams = new URLSearchParams(window.location.search);
    const fromInvoice = urlParams.get('from') === 'invoice';

    if (fromInvoice) {
        const voucherData = localStorage.getItem('anwar_bakery_voucher_data');
        if (voucherData) {
            const data = JSON.parse(voucherData);

            // Set form fields
            if (data.branchId) {
                document.getElementById('branchId').value = data.branchId;
                loadBranchData();
            }

            if (data.amount) {
                document.getElementById('amount').value = data.amount;
            }

            if (data.description) {
                document.getElementById('description').value = data.description;
            }

            if (data.date) {
                document.getElementById('voucherDate').value = data.date;
            }

            // Set customer as payee for returns
            if (data.customerId) {
                // Set payee type to customer for returns
                document.querySelector('input[name="payeeType"][value="customer"]').checked = true;
                togglePayeeType();

                setTimeout(() => {
                    const payeeSelect = document.getElementById('payeeId');
                    if (payeeSelect) {
                        payeeSelect.value = data.customerId;
                        updateJournalPreview();
                    }
                }, 1000);
            }

            // Store linked invoice data
            currentVoucher.linkedInvoiceId = data.linkedInvoiceId;
            currentVoucher.invoiceNumber = data.invoiceNumber;

            // Clear the stored data
            localStorage.removeItem('anwar_bakery_voucher_data');

            // Show success message
            showMessage(`تم تحضير سند الصرف للفاتورة رقم ${data.invoiceNumber}`, 'success');
        }
    }
}

// Update save voucher function to include linked invoice
function saveVoucher(voucherData) {
    // Add linked invoice data if available
    if (currentVoucher.linkedInvoiceId) {
        voucherData.linkedInvoiceId = currentVoucher.linkedInvoiceId;
        voucherData.invoiceNumber = currentVoucher.invoiceNumber;

        // Update invoice payment status for returns
        updateInvoicePayment(currentVoucher.linkedInvoiceId, voucherData.amount);
    }

    // Save to vouchers
    let vouchers = [];
    const savedVouchers = localStorage.getItem('anwar_bakery_vouchers');
    if (savedVouchers) {
        vouchers = JSON.parse(savedVouchers);
    }
    vouchers.push(voucherData);
    localStorage.setItem('anwar_bakery_vouchers', JSON.stringify(vouchers));

    // Update counter
    localStorage.setItem('anwar_bakery_payment_counter', voucherCounter.toString());

    // TODO: Update cash register/bank balance
    // TODO: Update account balances
    // TODO: Create journal entry record
}

// Update invoice payment for returns
function updateInvoicePayment(invoiceId, paidAmount) {
    const savedInvoices = localStorage.getItem('anwar_bakery_invoices');
    if (savedInvoices) {
        let invoices = JSON.parse(savedInvoices);
        const invoiceIndex = invoices.findIndex(inv => inv.id === invoiceId);

        if (invoiceIndex !== -1) {
            const invoice = invoices[invoiceIndex];

            // For return invoices, mark as paid when payment voucher is created
            if (invoice.type === 'sales_return') {
                invoice.paidAmount = invoice.totalAmount;
                invoice.status = 'paid';
            }

            // Save updated invoices
            localStorage.setItem('anwar_bakery_invoices', JSON.stringify(invoices));
        }
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadInitialData();

    // Load invoice data if coming from invoice
    setTimeout(() => {
        loadInvoiceData();
    }, 500);
});
