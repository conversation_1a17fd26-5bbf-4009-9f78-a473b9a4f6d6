<?php
/**
 * Direct API Test for Units System
 * اختبار مباشر لـ API وحدات القياس
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>اختبار API وحدات القياس</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".test { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }";
echo ".success { background-color: #d4edda; color: #155724; }";
echo ".error { background-color: #f8d7da; color: #721c24; }";
echo ".info { background-color: #d1ecf1; color: #0c5460; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 اختبار API وحدات القياس</h1>";

// Test 1: Database Connection
echo "<div class='test'>";
echo "<h3>1. اختبار اتصال قاعدة البيانات</h3>";

try {
    require_once 'api/config/database.php';
    $db = new Database();
    $conn = $db->getConnection();
    
    if ($conn) {
        echo "<div class='success'>✅ اتصال قاعدة البيانات نجح</div>";
        
        // Check if units table exists
        $stmt = $conn->query("SHOW TABLES LIKE 'units'");
        if ($stmt->rowCount() > 0) {
            echo "<div class='success'>✅ جدول units موجود</div>";
            
            // Check table structure
            $stmt = $conn->query("DESCRIBE units");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            echo "<div class='info'>📊 أعمدة الجدول: " . implode(', ', $columns) . "</div>";
            
            // Check if we have any units
            $stmt = $conn->query("SELECT COUNT(*) as count FROM units");
            $result = $stmt->fetch();
            echo "<div class='info'>📈 عدد الوحدات: " . $result['count'] . "</div>";
            
        } else {
            echo "<div class='error'>❌ جدول units غير موجود</div>";
        }
    } else {
        echo "<div class='error'>❌ فشل اتصال قاعدة البيانات</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 2: Units API
echo "<div class='test'>";
echo "<h3>2. اختبار Units API</h3>";

try {
    // Simulate API call
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['REQUEST_URI'] = '/api/units.php';
    
    ob_start();
    include 'api/units.php';
    $output = ob_get_clean();
    
    $result = json_decode($output, true);
    
    if ($result && isset($result['success'])) {
        if ($result['success']) {
            echo "<div class='success'>✅ API يعمل بشكل صحيح</div>";
            echo "<div class='info'>📊 عدد الوحدات المسترجعة: " . count($result['data']['units']) . "</div>";
            
            // Show first few units
            if (!empty($result['data']['units'])) {
                echo "<div class='info'>📋 أول 3 وحدات:</div>";
                foreach (array_slice($result['data']['units'], 0, 3) as $unit) {
                    echo "<div class='info'>• " . $unit['unitName'] . " (" . $unit['unitType'] . ")</div>";
                }
            }
        } else {
            echo "<div class='error'>❌ API أرجع خطأ: " . $result['error'] . "</div>";
        }
    } else {
        echo "<div class='error'>❌ استجابة API غير صحيحة</div>";
        echo "<div class='error'>الاستجابة: " . htmlspecialchars($output) . "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في API: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 3: Create Unit Test
echo "<div class='test'>";
echo "<h3>3. اختبار إنشاء وحدة جديدة</h3>";

try {
    $testUnit = [
        'unitName' => 'وحدة اختبار ' . date('H:i:s'),
        'unitType' => 'piece',
        'category' => 'general',
        'largeUnitName' => 'صندوق',
        'largeUnitCount' => 1,
        'smallUnitName' => 'قطعة',
        'smallUnitCount' => 24,
        'description' => 'وحدة اختبار تلقائية'
    ];
    
    // Simulate POST request
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_POST = $testUnit;
    
    // Mock php://input
    $tempFile = tempnam(sys_get_temp_dir(), 'test_input');
    file_put_contents($tempFile, json_encode($testUnit));
    
    // This is a simplified test - in real scenario we'd need to mock php://input
    echo "<div class='info'>🔄 محاولة إنشاء وحدة: " . $testUnit['unitName'] . "</div>";
    echo "<div class='info'>📏 " . $testUnit['largeUnitName'] . " = " . $testUnit['smallUnitCount'] . " " . $testUnit['smallUnitName'] . "</div>";
    
    // Direct database insert test
    if (isset($conn)) {
        $stmt = $conn->prepare("
            INSERT INTO units (
                name, symbol, type, category,
                large_unit_name, large_unit_count,
                small_unit_name, small_unit_count,
                description, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            $testUnit['unitName'],
            substr($testUnit['unitName'], 0, 3),
            $testUnit['unitType'],
            $testUnit['category'],
            $testUnit['largeUnitName'],
            $testUnit['largeUnitCount'],
            $testUnit['smallUnitName'],
            $testUnit['smallUnitCount'],
            $testUnit['description'],
            1
        ]);
        
        if ($result) {
            $unitId = $conn->lastInsertId();
            echo "<div class='success'>✅ تم إنشاء الوحدة بنجاح برقم: " . $unitId . "</div>";
        } else {
            echo "<div class='error'>❌ فشل إنشاء الوحدة</div>";
        }
    }
    
    unlink($tempFile);
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في إنشاء الوحدة: " . $e->getMessage() . "</div>";
}

echo "</div>";

// Test 4: Check localStorage Integration
echo "<div class='test'>";
echo "<h3>4. معلومات التكامل</h3>";
echo "<div class='info'>📁 مسار API: " . realpath('api/units.php') . "</div>";
echo "<div class='info'>📁 مسار قاعدة البيانات: " . realpath('api/config/database.php') . "</div>";
echo "<div class='info'>🌐 URL الحالي: " . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] . "</div>";
echo "<div class='info'>⏰ وقت الاختبار: " . date('Y-m-d H:i:s') . "</div>";
echo "</div>";

echo "<div class='test'>";
echo "<h3>5. توصيات الإصلاح</h3>";
echo "<div class='info'>1. تأكد من وجود جدول units في قاعدة البيانات</div>";
echo "<div class='info'>2. تأكد من صحة إعدادات قاعدة البيانات في api/config/database.php</div>";
echo "<div class='info'>3. تأكد من أن الخادم يدعم PHP و MySQL</div>";
echo "<div class='info'>4. تحقق من أن ملف units.js يستدعي API بشكل صحيح</div>";
echo "<div class='info'>5. افتح Developer Tools في المتصفح لرؤية أخطاء JavaScript</div>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
