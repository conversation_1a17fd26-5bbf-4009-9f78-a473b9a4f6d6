# مخبز أنور - نظام إدارة المخبز المتكامل

نظام إدارة شامل للمخابز والحلويات مع دعم العمل أوفلاين وأونلاين

## 🚀 المميزات

- ✅ **نظام مصادقة آمن** مع إدارة الصلاحيات
- ✅ **دعم العمل أوفلاين/أونلاين** مع مزامنة تلقائية
- ✅ **إدارة شاملة للمخزون** والمنتجات
- ✅ **نظام فواتير متكامل** (شراء، بيع، مرتجعات، بيع سريع)
- ✅ **قارئ باركود** للبيع السريع
- ✅ **إدارة العملاء والموردين**
- ✅ **شجرة حسابات محاسبية**
- ✅ **تقارير مفصلة**
- ✅ **واجهة عربية** مع دعم RTL

## 🛠️ التقنيات المستخدمة

- **Frontend**: Next.js 14 + TypeScript + Tailwind CSS
- **Database**: SQLite (أوفلاين) + Drizzle ORM
- **Authentication**: JWT + bcrypt
- **State Management**: Zustand
- **Icons**: Lucide React
- **Barcode**: QuaggaJS

## 📋 متطلبات النظام

- Node.js 18+ 
- npm أو yarn

## 🚀 التثبيت والتشغيل

### 1. تثبيت المكتبات
```bash
npm install
```

### 2. تهيئة قاعدة البيانات
```bash
npm run db:generate
npm run db:migrate
```

### 3. تشغيل التطبيق
```bash
npm run dev
```

### 4. فتح التطبيق
افتح المتصفح على: `http://localhost:3000`

## 🔐 بيانات الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## 📁 هيكل المشروع

```
AnwarBakery/
├── src/
│   ├── app/                    # صفحات Next.js
│   │   ├── dashboard/          # لوحة التحكم
│   │   ├── login/              # صفحة تسجيل الدخول
│   │   └── api/                # API Routes
│   ├── components/             # مكونات React
│   ├── lib/                    # مكتبات مساعدة
│   │   ├── auth/               # نظام المصادقة
│   │   └── database/           # قاعدة البيانات
│   └── middleware.ts           # حماية الصفحات
├── database/                   # ملفات قاعدة البيانات
├── drizzle/                    # ملفات الهجرة
└── scripts/                    # سكريبتات مساعدة
```

## 🎯 الوحدات المتاحة

### ✅ المكتملة
- [x] نظام تسجيل الدخول والمصادقة
- [x] لوحة التحكم الرئيسية
- [x] هيكل قاعدة البيانات
- [x] واجهة المستخدم الأساسية

### 🚧 قيد التطوير
- [ ] بيانات المنشأة
- [ ] شجرة الحسابات
- [ ] إدارة الموردين
- [ ] إدارة العملاء
- [ ] نظام الفواتير
- [ ] إدارة المخزون
- [ ] قارئ الباركود
- [ ] التقارير

## 🔧 الأوامر المتاحة

```bash
# تشغيل التطبيق في وضع التطوير
npm run dev

# بناء التطبيق للإنتاج
npm run build

# تشغيل التطبيق في وضع الإنتاج
npm run start

# فحص الكود
npm run lint

# إنشاء مخطط قاعدة البيانات
npm run db:generate

# تطبيق الهجرات
npm run db:migrate

# فتح أداة إدارة قاعدة البيانات
npm run db:studio
```

## 📱 دعم المنصات

- ✅ **متصفحات الويب** (Chrome, Firefox, Safari, Edge)
- 🚧 **تطبيق أندرويد** (قريباً مع Capacitor)
- 🚧 **تطبيق iOS** (قريباً مع Capacitor)
- 🚧 **تطبيق سطح المكتب** (قريباً مع Electron)

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة التغييرات
4. عمل Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT

## 📞 الدعم

للدعم والاستفسارات، يرجى التواصل عبر:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966501234567

---

**مخبز أنور** - نظام إدارة المخابز الأكثر تطوراً في المنطقة 🥖✨
