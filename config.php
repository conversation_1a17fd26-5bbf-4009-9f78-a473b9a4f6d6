<?php
// config.php - إعدادات قاعدة البيانات لنظام مخبز أنوار الحي
// Database configuration for Anwar Bakery System

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'anwar_bakery');

// إعدادات النظام
define('SYSTEM_NAME', 'نظام إدارة مخبز أنوار الحي');
define('SYSTEM_VERSION', '1.0.0');
define('SYSTEM_TIMEZONE', 'Asia/Riyadh');

// إعدادات الأمان
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('PASSWORD_MIN_LENGTH', 6);

// إعدادات الملفات
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5 ميجابايت
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'pdf', 'xlsx', 'xls']);

// إعدادات العملة الافتراضية
define('DEFAULT_CURRENCY', 'YER');
define('DEFAULT_CURRENCY_SYMBOL', 'ر.ي');

// إعدادات التاريخ والوقت
date_default_timezone_set(SYSTEM_TIMEZONE);

// دالة للاتصال بقاعدة البيانات
function getDBConnection() {
    try {
        $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
        
        if ($conn->connect_error) {
            throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
        }
        
        // تعيين ترميز UTF-8
        $conn->set_charset("utf8mb4");
        
        return $conn;
    } catch (Exception $e) {
        error_log("Database connection error: " . $e->getMessage());
        return false;
    }
}

// دالة للتحقق من وجود قاعدة البيانات
function checkDatabaseExists() {
    try {
        $conn = new mysqli(DB_HOST, DB_USER, DB_PASS);
        
        if ($conn->connect_error) {
            return false;
        }
        
        $result = $conn->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '" . DB_NAME . "'");
        $exists = $result && $result->num_rows > 0;
        
        $conn->close();
        return $exists;
    } catch (Exception $e) {
        return false;
    }
}

// دالة لإنشاء قاعدة البيانات
function createDatabase() {
    try {
        $conn = new mysqli(DB_HOST, DB_USER, DB_PASS);
        
        if ($conn->connect_error) {
            throw new Exception("فشل الاتصال بالخادم: " . $conn->connect_error);
        }
        
        $sql = "CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
        
        if ($conn->query($sql) === TRUE) {
            $conn->close();
            return true;
        } else {
            throw new Exception("خطأ في إنشاء قاعدة البيانات: " . $conn->error);
        }
    } catch (Exception $e) {
        error_log("Database creation error: " . $e->getMessage());
        return false;
    }
}

// دالة للتحقق من صحة الاتصال
function testConnection() {
    $conn = getDBConnection();
    if ($conn) {
        $conn->close();
        return true;
    }
    return false;
}

// دالة لتنظيف البيانات المدخلة
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// دالة لتشفير كلمات المرور
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// دالة للتحقق من كلمات المرور
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// دالة لإنشاء رمز مميز عشوائي
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

// دالة لتسجيل الأخطاء
function logError($message, $file = '', $line = '') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] Error: $message";
    
    if ($file) {
        $logMessage .= " in $file";
    }
    
    if ($line) {
        $logMessage .= " on line $line";
    }
    
    error_log($logMessage . PHP_EOL, 3, 'logs/error.log');
}

// دالة لإرسال استجابة JSON
function sendJSONResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// دالة للتحقق من صحة البريد الإلكتروني
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// دالة للتحقق من صحة رقم الهاتف
function isValidPhone($phone) {
    // تحقق بسيط من رقم الهاتف السعودي
    return preg_match('/^(05|5)[0-9]{8}$/', $phone);
}

// دالة لتنسيق التاريخ
function formatDate($date, $format = 'Y-m-d') {
    if (empty($date)) return '';
    
    try {
        $dateObj = new DateTime($date);
        return $dateObj->format($format);
    } catch (Exception $e) {
        return $date;
    }
}

// دالة لتنسيق المبالغ المالية
function formatCurrency($amount, $currency = DEFAULT_CURRENCY_SYMBOL) {
    return number_format($amount, 2) . ' ' . $currency;
}

// إعدادات معالجة الأخطاء
if (!is_dir('logs')) {
    mkdir('logs', 0755, true);
}

// تعيين معالج الأخطاء المخصص
set_error_handler(function($severity, $message, $file, $line) {
    logError($message, $file, $line);
});

// تعيين معالج الاستثناءات المخصص
set_exception_handler(function($exception) {
    logError($exception->getMessage(), $exception->getFile(), $exception->getLine());
});

// التحقق من إعدادات PHP المطلوبة
if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    die('يتطلب هذا النظام PHP 7.4 أو أحدث. الإصدار الحالي: ' . PHP_VERSION);
}

// التحقق من وجود امتدادات PHP المطلوبة
$requiredExtensions = ['mysqli', 'json', 'mbstring'];
foreach ($requiredExtensions as $extension) {
    if (!extension_loaded($extension)) {
        die("امتداد PHP المطلوب غير مثبت: $extension");
    }
}

// رسالة نجاح التحميل (للتطوير فقط)
if (defined('DEBUG') && DEBUG) {
    error_log("Config loaded successfully at " . date('Y-m-d H:i:s'));
}
?>
