{"app": {"name": "نظام إدارة مخبز أنوار الحي", "name_en": "Anwar Bakery Management System", "version": "1.0.0", "description": "نظام إدارة شامل للمخابز والحلويات", "author": "<PERSON>war <PERSON>y Team", "license": "Proprietary"}, "database": {"host": "localhost", "name": "anwar_bakery", "username": "root", "password": "", "charset": "utf8mb4", "port": 3306}, "security": {"jwt_secret": "anwar_bakery_secret_key_2024", "session_timeout": 86400, "password_min_length": 6, "max_login_attempts": 5, "lockout_duration": 900}, "features": {"multi_branch": true, "inventory_tracking": true, "accounting_module": true, "reports_module": true, "backup_system": true, "user_management": true, "audit_logs": true}, "defaults": {"currency": "YER", "currency_symbol": "﷼", "tax_rate": 15.0, "date_format": "Y-m-d", "time_format": "H:i:s", "timezone": "Asia/Riyadh", "language": "ar", "items_per_page": 50}, "invoice_settings": {"sales_prefix": "INV-S-", "purchase_prefix": "INV-P-", "return_sales_prefix": "RET-S-", "return_purchase_prefix": "RET-P-", "auto_number": true, "number_padding": 6}, "voucher_settings": {"receipt_prefix": "REC-", "payment_prefix": "PAY-", "journal_prefix": "JOU-", "auto_number": true, "number_padding": 6}, "backup_settings": {"auto_backup": true, "backup_time": "02:00", "retention_days": 30, "backup_path": "./backups/", "compress_backups": true}, "email_settings": {"smtp_host": "", "smtp_port": 587, "smtp_username": "", "smtp_password": "", "smtp_encryption": "tls", "from_email": "", "from_name": "نظام مخبز أنوار الحي"}, "file_upload": {"max_file_size": 2097152, "allowed_image_types": ["jpg", "jpeg", "png", "gif"], "allowed_document_types": ["pdf", "doc", "docx", "xls", "xlsx"], "upload_path": "./uploads/", "image_max_width": 1200, "image_max_height": 1200, "thumbnail_width": 200, "thumbnail_height": 200}, "api_settings": {"rate_limit": 1000, "rate_limit_window": 3600, "cors_enabled": true, "cors_origins": ["*"], "api_version": "v1", "documentation_enabled": true}, "logging": {"enabled": true, "level": "info", "log_file": "./logs/app.log", "max_file_size": 10485760, "max_files": 5, "log_queries": false, "log_errors": true, "log_user_actions": true}, "cache": {"enabled": false, "driver": "file", "ttl": 3600, "path": "./cache/"}, "performance": {"enable_compression": true, "minify_html": false, "minify_css": false, "minify_js": false, "enable_etags": true}, "maintenance": {"enabled": false, "message": "النظام تحت الصيانة، يرجى المحاولة لاحقاً", "allowed_ips": ["127.0.0.1"], "retry_after": 3600}, "notifications": {"low_stock_enabled": true, "low_stock_threshold": 10, "overdue_invoices_enabled": true, "backup_notifications": true, "system_errors_enabled": true}, "reports": {"default_format": "pdf", "include_logo": true, "include_company_info": true, "page_size": "A4", "orientation": "portrait", "font_family": "DejaVu Sans", "font_size": 12}, "accounting": {"fiscal_year_start": "01-01", "auto_post_invoices": true, "auto_post_vouchers": true, "require_balanced_entries": true, "decimal_places": 2, "negative_amounts_in_red": true}, "inventory": {"track_serial_numbers": false, "track_expiry_dates": false, "allow_negative_stock": false, "auto_reorder": false, "cost_method": "FIFO"}, "ui_settings": {"theme": "light", "sidebar_collapsed": false, "show_tooltips": true, "animation_enabled": true, "compact_mode": false, "rtl_support": true}, "integrations": {"payment_gateways": {"enabled": false, "providers": []}, "shipping": {"enabled": false, "providers": []}, "accounting_software": {"enabled": false, "provider": ""}}, "development": {"debug_mode": false, "show_sql_queries": false, "profiling_enabled": false, "error_reporting": "production"}}