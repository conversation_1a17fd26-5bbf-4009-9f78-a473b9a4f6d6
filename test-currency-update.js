// اختبار تحديث العملة
(function() {
    'use strict';
    
    // وظيفة لاختبار تحديث العملة
    function testCurrencyUpdate() {
        console.log('🧪 Testing currency update...');
        
        // الحصول على بيانات الشركة الحالية
        const companyData = JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
        console.log('Current company data:', companyData);
        
        // اختبار تحديث العملة إلى الدولار
        const testData = {
            ...companyData,
            currency: 'USD',
            currencySymbol: '$',
            updatedAt: new Date().toISOString()
        };
        
        // حفظ البيانات الجديدة
        localStorage.setItem('anwar_bakery_company', JSON.stringify(testData));
        
        // إرسال حدث التحديث
        window.dispatchEvent(new CustomEvent('companyDataUpdated', { detail: testData }));
        
        console.log('✅ Test currency update completed');
        
        // العودة للعملة الأصلية بعد 5 ثوان
        setTimeout(() => {
            const originalData = {
                ...testData,
                currency: 'SAR',
                currencySymbol: 'ر.س',
                updatedAt: new Date().toISOString()
            };
            
            localStorage.setItem('anwar_bakery_company', JSON.stringify(originalData));
            window.dispatchEvent(new CustomEvent('companyDataUpdated', { detail: originalData }));
            
            console.log('🔄 Reverted to original currency');
        }, 5000);
    }
    
    // إضافة زر اختبار للصفحة
    function addTestButton() {
        const testButton = document.createElement('button');
        testButton.textContent = '🧪 اختبار تحديث العملة';
        testButton.className = 'fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-blue-700 z-50';
        testButton.onclick = testCurrencyUpdate;
        
        document.body.appendChild(testButton);
        
        console.log('✅ Test button added');
    }
    
    // إضافة الزر عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة الزر فقط في بيئة التطوير
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            setTimeout(addTestButton, 2000);
        }
    });
    
    // إضافة وظيفة عامة للاختبار
    window.testCurrencyUpdate = testCurrencyUpdate;
    
    console.log('✅ Currency Test loaded');
    
})();
