// Sample units data
let units = [
    {
        id: 1,
        unitName: 'كيس دقيق 50كج',
        unitType: 'weight',
        largeUnitName: 'كيس',
        largeUnitCount: 1,
        smallUnitName: 'جرام',
        smallUnitCount: 50000,
        conversionFactor: 50000, // smallUnitCount / largeUnitCount
        category: 'raw_materials',
        isActive: true,
        description: 'كيس دقيق أبيض 50 كيلوجرام للمخبوزات',
        createdAt: '2024-01-01'
    },
    {
        id: 2,
        unitName: 'جالون زيت 20 لتر',
        unitType: 'volume',
        largeUnitName: 'جالون',
        largeUnitCount: 1,
        smallUnitName: 'مل',
        smallUnitCount: 20000,
        conversionFactor: 20000,
        category: 'raw_materials',
        isActive: true,
        description: 'جالون زيت نباتي 20 لتر للقلي والطبخ',
        createdAt: '2024-01-01'
    },
    {
        id: 3,
        unitName: 'كرتون بيض 30 حبة',
        unitType: 'count',
        largeUnitName: 'كرتون',
        largeUnitCount: 1,
        smallUnitName: 'حبة',
        smallUnitCount: 30,
        conversionFactor: 30,
        category: 'raw_materials',
        isActive: true,
        description: 'كرتون بيض طازج 30 حبة',
        createdAt: '2024-01-01'
    },
    {
        id: 4,
        unitName: 'علبة سكر 1كج',
        unitType: 'weight',
        largeUnitName: 'علبة',
        largeUnitCount: 1,
        smallUnitName: 'جرام',
        smallUnitCount: 1000,
        conversionFactor: 1000,
        category: 'raw_materials',
        isActive: true,
        description: 'علبة سكر أبيض ناعم 1 كيلوجرام',
        createdAt: '2024-01-01'
    },
    {
        id: 5,
        unitName: 'صندوق خبز 50 رغيف',
        unitType: 'count',
        largeUnitName: 'صندوق',
        largeUnitCount: 1,
        smallUnitName: 'رغيف',
        smallUnitCount: 50,
        conversionFactor: 50,
        category: 'finished_products',
        isActive: true,
        description: 'صندوق خبز أبيض 50 رغيف',
        createdAt: '2024-01-01'
    }
];

// Check authentication
function checkAuth() {
    const session = localStorage.getItem('anwar_bakery_session') ||
                   sessionStorage.getItem('anwar_bakery_session');

    if (!session) {
        window.location.href = 'login.html';
        return null;
    }

    return JSON.parse(session);
}

// Load user info
function loadUserInfo() {
    const session = checkAuth();
    if (session) {
        document.getElementById('userFullName').textContent = session.fullName;
        document.getElementById('userRole').textContent = session.role;
    }
}

// Load company info
function loadCompanyInfo() {
    // Use new settings system if available
    if (window.appSettings) {
        const companyName = window.appSettings.get('company', 'companyNameAr');
        if (companyName) {
            document.getElementById('sidebarCompanyName').textContent = companyName;
            document.title = `إدارة وحدات القياس - ${companyName}`;
        }
    } else {
        // Fallback to old system
        const savedData = localStorage.getItem('anwar_bakery_company');
        if (savedData) {
            const companyData = JSON.parse(savedData);

            if (companyData.companyNameAr) {
                document.getElementById('sidebarCompanyName').textContent = companyData.companyNameAr;
                document.title = `إدارة وحدات القياس - ${companyData.companyNameAr}`;
            }
        }
    }
}

// Load units from API and localStorage
async function loadUnits() {
    try {
        // Try to load from API first
        const response = await fetch('api/units.php');
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                units = result.data.units;
                // Save to localStorage as backup
                localStorage.setItem('anwar_bakery_units', JSON.stringify(units));
                console.log('✅ تم تحميل وحدات القياس من قاعدة البيانات');
            } else {
                throw new Error(result.error);
            }
        } else {
            throw new Error('فشل في الاتصال بالخادم');
        }
    } catch (error) {
        console.warn('⚠️ فشل تحميل وحدات القياس من قاعدة البيانات، جاري التحميل من localStorage:', error.message);

        // Fallback to localStorage
        const savedUnits = localStorage.getItem('anwar_bakery_units');
        if (savedUnits) {
            units = JSON.parse(savedUnits);
        }
    }

    renderUnits();
    updateStats();
}

// Save units to API and localStorage
async function saveUnits() {
    // Always save to localStorage first
    localStorage.setItem('anwar_bakery_units', JSON.stringify(units));

    // Try to sync with API
    try {
        const response = await fetch('api/sync-manager.php?action=sync-to-server', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                units: units
            })
        });

        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                console.log('✅ تم حفظ وحدات القياس في قاعدة البيانات');
            }
        }
    } catch (error) {
        console.warn('⚠️ فشل في حفظ وحدات القياس في قاعدة البيانات:', error.message);
    }
}

// Update statistics
function updateStats() {
    const totalUnits = units.length;
    const weightUnits = units.filter(unit => unit.unitType === 'weight').length;
    const volumeUnits = units.filter(unit => unit.unitType === 'volume').length;
    const countUnits = units.filter(unit => unit.unitType === 'count').length;

    document.getElementById('totalUnits').textContent = totalUnits;
    document.getElementById('weightUnits').textContent = weightUnits;
    document.getElementById('volumeUnits').textContent = volumeUnits;
    document.getElementById('countUnits').textContent = countUnits;
}

// Render units table
function renderUnits(filteredUnits = units) {
    const tbody = document.getElementById('unitsTableBody');
    tbody.innerHTML = '';

    filteredUnits.forEach(unit => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';

        const typeNames = {
            'weight': 'وزن',
            'volume': 'حجم',
            'count': 'عدد',
            'length': 'طول',
            'area': 'مساحة'
        };

        const categoryNames = {
            'raw_materials': 'مواد خام',
            'packaging': 'تعبئة وتغليف',
            'finished_products': 'منتجات نهائية'
        };

        const statusClass = unit.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
        const statusText = unit.isActive ? 'نشط' : 'غير نشط';

        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                <div class="flex items-center">
                    <span class="ml-2">${getUnitIcon(unit.unitType)}</span>
                    ${unit.unitName}
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${typeNames[unit.unitType]}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <span class="font-semibold">${unit.largeUnitCount}</span> ${unit.largeUnitName}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <span class="font-semibold">${unit.smallUnitCount.toLocaleString()}</span> ${unit.smallUnitName}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                    1:${unit.conversionFactor.toLocaleString()}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${categoryNames[unit.category]}</td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                    ${statusText}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button onclick="editUnit(${unit.id})" class="text-blue-600 hover:text-blue-900">تعديل</button>
                <button onclick="calculateUnitCost(${unit.id})" class="text-green-600 hover:text-green-900">حساب التكلفة</button>
                <button onclick="toggleUnitStatus(${unit.id})" class="text-yellow-600 hover:text-yellow-900">
                    ${unit.isActive ? 'إلغاء تفعيل' : 'تفعيل'}
                </button>
                <button onclick="deleteUnit(${unit.id})" class="text-red-600 hover:text-red-900">حذف</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Get unit icon based on type
function getUnitIcon(type) {
    const icons = {
        'weight': '⚖️',
        'volume': '🥤',
        'count': '🔢',
        'length': '📏',
        'area': '📐'
    };
    return icons[type] || '📏';
}

// Filter units
function filterUnits() {
    const searchTerm = document.getElementById('unitSearchInput').value.toLowerCase();
    const typeFilter = document.getElementById('unitTypeFilter').value;
    const categoryFilter = document.getElementById('unitCategoryFilter').value;
    const statusFilter = document.getElementById('unitStatusFilter').value;

    const filtered = units.filter(unit => {
        const matchesSearch = unit.unitName.toLowerCase().includes(searchTerm) ||
                            unit.largeUnitName.toLowerCase().includes(searchTerm) ||
                            unit.smallUnitName.toLowerCase().includes(searchTerm);
        const matchesType = !typeFilter || unit.unitType === typeFilter;
        const matchesCategory = !categoryFilter || unit.category === categoryFilter;
        let matchesStatus = true;

        if (statusFilter === 'active') {
            matchesStatus = unit.isActive;
        } else if (statusFilter === 'inactive') {
            matchesStatus = !unit.isActive;
        }

        return matchesSearch && matchesType && matchesCategory && matchesStatus;
    });

    renderUnits(filtered);
}

// Calculate unit cost
function calculateUnitCost(id) {
    const unit = units.find(u => u.id === id);
    if (unit) {
        const purchasePrice = prompt(`أدخل سعر شراء ${unit.largeUnitName} الواحد:`);
        if (purchasePrice && !isNaN(purchasePrice)) {
            const price = parseFloat(purchasePrice);
            const costPerSmallUnit = price / unit.conversionFactor;

            let message = `تكلفة ${unit.unitName}:\n\n`;
            message += `سعر ${unit.largeUnitName} الواحد: ${formatCurrency(price)}\n`;
            message += `تكلفة ${unit.smallUnitName} الواحد: ${formatCurrency(costPerSmallUnit)}\n\n`;
            message += `مثال للحساب:\n`;
            message += `إذا كانت الوصفة تحتاج 500 ${unit.smallUnitName}\n`;
            message += `التكلفة = 500 × ${costPerSmallUnit.toFixed(6)} = ${formatCurrency(500 * costPerSmallUnit)}`;

            alert(message);
        }
    }
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (sidebar.classList.contains('translate-x-full')) {
        sidebar.classList.remove('translate-x-full');
        overlay.classList.remove('hidden');
    } else {
        sidebar.classList.add('translate-x-full');
        overlay.classList.add('hidden');
    }
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_session');
        sessionStorage.removeItem('anwar_bakery_session');
        window.location.href = 'login.html';
    }
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Format currency using settings
function formatCurrency(amount) {
    if (window.appSettings) {
        return window.appSettings.formatCurrency(amount);
    } else {
        // Fallback formatting
        return `${amount.toFixed(2)} ر.س`;
    }
}

// Unit modal functions
function openAddUnitModal() {
    document.getElementById('unitModalTitle').textContent = 'إضافة وحدة قياس جديدة';
    document.getElementById('unitForm').reset();
    document.getElementById('unitId').value = '';
    document.getElementById('unitIsActive').checked = true;
    document.getElementById('largeUnitCount').value = 1;
    updateConversionDisplay();
    document.getElementById('unitModal').classList.add('active');
}

function editUnit(id) {
    const unit = units.find(u => u.id === id);
    if (unit) {
        document.getElementById('unitModalTitle').textContent = 'تعديل وحدة القياس';
        document.getElementById('unitId').value = unit.id;
        document.getElementById('unitName').value = unit.unitName;
        document.getElementById('unitType').value = unit.unitType;
        document.getElementById('unitCategory').value = unit.category;
        document.getElementById('largeUnitName').value = unit.largeUnitName;
        document.getElementById('largeUnitCount').value = unit.largeUnitCount;
        document.getElementById('smallUnitName').value = unit.smallUnitName;
        document.getElementById('smallUnitCount').value = unit.smallUnitCount;
        document.getElementById('unitDescription').value = unit.description || '';
        document.getElementById('unitIsActive').checked = unit.isActive;

        updateConversionDisplay();
        document.getElementById('unitModal').classList.add('active');
    }
}

function closeUnitModal() {
    document.getElementById('unitModal').classList.remove('active');
}

async function saveUnit() {
    const form = document.getElementById('unitForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const unitId = document.getElementById('unitId').value;
    const largeUnitCount = parseInt(document.getElementById('largeUnitCount').value);
    const smallUnitCount = parseFloat(document.getElementById('smallUnitCount').value);

    const unitData = {
        unitName: document.getElementById('unitName').value,
        unitType: document.getElementById('unitType').value,
        largeUnitName: document.getElementById('largeUnitName').value,
        largeUnitCount: largeUnitCount,
        smallUnitName: document.getElementById('smallUnitName').value,
        smallUnitCount: smallUnitCount,
        conversionFactor: smallUnitCount / largeUnitCount,
        category: document.getElementById('unitCategory').value,
        isActive: document.getElementById('unitIsActive').checked,
        description: document.getElementById('unitDescription').value
    };

    // Check if unit name already exists
    const existingUnit = units.find(u => u.unitName === unitData.unitName && u.id != unitId);
    if (existingUnit) {
        showMessage('اسم الوحدة موجود مسبقاً!', 'error');
        return;
    }

    try {
        let response;

        if (unitId) {
            // Update existing unit via API
            response = await fetch(`api/units.php/${unitId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(unitData)
            });
        } else {
            // Create new unit via API
            response = await fetch('api/units.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(unitData)
            });
        }

        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                // Reload units from API
                await loadUnits();
                // Force re-render to show the new unit
                renderUnits();
                updateStats();
                closeUnitModal();
                showMessage(unitId ? 'تم تحديث وحدة القياس بنجاح!' : 'تم إنشاء وحدة القياس بنجاح!', 'success');
                return;
            } else {
                throw new Error(result.error);
            }
        } else {
            throw new Error('فشل في الاتصال بالخادم');
        }
    } catch (error) {
        console.error('خطأ في حفظ وحدة القياس:', error);

        // Fallback to localStorage method
        if (unitId) {
            // Edit existing unit
            const unitIndex = units.findIndex(u => u.id == unitId);
            if (unitIndex !== -1) {
                units[unitIndex] = {
                    ...units[unitIndex],
                    ...unitData
                };
            }
        } else {
            // Add new unit
            const newUnit = {
                id: Math.max(...units.map(u => u.id), 0) + 1,
                ...unitData,
                createdAt: new Date().toISOString().split('T')[0]
            };
            units.push(newUnit);
        }

        saveUnits();
        renderUnits();
        updateStats();
        closeUnitModal();
        showMessage('تم حفظ وحدة القياس محلياً (سيتم المزامنة لاحقاً)', 'warning');
    }
}

function calculateConversion() {
    updateConversionDisplay();
}

function updateConversionDisplay() {
    const largeUnitName = document.getElementById('largeUnitName').value || 'الوحدة الكبيرة';
    const largeUnitCount = parseInt(document.getElementById('largeUnitCount').value) || 1;
    const smallUnitName = document.getElementById('smallUnitName').value || 'الوحدة الصغيرة';
    const smallUnitCount = parseFloat(document.getElementById('smallUnitCount').value) || 0;

    const conversionText = `${largeUnitCount} ${largeUnitName} = ${smallUnitCount.toLocaleString()} ${smallUnitName}`;
    document.getElementById('conversionDisplay').textContent = conversionText;
}

function toggleUnitStatus(id) {
    const unit = units.find(u => u.id === id);
    if (unit) {
        unit.isActive = !unit.isActive;
        saveUnits();
        renderUnits();
        updateStats();
        showMessage(`تم ${unit.isActive ? 'تفعيل' : 'إلغاء تفعيل'} الوحدة بنجاح!`, 'success');
    }
}

async function deleteUnit(id) {
    if (confirm('هل أنت متأكد من حذف هذه الوحدة؟')) {
        try {
            const response = await fetch(`api/units.php/${id}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    // Reload units from API
                    await loadUnits();
                    showMessage('تم حذف الوحدة بنجاح!', 'success');
                    return;
                } else {
                    throw new Error(result.error);
                }
            } else {
                throw new Error('فشل في الاتصال بالخادم');
            }
        } catch (error) {
            console.error('خطأ في حذف وحدة القياس:', error);

            // Fallback to localStorage method
            units = units.filter(u => u.id !== id);
            saveUnits();
            renderUnits();
            updateStats();
            showMessage('تم حذف الوحدة محلياً (سيتم المزامنة لاحقاً)', 'warning');
        }
    }
}

// Calculator functions
function showUnitCalculator() {
    populateCalculatorUnits();
    document.getElementById('calculatorModal').classList.add('active');
}

function closeCalculatorModal() {
    document.getElementById('calculatorModal').classList.remove('active');
}

function populateCalculatorUnits() {
    const calculatorUnit = document.getElementById('calculatorUnit');
    calculatorUnit.innerHTML = '<option value="">اختر الوحدة</option>';

    units.filter(unit => unit.isActive).forEach(unit => {
        const option = document.createElement('option');
        option.value = unit.id;
        option.textContent = unit.unitName;
        calculatorUnit.appendChild(option);
    });
}

function updateCalculator() {
    const unitId = document.getElementById('calculatorUnit').value;
    const fromUnitSelect = document.getElementById('calculatorFromUnit');

    fromUnitSelect.innerHTML = '<option value="">اختر</option>';

    if (unitId) {
        const unit = units.find(u => u.id == unitId);
        if (unit) {
            const option1 = document.createElement('option');
            option1.value = 'large';
            option1.textContent = unit.largeUnitName;
            fromUnitSelect.appendChild(option1);

            const option2 = document.createElement('option');
            option2.value = 'small';
            option2.textContent = unit.smallUnitName;
            fromUnitSelect.appendChild(option2);
        }
    }

    document.getElementById('calculatorResult').textContent = 'اختر الوحدة والكمية للحساب';
}

function performCalculation() {
    const unitId = document.getElementById('calculatorUnit').value;
    const quantity = parseFloat(document.getElementById('calculatorQuantity').value);
    const fromUnit = document.getElementById('calculatorFromUnit').value;

    if (!unitId || !quantity || !fromUnit) {
        document.getElementById('calculatorResult').textContent = 'اختر الوحدة والكمية للحساب';
        return;
    }

    const unit = units.find(u => u.id == unitId);
    if (!unit) return;

    let result = '';

    if (fromUnit === 'large') {
        // Convert from large to small
        const smallQuantity = quantity * unit.conversionFactor;
        result = `${quantity} ${unit.largeUnitName} = ${smallQuantity.toLocaleString()} ${unit.smallUnitName}`;
    } else {
        // Convert from small to large
        const largeQuantity = quantity / unit.conversionFactor;
        result = `${quantity.toLocaleString()} ${unit.smallUnitName} = ${largeQuantity.toFixed(4)} ${unit.largeUnitName}`;
    }

    document.getElementById('calculatorResult').textContent = result;
}
