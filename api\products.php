<?php
/**
 * Products API for Anwar Bakery Management System
 * واجهة برمجة التطبيقات للمنتجات - نظام إدارة مخبز أنوار الحي
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config/database.php';

class ProductsAPI {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }

    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $action = $_GET['action'] ?? '';
        $id = $_GET['id'] ?? null;

        // Verify authentication
        $user = $this->getCurrentUser();
        if (!$user) {
            return $this->sendError('Unauthorized', 401);
        }

        try {
            switch ($method) {
                case 'GET':
                    if ($id) {
                        return $this->getProduct($id);
                    } else {
                        switch ($action) {
                            case 'categories':
                                return $this->getCategories();
                            case 'low-stock':
                                return $this->getLowStockProducts();
                            case 'search':
                                return $this->searchProducts();
                            default:
                                return $this->getProducts();
                        }
                    }
                    break;

                case 'POST':
                    switch ($action) {
                        case 'category':
                            return $this->createCategory();
                        default:
                            return $this->createProduct();
                    }
                    break;

                case 'PUT':
                    if ($id) {
                        switch ($action) {
                            case 'category':
                                return $this->updateCategory($id);
                            case 'stock':
                                return $this->updateStock($id);
                            default:
                                return $this->updateProduct($id);
                        }
                    }
                    return $this->sendError('Product ID required', 400);
                    break;

                case 'DELETE':
                    if ($id) {
                        switch ($action) {
                            case 'category':
                                return $this->deleteCategory($id);
                            default:
                                return $this->deleteProduct($id);
                        }
                    }
                    return $this->sendError('Product ID required', 400);
                    break;

                default:
                    return $this->sendError('Method not allowed', 405);
            }
        } catch (Exception $e) {
            return $this->sendError($e->getMessage(), 500);
        }
    }

    /**
     * Get all products
     * الحصول على جميع المنتجات
     */
    private function getProducts() {
        $page = $_GET['page'] ?? 1;
        $limit = $_GET['limit'] ?? 50;
        $offset = ($page - 1) * $limit;

        $stmt = $this->conn->prepare("
            SELECT 
                p.*,
                pc.name as category_name,
                u.name as unit_name,
                u.symbol as unit_symbol
            FROM products p
            LEFT JOIN product_categories pc ON p.category_id = pc.id
            LEFT JOIN units u ON p.unit_id = u.id
            WHERE p.is_active = 1
            ORDER BY p.name
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$limit, $offset]);
        $products = $stmt->fetchAll();

        // Get total count
        $countStmt = $this->conn->query("SELECT COUNT(*) as total FROM products WHERE is_active = 1");
        $total = $countStmt->fetch()['total'];

        return $this->sendSuccess([
            'products' => $products,
            'pagination' => [
                'page' => (int)$page,
                'limit' => (int)$limit,
                'total' => (int)$total,
                'pages' => ceil($total / $limit)
            ]
        ]);
    }

    /**
     * Get single product
     * الحصول على منتج واحد
     */
    private function getProduct($id) {
        $stmt = $this->conn->prepare("
            SELECT 
                p.*,
                pc.name as category_name,
                u.name as unit_name,
                u.symbol as unit_symbol
            FROM products p
            LEFT JOIN product_categories pc ON p.category_id = pc.id
            LEFT JOIN units u ON p.unit_id = u.id
            WHERE p.id = ?
        ");
        $stmt->execute([$id]);
        $product = $stmt->fetch();

        if (!$product) {
            return $this->sendError('Product not found', 404);
        }

        return $this->sendSuccess($product);
    }

    /**
     * Create new product
     * إنشاء منتج جديد
     */
    private function createProduct() {
        $user = $this->getCurrentUser();
        if (!in_array($user['role'], ['admin', 'manager'])) {
            return $this->sendError('Insufficient permissions', 403);
        }

        $input = json_decode(file_get_contents('php://input'), true);
        
        $required = ['code', 'name', 'unit_id', 'selling_price'];
        foreach ($required as $field) {
            if (!isset($input[$field]) || empty(trim($input[$field]))) {
                return $this->sendError("Field {$field} is required", 400);
            }
        }

        // Check if code exists
        $stmt = $this->conn->prepare("SELECT id FROM products WHERE code = ?");
        $stmt->execute([$input['code']]);
        if ($stmt->fetch()) {
            return $this->sendError('Product code already exists', 409);
        }

        $stmt = $this->conn->prepare("
            INSERT INTO products (
                code, name, description, category_id, unit_id, 
                cost_price, selling_price, min_stock_level, max_stock_level,
                current_stock, reorder_point, barcode, image_url
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $result = $stmt->execute([
            $input['code'],
            $input['name'],
            $input['description'] ?? '',
            $input['category_id'] ?? null,
            $input['unit_id'],
            $input['cost_price'] ?? 0,
            $input['selling_price'],
            $input['min_stock_level'] ?? 0,
            $input['max_stock_level'] ?? 0,
            $input['current_stock'] ?? 0,
            $input['reorder_point'] ?? 0,
            $input['barcode'] ?? '',
            $input['image_url'] ?? ''
        ]);

        if ($result) {
            $productId = $this->conn->lastInsertId();
            $this->logActivity($user['id'], 'create_product', 'products', $productId);
            
            return $this->sendSuccess([
                'id' => $productId,
                'message' => 'Product created successfully'
            ]);
        } else {
            return $this->sendError('Failed to create product', 500);
        }
    }

    /**
     * Update product
     * تحديث منتج
     */
    private function updateProduct($id) {
        $user = $this->getCurrentUser();
        if (!in_array($user['role'], ['admin', 'manager'])) {
            return $this->sendError('Insufficient permissions', 403);
        }

        $input = json_decode(file_get_contents('php://input'), true);

        // Get current product data
        $stmt = $this->conn->prepare("SELECT * FROM products WHERE id = ?");
        $stmt->execute([$id]);
        $currentProduct = $stmt->fetch();

        if (!$currentProduct) {
            return $this->sendError('Product not found', 404);
        }

        // Check if code exists for other products
        if (isset($input['code']) && $input['code'] !== $currentProduct['code']) {
            $stmt = $this->conn->prepare("SELECT id FROM products WHERE code = ? AND id != ?");
            $stmt->execute([$input['code'], $id]);
            if ($stmt->fetch()) {
                return $this->sendError('Product code already exists', 409);
            }
        }

        $updateFields = [];
        $updateValues = [];

        $allowedFields = [
            'code', 'name', 'description', 'category_id', 'unit_id',
            'cost_price', 'selling_price', 'min_stock_level', 'max_stock_level',
            'reorder_point', 'barcode', 'image_url', 'is_active'
        ];

        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updateFields[] = "{$field} = ?";
                $updateValues[] = $input[$field];
            }
        }

        if (empty($updateFields)) {
            return $this->sendError('No fields to update', 400);
        }

        $updateValues[] = $id;
        $sql = "UPDATE products SET " . implode(', ', $updateFields) . " WHERE id = ?";
        
        $stmt = $this->conn->prepare($sql);
        if ($stmt->execute($updateValues)) {
            $this->logActivity($user['id'], 'update_product', 'products', $id);
            return $this->sendSuccess(['message' => 'Product updated successfully']);
        } else {
            return $this->sendError('Failed to update product', 500);
        }
    }

    /**
     * Update product stock
     * تحديث مخزون المنتج
     */
    private function updateStock($id) {
        $user = $this->getCurrentUser();
        $input = json_decode(file_get_contents('php://input'), true);

        if (!isset($input['quantity']) || !isset($input['type'])) {
            return $this->sendError('Quantity and type are required', 400);
        }

        $quantity = (float)$input['quantity'];
        $type = $input['type']; // 'in', 'out', 'adjustment'
        $notes = $input['notes'] ?? '';
        $branchId = $input['branch_id'] ?? 1;

        // Get current stock
        $stmt = $this->conn->prepare("SELECT current_stock, cost_price FROM products WHERE id = ?");
        $stmt->execute([$id]);
        $product = $stmt->fetch();

        if (!$product) {
            return $this->sendError('Product not found', 404);
        }

        $currentStock = (float)$product['current_stock'];
        $newStock = $currentStock;

        switch ($type) {
            case 'in':
                $newStock += $quantity;
                break;
            case 'out':
                $newStock -= $quantity;
                break;
            case 'adjustment':
                $newStock = $quantity;
                break;
            default:
                return $this->sendError('Invalid movement type', 400);
        }

        if ($newStock < 0) {
            return $this->sendError('Insufficient stock', 400);
        }

        $this->conn->beginTransaction();

        try {
            // Update product stock
            $stmt = $this->conn->prepare("UPDATE products SET current_stock = ? WHERE id = ?");
            $stmt->execute([$newStock, $id]);

            // Record inventory movement
            $stmt = $this->conn->prepare("
                INSERT INTO inventory_movements 
                (product_id, branch_id, movement_type, quantity, unit_cost, total_cost, reference_type, notes, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $movementQuantity = $type === 'adjustment' ? ($newStock - $currentStock) : 
                               ($type === 'out' ? -$quantity : $quantity);
            
            $stmt->execute([
                $id,
                $branchId,
                $type,
                $movementQuantity,
                $product['cost_price'],
                $movementQuantity * $product['cost_price'],
                'adjustment',
                $notes,
                $user['id']
            ]);

            $this->conn->commit();
            $this->logActivity($user['id'], 'update_stock', 'products', $id);

            return $this->sendSuccess([
                'old_stock' => $currentStock,
                'new_stock' => $newStock,
                'message' => 'Stock updated successfully'
            ]);

        } catch (Exception $e) {
            $this->conn->rollback();
            return $this->sendError('Failed to update stock: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get product categories
     * الحصول على فئات المنتجات
     */
    private function getCategories() {
        $stmt = $this->conn->query("
            SELECT * FROM product_categories 
            WHERE is_active = 1 
            ORDER BY name
        ");
        $categories = $stmt->fetchAll();

        return $this->sendSuccess($categories);
    }

    /**
     * Get low stock products
     * الحصول على المنتجات منخفضة المخزون
     */
    private function getLowStockProducts() {
        $stmt = $this->conn->query("
            SELECT 
                p.*,
                pc.name as category_name,
                u.name as unit_name
            FROM products p
            LEFT JOIN product_categories pc ON p.category_id = pc.id
            LEFT JOIN units u ON p.unit_id = u.id
            WHERE p.is_active = 1 
            AND p.track_inventory = 1
            AND p.current_stock <= p.reorder_point
            ORDER BY p.current_stock ASC
        ");
        $products = $stmt->fetchAll();

        return $this->sendSuccess($products);
    }

    /**
     * Search products
     * البحث في المنتجات
     */
    private function searchProducts() {
        $query = $_GET['q'] ?? '';
        if (empty($query)) {
            return $this->sendError('Search query is required', 400);
        }

        $stmt = $this->conn->prepare("
            SELECT 
                p.*,
                pc.name as category_name,
                u.name as unit_name
            FROM products p
            LEFT JOIN product_categories pc ON p.category_id = pc.id
            LEFT JOIN units u ON p.unit_id = u.id
            WHERE p.is_active = 1 
            AND (p.name LIKE ? OR p.code LIKE ? OR p.barcode LIKE ?)
            ORDER BY p.name
            LIMIT 20
        ");
        
        $searchTerm = "%{$query}%";
        $stmt->execute([$searchTerm, $searchTerm, $searchTerm]);
        $products = $stmt->fetchAll();

        return $this->sendSuccess($products);
    }

    // Helper methods (getCurrentUser, logActivity, sendSuccess, sendError)
    private function getCurrentUser() {
        // Implementation similar to auth.php
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? '';
        
        if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return null;
        }

        $token = $matches[1];
        $parts = explode('.', $token);
        
        if (count($parts) !== 3) {
            return null;
        }

        $payload = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $parts[1])), true);
        
        if (!$payload || $payload['exp'] < time()) {
            return null;
        }

        $stmt = $this->conn->prepare("
            SELECT id, username, full_name, role 
            FROM users 
            WHERE id = ? AND is_active = 1
        ");
        $stmt->execute([$payload['user_id']]);
        
        return $stmt->fetch();
    }

    private function logActivity($userId, $action, $tableName, $recordId) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO system_logs (user_id, action, table_name, record_id, ip_address) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$userId, $action, $tableName, $recordId, $_SERVER['REMOTE_ADDR'] ?? null]);
        } catch (Exception $e) {
            error_log("Failed to log activity: " . $e->getMessage());
        }
    }

    private function sendSuccess($data, $message = 'Success') {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => $message,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }

    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'message' => $message
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
}

$api = new ProductsAPI();
$api->handleRequest();
?>
