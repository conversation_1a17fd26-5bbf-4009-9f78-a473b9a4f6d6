<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البيع السريع - مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .product-card:hover { transform: translateY(-2px); }
        .calculator-btn {
            transition: all 0.2s ease;
            min-height: 60px;
        }
        .calculator-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .cart-item {
            animation: slideIn 0.3s ease-out;
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        .total-display {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Messages Container -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 max-w-md"></div>

    <!-- Mobile menu overlay -->
    <div id="mobileOverlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 lg:hidden hidden"></div>

    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform translate-x-full lg:translate-x-0 lg:static lg:inset-0 sidebar-transition">
            <div class="h-16 px-4 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between">
                <h1 id="sidebarCompanyName" class="text-white text-lg font-bold">مخبز أنوار الحي</h1>
                <button onclick="toggleSidebar()" class="lg:hidden text-white">
                    <span class="text-xl">✕</span>
                </button>
            </div>

            <nav class="mt-5 px-2 space-y-1 h-full overflow-y-auto pb-20">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    لوحة التحكم
                </a>
                <a href="pos-system.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🛒</span>
                    نظام نقاط البيع
                </a>
                <a href="quick-sale.html" class="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">
                    <span class="ml-3">⚡</span>
                    البيع السريع
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="cash-registers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">💰</span>
                    الصناديق
                </a>
                <a href="reports.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📈</span>
                    التقارير
                </a>

                <!-- User Info & Logout -->
                <div class="absolute bottom-4 left-2 right-2">
                    <div class="bg-gray-50 rounded-lg p-3 mb-2">
                        <p id="userFullName" class="text-sm font-medium text-gray-900">كاشير</p>
                        <p id="userRole" class="text-xs text-gray-500">cashier</p>
                    </div>
                    <button onclick="logout()" class="w-full text-right px-3 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50 flex items-center">
                        <span class="ml-3">🚪</span>
                        تسجيل الخروج
                    </button>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 overflow-hidden">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <button onclick="toggleSidebar()" class="lg:hidden text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">☰</span>
                            </button>
                            <span class="text-lg font-semibold text-gray-900">البيع السريع</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-500" id="currentDateTime"></span>
                            <button onclick="openCashRegister()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center">
                                <span class="ml-2">💰</span>
                                فتح الصندوق
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Branch and Cash Register Info -->
                <div class="bg-white rounded-xl shadow-lg p-4 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <!-- Branch Info -->
                        <div class="flex items-center">
                            <span class="text-2xl ml-3">🏢</span>
                            <div>
                                <p class="text-sm text-gray-600">الفرع</p>
                                <p id="currentBranch" class="font-semibold text-gray-900">الفرع الرئيسي</p>
                            </div>
                        </div>

                        <!-- Cash Register Info -->
                        <div class="flex items-center">
                            <span class="text-2xl ml-3">💰</span>
                            <div>
                                <p class="text-sm text-gray-600">الصندوق النشط</p>
                                <p id="activeCashRegister" class="font-semibold text-gray-900">صندوق رقم 1</p>
                            </div>
                        </div>

                        <!-- Current Balance -->
                        <div class="flex items-center">
                            <span class="text-2xl ml-3">💵</span>
                            <div>
                                <p class="text-sm text-gray-600">الرصيد الحالي</p>
                                <p id="currentBalance" class="font-semibold text-green-600"><span id="currentBalanceValue">0.00</span> <span class="currency-symbol"></span></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Products Section -->
                    <div class="lg:col-span-2">
                        <!-- Search and Categories -->
                        <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                            <div class="flex flex-col md:flex-row gap-4 mb-4">
                                <div class="flex-1">
                                    <input type="text" id="productSearch" placeholder="البحث عن المنتجات..."
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                           onkeyup="searchProducts()">
                                </div>
                                <div>
                                    <select id="categoryFilter" class="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="filterByCategory()">
                                        <option value="">جميع الفئات</option>
                                        <option value="bread">خبز</option>
                                        <option value="pastry">معجنات</option>
                                        <option value="cake">كيك</option>
                                        <option value="drinks">مشروبات</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Products Grid -->
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                    <span class="ml-2">🛍️</span>
                                    المنتجات المتاحة
                                </h3>
                                <button onclick="reloadProducts()"
                                        class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm flex items-center">
                                    <span class="ml-1">🔄</span>
                                    إعادة تحميل
                                </button>
                            </div>
                            <div id="productsGrid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                <!-- Products will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- Cart and Calculator Section -->
                    <div class="lg:col-span-1">
                        <!-- Cart -->
                        <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center justify-between">
                                <span class="flex items-center">
                                    <span class="ml-2">🛒</span>
                                    سلة المشتريات
                                </span>
                                <button onclick="clearCart()" class="text-red-600 hover:text-red-800 text-sm">
                                    مسح الكل
                                </button>
                            </h3>
                            <div id="cartItems" class="space-y-3 mb-4 max-h-64 overflow-y-auto">
                                <!-- Cart items will be populated here -->
                            </div>

                            <!-- Total -->
                            <div class="total-display rounded-lg p-4 text-white">
                                <div class="flex justify-between items-center">
                                    <span class="text-lg font-medium">الإجمالي:</span>
                                    <span id="totalAmount" class="text-2xl font-bold">0.00 <span class="currency-symbol"></span></span>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Calculator -->
                        <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <span class="ml-2">🧮</span>
                                حاسبة سريعة
                            </h3>
                            <div class="grid grid-cols-3 gap-2">
                                <button onclick="addToCalculator('7')" class="calculator-btn bg-gray-100 hover:bg-gray-200 rounded-lg font-semibold">7</button>
                                <button onclick="addToCalculator('8')" class="calculator-btn bg-gray-100 hover:bg-gray-200 rounded-lg font-semibold">8</button>
                                <button onclick="addToCalculator('9')" class="calculator-btn bg-gray-100 hover:bg-gray-200 rounded-lg font-semibold">9</button>
                                <button onclick="addToCalculator('4')" class="calculator-btn bg-gray-100 hover:bg-gray-200 rounded-lg font-semibold">4</button>
                                <button onclick="addToCalculator('5')" class="calculator-btn bg-gray-100 hover:bg-gray-200 rounded-lg font-semibold">5</button>
                                <button onclick="addToCalculator('6')" class="calculator-btn bg-gray-100 hover:bg-gray-200 rounded-lg font-semibold">6</button>
                                <button onclick="addToCalculator('1')" class="calculator-btn bg-gray-100 hover:bg-gray-200 rounded-lg font-semibold">1</button>
                                <button onclick="addToCalculator('2')" class="calculator-btn bg-gray-100 hover:bg-gray-200 rounded-lg font-semibold">2</button>
                                <button onclick="addToCalculator('3')" class="calculator-btn bg-gray-100 hover:bg-gray-200 rounded-lg font-semibold">3</button>
                                <button onclick="addToCalculator('0')" class="calculator-btn bg-gray-100 hover:bg-gray-200 rounded-lg font-semibold col-span-2">0</button>
                                <button onclick="addToCalculator('.')" class="calculator-btn bg-gray-100 hover:bg-gray-200 rounded-lg font-semibold">.</button>
                                <button onclick="clearCalculator()" class="calculator-btn bg-red-500 hover:bg-red-600 text-white rounded-lg font-semibold col-span-2">مسح</button>
                                <button onclick="addCustomAmount()" class="calculator-btn bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-semibold">إضافة</button>
                            </div>
                            <input type="text" id="calculatorDisplay" readonly
                                   class="w-full mt-4 px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-center text-xl font-bold"
                                   placeholder="0">
                        </div>

                        <!-- Cash Payment -->
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <span class="ml-2">💵</span>
                                الدفع النقدي
                            </h3>

                            <!-- Cash Register Selection -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">الصندوق</label>
                                <select id="cashRegisterSelect"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                                    <option value="">اختر الصندوق</option>
                                </select>
                            </div>

                            <!-- Process Payment Button -->
                            <button id="processPaymentBtn" onclick="processPayment()"
                                    class="w-full bg-green-600 hover:bg-green-700 text-white py-4 rounded-lg font-semibold flex items-center justify-center">
                                <span class="ml-2">💵</span>
                                إتمام الدفع النقدي
                            </button>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Include currency manager first -->
    <script src="currency-manager.js"></script>
    <script src="update-all-currencies.js"></script>

    <script src="quick-sale.js"></script>
    <script src="activate-printing-system.js"></script>
</body>
</html>
