// Company Settings Management System
let companyData = {};

// Default company data
const defaultCompanyData = {
    companyNameAr: 'مخبز أنوار الحي',
    companyNameEn: 'Anwar Bakery',
    companySlogan: 'نظام إدارة المخبز المتكامل',
    businessType: 'bakery',
    phone: '+966501234567',
    email: '<EMAIL>',
    address: 'الرياض، المملكة العربية السعودية',
    city: 'الرياض',
    country: 'السعودية',
    commercialRegister: '',
    taxNumber: '',
    workingHours: '6:00 ص - 12:00 م',
    workingDays: 'السبت - الخميس',
    currency: 'SAR',
    currencySymbol: 'ر.س',
    taxRate: 15,
    notes: '',
    logo: null,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
};

// Check authentication
function checkAuth() {
    const session = localStorage.getItem('anwar_bakery_session') ||
                   sessionStorage.getItem('anwar_bakery_session');

    if (!session) {
        window.location.href = 'login.html';
        return null;
    }

    return JSON.parse(session);
}

// Load user info
function loadUserInfo() {
    const session = checkAuth();
    if (session) {
        const userFullNameElement = document.getElementById('userFullName');
        const userRoleElement = document.getElementById('userRole');

        if (userFullNameElement) userFullNameElement.textContent = session.fullName;
        if (userRoleElement) userRoleElement.textContent = session.role;
    }
}

// Load company data
function loadCompanyData() {
    const savedData = localStorage.getItem('anwar_bakery_company');
    companyData = savedData ? JSON.parse(savedData) : { ...defaultCompanyData };

    // Ensure all required fields exist
    Object.keys(defaultCompanyData).forEach(key => {
        if (companyData[key] === undefined) {
            companyData[key] = defaultCompanyData[key];
        }
    });

    // Fill form fields
    Object.keys(companyData).forEach(key => {
        const element = document.getElementById(key);
        if (element && key !== 'logo') {
            element.value = companyData[key];
        }
    });

    // Load logo if exists
    if (companyData.logo) {
        const logoPreview = document.getElementById('logoPreview');
        if (logoPreview) {
            logoPreview.style.backgroundImage = `url(${companyData.logo})`;
            logoPreview.innerHTML = '';
        }
    }

    // Update sidebar company name
    updateSidebarCompanyName(companyData.companyNameAr);
}

// Update sidebar company name
function updateSidebarCompanyName(name) {
    const sidebarElement = document.getElementById('sidebarCompanyName');
    if (sidebarElement) {
        sidebarElement.textContent = name || 'مخبز أنوار الحي';
    }
}

// Handle logo upload
function handleLogoUpload(event) {
    const file = event.target.files[0];
    if (file) {
        if (file.size > 2 * 1024 * 1024) {
            showMessage('حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت.', 'error');
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            const logoPreview = document.getElementById('logoPreview');
            if (logoPreview) {
                logoPreview.style.backgroundImage = `url(${e.target.result})`;
                logoPreview.innerHTML = '';
            }
        };
        reader.readAsDataURL(file);
    }
}

// Remove logo
function removeLogo() {
    const logoPreview = document.getElementById('logoPreview');
    const logoFile = document.getElementById('logoFile');

    if (logoPreview) {
        logoPreview.style.backgroundImage = '';
        logoPreview.innerHTML = '<span class="text-4xl text-gray-400">🏪</span>';
    }

    if (logoFile) {
        logoFile.value = '';
    }
}

// Save company data
function saveCompanyData() {
    const form = document.getElementById('companyForm');
    if (!form || !form.checkValidity()) {
        if (form) form.reportValidity();
        return;
    }

    const newCompanyData = { ...companyData };

    // Get form data
    const formElements = form.querySelectorAll('input, select, textarea');
    formElements.forEach(element => {
        if (element.id && element.id !== 'logoFile') {
            newCompanyData[element.id] = element.value;
        }
    });

    // معالجة خاصة للعملة - تحديث رمز العملة تلقائ<|im_start|>
    if (newCompanyData.currency) {
        const currencySymbols = {
            'SAR': 'ر.س',
            'YER': 'ر.ي',
            'USD': '$',
            'EUR': '€',
            'AED': 'د.إ',
            'KWD': 'د.ك',
            'QAR': 'ر.ق'
        };

        newCompanyData.currencySymbol = currencySymbols[newCompanyData.currency] || 'ر.س';
    }

    // Get logo data
    const logoPreview = document.getElementById('logoPreview');
    if (logoPreview && logoPreview.style.backgroundImage) {
        const logoUrl = logoPreview.style.backgroundImage.slice(5, -2);
        if (logoUrl && logoUrl !== '') {
            newCompanyData.logo = logoUrl;
        }
    }

    // Update timestamp
    newCompanyData.updatedAt = new Date().toISOString();

    // Save to localStorage
    localStorage.setItem('anwar_bakery_company', JSON.stringify(newCompanyData));
    companyData = newCompanyData;

    // Update sidebar
    updateSidebarCompanyName(newCompanyData.companyNameAr);

    // Show success message
    showMessage('تم حفظ بيانات المنشأة وتطبيقها على كامل النظام بنجاح!', 'success');

    // Trigger update event for other pages
    window.dispatchEvent(new CustomEvent('companyDataUpdated', { detail: newCompanyData }));
}

// Reset form
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
        loadCompanyData();
        showMessage('تم إعادة تعيين البيانات.', 'info');
    }
}

// Get currency symbol
function getCurrencySymbol() {
    return companyData.currencySymbol || 'ر.س';
}

// Get company info
function getCompanyInfo() {
    return companyData;
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    if (!container) return;

    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700',
        'warning': 'bg-yellow-50 border-yellow-200 text-yellow-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (sidebar && overlay) {
        if (sidebar.classList.contains('translate-x-full')) {
            sidebar.classList.remove('translate-x-full');
            overlay.classList.remove('hidden');
        } else {
            sidebar.classList.add('translate-x-full');
            overlay.classList.add('hidden');
        }
    }
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_session');
        sessionStorage.removeItem('anwar_bakery_session');
        window.location.href = 'login.html';
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadUserInfo();
    loadCompanyData();

    // Close sidebar when clicking overlay
    const overlay = document.getElementById('mobileOverlay');
    if (overlay) {
        overlay.addEventListener('click', toggleSidebar);
    }
});

// Listen for company data updates from other pages
window.addEventListener('companyDataUpdated', function(event) {
    companyData = event.detail;
    updateSidebarCompanyName(companyData.companyNameAr);
});
