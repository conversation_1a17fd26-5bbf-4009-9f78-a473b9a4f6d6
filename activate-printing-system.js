// Activate Printing System - تفعيل نظام الطباعة الشامل
// This file ensures all printing functions work across the application

(function() {
    'use strict';

    console.log('🖨️ Activating comprehensive printing system...');

    // Ensure advanced print is loaded
    if (typeof window.advancedPrint === 'undefined') {
        console.warn('⚠️ Advanced print not loaded, creating fallback...');
        
        // Create basic print fallback
        window.advancedPrint = {
            printInvoice: function(invoiceData, options = {}) {
                console.log('📄 Printing invoice:', invoiceData);
                
                // Create print content
                const printContent = generateBasicInvoicePrint(invoiceData, options);
                
                // Open print window
                const printWindow = window.open('', '_blank', 'width=800,height=600');
                if (printWindow) {
                    printWindow.document.write(printContent);
                    printWindow.document.close();
                    printWindow.focus();
                    setTimeout(() => printWindow.print(), 500);
                } else {
                    alert('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة.');
                }
            },
            
            printReceipt: function(invoiceData, options = {}) {
                console.log('🧾 Printing receipt:', invoiceData);
                
                // Create receipt content
                const receiptContent = generateBasicReceiptPrint(invoiceData, options);
                
                // Open print window
                const printWindow = window.open('', '_blank', 'width=400,height=600');
                if (printWindow) {
                    printWindow.document.write(receiptContent);
                    printWindow.document.close();
                    printWindow.focus();
                    setTimeout(() => printWindow.print(), 500);
                } else {
                    alert('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة.');
                }
            }
        };
    }

    // Generate basic invoice print HTML
    function generateBasicInvoicePrint(invoiceData, options) {
        const company = getCompanyInfo();
        const currency = company.currency || 'ر.س';
        
        return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة ${invoiceData.number}</title>
            <style>
                @page { size: A4; margin: 20mm; }
                body { 
                    font-family: 'Arial', 'Tahoma', sans-serif; 
                    font-size: 12px; 
                    line-height: 1.4; 
                    direction: rtl; 
                    text-align: right;
                    margin: 0;
                    padding: 0;
                }
                .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
                .company-name { font-size: 24px; font-weight: bold; color: #2563eb; margin-bottom: 10px; }
                .invoice-title { font-size: 20px; font-weight: bold; color: #dc2626; text-align: center; margin: 20px 0; }
                .invoice-info { display: flex; justify-content: space-between; margin-bottom: 30px; }
                .info-section { flex: 1; }
                .info-row { margin-bottom: 8px; }
                .label { font-weight: bold; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                th { background: #f5f5f5; font-weight: bold; }
                .totals { margin-left: auto; width: 300px; margin-top: 20px; }
                .total-row { display: flex; justify-content: space-between; padding: 8px; border-bottom: 1px solid #ddd; }
                .final-total { background: #f5f5f5; font-weight: bold; }
                .footer { margin-top: 50px; text-align: center; font-size: 10px; color: #666; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="company-name">${company.companyNameAr || 'مخبز أنوار الحي'}</div>
                <div>${company.address || ''}</div>
                <div>${company.phone || ''}</div>
            </div>
            
            <div class="invoice-title">${getInvoiceTitle(invoiceData.type)}</div>
            
            <div class="invoice-info">
                <div class="info-section">
                    <div class="info-row"><span class="label">رقم الفاتورة:</span> ${invoiceData.number}</div>
                    <div class="info-row"><span class="label">التاريخ:</span> ${formatDate(invoiceData.date)}</div>
                    <div class="info-row"><span class="label">الوقت:</span> ${formatTime(new Date())}</div>
                </div>
                <div class="info-section">
                    <div class="info-row"><span class="label">${invoiceData.type === 'purchase' ? 'المورد' : 'العميل'}:</span> ${invoiceData.supplier_name || invoiceData.customer_name || 'نقدي'}</div>
                    <div class="info-row"><span class="label">طريقة الدفع:</span> ${getPaymentMethodName(invoiceData.payment_method)}</div>
                </div>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>م</th>
                        <th>الصنف</th>
                        <th>الكمية</th>
                        <th>الوحدة</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    ${invoiceData.items.map((item, index) => `
                        <tr>
                            <td>${index + 1}</td>
                            <td style="text-align: right;">${item.name}</td>
                            <td>${formatNumber(item.quantity)}</td>
                            <td>${item.unit || 'قطعة'}</td>
                            <td>${formatCurrency(item.price, currency)}</td>
                            <td>${formatCurrency(item.total, currency)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
            
            <div class="totals">
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>${formatCurrency(invoiceData.subtotal, currency)}</span>
                </div>
                ${invoiceData.tax > 0 ? `
                <div class="total-row">
                    <span>ضريبة القيمة المضافة (15%):</span>
                    <span>${formatCurrency(invoiceData.tax, currency)}</span>
                </div>
                ` : ''}
                <div class="total-row final-total">
                    <span>الإجمالي النهائي:</span>
                    <span>${formatCurrency(invoiceData.total, currency)}</span>
                </div>
            </div>
            
            ${invoiceData.notes ? `<div style="margin-top: 20px;"><strong>ملاحظات:</strong> ${invoiceData.notes}</div>` : ''}
            
            <div class="footer">
                <div>شكراً لتعاملكم معنا</div>
                <div>تاريخ الطباعة: ${formatDateTime(new Date())}</div>
            </div>
            
            <script>
                window.onload = function() {
                    setTimeout(function() { window.print(); }, 500);
                };
            </script>
        </body>
        </html>
        `;
    }

    // Generate basic receipt print HTML
    function generateBasicReceiptPrint(invoiceData, options) {
        const company = getCompanyInfo();
        const currency = company.currency || 'ر.س';
        
        return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>إيصال ${invoiceData.number}</title>
            <style>
                @page { size: 80mm auto; margin: 0; }
                body { 
                    font-family: 'Courier New', monospace; 
                    font-size: 12px; 
                    margin: 0; 
                    padding: 10px; 
                    width: 80mm;
                    direction: rtl;
                }
                .center { text-align: center; }
                .bold { font-weight: bold; }
                .line { border-top: 1px dashed #000; margin: 5px 0; }
                .item { display: flex; justify-content: space-between; margin: 2px 0; }
                .total { font-size: 14px; font-weight: bold; }
                .small { font-size: 10px; color: #666; }
            </style>
        </head>
        <body>
            <div class="center bold">${company.companyNameAr || 'مخبز أنوار الحي'}</div>
            <div class="center">${company.phone || ''}</div>
            <div class="line"></div>
            <div>رقم الفاتورة: ${invoiceData.number}</div>
            <div>التاريخ: ${formatDateTime(new Date())}</div>
            <div class="line"></div>
            ${invoiceData.items.map(item => `
                <div class="item">
                    <span>${item.name}</span>
                    <span>${formatCurrency(item.total, currency)}</span>
                </div>
                <div class="small">${item.quantity} × ${formatCurrency(item.price, currency)}</div>
            `).join('')}
            <div class="line"></div>
            <div class="item total">
                <span>الإجمالي:</span>
                <span>${formatCurrency(invoiceData.total, currency)}</span>
            </div>
            <div class="line"></div>
            <div class="center">شكراً لزيارتكم</div>
            <script>
                window.onload = function() {
                    setTimeout(function() { window.print(); }, 500);
                };
            </script>
        </body>
        </html>
        `;
    }

    // Helper functions
    function getCompanyInfo() {
        try {
            return JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
        } catch {
            return {};
        }
    }

    function getInvoiceTitle(type) {
        const titles = {
            'sales': 'فاتورة مبيعات',
            'purchase': 'فاتورة مشتريات',
            'sales_return': 'فاتورة مرتجع مبيعات',
            'purchase_return': 'فاتورة مرتجع مشتريات'
        };
        return titles[type] || 'فاتورة';
    }

    function getPaymentMethodName(method) {
        const methods = {
            'cash': 'نقدي',
            'credit': 'آجل',
            'bank': 'تحويل بنكي'
        };
        return methods[method] || 'نقدي';
    }

    function formatDate(date) {
        return new Date(date).toLocaleDateString('ar-SA');
    }

    function formatTime(time) {
        return new Date(time).toLocaleTimeString('ar-SA');
    }

    function formatDateTime(datetime) {
        return new Date(datetime).toLocaleString('ar-SA');
    }

    function formatNumber(number) {
        return parseFloat(number).toLocaleString('ar-SA');
    }

    function formatCurrency(amount, currency = 'ر.س') {
        return `${parseFloat(amount).toLocaleString('ar-SA', { minimumFractionDigits: 2 })} ${currency}`;
    }

    // Global print functions
    window.printInvoice = function(invoiceData, options) {
        if (invoiceData) {
            window.advancedPrint.printInvoice(invoiceData, options);
        } else {
            console.warn('No invoice data provided for printing');
        }
    };

    window.printReceipt = function(invoiceData, options) {
        if (invoiceData) {
            window.advancedPrint.printReceipt(invoiceData, options);
        } else {
            console.warn('No invoice data provided for receipt printing');
        }
    };

    // Activate print buttons on all pages
    document.addEventListener('DOMContentLoaded', function() {
        // Find and activate print buttons
        const printButtons = document.querySelectorAll('[onclick*="printInvoice"], [onclick*="printReceipt"]');
        printButtons.forEach(button => {
            button.style.opacity = '1';
            button.disabled = false;
            button.classList.remove('disabled');
        });

        console.log('✅ Printing system activated successfully!');
    });

    console.log('🖨️ Comprehensive printing system loaded and ready!');

})();
