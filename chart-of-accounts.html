<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شجرة الحسابات - نظام إدارة المخبز</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .modal {
            display: none;
        }
        .modal.active {
            display: flex;
        }
        .main-tab-content {
            display: none;
        }
        .main-tab-content.active {
            display: block;
        }
        .account-tree {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 400px;
        }
        .account-level-0 { margin-right: 0; }
        .account-level-1 { margin-right: 20px; }
        .account-level-2 { margin-right: 40px; }
        .account-level-3 { margin-right: 60px; }
        .account-level-4 { margin-right: 80px; }

        /* Custom scrollbar for tree container */
        .tree-container {
            height: 400px;
            overflow-y: scroll;
            overflow-x: hidden;
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 #f7fafc;
        }

        /* Ensure main content scrolls */
        main {
            height: calc(100vh - 4rem);
            overflow-y: auto;
        }

        /* Sidebar scrolling */
        nav {
            max-height: calc(100vh - 200px);
            display: flex;
            flex-direction: column;
        }

        /* User info at bottom */
        .mt-auto {
            margin-top: auto;
        }

        .tree-container::-webkit-scrollbar {
            width: 8px;
        }

        .tree-container::-webkit-scrollbar-track {
            background: #f7fafc;
            border-radius: 4px;
        }

        .tree-container::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 4px;
        }

        .tree-container::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        /* Account hover effects */
        .account-item {
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .account-item:hover {
            background-color: #f8fafc;
            transform: translateX(-2px);
            border-color: #e2e8f0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .account-item:hover .action-buttons {
            opacity: 1 !important;
        }

        /* Default account styling */
        .default-account {
            background-color: #eff6ff;
            border-left: 4px solid #3b82f6;
        }

        .default-account:hover {
            background-color: #dbeafe;
        }

        /* Action buttons */
        .action-buttons {
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        /* Tree lines */
        .tree-line {
            border-left: 2px solid #e5e7eb;
            padding-left: 1rem;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Mobile menu overlay -->
    <div id="mobileOverlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 lg:hidden hidden"></div>

    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform translate-x-full lg:translate-x-0 lg:static lg:inset-0 sidebar-transition">
            <div class="h-16 px-4 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between">
                <h1 id="sidebarCompanyName" class="text-white text-lg font-bold">مخبز أنوار الحي</h1>
                <button onclick="toggleSidebar()" class="lg:hidden text-white">
                    <span class="text-xl">✕</span>
                </button>
            </div>

            <nav class="mt-5 px-2 space-y-1 h-full overflow-y-auto pb-20">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    لوحة التحكم
                </a>
                <a href="users-management.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👥</span>
                    إدارة المستخدمين
                </a>
                <a href="company-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏢</span>
                    بيانات المنشأة
                </a>
                <a href="system-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">⚙️</span>
                    إعدادات النظام
                </a>
                <a href="system-admin.html" class="flex items-center px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 hover:text-red-700 rounded-md border border-red-200">
                    <span class="ml-3">🔧</span>
                    إدارة النظام المتقدمة
                </a>
                <a href="branches-warehouses.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏪</span>
                    الفروع والمخازن
                </a>
                <a href="units.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📏</span>
                    وحدات القياس
                </a>
                <a href="chart-of-accounts.html" class="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">
                    <span class="ml-3">🌳</span>
                    شجرة الحسابات
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="damage-entry.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🗑️</span>
                    قيد التالف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🚚</span>
                    الموردين
                </a>
                <a href="employees.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👨‍💼</span>
                    الموظفين
                </a>
                <a href="cash-registers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">💰</span>
                    الصناديق
                </a>
                <a href="banks.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏦</span>
                    البنوك
                </a>
                <a href="owners.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👑</span>
                    الملاك
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="vouchers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📋</span>
                    السندات
                </a>
                <a href="journal-entry.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📝</span>
                    القيود اليومية
                </a>
                <a href="inventory.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📊</span>
                    المخزون
                </a>
                <a href="reports.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📈</span>
                    التقارير
                </a>

                <!-- User Info & Logout -->
                <div class="absolute bottom-4 left-2 right-2">
                    <div class="bg-gray-50 rounded-lg p-3 mb-2">
                        <p id="userFullName" class="text-sm font-medium text-gray-900">مدير النظام</p>
                        <p id="userRole" class="text-xs text-gray-500">admin</p>
                    </div>
                    <button onclick="logout()" class="w-full text-right px-3 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50 flex items-center">
                        <span class="ml-3">🚪</span>
                        تسجيل الخروج
                    </button>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col min-h-0">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <button onclick="toggleSidebar()" class="lg:hidden text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">☰</span>
                            </button>
                            <span class="text-lg font-semibold text-gray-900">شجرة الحسابات المحاسبية</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-500" id="currentDateTime"></span>
                            <div class="flex items-center space-x-2">
                                <button onclick="openAddAccountModal()" class="bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700 flex items-center text-sm">
                                    <span class="ml-1">➕</span>
                                    إضافة حساب
                                </button>
                                <button onclick="exportAccountsToExcel()" class="bg-green-600 text-white px-3 py-2 rounded-md hover:bg-green-700 flex items-center text-sm">
                                    <span class="ml-1">📊</span>
                                    تصدير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2 flex items-center">
                        <span class="ml-3">🌳</span>
                        شجرة الحسابات المحاسبية
                    </h1>
                    <p class="text-gray-600">
                        إدارة شاملة لشجرة الحسابات المحاسبية مع ربط الفروع والعمليات المالية
                    </p>
                </div>

                <!-- Success/Error Messages -->
                <div id="messageContainer" class="mb-6"></div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-lg p-4 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100 text-xs">إجمالي الحسابات</p>
                                <p class="text-2xl font-bold" id="totalAccounts">0</p>
                            </div>
                            <div class="text-2xl opacity-80">🌳</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg p-4 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-100 text-xs">الأصول</p>
                                <p class="text-2xl font-bold" id="assetsCount">0</p>
                            </div>
                            <div class="text-2xl opacity-80">📈</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-red-500 to-red-600 rounded-xl shadow-lg p-4 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-red-100 text-xs">الخصوم</p>
                                <p class="text-2xl font-bold" id="liabilitiesCount">0</p>
                            </div>
                            <div class="text-2xl opacity-80">📉</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl shadow-lg p-4 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-100 text-xs">الإيرادات</p>
                                <p class="text-2xl font-bold" id="revenuesCount">0</p>
                            </div>
                            <div class="text-2xl opacity-80">💰</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl shadow-lg p-4 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-orange-100 text-xs">المصروفات</p>
                                <p class="text-2xl font-bold" id="expensesCount">0</p>
                            </div>
                            <div class="text-2xl opacity-80">💸</div>
                        </div>
                    </div>
                </div>

                <!-- Tabs -->
                <div class="mb-6">
                    <div class="border-b border-gray-200">
                        <nav class="-mb-px flex space-x-8">
                            <button onclick="switchMainTab('tree')" class="main-tab-button py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
                                🌳 عرض الشجرة
                            </button>
                            <button onclick="switchMainTab('list')" class="main-tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                                📋 عرض القائمة
                            </button>
                            <button onclick="switchMainTab('balances')" class="main-tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                                ⚖️ الأرصدة
                            </button>
                        </nav>
                    </div>
                </div>

                <!-- Tree View Tab -->
                <div id="treeTab" class="main-tab-content active">
                    <!-- Tree filters -->
                    <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <input type="text" id="treeSearchInput" placeholder="البحث في الحسابات..."
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                       onkeyup="filterAccountsTree()">
                            </div>
                            <div>
                                <select id="accountTypeFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterAccountsTree()">
                                    <option value="">جميع الأنواع</option>
                                    <option value="assets">الأصول</option>
                                    <option value="liabilities">الخصوم</option>
                                    <option value="equity">حقوق الملكية</option>
                                    <option value="revenues">الإيرادات</option>
                                    <option value="expenses">المصروفات</option>
                                </select>
                            </div>
                            <div>
                                <select id="accountLevelFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterAccountsTree()">
                                    <option value="">جميع المستويات</option>
                                    <option value="0">المستوى الأول</option>
                                    <option value="1">المستوى الثاني</option>
                                    <option value="2">المستوى الثالث</option>
                                    <option value="3">المستوى الرابع</option>
                                    <option value="4">المستوى الخامس</option>
                                </select>
                            </div>
                            <div>
                                <button onclick="expandAllAccounts()" class="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center justify-center">
                                    <span class="ml-2">🔽</span>
                                    توسيع الكل
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Accounts Tree -->
                    <div class="bg-white rounded-xl shadow-lg">
                        <div class="p-4 border-b border-gray-200">
                            <div class="flex justify-between items-center">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">🌳 شجرة الحسابات</h3>
                                    <p class="text-sm text-gray-600 mt-1">انقر على الحساب لعرض التفاصيل • انقر على السهم للتوسيع/الطي</p>
                                </div>
                                <div class="flex space-x-2">
                                    <button onclick="debugAccountBalances()" class="bg-red-600 text-white px-3 py-2 rounded-lg hover:bg-red-700 text-sm">
                                        🔍 فحص الأرصدة
                                    </button>
                                    <button onclick="loadAccounts()" class="bg-purple-600 text-white px-3 py-2 rounded-lg hover:bg-purple-700 text-sm">
                                        🔄 إعادة تحميل
                                    </button>
                                    <button onclick="collapseAllAccounts()" class="bg-gray-100 text-gray-700 px-3 py-2 rounded-lg hover:bg-gray-200 text-sm">
                                        ▶️ طي الكل
                                    </button>
                                    <button onclick="openAddAccountModal()" class="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 text-sm">
                                        ➕ إضافة حساب
                                    </button>
                                    <button onclick="exportAccountsToExcel()" class="bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 text-sm">
                                        📊 تصدير Excel
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="p-4 tree-container">
                            <div id="accountsTree" class="account-tree space-y-1">
                                <!-- Tree will be populated here -->
                            </div>
                        </div>

                        <!-- Legend -->
                        <div class="p-4 bg-gray-50 border-t border-gray-200">
                            <div class="flex flex-wrap gap-4 text-xs">
                                <div class="flex items-center">
                                    <span class="w-3 h-3 bg-blue-100 rounded-full ml-2"></span>
                                    <span class="text-gray-600">حساب افتراضي</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-blue-600 ml-2">🔽</span>
                                    <span class="text-gray-600">قابل للتوسيع</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-blue-600 ml-2">▶️</span>
                                    <span class="text-gray-600">مطوي</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-blue-600 ml-2">📄</span>
                                    <span class="text-gray-600">حساب نهائي</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="chart-of-accounts.js"></script>
</body>
</html>
