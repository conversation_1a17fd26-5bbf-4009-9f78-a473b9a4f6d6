<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وتشخيص وحدات القياس - مخبز أنوار</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .log-entry { margin: 5px 0; padding: 8px; border-radius: 4px; font-family: monospace; }
        .log-success { background-color: #d4edda; color: #155724; }
        .log-error { background-color: #f8d7da; color: #721c24; }
        .log-warning { background-color: #fff3cd; color: #856404; }
        .log-info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-6 text-center">
                🔧 اختبار وتشخيص وحدات القياس
            </h1>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Test Controls -->
                <div class="bg-blue-50 rounded-lg p-4">
                    <h2 class="text-xl font-semibold text-blue-900 mb-4">اختبارات النظام</h2>
                    <div class="space-y-2">
                        <button onclick="testDatabaseConnection()" class="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                            اختبار اتصال قاعدة البيانات
                        </button>
                        <button onclick="testUnitsAPI()" class="w-full bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                            اختبار API وحدات القياس
                        </button>
                        <button onclick="testCreateUnit()" class="w-full bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700">
                            اختبار إنشاء وحدة جديدة
                        </button>
                        <button onclick="testLocalStorage()" class="w-full bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                            اختبار localStorage
                        </button>
                        <button onclick="clearLogs()" class="w-full bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                            مسح السجل
                        </button>
                    </div>
                </div>

                <!-- Quick Unit Creator -->
                <div class="bg-green-50 rounded-lg p-4">
                    <h2 class="text-xl font-semibold text-green-900 mb-4">إنشاء وحدة سريع</h2>
                    <div class="space-y-3">
                        <input type="text" id="quickUnitName" placeholder="اسم الوحدة" class="w-full px-3 py-2 border rounded">
                        <select id="quickUnitType" class="w-full px-3 py-2 border rounded">
                            <option value="">نوع الوحدة</option>
                            <option value="weight">وزن</option>
                            <option value="volume">حجم</option>
                            <option value="piece">قطعة</option>
                        </select>
                        <input type="text" id="quickLargeUnit" placeholder="الوحدة الكبيرة" class="w-full px-3 py-2 border rounded">
                        <input type="text" id="quickSmallUnit" placeholder="الوحدة الصغيرة" class="w-full px-3 py-2 border rounded">
                        <input type="number" id="quickSmallCount" placeholder="عدد الوحدة الصغيرة" class="w-full px-3 py-2 border rounded">
                        <button onclick="createQuickUnit()" class="w-full bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                            إنشاء الوحدة
                        </button>
                    </div>
                </div>
            </div>

            <!-- Log Display -->
            <div class="mt-8">
                <h3 class="text-lg font-semibold mb-4">سجل الاختبارات</h3>
                <div id="logContainer" class="bg-gray-50 rounded-lg p-4 h-96 overflow-y-auto border">
                    <div class="log-entry log-info">جاهز لبدء الاختبارات...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const container = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(entry);
            container.scrollTop = container.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '<div class="log-entry log-info">تم مسح السجل...</div>';
        }

        async function testDatabaseConnection() {
            log('🔄 اختبار اتصال قاعدة البيانات...', 'info');
            
            try {
                const response = await fetch('api/sync-manager.php?action=check-connection', {
                    method: 'POST'
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        log('✅ اتصال قاعدة البيانات نشط', 'success');
                        log(`📊 معلومات الخادم: ${result.data.message}`, 'info');
                    } else {
                        log(`❌ فشل اتصال قاعدة البيانات: ${result.error}`, 'error');
                    }
                } else {
                    log(`❌ خطأ HTTP: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ خطأ في الشبكة: ${error.message}`, 'error');
            }
        }

        async function testUnitsAPI() {
            log('🔄 اختبار API وحدات القياس...', 'info');
            
            try {
                // Test GET all units
                const response = await fetch('api/units.php');
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        log(`✅ تم جلب ${result.data.units.length} وحدة من قاعدة البيانات`, 'success');
                        
                        // Show first few units
                        result.data.units.slice(0, 3).forEach(unit => {
                            log(`📏 ${unit.unitName} (${unit.largeUnitName} → ${unit.smallUnitName})`, 'info');
                        });
                    } else {
                        log(`❌ فشل جلب الوحدات: ${result.error}`, 'error');
                    }
                } else {
                    log(`❌ خطأ HTTP: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ خطأ في API: ${error.message}`, 'error');
            }
        }

        async function testCreateUnit() {
            log('🔄 اختبار إنشاء وحدة جديدة...', 'info');
            
            const testUnit = {
                unitName: 'وحدة اختبار ' + Date.now(),
                unitType: 'piece',
                category: 'general',
                largeUnitName: 'صندوق',
                largeUnitCount: 1,
                smallUnitName: 'قطعة',
                smallUnitCount: 12,
                description: 'وحدة اختبار تلقائية'
            };

            try {
                const response = await fetch('api/units.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testUnit)
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        log(`✅ تم إنشاء وحدة جديدة برقم: ${result.data.unit_id}`, 'success');
                        log(`📏 ${testUnit.unitName}: ${testUnit.largeUnitName} = ${testUnit.smallUnitCount} ${testUnit.smallUnitName}`, 'info');
                    } else {
                        log(`❌ فشل إنشاء الوحدة: ${result.error}`, 'error');
                    }
                } else {
                    log(`❌ خطأ HTTP: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ خطأ في إنشاء الوحدة: ${error.message}`, 'error');
            }
        }

        async function createQuickUnit() {
            const unitName = document.getElementById('quickUnitName').value.trim();
            const unitType = document.getElementById('quickUnitType').value;
            const largeUnit = document.getElementById('quickLargeUnit').value.trim();
            const smallUnit = document.getElementById('quickSmallUnit').value.trim();
            const smallCount = parseFloat(document.getElementById('quickSmallCount').value);

            if (!unitName || !unitType || !largeUnit || !smallUnit || !smallCount) {
                log('❌ يرجى ملء جميع الحقول', 'error');
                return;
            }

            log(`🔄 إنشاء وحدة: ${unitName}...`, 'info');

            const unitData = {
                unitName: unitName,
                unitType: unitType,
                category: 'general',
                largeUnitName: largeUnit,
                largeUnitCount: 1,
                smallUnitName: smallUnit,
                smallUnitCount: smallCount,
                description: `وحدة مخصصة: ${largeUnit} = ${smallCount} ${smallUnit}`
            };

            try {
                const response = await fetch('api/units.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(unitData)
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        log(`✅ تم إنشاء ${unitName} بنجاح!`, 'success');
                        
                        // Clear form
                        document.getElementById('quickUnitName').value = '';
                        document.getElementById('quickUnitType').value = '';
                        document.getElementById('quickLargeUnit').value = '';
                        document.getElementById('quickSmallUnit').value = '';
                        document.getElementById('quickSmallCount').value = '';
                    } else {
                        log(`❌ فشل إنشاء الوحدة: ${result.error}`, 'error');
                    }
                } else {
                    log(`❌ خطأ HTTP: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ خطأ في الشبكة: ${error.message}`, 'error');
            }
        }

        function testLocalStorage() {
            log('🔄 اختبار localStorage...', 'info');
            
            try {
                // Check if localStorage is available
                if (typeof(Storage) !== "undefined") {
                    log('✅ localStorage متاح', 'success');
                    
                    // Check for units data
                    const unitsData = localStorage.getItem('anwar_bakery_units');
                    if (unitsData) {
                        const units = JSON.parse(unitsData);
                        log(`📦 يوجد ${units.length} وحدة في localStorage`, 'info');
                    } else {
                        log('⚠️ لا توجد وحدات في localStorage', 'warning');
                    }
                    
                    // Test write/read
                    const testKey = 'test_' + Date.now();
                    localStorage.setItem(testKey, 'test_value');
                    const testValue = localStorage.getItem(testKey);
                    localStorage.removeItem(testKey);
                    
                    if (testValue === 'test_value') {
                        log('✅ اختبار الكتابة/القراءة نجح', 'success');
                    } else {
                        log('❌ فشل اختبار الكتابة/القراءة', 'error');
                    }
                } else {
                    log('❌ localStorage غير متاح', 'error');
                }
            } catch (error) {
                log(`❌ خطأ في localStorage: ${error.message}`, 'error');
            }
        }

        // Auto-run basic tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 بدء الاختبارات التلقائية...', 'info');
            setTimeout(testLocalStorage, 1000);
            setTimeout(testDatabaseConnection, 2000);
            setTimeout(testUnitsAPI, 3000);
        });
    </script>
</body>
</html>
