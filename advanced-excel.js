// Advanced Excel Export System - نظام تصدير Excel المتقدم
// Professional Excel export with formatting, charts, and multiple sheets

class AdvancedExcel {
    constructor() {
        this.loadSheetJS();
        this.companySettings = this.getCompanySettings();
    }

    /**
     * Load SheetJS library
     * تحميل مكتبة SheetJS
     */
    loadSheetJS() {
        if (typeof XLSX === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
            script.onload = () => {
                console.log('SheetJS loaded for advanced Excel features');
            };
            document.head.appendChild(script);
        }
    }

    /**
     * Export financial reports with professional formatting
     * تصدير التقارير المالية بتنسيق احترافي
     */
    exportFinancialReport(reportData, reportType, dateRange = {}) {
        try {
            const wb = XLSX.utils.book_new();
            
            // Main report sheet
            this.addMainReportSheet(wb, reportData, reportType, dateRange);
            
            // Summary sheet
            this.addSummarySheet(wb, reportData, reportType);
            
            // Charts data sheet
            this.addChartsDataSheet(wb, reportData, reportType);
            
            // Details sheet (if applicable)
            if (reportData.details) {
                this.addDetailsSheet(wb, reportData.details, reportType);
            }
            
            // Generate filename
            const filename = this.generateReportFilename(reportType, dateRange);
            
            // Write file
            XLSX.writeFile(wb, filename);
            
            return { success: true, filename };
            
        } catch (error) {
            console.error('Advanced Excel export error:', error);
            return { success: false, message: 'فشل في تصدير التقرير: ' + error.message };
        }
    }

    /**
     * Add main report sheet with professional formatting
     * إضافة ورقة التقرير الرئيسي بتنسيق احترافي
     */
    addMainReportSheet(wb, reportData, reportType, dateRange) {
        const wsData = [];
        
        // Company header
        const company = this.companySettings;
        wsData.push([company.companyNameAr || 'مخبز أنوار الحي']);
        wsData.push([company.address || '']);
        wsData.push([company.phone || '']);
        wsData.push([]); // Empty row
        
        // Report title and date
        wsData.push([this.getReportTitle(reportType)]);
        wsData.push([`الفترة: ${this.formatDateRange(dateRange)}`]);
        wsData.push([`تاريخ التصدير: ${new Date().toLocaleString('ar-SA')}`]);
        wsData.push([]); // Empty row
        
        // Report data
        if (reportData.main && reportData.main.length > 0) {
            // Headers
            const headers = Object.keys(reportData.main[0]);
            wsData.push(headers);
            
            // Data rows
            reportData.main.forEach(row => {
                wsData.push(Object.values(row));
            });
        }
        
        // Create worksheet
        const ws = XLSX.utils.aoa_to_sheet(wsData);
        
        // Apply formatting
        this.applyReportFormatting(ws, wsData, reportType);
        
        // Set column widths
        ws['!cols'] = this.calculateOptimalWidths(wsData);
        
        // Set RTL
        ws['!dir'] = 'rtl';
        
        // Add to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'التقرير الرئيسي');
    }

    /**
     * Add summary sheet with key metrics
     * إضافة ورقة الملخص مع المؤشرات الرئيسية
     */
    addSummarySheet(wb, reportData, reportType) {
        const wsData = [];
        
        // Title
        wsData.push(['ملخص التقرير']);
        wsData.push([]);
        
        // Key metrics based on report type
        const metrics = this.calculateKeyMetrics(reportData, reportType);
        
        metrics.forEach(metric => {
            wsData.push([metric.label, metric.value]);
        });
        
        // Add charts instructions
        wsData.push([]);
        wsData.push(['تعليمات الرسوم البيانية:']);
        wsData.push(['1. انتقل إلى ورقة "بيانات الرسوم البيانية"']);
        wsData.push(['2. حدد البيانات المطلوبة']);
        wsData.push(['3. أدرج رسم بياني من قائمة "إدراج"']);
        
        // Create worksheet
        const ws = XLSX.utils.aoa_to_sheet(wsData);
        
        // Apply summary formatting
        this.applySummaryFormatting(ws, wsData);
        
        ws['!cols'] = this.calculateOptimalWidths(wsData);
        ws['!dir'] = 'rtl';
        
        XLSX.utils.book_append_sheet(wb, ws, 'الملخص');
    }

    /**
     * Add charts data sheet for creating charts in Excel
     * إضافة ورقة بيانات الرسوم البيانية
     */
    addChartsDataSheet(wb, reportData, reportType) {
        const wsData = [];
        
        // Title
        wsData.push(['بيانات الرسوم البيانية']);
        wsData.push([]);
        
        // Prepare chart data based on report type
        const chartData = this.prepareChartData(reportData, reportType);
        
        chartData.forEach(chart => {
            wsData.push([chart.title]);
            wsData.push([]);
            
            // Headers
            wsData.push(chart.headers);
            
            // Data
            chart.data.forEach(row => {
                wsData.push(row);
            });
            
            wsData.push([]); // Separator
        });
        
        // Create worksheet
        const ws = XLSX.utils.aoa_to_sheet(wsData);
        
        ws['!cols'] = this.calculateOptimalWidths(wsData);
        ws['!dir'] = 'rtl';
        
        XLSX.utils.book_append_sheet(wb, ws, 'بيانات الرسوم البيانية');
    }

    /**
     * Add details sheet with transaction-level data
     * إضافة ورقة التفاصيل مع بيانات المعاملات
     */
    addDetailsSheet(wb, detailsData, reportType) {
        const wsData = [];
        
        // Title
        wsData.push(['تفاصيل التقرير']);
        wsData.push([]);
        
        if (detailsData && detailsData.length > 0) {
            // Headers
            const headers = Object.keys(detailsData[0]);
            wsData.push(headers);
            
            // Data
            detailsData.forEach(row => {
                wsData.push(Object.values(row));
            });
        }
        
        // Create worksheet
        const ws = XLSX.utils.aoa_to_sheet(wsData);
        
        // Apply details formatting
        this.applyDetailsFormatting(ws, wsData);
        
        ws['!cols'] = this.calculateOptimalWidths(wsData);
        ws['!dir'] = 'rtl';
        
        XLSX.utils.book_append_sheet(wb, ws, 'التفاصيل');
    }

    /**
     * Export inventory report with stock analysis
     * تصدير تقرير المخزون مع تحليل المخزون
     */
    exportInventoryReport(inventoryData, options = {}) {
        try {
            const wb = XLSX.utils.book_new();
            
            // Current stock sheet
            this.addInventorySheet(wb, inventoryData.current, 'المخزون الحالي');
            
            // Low stock sheet
            if (inventoryData.lowStock) {
                this.addInventorySheet(wb, inventoryData.lowStock, 'المخزون المنخفض');
            }
            
            // Stock movements sheet
            if (inventoryData.movements) {
                this.addMovementsSheet(wb, inventoryData.movements);
            }
            
            // Stock valuation sheet
            if (inventoryData.valuation) {
                this.addValuationSheet(wb, inventoryData.valuation);
            }
            
            const filename = `تقرير_المخزون_${new Date().toISOString().slice(0, 10)}.xlsx`;
            XLSX.writeFile(wb, filename);
            
            return { success: true, filename };
            
        } catch (error) {
            console.error('Inventory report export error:', error);
            return { success: false, message: 'فشل في تصدير تقرير المخزون: ' + error.message };
        }
    }

    /**
     * Export sales analysis with customer insights
     * تصدير تحليل المبيعات مع رؤى العملاء
     */
    exportSalesAnalysis(salesData, period = 'monthly') {
        try {
            const wb = XLSX.utils.book_new();
            
            // Sales summary
            this.addSalesSummarySheet(wb, salesData.summary, period);
            
            // Top products
            this.addTopProductsSheet(wb, salesData.topProducts);
            
            // Customer analysis
            this.addCustomerAnalysisSheet(wb, salesData.customers);
            
            // Daily/Monthly trends
            this.addTrendsSheet(wb, salesData.trends, period);
            
            const filename = `تحليل_المبيعات_${period}_${new Date().toISOString().slice(0, 10)}.xlsx`;
            XLSX.writeFile(wb, filename);
            
            return { success: true, filename };
            
        } catch (error) {
            console.error('Sales analysis export error:', error);
            return { success: false, message: 'فشل في تصدير تحليل المبيعات: ' + error.message };
        }
    }

    /**
     * Export complete business dashboard
     * تصدير لوحة تحكم الأعمال الكاملة
     */
    exportBusinessDashboard(dashboardData) {
        try {
            const wb = XLSX.utils.book_new();
            
            // Executive summary
            this.addExecutiveSummarySheet(wb, dashboardData.summary);
            
            // Financial overview
            this.addFinancialOverviewSheet(wb, dashboardData.financial);
            
            // Operations metrics
            this.addOperationsSheet(wb, dashboardData.operations);
            
            // Performance indicators
            this.addKPISheet(wb, dashboardData.kpis);
            
            const filename = `لوحة_التحكم_${new Date().toISOString().slice(0, 10)}.xlsx`;
            XLSX.writeFile(wb, filename);
            
            return { success: true, filename };
            
        } catch (error) {
            console.error('Dashboard export error:', error);
            return { success: false, message: 'فشل في تصدير لوحة التحكم: ' + error.message };
        }
    }

    // Helper methods
    getCompanySettings() {
        return JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
    }

    getReportTitle(reportType) {
        const titles = {
            'income_statement': 'قائمة الدخل',
            'balance_sheet': 'الميزانية العمومية',
            'trial_balance': 'ميزان المراجعة',
            'sales_report': 'تقرير المبيعات',
            'purchase_report': 'تقرير المشتريات',
            'inventory_report': 'تقرير المخزون',
            'customer_report': 'تقرير العملاء',
            'supplier_report': 'تقرير الموردين'
        };
        return titles[reportType] || 'تقرير';
    }

    formatDateRange(dateRange) {
        if (dateRange.from && dateRange.to) {
            return `من ${dateRange.from} إلى ${dateRange.to}`;
        } else if (dateRange.month) {
            return `شهر ${dateRange.month}`;
        } else if (dateRange.year) {
            return `سنة ${dateRange.year}`;
        }
        return 'جميع الفترات';
    }

    generateReportFilename(reportType, dateRange) {
        const reportName = this.getReportTitle(reportType).replace(/\s+/g, '_');
        const date = new Date().toISOString().slice(0, 10);
        const period = dateRange.month ? `_${dateRange.month}` : dateRange.year ? `_${dateRange.year}` : '';
        return `${reportName}${period}_${date}.xlsx`;
    }

    calculateKeyMetrics(reportData, reportType) {
        // This would be implemented based on specific report type
        const metrics = [];
        
        if (reportType === 'sales_report' && reportData.main) {
            const totalSales = reportData.main.reduce((sum, row) => sum + (parseFloat(row.total) || 0), 0);
            const totalQuantity = reportData.main.reduce((sum, row) => sum + (parseFloat(row.quantity) || 0), 0);
            
            metrics.push(
                { label: 'إجمالي المبيعات', value: totalSales },
                { label: 'إجمالي الكمية', value: totalQuantity },
                { label: 'عدد المعاملات', value: reportData.main.length }
            );
        }
        
        return metrics;
    }

    prepareChartData(reportData, reportType) {
        // Prepare data suitable for creating charts in Excel
        const chartData = [];
        
        if (reportType === 'sales_report' && reportData.main) {
            // Sales by product chart
            const productSales = {};
            reportData.main.forEach(row => {
                const product = row.product || row.name;
                const total = parseFloat(row.total) || 0;
                productSales[product] = (productSales[product] || 0) + total;
            });
            
            chartData.push({
                title: 'المبيعات حسب المنتج',
                headers: ['المنتج', 'المبيعات'],
                data: Object.entries(productSales)
            });
        }
        
        return chartData;
    }

    calculateOptimalWidths(wsData) {
        if (!wsData || wsData.length === 0) return [];
        
        const maxCols = Math.max(...wsData.map(row => row.length));
        const widths = [];
        
        for (let col = 0; col < maxCols; col++) {
            let maxWidth = 10; // Minimum width
            
            wsData.forEach(row => {
                if (row[col] !== undefined && row[col] !== null) {
                    const cellLength = String(row[col]).length;
                    maxWidth = Math.max(maxWidth, cellLength);
                }
            });
            
            widths.push({ wch: Math.min(maxWidth + 2, 50) }); // Max width 50
        }
        
        return widths;
    }

    applyReportFormatting(ws, wsData, reportType) {
        // Apply professional formatting to the worksheet
        // This would include colors, fonts, borders, etc.
        // Implementation depends on the specific requirements
    }

    applySummaryFormatting(ws, wsData) {
        // Apply formatting specific to summary sheets
    }

    applyDetailsFormatting(ws, wsData) {
        // Apply formatting specific to details sheets
    }

    // Additional sheet creation methods would be implemented here...
    addInventorySheet(wb, data, sheetName) { /* Implementation */ }
    addMovementsSheet(wb, data) { /* Implementation */ }
    addValuationSheet(wb, data) { /* Implementation */ }
    addSalesSummarySheet(wb, data, period) { /* Implementation */ }
    addTopProductsSheet(wb, data) { /* Implementation */ }
    addCustomerAnalysisSheet(wb, data) { /* Implementation */ }
    addTrendsSheet(wb, data, period) { /* Implementation */ }
    addExecutiveSummarySheet(wb, data) { /* Implementation */ }
    addFinancialOverviewSheet(wb, data) { /* Implementation */ }
    addOperationsSheet(wb, data) { /* Implementation */ }
    addKPISheet(wb, data) { /* Implementation */ }
}

// Create global instance
window.advancedExcel = new AdvancedExcel();

// Export functions for global use
window.exportFinancialReport = (data, type, dateRange) => window.advancedExcel.exportFinancialReport(data, type, dateRange);
window.exportInventoryReport = (data, options) => window.advancedExcel.exportInventoryReport(data, options);
window.exportSalesAnalysis = (data, period) => window.advancedExcel.exportSalesAnalysis(data, period);
window.exportBusinessDashboard = (data) => window.advancedExcel.exportBusinessDashboard(data);
