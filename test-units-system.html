<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام وحدات القياس - مخبز أنوار</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .test-pass { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .test-fail { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .test-info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-6 text-center">
                🧪 اختبار نظام وحدات القياس
            </h1>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- API Tests -->
                <div class="bg-blue-50 rounded-lg p-4">
                    <h2 class="text-xl font-semibold text-blue-900 mb-4">اختبارات API</h2>
                    <div id="apiTests"></div>
                    <button onclick="runAPITests()" class="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                        تشغيل اختبارات API
                    </button>
                </div>

                <!-- Database Tests -->
                <div class="bg-green-50 rounded-lg p-4">
                    <h2 class="text-xl font-semibold text-green-900 mb-4">اختبارات قاعدة البيانات</h2>
                    <div id="dbTests"></div>
                    <button onclick="runDatabaseTests()" class="mt-4 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                        تشغيل اختبارات قاعدة البيانات
                    </button>
                </div>

                <!-- Conversion Tests -->
                <div class="bg-yellow-50 rounded-lg p-4">
                    <h2 class="text-xl font-semibold text-yellow-900 mb-4">اختبارات التحويل</h2>
                    <div id="conversionTests"></div>
                    <button onclick="runConversionTests()" class="mt-4 bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700">
                        تشغيل اختبارات التحويل
                    </button>
                </div>

                <!-- Sync Tests -->
                <div class="bg-purple-50 rounded-lg p-4">
                    <h2 class="text-xl font-semibold text-purple-900 mb-4">اختبارات المزامنة</h2>
                    <div id="syncTests"></div>
                    <button onclick="runSyncTests()" class="mt-4 bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                        تشغيل اختبارات المزامنة
                    </button>
                </div>
            </div>

            <!-- Run All Tests -->
            <div class="mt-8 text-center">
                <button onclick="runAllTests()" class="bg-gray-800 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-900">
                    🚀 تشغيل جميع الاختبارات
                </button>
            </div>

            <!-- Test Results Summary -->
            <div id="testSummary" class="mt-8 p-4 bg-gray-50 rounded-lg hidden">
                <h3 class="text-lg font-semibold mb-2">ملخص النتائج</h3>
                <div id="summaryContent"></div>
            </div>
        </div>
    </div>

    <script>
        let testResults = {
            passed: 0,
            failed: 0,
            total: 0
        };

        function addTestResult(container, testName, passed, message) {
            const div = document.createElement('div');
            div.className = `test-result ${passed ? 'test-pass' : 'test-fail'}`;
            div.innerHTML = `
                <strong>${passed ? '✅' : '❌'} ${testName}</strong><br>
                <small>${message}</small>
            `;
            document.getElementById(container).appendChild(div);
            
            testResults.total++;
            if (passed) testResults.passed++;
            else testResults.failed++;
        }

        function addTestInfo(container, message) {
            const div = document.createElement('div');
            div.className = 'test-result test-info';
            div.innerHTML = `<strong>ℹ️ ${message}</strong>`;
            document.getElementById(container).appendChild(div);
        }

        async function runAPITests() {
            const container = 'apiTests';
            document.getElementById(container).innerHTML = '';
            
            addTestInfo(container, 'بدء اختبارات API...');

            // Test 1: Get all units
            try {
                const response = await fetch('api/units.php');
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addTestResult(container, 'جلب وحدات القياس', true, `تم جلب ${result.data.units.length} وحدة بنجاح`);
                } else {
                    addTestResult(container, 'جلب وحدات القياس', false, result.error || 'خطأ غير معروف');
                }
            } catch (error) {
                addTestResult(container, 'جلب وحدات القياس', false, error.message);
            }

            // Test 2: Create new unit
            try {
                const testUnit = {
                    unitName: 'وحدة اختبار ' + Date.now(),
                    unitType: 'piece',
                    category: 'general',
                    largeUnitName: 'صندوق',
                    largeUnitCount: 1,
                    smallUnitName: 'قطعة',
                    smallUnitCount: 24,
                    description: 'وحدة اختبار للنظام'
                };

                const response = await fetch('api/units.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testUnit)
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addTestResult(container, 'إنشاء وحدة جديدة', true, `تم إنشاء الوحدة برقم ${result.data.unit_id}`);
                } else {
                    addTestResult(container, 'إنشاء وحدة جديدة', false, result.error || 'خطأ غير معروف');
                }
            } catch (error) {
                addTestResult(container, 'إنشاء وحدة جديدة', false, error.message);
            }

            // Test 3: Get unit types
            try {
                const response = await fetch('api/units.php?action=types');
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addTestResult(container, 'جلب أنواع الوحدات', true, `تم جلب ${result.data.types.length} نوع`);
                } else {
                    addTestResult(container, 'جلب أنواع الوحدات', false, result.error || 'خطأ غير معروف');
                }
            } catch (error) {
                addTestResult(container, 'جلب أنواع الوحدات', false, error.message);
            }
        }

        async function runDatabaseTests() {
            const container = 'dbTests';
            document.getElementById(container).innerHTML = '';
            
            addTestInfo(container, 'بدء اختبارات قاعدة البيانات...');

            // Test database connection
            try {
                const response = await fetch('api/sync-manager.php?action=check-connection', {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addTestResult(container, 'اتصال قاعدة البيانات', true, 'الاتصال نشط ومتاح');
                } else {
                    addTestResult(container, 'اتصال قاعدة البيانات', false, result.error || 'فشل الاتصال');
                }
            } catch (error) {
                addTestResult(container, 'اتصال قاعدة البيانات', false, error.message);
            }

            // Test sync status
            try {
                const response = await fetch('api/sync-manager.php?action=status');
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addTestResult(container, 'حالة المزامنة', true, 'نظام المزامنة يعمل بشكل صحيح');
                } else {
                    addTestResult(container, 'حالة المزامنة', false, result.error || 'خطأ في المزامنة');
                }
            } catch (error) {
                addTestResult(container, 'حالة المزامنة', false, error.message);
            }
        }

        function runConversionTests() {
            const container = 'conversionTests';
            document.getElementById(container).innerHTML = '';
            
            addTestInfo(container, 'بدء اختبارات التحويل...');

            // Test conversion calculations
            const testCases = [
                { large: 1, small: 1000, expected: 1000, name: 'كيلوجرام إلى جرام' },
                { large: 1, small: 50, expected: 50, name: 'صندوق إلى قطعة' },
                { large: 2, small: 100, expected: 50, name: 'تحويل مركب' }
            ];

            testCases.forEach(test => {
                const conversionFactor = test.small / test.large;
                const passed = conversionFactor === test.expected;
                addTestResult(container, test.name, passed, 
                    `معامل التحويل: ${conversionFactor} (متوقع: ${test.expected})`);
            });
        }

        async function runSyncTests() {
            const container = 'syncTests';
            document.getElementById(container).innerHTML = '';
            
            addTestInfo(container, 'بدء اختبارات المزامنة...');

            // Test localStorage sync
            const testData = { units: [{ id: 999, unitName: 'اختبار مزامنة', unitType: 'piece' }] };
            
            try {
                const response = await fetch('api/sync-manager.php?action=sync-to-server', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addTestResult(container, 'مزامنة البيانات', true, 'تم رفع البيانات للخادم بنجاح');
                } else {
                    addTestResult(container, 'مزامنة البيانات', false, result.error || 'فشل المزامنة');
                }
            } catch (error) {
                addTestResult(container, 'مزامنة البيانات', false, error.message);
            }
        }

        async function runAllTests() {
            testResults = { passed: 0, failed: 0, total: 0 };
            
            await runAPITests();
            await runDatabaseTests();
            runConversionTests();
            await runSyncTests();
            
            // Show summary
            const summary = document.getElementById('testSummary');
            const content = document.getElementById('summaryContent');
            
            const successRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
            
            content.innerHTML = `
                <div class="grid grid-cols-3 gap-4 text-center">
                    <div class="bg-green-100 p-3 rounded">
                        <div class="text-2xl font-bold text-green-600">${testResults.passed}</div>
                        <div class="text-sm text-green-700">نجح</div>
                    </div>
                    <div class="bg-red-100 p-3 rounded">
                        <div class="text-2xl font-bold text-red-600">${testResults.failed}</div>
                        <div class="text-sm text-red-700">فشل</div>
                    </div>
                    <div class="bg-blue-100 p-3 rounded">
                        <div class="text-2xl font-bold text-blue-600">${successRate}%</div>
                        <div class="text-sm text-blue-700">معدل النجاح</div>
                    </div>
                </div>
            `;
            
            summary.classList.remove('hidden');
        }
    </script>
</body>
</html>
