<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة النظام المتقدمة - مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .admin-card {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .admin-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #3b82f6;
        }
        .progress-bar {
            background: linear-gradient(90deg, #10b981 0%, #059669 100%);
            transition: width 0.5s ease;
        }
        .danger-zone {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            border: 2px solid #f87171;
        }
        .success-zone {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border: 2px solid #34d399;
        }
        .warning-zone {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #fbbf24;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Mobile menu overlay -->
    <div id="mobileOverlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 lg:hidden hidden"></div>

    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform translate-x-full lg:translate-x-0 lg:static lg:inset-0 sidebar-transition">
            <div class="h-16 px-4 bg-gradient-to-r from-red-600 to-red-700 flex items-center justify-between">
                <h1 id="sidebarCompanyName" class="text-white text-lg font-bold">مخبز أنوار الحي</h1>
                <button onclick="toggleSidebar()" class="lg:hidden text-white">
                    <span class="text-xl">✕</span>
                </button>
            </div>

            <nav class="mt-5 px-2 space-y-1 h-full overflow-y-auto pb-20">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    لوحة التحكم
                </a>
                <a href="system-admin.html" class="flex items-center px-3 py-2 text-sm font-medium text-red-600 bg-red-50 rounded-md">
                    <span class="ml-3">⚙️</span>
                    إدارة النظام المتقدمة
                </a>
                <a href="users-management.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👥</span>
                    إدارة المستخدمين
                </a>
                <a href="company-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏢</span>
                    بيانات المنشأة
                </a>
                <a href="system-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🔧</span>
                    إعدادات النظام
                </a>
                <a href="chart-of-accounts.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🌳</span>
                    شجرة الحسابات
                </a>
                <a href="reports.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📈</span>
                    التقارير
                </a>

                <!-- User Info & Logout -->
                <div class="absolute bottom-4 left-2 right-2">
                    <div class="bg-red-50 rounded-lg p-3 mb-2 border border-red-200">
                        <p id="userFullName" class="text-sm font-medium text-red-900">مدير النظام</p>
                        <p id="userRole" class="text-xs text-red-600">admin</p>
                        <p class="text-xs text-red-500 mt-1">⚠️ وضع المدير المتقدم</p>
                    </div>
                    <button onclick="logout()" class="w-full text-right px-3 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50 flex items-center">
                        <span class="ml-3">🚪</span>
                        تسجيل الخروج
                    </button>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 overflow-hidden">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <button onclick="toggleSidebar()" class="lg:hidden text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">☰</span>
                            </button>
                            <span class="text-lg font-semibold text-gray-900">إدارة النظام المتقدمة</span>
                            <span class="mr-3 px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">وضع المدير</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-500" id="currentDateTime"></span>
                            <div class="flex items-center space-x-2">
                                <button onclick="runSystemDiagnostics()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center">
                                    <span class="ml-2">🔍</span>
                                    فحص النظام
                                </button>
                                <button onclick="createBackup()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center">
                                    <span class="ml-2">💾</span>
                                    نسخة احتياطية
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Warning Banner -->
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div class="flex items-center">
                        <span class="text-red-500 text-xl ml-3">⚠️</span>
                        <div>
                            <h3 class="text-red-800 font-semibold">تحذير: وضع إدارة النظام المتقدمة</h3>
                            <p class="text-red-700 text-sm">هذه الصفحة تحتوي على أدوات متقدمة قد تؤثر على النظام بالكامل. يرجى التأكد من صحة العمليات قبل التنفيذ.</p>
                        </div>
                    </div>
                </div>

                <!-- Success/Error Messages -->
                <div id="messageContainer" class="mb-6"></div>

                <!-- System Status -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-100 text-sm">حالة النظام</p>
                                <p class="text-2xl font-bold" id="systemStatus">سليم</p>
                            </div>
                            <div class="text-3xl opacity-80">✅</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100 text-sm">حجم البيانات</p>
                                <p class="text-2xl font-bold" id="dataSize">2.4 MB</p>
                            </div>
                            <div class="text-3xl opacity-80">💾</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl shadow-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-100 text-sm">آخر نسخة احتياطية</p>
                                <p class="text-lg font-bold" id="lastBackup">اليوم</p>
                            </div>
                            <div class="text-3xl opacity-80">🔄</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl shadow-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-orange-100 text-sm">السنة المالية</p>
                                <p class="text-2xl font-bold" id="fiscalYear">2024</p>
                            </div>
                            <div class="text-3xl opacity-80">📅</div>
                        </div>
                    </div>
                </div>

                <!-- Main Admin Sections -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Financial Year Management -->
                    <div class="admin-card bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
                            <span class="ml-3">📅</span>
                            إدارة السنة المالية
                        </h3>
                        <div class="space-y-4">
                            <div class="bg-blue-50 rounded-lg p-4">
                                <h4 class="font-semibold text-blue-900 mb-2">السنة المالية الحالية</h4>
                                <p class="text-blue-700">من 1 يناير 2024 إلى 31 ديسمبر 2024</p>
                                <div class="mt-2">
                                    <div class="bg-blue-200 rounded-full h-2">
                                        <div class="progress-bar h-2 rounded-full" style="width: 75%"></div>
                                    </div>
                                    <p class="text-xs text-blue-600 mt-1">75% من السنة المالية مكتملة</p>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 gap-3">
                                <button onclick="openYearEndModal()" class="w-full bg-purple-600 text-white py-3 rounded-lg hover:bg-purple-700 flex items-center justify-center">
                                    <span class="ml-2">🔄</span>
                                    ترحيل نهاية السنة
                                </button>
                                <button onclick="openNewYearModal()" class="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 flex items-center justify-center">
                                    <span class="ml-2">🆕</span>
                                    بدء سنة مالية جديدة
                                </button>
                                <button onclick="viewOpeningBalances()" class="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 flex items-center justify-center">
                                    <span class="ml-2">⚖️</span>
                                    عرض الأرصدة الافتتاحية
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Backup & Restore -->
                    <div class="admin-card bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
                            <span class="ml-3">💾</span>
                            النسخ الاحتياطية والاستعادة
                        </h3>
                        <div class="space-y-4">
                            <div class="success-zone rounded-lg p-4">
                                <h4 class="font-semibold text-green-900 mb-2">النسخ الاحتياطية التلقائية</h4>
                                <p class="text-green-700 text-sm">يتم إنشاء نسخة احتياطية تلقائياً كل يوم في الساعة 2:00 صباحاً</p>
                                <div class="mt-2 flex items-center">
                                    <span class="w-2 h-2 bg-green-500 rounded-full ml-2"></span>
                                    <span class="text-green-600 text-sm">مفعل</span>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 gap-3">
                                <button onclick="createManualBackup()" class="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 flex items-center justify-center">
                                    <span class="ml-2">💾</span>
                                    إنشاء نسخة احتياطية يدوية
                                </button>
                                <button onclick="openRestoreModal()" class="w-full bg-orange-600 text-white py-3 rounded-lg hover:bg-orange-700 flex items-center justify-center">
                                    <span class="ml-2">📥</span>
                                    استعادة نسخة احتياطية
                                </button>
                                <button onclick="viewBackupHistory()" class="w-full bg-gray-600 text-white py-3 rounded-lg hover:bg-gray-700 flex items-center justify-center">
                                    <span class="ml-2">📋</span>
                                    سجل النسخ الاحتياطية
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Admin Tools -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-8">
                    <!-- Data Management -->
                    <div class="admin-card bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
                            <span class="ml-2">🗄️</span>
                            إدارة البيانات
                        </h3>
                        <div class="space-y-3">
                            <button onclick="exportAllData()" class="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 text-sm">
                                📊 تصدير جميع البيانات
                            </button>
                            <button onclick="importData()" class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 text-sm">
                                📥 استيراد البيانات
                            </button>
                            <button onclick="cleanupData()" class="w-full bg-yellow-600 text-white py-2 rounded-lg hover:bg-yellow-700 text-sm">
                                🧹 تنظيف البيانات
                            </button>
                        </div>
                    </div>

                    <!-- System Diagnostics -->
                    <div class="admin-card bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
                            <span class="ml-2">🔍</span>
                            تشخيص النظام
                        </h3>
                        <div class="space-y-3">
                            <button onclick="checkDataIntegrity()" class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 text-sm">
                                ✅ فحص سلامة البيانات
                            </button>
                            <button onclick="checkAccountBalance()" class="w-full bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700 text-sm">
                                ⚖️ فحص توازن الحسابات
                            </button>
                            <button onclick="generateSystemReport()" class="w-full bg-gray-600 text-white py-2 rounded-lg hover:bg-gray-700 text-sm">
                                📋 تقرير حالة النظام
                            </button>
                        </div>
                    </div>

                    <!-- Advanced Features -->
                    <div class="admin-card bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
                            <span class="ml-2">🚀</span>
                            الميزات المتقدمة
                        </h3>
                        <div class="space-y-3">
                            <!-- Database Sync -->
                            <div class="border-b pb-3 mb-3">
                                <h4 class="font-semibold text-sm text-gray-700 mb-2">🌐 مزامنة قاعدة البيانات</h4>
                                <div class="grid grid-cols-2 gap-2">
                                    <button onclick="checkConnection()" class="bg-blue-600 text-white py-1 px-2 rounded text-xs hover:bg-blue-700">
                                        فحص الاتصال
                                    </button>
                                    <button onclick="fullSync()" class="bg-green-600 text-white py-1 px-2 rounded text-xs hover:bg-green-700">
                                        مزامنة كاملة
                                    </button>
                                </div>
                            </div>

                            <!-- Advanced Printing -->
                            <div class="border-b pb-3 mb-3">
                                <h4 class="font-semibold text-sm text-gray-700 mb-2">🖨️ طباعة متقدمة</h4>
                                <div class="grid grid-cols-2 gap-2">
                                    <button onclick="testPrintInvoice()" class="bg-indigo-600 text-white py-1 px-2 rounded text-xs hover:bg-indigo-700">
                                        اختبار فاتورة
                                    </button>
                                    <button onclick="testPrintReceipt()" class="bg-teal-600 text-white py-1 px-2 rounded text-xs hover:bg-teal-700">
                                        اختبار إيصال
                                    </button>
                                </div>
                            </div>

                            <!-- Advanced Excel -->
                            <div>
                                <h4 class="font-semibold text-sm text-gray-700 mb-2">📊 تصدير Excel متقدم</h4>
                                <div class="grid grid-cols-1 gap-2">
                                    <button onclick="exportAdvancedSalesReport()" class="bg-emerald-600 text-white py-1 px-2 rounded text-xs hover:bg-emerald-700">
                                        تقرير مبيعات متقدم
                                    </button>
                                    <button onclick="exportBusinessDashboard()" class="bg-rose-600 text-white py-1 px-2 rounded text-xs hover:bg-rose-700">
                                        لوحة التحكم الشاملة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Danger Zone -->
                    <div class="admin-card danger-zone rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-bold text-red-900 mb-4 flex items-center">
                            <span class="ml-2">⚠️</span>
                            المنطقة الخطرة
                        </h3>
                        <div class="space-y-3">
                            <button onclick="resetFinancialDataOnly()" class="w-full bg-orange-600 text-white py-2 rounded-lg hover:bg-orange-700 text-sm">
                                💰 إعادة تعيين البيانات المالية فقط
                            </button>
                            <button onclick="resetSystemToCleanState()" class="w-full bg-red-600 text-white py-2 rounded-lg hover:bg-red-700 text-sm">
                                🔄 إعادة تعيين النظام كاملاً
                            </button>
                            <button onclick="initializeDashboardWithZeros()" class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 text-sm">
                                📊 تصفير لوحة التحكم
                            </button>
                            <button onclick="resetDatabaseMySQL()" class="w-full bg-red-800 text-white py-2 rounded-lg hover:bg-red-900 text-sm">
                                🗑️ تصفير قاعدة البيانات (MySQL)
                            </button>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- تحذير عند فتح الصفحة من file:// -->
    <script>
    if (location.protocol === 'file:') {
        document.write('<div style="background:#fee2e2;color:#b91c1c;padding:20px;text-align:center;font-size:18px;font-weight:bold;z-index:9999;position:fixed;top:0;left:0;right:0;direction:rtl;">⚠️ لا يمكن تشغيل النظام بشكل صحيح عند فتح الصفحة مباشرة من القرص (file://)<br>يرجى تشغيل النظام عبر سيرفر محلي مثل XAMPP أو PHP أو WAMP وفتح الرابط <span style="color:#991b1b;">http://localhost/AnwarBakery/system-admin.html</span></div><div style="height:80px"></div>');
    }
    </script>

    <!-- Include utilities -->
    <script src="excel-utils.js"></script>
    <script src="advanced-excel.js"></script>
    <script src="advanced-print.js"></script>
    <script src="sync-client.js"></script>
    <script src="system-diagnostics.js"></script>
    <script src="reset-system.js"></script>
    <!-- Main admin script -->
    <script src="system-admin.js"></script>
</body>
</html>
