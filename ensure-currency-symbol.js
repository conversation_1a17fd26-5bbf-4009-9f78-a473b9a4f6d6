// التأكد من وجود رمز العملة في بيانات الشركة
(function() {
    'use strict';
    
    function ensureCurrencySymbol() {
        try {
            const companyDataStr = localStorage.getItem('anwar_bakery_company');
            let companyData = {};
            
            if (companyDataStr) {
                companyData = JSON.parse(companyDataStr);
            }
            
            // إذا لم يكن هناك رمز عملة، أضفه
            if (!companyData.currencySymbol) {
                const currencySymbols = {
                    'SAR': 'ر.س',
                    'YER': '﷼',
                    'USD': '$',
                    'EUR': '€',
                    'AED': 'د.إ',
                    'KWD': 'د.ك',
                    'QAR': 'ر.ق'
                };
                
                const currency = companyData.currency || 'SAR';
                companyData.currencySymbol = currencySymbols[currency] || 'ر.س';
                
                // حفظ البيانات المحدثة
                localStorage.setItem('anwar_bakery_company', JSON.stringify(companyData));
                
                console.log('✅ Currency symbol added to company data:', companyData.currencySymbol);
            }
            
        } catch (error) {
            console.error('Error ensuring currency symbol:', error);
        }
    }
    
    // تطبيق عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', ensureCurrencySymbol);
    } else {
        ensureCurrencySymbol();
    }
    
})();

console.log('✅ Currency Symbol Ensurer loaded');
