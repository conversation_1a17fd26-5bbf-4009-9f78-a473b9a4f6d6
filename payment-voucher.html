<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سند صرف - مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .custom-scroll {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 #f7fafc;
        }

        .custom-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scroll::-webkit-scrollbar-track {
            background: #f7fafc;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        .account-preview {
            transition: all 0.3s ease;
        }

        .account-preview.show {
            max-height: 200px;
            opacity: 1;
        }

        .account-preview.hide {
            max-height: 0;
            opacity: 0;
            overflow: hidden;
        }

        .required-field {
            border-color: #ef4444;
        }

        .balance-warning {
            background-color: #fef2f2;
            border-color: #fecaca;
            color: #dc2626;
        }

        .balance-sufficient {
            background-color: #f0fdf4;
            border-color: #bbf7d0;
            color: #16a34a;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="bg-white w-64 shadow-lg flex flex-col">
            <div class="p-4 flex-shrink-0">
                <div class="flex items-center mb-8">
                    <span class="text-2xl ml-3">🍞</span>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800" id="companyName">مخبز أنوار الحي</h1>
                        <p class="text-sm text-gray-600" id="companySlogan">جودة تستحق الثقة</p>
                    </div>
                </div>
            </div>

            <nav class="flex-1 overflow-y-auto px-4 pb-4 custom-scroll">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    الرئيسية
                </a>
                <a href="vouchers.html" class="flex items-center px-3 py-2 text-sm font-medium text-red-600 bg-red-50 rounded-md">
                    <span class="ml-3">📋</span>
                    السندات
                </a>
                <a href="chart-of-accounts.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🌳</span>
                    شجرة الحسابات
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏭</span>
                    الموردين
                </a>

                <!-- User Info & Logout -->
                <div class="mt-auto p-4 border-t border-gray-200">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                            أ
                        </div>
                        <div class="mr-3 flex-1">
                            <p id="userFullName" class="text-sm font-medium text-gray-900">أحمد محمد</p>
                            <p id="userRole" class="text-xs text-gray-500">مدير النظام</p>
                        </div>
                        <button onclick="logout()" class="text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50" title="تسجيل الخروج">
                            <span class="text-lg">🚪</span>
                        </button>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col min-h-0">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <a href="vouchers.html" class="text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">←</span>
                            </a>
                            <h2 class="text-xl font-semibold text-gray-900">سند صرف جديد</h2>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="window.history.back()" class="text-gray-600 hover:text-gray-800 px-2 py-2 rounded text-sm">
                                ← رجوع
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6 custom-scroll">
                <!-- Success/Error Messages -->
                <div id="messageContainer" class="mb-6"></div>

                <!-- Balance Warning -->
                <div id="balanceWarning" class="mb-6" style="display: none;">
                    <div class="border rounded-lg p-4 balance-warning">
                        <div class="flex items-center">
                            <span class="ml-2">⚠️</span>
                            <span id="balanceWarningText">تحذير: الرصيد غير كافي</span>
                        </div>
                    </div>
                </div>

                <!-- Payment Voucher Form -->
                <div class="bg-white rounded-lg shadow-sm">
                    <!-- Voucher Header - Compact -->
                    <div class="p-4 border-b border-gray-200">
                        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                            <!-- Voucher Number -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">رقم السند</label>
                                <input type="text" id="voucherNumber" readonly
                                       class="w-full px-2 py-1.5 border border-gray-300 rounded bg-gray-50 text-sm"
                                       value="PAY-2024-001">
                            </div>

                            <!-- Voucher Date -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">التاريخ *</label>
                                <input type="date" id="voucherDate" required
                                       class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 text-sm">
                            </div>

                            <!-- Branch -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">الفرع *</label>
                                <select id="branchId" required onchange="loadBranchData()"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 text-sm">
                                    <option value="">اختر الفرع</option>
                                </select>
                            </div>

                            <!-- Payee Type -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">نوع المستفيد</label>
                                <div class="grid grid-cols-2 gap-1">
                                    <label class="flex items-center text-xs">
                                        <input type="radio" name="payeeType" value="supplier" checked onchange="togglePayeeType()"
                                               class="ml-1 text-red-600 focus:ring-red-500 scale-75">
                                        مورد
                                    </label>
                                    <label class="flex items-center text-xs">
                                        <input type="radio" name="payeeType" value="customer" onchange="togglePayeeType()"
                                               class="ml-1 text-red-600 focus:ring-red-500 scale-75">
                                        عميل
                                    </label>
                                    <label class="flex items-center text-xs">
                                        <input type="radio" name="payeeType" value="employee" onchange="togglePayeeType()"
                                               class="ml-1 text-red-600 focus:ring-red-500 scale-75">
                                        موظف
                                    </label>
                                    <label class="flex items-center text-xs">
                                        <input type="radio" name="payeeType" value="other" onchange="togglePayeeType()"
                                               class="ml-1 text-red-600 focus:ring-red-500 scale-75">
                                        أخرى
                                    </label>
                                </div>
                            </div>

                            <!-- Payee Selection -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1" id="payeeLabel">المورد *</label>
                                <select id="payeeId" required onchange="loadPayeeData()"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 text-sm">
                                    <option value="">اختر المورد</option>
                                </select>
                            </div>

                            <!-- Payment Method -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">طريقة الدفع</label>
                                <select id="paymentMethod" onchange="togglePaymentMethod()"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 text-sm">
                                    <option value="cash">نقدي</option>
                                    <option value="bank">تحويل بنكي</option>
                                    <option value="check">شيك</option>
                                    <option value="card">بطاقة ائتمان</option>
                                </select>
                            </div>
                        </div>

                        <!-- Second Row - Payment Source and Details -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-3 mt-3">
                            <!-- Cash Register (for cash payments) -->
                            <div id="cashRegisterField">
                                <label class="block text-xs font-medium text-gray-600 mb-1">الصندوق *</label>
                                <select id="cashRegisterId" required onchange="checkBalance()"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 text-sm">
                                    <option value="">اختر الصندوق</option>
                                </select>
                                <div id="cashRegisterBalance" class="text-xs mt-1"></div>
                            </div>

                            <!-- Bank (for non-cash payments) -->
                            <div id="bankField" style="display: none;">
                                <label class="block text-xs font-medium text-gray-600 mb-1">البنك *</label>
                                <select id="bankId" onchange="checkBalance()"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 text-sm">
                                    <option value="">اختر البنك</option>
                                </select>
                                <div id="bankBalance" class="text-xs mt-1"></div>
                            </div>

                            <!-- Amount -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">المبلغ *</label>
                                <input type="number" id="amount" required min="0" step="0.01"
                                       class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 text-sm"
                                       onchange="checkBalance(); updateJournalPreview();">
                            </div>

                            <!-- Reference Number -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1" id="referenceLabel">رقم المرجع</label>
                                <input type="text" id="referenceNumber" placeholder="رقم الشيك أو التحويل"
                                       class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 text-sm">
                            </div>

                            <!-- Account -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">الحساب المقابل</label>
                                <select id="accountId" onchange="updateJournalPreview()"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 text-sm">
                                    <option value="">اختيار تلقائي</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="p-4 border-b border-gray-200">
                        <div>
                            <label class="block text-xs font-medium text-gray-600 mb-1">البيان *</label>
                            <input type="text" id="description" required placeholder="وصف سبب الصرف..."
                                   class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 text-sm">
                        </div>
                    </div>

                    <!-- Journal Entry Preview -->
                    <div class="p-4 bg-gray-50 border-b border-gray-200">
                        <h3 class="text-sm font-semibold text-gray-900 mb-3">معاينة القيد المحاسبي</h3>
                        <div id="journalPreview" class="account-preview hide">
                            <div class="bg-white rounded border p-3">
                                <div class="grid grid-cols-3 gap-4 text-xs font-medium text-gray-500 mb-2">
                                    <div>الحساب</div>
                                    <div class="text-center">مدين</div>
                                    <div class="text-center">دائن</div>
                                </div>
                                <div id="journalEntries" class="space-y-1">
                                    <!-- Journal entries will be populated here -->
                                </div>
                                <div class="border-t pt-2 mt-2">
                                    <div class="grid grid-cols-3 gap-4 text-xs font-bold">
                                        <div>الإجمالي:</div>
                                        <div class="text-center" id="totalDebit">0</div>
                                        <div class="text-center" id="totalCredit">0</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="p-4">
                        <div class="flex justify-between items-center">
                            <div class="flex space-x-2">
                                <button onclick="previewVoucher()" class="bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 text-sm">
                                    👁️ معاينة
                                </button>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="saveDraft()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 text-sm">
                                    💾 حفظ مسودة
                                </button>
                                <button onclick="saveAndPrintThermal()" class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 text-sm">
                                    🧾 طباعة حرارية
                                </button>
                                <button onclick="saveAndPrintRegular()" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 text-sm">
                                    🖨️ طباعة عادية
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Print Templates (Hidden) -->
    <div id="printTemplates" style="display: none;">
        <!-- Regular Print Template -->
        <div id="regularPrintTemplate" class="print-template">
            <div style="max-width: 800px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
                <!-- Header -->
                <div style="text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px;">
                    <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 15px;">
                        <div style="font-size: 48px; margin-left: 15px;">🍞</div>
                        <div>
                            <h1 id="printCompanyName" style="margin: 0; font-size: 24px; color: #333;">مخبز أنوار الحي</h1>
                            <p id="printCompanySlogan" style="margin: 5px 0 0 0; color: #666; font-size: 14px;">جودة تستحق الثقة</p>
                        </div>
                    </div>
                    <h2 style="margin: 0; font-size: 20px; color: #dc2626;">سند صرف</h2>
                    <p style="margin: 5px 0 0 0; color: #666;">رقم السند: <span id="printVoucherNumber"></span></p>
                </div>

                <!-- Voucher Details -->
                <div style="margin-bottom: 30px;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold; width: 25%;">التاريخ:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;" id="printDate"></td>
                            <td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold; width: 25%;">الفرع:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;" id="printBranch"></td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">دفعنا إلى:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;" id="printPayee"></td>
                            <td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">طريقة الدفع:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;" id="printPaymentMethod"></td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">المبلغ:</td>
                            <td style="padding: 8px; border: 1px solid #ddd; font-size: 18px; font-weight: bold; color: #dc2626;" id="printAmount"></td>
                            <td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">رقم المرجع:</td>
                            <td style="padding: 8px; border: 1px solid #ddd;" id="printReference"></td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd; background: #f9f9f9; font-weight: bold;">البيان:</td>
                            <td colspan="3" style="padding: 8px; border: 1px solid #ddd;" id="printDescription"></td>
                        </tr>
                    </table>
                </div>

                <!-- Journal Entry -->
                <div style="margin-bottom: 30px;">
                    <h3 style="color: #333; border-bottom: 1px solid #ddd; padding-bottom: 5px;">القيد المحاسبي:</h3>
                    <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                        <thead>
                            <tr style="background: #f3f4f6;">
                                <th style="padding: 8px; border: 1px solid #ddd; text-align: right;">الحساب</th>
                                <th style="padding: 8px; border: 1px solid #ddd; text-align: center;">مدين</th>
                                <th style="padding: 8px; border: 1px solid #ddd; text-align: center;">دائن</th>
                            </tr>
                        </thead>
                        <tbody id="printJournalEntries">
                            <!-- Journal entries will be populated here -->
                        </tbody>
                    </table>
                </div>

                <!-- Signatures -->
                <div style="margin-top: 50px;">
                    <table style="width: 100%;">
                        <tr>
                            <td style="text-align: center; padding: 20px; border-top: 1px solid #333; width: 33%;">
                                <strong>الدافع</strong><br>
                                <span style="font-size: 12px; color: #666;">التوقيع والختم</span>
                            </td>
                            <td style="width: 34%;"></td>
                            <td style="text-align: center; padding: 20px; border-top: 1px solid #333; width: 33%;">
                                <strong>المستفيد</strong><br>
                                <span style="font-size: 12px; color: #666;">التوقيع</span>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- Footer -->
                <div style="margin-top: 30px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #ddd; padding-top: 15px;">
                    <p>تم إنشاء السند بواسطة: <span id="printCreatedBy"></span></p>
                    <p>تاريخ الإنشاء: <span id="printCreatedAt"></span></p>
                </div>
            </div>
        </div>

        <!-- Thermal Print Template -->
        <div id="thermalPrintTemplate" class="print-template">
            <div style="width: 80mm; font-family: monospace; font-size: 12px; line-height: 1.2;">
                <!-- Header -->
                <div style="text-align: center; border-bottom: 1px dashed #333; padding-bottom: 10px; margin-bottom: 10px;">
                    <div style="font-size: 16px;">🍞</div>
                    <div style="font-weight: bold; font-size: 14px;" id="thermalCompanyName">مخبز أنوار الحي</div>
                    <div style="font-size: 10px;" id="thermalCompanySlogan">جودة تستحق الثقة</div>
                    <div style="font-weight: bold; margin-top: 5px;">سند صرف</div>
                    <div style="font-size: 10px;">رقم: <span id="thermalVoucherNumber"></span></div>
                </div>

                <!-- Details -->
                <div style="margin-bottom: 10px;">
                    <div>التاريخ: <span id="thermalDate"></span></div>
                    <div>الفرع: <span id="thermalBranch"></span></div>
                    <div>دفعنا إلى: <span id="thermalPayee"></span></div>
                    <div>المبلغ: <span id="thermalAmount" style="font-weight: bold;"></span></div>
                    <div>البيان: <span id="thermalDescription"></span></div>
                    <div>طريقة الدفع: <span id="thermalPaymentMethod"></span></div>
                    <div id="thermalReferenceDiv" style="display: none;">رقم المرجع: <span id="thermalReference"></span></div>
                </div>

                <!-- Footer -->
                <div style="border-top: 1px dashed #333; padding-top: 10px; text-align: center; font-size: 10px;">
                    <div>المدخل: <span id="thermalCreatedBy"></span></div>
                    <div><span id="thermalCreatedAt"></span></div>
                    <div style="margin-top: 10px;">شكراً لكم</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Load invoice data from localStorage if coming from invoice
        function loadInvoiceData() {
            const voucherData = localStorage.getItem('voucherData');
            if (voucherData) {
                try {
                    const data = JSON.parse(voucherData);
                    if (data.type === 'payment' && data.source === 'purchase_invoice') {
                        const invoiceData = data.data;

                        // Fill form with invoice data
                        document.getElementById('voucherDate').value = invoiceData.date;
                        document.getElementById('amount').value = parseFloat(invoiceData.amount.replace(/[^\d.-]/g, '')) || 0;
                        document.getElementById('referenceNumber').value = invoiceData.invoiceNumber;
                        document.getElementById('description').value = `دفع فاتورة مشتريات رقم ${invoiceData.invoiceNumber}`;

                        // Set payment method
                        if (invoiceData.paymentMethod) {
                            document.getElementById('paymentMethod').value = invoiceData.paymentMethod;
                            togglePaymentMethod();
                        }

                        // Set bank if bank payment
                        if (invoiceData.bankId && invoiceData.paymentMethod === 'bank') {
                            document.getElementById('bankId').value = invoiceData.bankId;
                        }

                        // Set payee type and selection
                        if (invoiceData.sellerType) {
                            document.querySelector(`input[name="payeeType"][value="${invoiceData.sellerType}"]`).checked = true;
                            togglePayeeType();

                            // Set the selected payee
                            setTimeout(() => {
                                if (invoiceData.sellerId) {
                                    document.getElementById('payeeId').value = invoiceData.sellerId;
                                }
                            }, 100);
                        }

                        // Add notes if any
                        if (invoiceData.notes) {
                            const currentDesc = document.getElementById('description').value;
                            document.getElementById('description').value = `${currentDesc} - ${invoiceData.notes}`;
                        }

                        // Show success message
                        showMessage('تم تحميل بيانات الفاتورة بنجاح', 'success');

                        // Clear the stored data
                        localStorage.removeItem('voucherData');

                        // Update journal preview
                        setTimeout(() => {
                            updateJournalPreview();
                        }, 200);
                    }
                } catch (error) {
                    console.error('Error loading invoice data:', error);
                    showMessage('خطأ في تحميل بيانات الفاتورة', 'error');
                }
            }
        }

        // Toggle payment method fields
        function togglePaymentMethod() {
            const paymentMethod = document.getElementById('paymentMethod').value;
            const cashRegisterField = document.getElementById('cashRegisterField');
            const bankField = document.getElementById('bankField');
            const referenceLabel = document.getElementById('referenceLabel');

            if (paymentMethod === 'cash') {
                cashRegisterField.style.display = 'block';
                bankField.style.display = 'none';
                document.getElementById('cashRegisterId').required = true;
                document.getElementById('bankId').required = false;
                referenceLabel.textContent = 'رقم المرجع';
            } else {
                cashRegisterField.style.display = 'none';
                bankField.style.display = 'block';
                document.getElementById('cashRegisterId').required = false;
                document.getElementById('bankId').required = true;

                if (paymentMethod === 'check') {
                    referenceLabel.textContent = 'رقم الشيك *';
                    document.getElementById('referenceNumber').required = true;
                } else if (paymentMethod === 'bank') {
                    referenceLabel.textContent = 'رقم التحويل';
                    document.getElementById('referenceNumber').required = false;
                } else {
                    referenceLabel.textContent = 'رقم المرجع';
                    document.getElementById('referenceNumber').required = false;
                }
            }
        }

        // Toggle payee type
        function togglePayeeType() {
            const payeeType = document.querySelector('input[name="payeeType"]:checked').value;
            const payeeLabel = document.getElementById('payeeLabel');
            const payeeSelect = document.getElementById('payeeId');

            // Clear current options
            payeeSelect.innerHTML = '<option value="">اختر...</option>';

            switch (payeeType) {
                case 'supplier':
                    payeeLabel.textContent = 'المورد *';
                    // Load suppliers
                    loadSuppliers();
                    break;
                case 'customer':
                    payeeLabel.textContent = 'العميل *';
                    // Load customers
                    loadCustomers();
                    break;
                case 'employee':
                    payeeLabel.textContent = 'الموظف *';
                    // Load employees
                    loadEmployees();
                    break;
                case 'other':
                    payeeLabel.textContent = 'المستفيد *';
                    // Load other payees or allow manual entry
                    break;
            }
        }

        // Load suppliers
        function loadSuppliers() {
            const suppliers = [
                { id: 1, name: 'مطاحن الدقيق المتحدة' },
                { id: 2, name: 'شركة السكر والحلويات' },
                { id: 3, name: 'مورد الزيوت الطبيعية' }
            ];

            const select = document.getElementById('payeeId');
            suppliers.forEach(supplier => {
                const option = document.createElement('option');
                option.value = supplier.id;
                option.textContent = supplier.name;
                select.appendChild(option);
            });
        }

        // Load customers
        function loadCustomers() {
            const customers = [
                { id: 1, name: 'مطعم الأصالة' },
                { id: 2, name: 'كافيه النخبة' },
                { id: 3, name: 'فندق الضيافة' }
            ];

            const select = document.getElementById('payeeId');
            customers.forEach(customer => {
                const option = document.createElement('option');
                option.value = customer.id;
                option.textContent = customer.name;
                select.appendChild(option);
            });
        }

        // Load employees
        function loadEmployees() {
            const employees = [
                { id: 1, name: 'أحمد محمد - مدير' },
                { id: 2, name: 'فاطمة علي - محاسبة' },
                { id: 3, name: 'محمد سالم - خباز' }
            ];

            const select = document.getElementById('payeeId');
            employees.forEach(employee => {
                const option = document.createElement('option');
                option.value = employee.id;
                option.textContent = employee.name;
                select.appendChild(option);
            });
        }

        // Show message
        function showMessage(message, type) {
            const container = document.getElementById('messageContainer');
            const alertClass = type === 'success' ? 'bg-green-100 border-green-400 text-green-700' : 'bg-red-100 border-red-400 text-red-700';

            container.innerHTML = `
                <div class="border px-4 py-3 rounded ${alertClass}" role="alert">
                    <span class="block sm:inline">${message}</span>
                </div>
            `;

            // Auto hide after 5 seconds
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        // Update journal preview
        function updateJournalPreview() {
            const amount = parseFloat(document.getElementById('amount').value) || 0;
            const paymentMethod = document.getElementById('paymentMethod').value;

            if (amount > 0) {
                const preview = document.getElementById('journalPreview');
                const entries = document.getElementById('journalEntries');

                let debitAccount = '';
                let creditAccount = '';

                if (paymentMethod === 'cash') {
                    creditAccount = 'الصندوق';
                } else {
                    creditAccount = 'البنك';
                }

                debitAccount = 'حساب المورد'; // أو حسب نوع المستفيد

                entries.innerHTML = `
                    <div class="grid grid-cols-3 gap-4 text-xs">
                        <div>${debitAccount}</div>
                        <div class="text-center">${amount.toFixed(2)}</div>
                        <div class="text-center">-</div>
                    </div>
                    <div class="grid grid-cols-3 gap-4 text-xs">
                        <div>${creditAccount}</div>
                        <div class="text-center">-</div>
                        <div class="text-center">${amount.toFixed(2)}</div>
                    </div>
                `;

                document.getElementById('totalDebit').textContent = amount.toFixed(2);
                document.getElementById('totalCredit').textContent = amount.toFixed(2);

                preview.classList.remove('hide');
                preview.classList.add('show');
            }
        }

        // Save functions
        function saveDraft() {
            showMessage('تم حفظ المسودة بنجاح', 'success');
        }

        function saveAndPrintThermal() {
            showMessage('تم حفظ السند وإرساله للطباعة الحرارية', 'success');
        }

        function saveAndPrintRegular() {
            showMessage('تم حفظ السند وإرساله للطباعة العادية', 'success');
        }

        function previewVoucher() {
            updateJournalPreview();
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Set today's date
            document.getElementById('voucherDate').value = new Date().toISOString().split('T')[0];

            // Load invoice data if available
            loadInvoiceData();

            // Initialize payee type
            togglePayeeType();

            // Initialize payment method
            togglePaymentMethod();
        });
    </script>
    <!-- Include advanced libraries -->
    <script src="advanced-excel.js"></script>
    <script src="advanced-print.js"></script>
    <script src="excel-utils.js"></script>
    <script src="global-advanced-features.js"></script>
    <script src="activate-advanced-features.js"></script>

    <script src="payment-voucher.js"></script>
</body>
</html>
