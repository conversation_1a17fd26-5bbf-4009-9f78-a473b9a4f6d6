<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفروع والمخازن - مخبز أنور</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="app-settings.js"></script>
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .modal { display: none; }
        .modal.active { display: flex; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }

        /* تحسين مظهر النماذج */
        .modal .bg-white {
            max-height: 90vh;
        }

        input[type="text"],
        input[type="number"],
        input[type="tel"],
        input[type="email"],
        select,
        textarea {
            font-size: 16px !important;
            line-height: 1.5;
        }

        .tab-button {
            font-size: 14px;
            padding: 8px 12px;
            transition: all 0.2s ease;
        }

        button {
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .modal form {
            max-height: calc(90vh - 120px);
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Mobile menu overlay -->
    <div id="mobileOverlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 lg:hidden hidden"></div>

    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform translate-x-full lg:translate-x-0 lg:static lg:inset-0 sidebar-transition">
            <div class="h-16 px-4 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between">
                <h1 id="sidebarCompanyName" class="text-white text-lg font-bold">مخبز أنور</h1>
                <button onclick="toggleSidebar()" class="lg:hidden text-white">
                    <span class="text-xl">✕</span>
                </button>
            </div>

            <nav class="mt-5 px-2 space-y-1 h-full overflow-y-auto pb-20">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    لوحة التحكم
                </a>
                <a href="users-management.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👥</span>
                    إدارة المستخدمين
                </a>
                <a href="company-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏢</span>
                    بيانات المنشأة
                </a>
                <a href="branches-warehouses.html" class="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">
                    <span class="ml-3">🏪</span>
                    الفروع والمخازن
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🚚</span>
                    الموردين
                </a>
                <a href="cash-registers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">💰</span>
                    الصناديق
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="inventory.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📊</span>
                    المخزون
                </a>
                <a href="reports.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📈</span>
                    التقارير
                </a>

                <!-- User Info & Logout -->
                <div class="absolute bottom-4 left-2 right-2">
                    <div class="bg-gray-50 rounded-lg p-3 mb-2">
                        <p id="userFullName" class="text-sm font-medium text-gray-900">مدير النظام</p>
                        <p id="userRole" class="text-xs text-gray-500">admin</p>
                    </div>
                    <button onclick="logout()" class="w-full text-right px-3 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50 flex items-center">
                        <span class="ml-3">🚪</span>
                        تسجيل الخروج
                    </button>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 overflow-hidden">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <button onclick="toggleSidebar()" class="lg:hidden text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">☰</span>
                            </button>
                            <span class="text-lg font-semibold text-gray-900">إدارة الفروع والمخازن</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button onclick="openAddBranchModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center">
                                <span class="ml-2">🏪</span>
                                إضافة فرع جديد
                            </button>
                            <button onclick="openAddWarehouseModal()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center">
                                <span class="ml-2">🏬</span>
                                إضافة مخزن جديد
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2 flex items-center">
                        <span class="ml-3">🏪</span>
                        إدارة الفروع والمخازن المتكاملة
                    </h1>
                    <p class="text-gray-600">
                        إدارة شاملة للفروع والمخازن مع الصناديق والحسابات البنكية والمنتجات
                    </p>
                </div>

                <!-- Success/Error Messages -->
                <div id="messageContainer" class="mb-6"></div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-6 gap-4 mb-8">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-lg p-4 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100 text-xs">إجمالي الفروع</p>
                                <p class="text-2xl font-bold" id="totalBranches">0</p>
                            </div>
                            <div class="text-2xl opacity-80">🏪</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg p-4 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-100 text-xs">إجمالي المخازن</p>
                                <p class="text-2xl font-bold" id="totalWarehouses">0</p>
                            </div>
                            <div class="text-2xl opacity-80">🏬</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl shadow-lg p-4 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-100 text-xs">الفروع النشطة</p>
                                <p class="text-2xl font-bold" id="activeBranches">0</p>
                            </div>
                            <div class="text-2xl opacity-80">✅</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl shadow-lg p-4 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-orange-100 text-xs">المخازن النشطة</p>
                                <p class="text-2xl font-bold" id="activeWarehouses">0</p>
                            </div>
                            <div class="text-2xl opacity-80">🟢</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-xl shadow-lg p-4 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-yellow-100 text-xs">إجمالي المخازن</p>
                                <p class="text-2xl font-bold" id="totalWarehouses">0</p>
                            </div>
                            <div class="text-2xl opacity-80">🏬</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-xl shadow-lg p-4 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-indigo-100 text-xs">المنتجات المتاحة</p>
                                <p class="text-2xl font-bold" id="totalProducts">0</p>
                            </div>
                            <div class="text-2xl opacity-80">📦</div>
                        </div>
                    </div>
                </div>

                <!-- Tabs -->
                <div class="mb-6">
                    <div class="border-b border-gray-200">
                        <nav class="-mb-px flex space-x-8">
                            <button onclick="switchMainTab('branches')" class="main-tab-button py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
                                🏪 الفروع
                            </button>
                            <button onclick="switchMainTab('warehouses')" class="main-tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                                🏬 المخازن
                            </button>
                            <button onclick="switchMainTab('inventory')" class="main-tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                                📦 توزيع المخزون
                            </button>
                        </nav>
                    </div>
                </div>

                <!-- Branches Tab -->
                <div id="branchesTab" class="main-tab-content active">
                    <!-- Branches filters -->
                    <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <input type="text" id="branchSearchInput" placeholder="البحث في الفروع..."
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                       onkeyup="filterBranches()">
                            </div>
                            <div>
                                <select id="branchStatusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterBranches()">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div>
                                <button onclick="exportBranchesToExcel()" class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center justify-center">
                                    <span class="ml-2">📊</span>
                                    تصدير الفروع
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Branches Table -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">كود الفرع</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">اسم الفرع</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">المدينة</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">المدير</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">الهاتف</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">عدد المخازن</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="branchesTableBody" class="bg-white divide-y divide-gray-200">
                                    <!-- Branches will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Warehouses Tab -->
                <div id="warehousesTab" class="main-tab-content">
                    <!-- Warehouses filters -->
                    <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <input type="text" id="warehouseSearchInput" placeholder="البحث في المخازن..."
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                       onkeyup="filterWarehouses()">
                            </div>
                            <div>
                                <select id="warehouseBranchFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterWarehouses()">
                                    <option value="">جميع الفروع</option>
                                </select>
                            </div>
                            <div>
                                <select id="warehouseStatusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterWarehouses()">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div>
                                <button onclick="exportWarehousesToExcel()" class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center justify-center">
                                    <span class="ml-2">📊</span>
                                    تصدير المخازن
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Warehouses Table -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">كود المخزن</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">اسم المخزن</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">الفرع</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">النوع</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">السعة</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">المساحة</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="warehousesTableBody" class="bg-white divide-y divide-gray-200">
                                    <!-- Warehouses will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Inventory Distribution Tab -->
                <div id="inventoryTab" class="main-tab-content">
                    <!-- Inventory filters -->
                    <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <input type="text" id="inventorySearchInput" placeholder="البحث في الأصناف..."
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                       onkeyup="filterInventory()">
                            </div>
                            <div>
                                <select id="inventoryBranchFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterInventory()">
                                    <option value="">جميع الفروع</option>
                                </select>
                            </div>
                            <div>
                                <select id="inventoryWarehouseFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterInventory()">
                                    <option value="">جميع المخازن</option>
                                </select>
                            </div>
                            <div>
                                <button onclick="openTransferModal()" class="w-full bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center justify-center">
                                    <span class="ml-2">🔄</span>
                                    نقل مخزون
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Inventory Table -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">كود الصنف</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">اسم الصنف</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">الفرع</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">المخزن</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">الوحدة</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">آخر تحديث</th>
                                        <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="inventoryTableBody" class="bg-white divide-y divide-gray-200">
                                    <!-- Inventory will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>




            </main>
        </div>
    </div>

    <!-- Add/Edit Branch Modal -->
    <div id="branchModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 id="branchModalTitle" class="text-lg font-medium text-gray-900">إضافة فرع جديد</h3>
                <button onclick="closeBranchModal()" class="text-gray-400 hover:text-gray-600">
                    <span class="text-2xl">×</span>
                </button>
            </div>

            <form id="branchForm" class="px-4 py-3">
                <input type="hidden" id="branchId" value="">

                <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">كود الفرع *</label>
                            <input type="text" id="branchCode" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="مثال: BR001">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم الفرع *</label>
                            <input type="text" id="branchName" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="مثال: فرع الرياض الرئيسي">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">المدينة *</label>
                            <input type="text" id="branchCity" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="مثال: الرياض">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الحي</label>
                            <input type="text" id="branchDistrict"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="مثال: العليا">
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">العنوان التفصيلي</label>
                            <textarea id="branchAddress" rows="2"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                      placeholder="العنوان الكامل للفرع..."></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">مدير الفرع</label>
                            <input type="text" id="branchManager"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="اسم مدير الفرع">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">هاتف الفرع</label>
                            <input type="tel" id="branchPhone"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="مثال: 0112345678">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                            <input type="email" id="branchEmail"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="<EMAIL>">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ الافتتاح</label>
                            <input type="date" id="branchOpeningDate"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="branchIsActive" checked
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="branchIsActive" class="mr-2 block text-sm text-gray-900">الفرع نشط</label>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                            <textarea id="branchNotes" rows="2"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                      placeholder="أي ملاحظات إضافية..."></textarea>
                        </div>
                    </div>
                </div>
            </form>

            <div class="px-4 py-3 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="closeBranchModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                    إلغاء
                </button>
                <button onclick="saveBranch()" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700">
                    حفظ الفرع
                </button>
            </div>
        </div>
    </div>

    <!-- Add/Edit Warehouse Modal -->
    <div id="warehouseModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 id="warehouseModalTitle" class="text-lg font-medium text-gray-900">إضافة مخزن جديد</h3>
                <button onclick="closeWarehouseModal()" class="text-gray-400 hover:text-gray-600">
                    <span class="text-2xl">×</span>
                </button>
            </div>

            <form id="warehouseForm" class="px-4 py-3">
                <input type="hidden" id="warehouseId" value="">

                <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">كود المخزن *</label>
                            <input type="text" id="warehouseCode" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="مثال: WH001">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم المخزن *</label>
                            <input type="text" id="warehouseName" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="مثال: المخزن الرئيسي">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الفرع *</label>
                            <select id="warehouseBranch" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                                <option value="">اختر الفرع</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">نوع المخزن *</label>
                            <select id="warehouseType" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                                <option value="">اختر النوع</option>
                                <option value="main">رئيسي</option>
                                <option value="secondary">فرعي</option>
                                <option value="cold">مبردات</option>
                                <option value="dry">جاف</option>
                                <option value="finished">منتجات نهائية</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">السعة</label>
                            <input type="number" id="warehouseCapacity" step="0.01" min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="1000">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">المساحة</label>
                            <input type="number" id="warehouseArea" step="0.01" min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">وحدة القياس</label>
                            <select id="warehouseUnit"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                                <option value="متر مربع">متر مربع</option>
                                <option value="متر مكعب">متر مكعب</option>
                                <option value="طن">طن</option>
                                <option value="كيلوجرام">كيلوجرام</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">درجة الحرارة</label>
                            <input type="text" id="warehouseTemperature"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="مثال: 18-25 درجة مئوية">
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="warehouseIsActive" checked
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="warehouseIsActive" class="mr-2 block text-sm text-gray-900">المخزن نشط</label>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">الموقع التفصيلي</label>
                            <textarea id="warehouseLocation" rows="2"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                      placeholder="الموقع التفصيلي للمخزن داخل الفرع..."></textarea>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                            <textarea id="warehouseNotes" rows="2"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                      placeholder="أي ملاحظات إضافية..."></textarea>
                        </div>
                    </div>
                </div>
            </form>

            <div class="px-4 py-3 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="closeWarehouseModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                    إلغاء
                </button>
                <button onclick="saveWarehouse()" class="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700">
                    حفظ المخزن
                </button>
            </div>
        </div>
    </div>

    <!-- Transfer Inventory Modal -->
    <div id="transferModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">نقل مخزون</h3>
                <button onclick="closeTransferModal()" class="text-gray-400 hover:text-gray-600">
                    <span class="text-2xl">×</span>
                </button>
            </div>

            <form id="transferForm" class="px-4 py-3">
                <div class="space-y-4">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                        <div class="flex items-center">
                            <span class="text-blue-600 ml-2">ℹ️</span>
                            <p class="text-sm text-blue-700">
                                يمكنك نقل كمية من صنف معين من مخزن إلى آخر
                            </p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الصنف *</label>
                            <select id="transferItem" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                    onchange="updateTransferItemInfo()">
                                <option value="">اختر الصنف</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الكمية المتاحة</label>
                            <input type="text" id="availableQuantity" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-base"
                                   placeholder="0">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">من فرع</label>
                            <select id="fromBranch"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                    onchange="updateFromWarehouses()">
                                <option value="">اختر الفرع</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">من مخزن *</label>
                            <select id="fromWarehouse" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                                <option value="">اختر المخزن</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">إلى فرع</label>
                            <select id="toBranch"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                    onchange="updateToWarehouses()">
                                <option value="">اختر الفرع</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">إلى مخزن *</label>
                            <select id="toWarehouse" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                                <option value="">اختر المخزن</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الكمية المنقولة *</label>
                            <input type="number" id="transferQuantity" step="0.01" min="0" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="0">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ النقل</label>
                            <input type="date" id="transferDate"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">سبب النقل</label>
                            <textarea id="transferReason" rows="2"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                      placeholder="سبب نقل المخزون..."></textarea>
                        </div>
                    </div>
                </div>
            </form>

            <div class="px-4 py-3 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="closeTransferModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                    إلغاء
                </button>
                <button onclick="executeTransfer()" class="px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-lg hover:bg-purple-700">
                    تنفيذ النقل
                </button>
            </div>
        </div>
    </div>



    <!-- Include currency manager -->
    <script src="currency-manager.js"></script>
    <script src="update-all-currencies.js"></script>

    <script src="branches-warehouses.js"></script>
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadUserInfo();
            loadCompanyInfo();
            loadBranches();
            loadWarehouses();
            loadInventoryDistribution();
        });
    </script>

</body>
</html>