// Invoices Management System
let invoices = [];
let currentInvoice = null;
let invoiceCounter = 1;

// Invoice types
const INVOICE_TYPES = {
    PURCHASE: 'purchase',
    SALES: 'sales',
    PURCHASE_RETURN: 'purchase-return',
    SALES_RETURN: 'sales-return',
    QUICK_SALE: 'quick-sale'
};

// Invoice status
const INVOICE_STATUS = {
    DRAFT: 'draft',
    PENDING: 'pending',
    PAID: 'paid',
    PARTIAL: 'partial',
    CANCELLED: 'cancelled'
};

// Load invoices from localStorage
function loadInvoices() {
    const savedInvoices = localStorage.getItem('anwar_bakery_invoices');
    if (savedInvoices) {
        invoices = JSON.parse(savedInvoices);
        // Update counter based on existing invoices
        if (invoices.length > 0) {
            const maxId = Math.max(...invoices.map(inv => parseInt(inv.invoiceNumber) || 0));
            invoiceCounter = maxId + 1;
        }
    }
    renderInvoicesTable();
    updateStats();
}

// Save invoices to localStorage
function saveInvoices() {
    localStorage.setItem('anwar_bakery_invoices', JSON.stringify(invoices));
}

// Get currency symbol from settings - FIXED TO USE CORRECT SETTINGS
function getCurrencySymbol() {
    try {
        // First try to get from window.appSettings (the correct way)
        if (window.appSettings) {
            const financial = window.appSettings.get('financial');
            if (financial && financial.currencySymbol) {
                return financial.currencySymbol;
            }
            if (financial && financial.baseCurrency) {
                const currencySymbols = {
                    'SAR': 'ر.ي',
                    'YER': '﷼',
                    'USD': '$',
                    'EUR': '€',
                    'AED': 'د.إ',
                    'KWD': 'د.ك',
                    'QAR': 'ر.ق'
                };
                return currencySymbols[financial.baseCurrency] || '﷼';
            }
        }

        // Fallback: try to get from localStorage directly
        const savedSettings = localStorage.getItem('anwar_bakery_settings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);
            if (settings.financial && settings.financial.currencySymbol) {
                return settings.financial.currencySymbol;
            }
            if (settings.financial && settings.financial.baseCurrency) {
                const currencySymbols = {
                    'SAR': 'ر.ي',
                    'YER': '﷼',
                    'USD': '$',
                    'EUR': '€',
                    'AED': 'د.إ',
                    'KWD': 'د.ك',
                    'QAR': 'ر.ق'
                };
                return currencySymbols[settings.financial.baseCurrency] || '﷼';
            }
        }

        // Last fallback to company data
        const companyData = localStorage.getItem('anwar_bakery_company');
        if (companyData) {
            const company = JSON.parse(companyData);
            if (company.currencySymbol) {
                return company.currencySymbol;
            }
            if (company.currency) {
                const currencySymbols = {
                    'SAR': 'ر.ي',
                    'YER': '﷼',
                    'USD': '$',
                    'EUR': '€',
                    'AED': 'د.إ',
                    'KWD': 'د.ك',
                    'QAR': 'ر.ق'
                };
                return currencySymbols[company.currency] || '﷼';
            }
        }
    } catch (error) {
        console.error('Error getting currency symbol:', error);
    }

    return '﷼'; // Default currency symbol
}

// Debug function to check currency settings
function debugCurrencySettings() {
    console.log('🔍 Debugging currency settings:');

    // Check window.appSettings
    if (window.appSettings) {
        const financial = window.appSettings.get('financial');
        console.log('✅ window.appSettings.financial:', financial);
    } else {
        console.log('❌ window.appSettings not available');
    }

    // Check localStorage
    const savedSettings = localStorage.getItem('anwar_bakery_settings');
    if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        console.log('✅ localStorage settings:', settings);
        console.log('✅ localStorage financial:', settings.financial);
    } else {
        console.log('❌ No settings in localStorage');
    }

    // Check company data
    const companyData = localStorage.getItem('anwar_bakery_company');
    if (companyData) {
        const company = JSON.parse(companyData);
        console.log('✅ Company data:', company);
    } else {
        console.log('❌ No company data');
    }

    // Check current currency symbol
    const currentSymbol = getCurrencySymbol();
    console.log('🎯 Current currency symbol:', currentSymbol);
}

// Load user info
function loadUserInfo() {
    const savedUser = localStorage.getItem('anwar_bakery_current_user');
    if (savedUser) {
        const user = JSON.parse(savedUser);
        const userFullNameElement = document.getElementById('userFullName');
        const userRoleElement = document.getElementById('userRole');

        if (userFullNameElement) {
            userFullNameElement.textContent = user.fullName || 'أحمد محمد';
        }
        if (userRoleElement) {
            userRoleElement.textContent = user.role === 'admin' ? 'مدير النظام' : (user.role || 'مدير النظام');
        }
    }
}

// Load company info
function loadCompanyInfo() {
    const savedCompany = localStorage.getItem('anwar_bakery_company');
    if (savedCompany) {
        const company = JSON.parse(savedCompany);
        const companyNameElement = document.getElementById('sidebarCompanyName');
        if (companyNameElement) {
            companyNameElement.textContent = company.companyName || 'مخبز أنوار الحي';
        }
    }
}

// Open invoice type
function openInvoiceType(type) {
    switch(type) {
        case 'purchase':
            openPurchaseInvoice();
            break;
        case 'sales':
            openSalesInvoice();
            break;
        case 'purchase-return':
            openPurchaseReturn();
            break;
        case 'sales-return':
            openSalesReturn();
            break;
        case 'quick-sale':
            openQuickSale();
            break;
        default:
            showMessage('نوع الفاتورة غير مدعوم', 'error');
    }
}

// Open purchase invoice
function openPurchaseInvoice() {
    window.location.href = 'purchase-invoice.html';
}

// Open sales invoice
function openSalesInvoice() {
    window.location.href = 'sales-invoice.html';
}

// Open purchase return
function openPurchaseReturn() {
    window.location.href = 'purchase-return.html';
}

// Open sales return
function openSalesReturn() {
    window.location.href = 'sales-return.html';
}

// Open quick sale
function openQuickSale() {
    window.location.href = 'quick-sale.html';
}

// Open new invoice modal (legacy function)
function openNewInvoiceModal() {
    showMessage('يرجى اختيار نوع الفاتورة من البطاقات أعلاه', 'info');
}

// Open quick sale modal (legacy function)
function openQuickSaleModal() {
    openQuickSale();
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    if (!container) return;

    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_current_user');
        window.location.href = 'login.html';
    }
}

// Toggle sidebar for mobile
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (sidebar && overlay) {
        sidebar.classList.toggle('translate-x-full');
        overlay.classList.toggle('hidden');
    }
}

// Render invoices table
function renderInvoicesTable() {
    const tableBody = document.getElementById('salesInvoicesTableBody');
    if (!tableBody) return;

    if (invoices.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="10" class="px-6 py-12 text-center text-gray-500">
                    <div class="flex flex-col items-center">
                        <span class="text-4xl mb-4">📄</span>
                        <p class="text-lg mb-2">لا توجد فواتير حتى الآن</p>
                        <p class="text-sm">ابدأ بإنشاء فاتورة جديدة من البطاقات أعلاه</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // Filter sales invoices (exclude quick sales)
    const salesInvoices = invoices.filter(invoice =>
        invoice.type === INVOICE_TYPES.SALES
    );

    const currencySymbol = getCurrencySymbol();

    tableBody.innerHTML = salesInvoices.map(invoice => {
        const remainingAmount = (invoice.totalAmount || 0) - (invoice.paidAmount || 0);
        const statusBadge = getStatusBadge(invoice.status);
        const voucherButtons = getVoucherButtons(invoice);

        return `
            <tr class="hover:bg-gray-50">
                <td class="px-4 py-3 text-sm font-medium text-gray-900">${invoice.invoiceNumber}</td>
                <td class="px-4 py-3 text-sm text-gray-500">${formatDate(invoice.date)}</td>
                <td class="px-4 py-3 text-sm text-gray-500">${invoice.customerName || 'عميل نقدي'}</td>
                <td class="px-4 py-3 text-sm text-gray-500">${invoice.branchName || 'غير محدد'}</td>
                <td class="px-4 py-3 text-sm font-medium text-gray-900">${(invoice.totalAmount || 0).toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}</td>
                <td class="px-4 py-3 text-sm text-green-600 font-medium">${(invoice.paidAmount || 0).toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}</td>
                <td class="px-4 py-3 text-sm ${remainingAmount > 0 ? 'text-red-600' : 'text-green-600'} font-medium">${remainingAmount.toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}</td>
                <td class="px-4 py-3">${statusBadge}</td>
                <td class="px-4 py-3 text-center">${voucherButtons}</td>
                <td class="px-4 py-3 text-center">
                    <div class="flex justify-center space-x-1">
                        <button onclick="viewInvoice('${invoice.id}')" class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50" title="عرض">
                            👁️
                        </button>
                        <button onclick="editInvoice('${invoice.id}')" class="text-yellow-600 hover:text-yellow-900 p-1 rounded hover:bg-yellow-50" title="تعديل">
                            ✏️
                        </button>
                        <button onclick="printInvoice('${invoice.id}')" class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50" title="طباعة">
                            🖨️
                        </button>
                        <button onclick="deleteInvoice('${invoice.id}')" class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50" title="حذف">
                            🗑️
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// Get status badge
function getStatusBadge(status) {
    const badges = {
        [INVOICE_STATUS.PAID]: '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">مدفوعة</span>',
        [INVOICE_STATUS.PENDING]: '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">معلقة</span>',
        [INVOICE_STATUS.PARTIAL]: '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">جزئية</span>',
        [INVOICE_STATUS.CANCELLED]: '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">ملغية</span>',
        [INVOICE_STATUS.DRAFT]: '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">مسودة</span>'
    };
    return badges[status] || badges[INVOICE_STATUS.PENDING];
}

// Get voucher buttons
function getVoucherButtons(invoice) {
    const remainingAmount = (invoice.totalAmount || 0) - (invoice.paidAmount || 0);
    const hasLinkedVouchers = checkLinkedVouchers(invoice.id);

    let buttons = '';

    // Receipt voucher button (if there's remaining amount)
    if (remainingAmount > 0) {
        buttons += `
            <button onclick="createReceiptVoucher('${invoice.id}')"
                    class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs hover:bg-green-200 mr-1"
                    title="إنشاء سند قبض">
                📥 قبض
            </button>
        `;
    }

    // Payment voucher button (for returns)
    if (invoice.type === INVOICE_TYPES.SALES_RETURN) {
        buttons += `
            <button onclick="createPaymentVoucher('${invoice.id}')"
                    class="bg-red-100 text-red-800 px-2 py-1 rounded text-xs hover:bg-red-200 mr-1"
                    title="إنشاء سند صرف">
                📤 صرف
            </button>
        `;
    }

    // View linked vouchers button
    if (hasLinkedVouchers) {
        buttons += `
            <button onclick="viewLinkedVouchers('${invoice.id}')"
                    class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs hover:bg-blue-200 mr-1"
                    title="عرض السندات المرتبطة">
                📋 السندات
            </button>
        `;
    }

    return buttons || '<span class="text-gray-400 text-xs">لا توجد</span>';
}

// Check if invoice has linked vouchers
function checkLinkedVouchers(invoiceId) {
    const vouchers = JSON.parse(localStorage.getItem('anwar_bakery_vouchers') || '[]');
    return vouchers.some(voucher => voucher.linkedInvoiceId === invoiceId);
}

// Format date
function formatDate(dateString) {
    if (!dateString) return 'غير محدد';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

// Update statistics
function updateStats() {
    const totalInvoicesElement = document.getElementById('totalInvoices');
    const todaySalesElement = document.getElementById('todaySales');
    const monthSalesElement = document.getElementById('monthSales');
    const pendingInvoicesElement = document.getElementById('pendingInvoices');

    if (totalInvoicesElement) {
        totalInvoicesElement.textContent = '0';
    }
    if (todaySalesElement) {
        todaySalesElement.textContent = '0';
    }
    if (monthSalesElement) {
        monthSalesElement.textContent = '0';
    }
    if (pendingInvoicesElement) {
        pendingInvoicesElement.textContent = '0';
    }
}

// Create receipt voucher from invoice
function createReceiptVoucher(invoiceId) {
    const invoice = invoices.find(inv => inv.id === invoiceId);
    if (!invoice) {
        showMessage('الفاتورة غير موجودة', 'error');
        return;
    }

    const remainingAmount = (invoice.totalAmount || 0) - (invoice.paidAmount || 0);
    if (remainingAmount <= 0) {
        showMessage('الفاتورة مدفوعة بالكامل', 'info');
        return;
    }

    // Store invoice data for voucher creation
    const voucherData = {
        linkedInvoiceId: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        invoiceType: invoice.type,
        customerId: invoice.customerId,
        customerName: invoice.customerName,
        branchId: invoice.branchId,
        branchName: invoice.branchName,
        amount: remainingAmount,
        description: `تحصيل من فاتورة رقم ${invoice.invoiceNumber}`,
        date: new Date().toISOString().split('T')[0]
    };

    localStorage.setItem('anwar_bakery_voucher_data', JSON.stringify(voucherData));

    // Redirect to receipt voucher page
    window.location.href = 'receipt-voucher.html?from=invoice';
}

// Create payment voucher from invoice
function createPaymentVoucher(invoiceId) {
    const invoice = invoices.find(inv => inv.id === invoiceId);
    if (!invoice) {
        showMessage('الفاتورة غير موجودة', 'error');
        return;
    }

    // Store invoice data for voucher creation
    const voucherData = {
        linkedInvoiceId: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        invoiceType: invoice.type,
        customerId: invoice.customerId,
        customerName: invoice.customerName,
        branchId: invoice.branchId,
        branchName: invoice.branchName,
        amount: invoice.totalAmount || 0,
        description: `رد مبلغ فاتورة مرتجع رقم ${invoice.invoiceNumber}`,
        date: new Date().toISOString().split('T')[0]
    };

    localStorage.setItem('anwar_bakery_voucher_data', JSON.stringify(voucherData));

    // Redirect to payment voucher page
    window.location.href = 'payment-voucher.html?from=invoice';
}

// View linked vouchers
function viewLinkedVouchers(invoiceId) {
    const vouchers = JSON.parse(localStorage.getItem('anwar_bakery_vouchers') || '[]');
    const linkedVouchers = vouchers.filter(voucher => voucher.linkedInvoiceId === invoiceId);

    if (linkedVouchers.length === 0) {
        showMessage('لا توجد سندات مرتبطة بهذه الفاتورة', 'info');
        return;
    }

    // Create modal to show linked vouchers
    showLinkedVouchersModal(linkedVouchers);
}

// Show linked vouchers modal
function showLinkedVouchersModal(vouchers) {
    const currencySymbol = getCurrencySymbol();

    const modalHtml = `
        <div id="linkedVouchersModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">السندات المرتبطة</h3>
                        <button onclick="closeLinkedVouchersModal()" class="text-gray-400 hover:text-gray-600">
                            <span class="text-xl">×</span>
                        </button>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full border-collapse border border-gray-300">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="border border-gray-300 px-4 py-2 text-right text-xs font-medium text-gray-500">رقم السند</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right text-xs font-medium text-gray-500">النوع</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right text-xs font-medium text-gray-500">التاريخ</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right text-xs font-medium text-gray-500">المبلغ</th>
                                    <th class="border border-gray-300 px-4 py-2 text-right text-xs font-medium text-gray-500">البيان</th>
                                    <th class="border border-gray-300 px-4 py-2 text-center text-xs font-medium text-gray-500">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${vouchers.map(voucher => `
                                    <tr class="hover:bg-gray-50">
                                        <td class="border border-gray-300 px-4 py-2 text-sm">${voucher.voucherNumber}</td>
                                        <td class="border border-gray-300 px-4 py-2 text-sm">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${voucher.type === 'receipt' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                                ${voucher.type === 'receipt' ? '📥 قبض' : '📤 صرف'}
                                            </span>
                                        </td>
                                        <td class="border border-gray-300 px-4 py-2 text-sm">${formatDate(voucher.date)}</td>
                                        <td class="border border-gray-300 px-4 py-2 text-sm font-medium">${(voucher.amount || 0).toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}</td>
                                        <td class="border border-gray-300 px-4 py-2 text-sm">${voucher.description}</td>
                                        <td class="border border-gray-300 px-4 py-2 text-center">
                                            <button onclick="viewVoucher('${voucher.id}')" class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50" title="عرض">
                                                👁️
                                            </button>
                                            <button onclick="printVoucher('${voucher.id}')" class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50 mr-1" title="طباعة">
                                                🖨️
                                            </button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4 flex justify-end">
                        <button onclick="closeLinkedVouchersModal()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                            إغلاق
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

// Close linked vouchers modal
function closeLinkedVouchersModal() {
    const modal = document.getElementById('linkedVouchersModal');
    if (modal) {
        modal.remove();
    }
}

// View voucher
function viewVoucher(voucherId) {
    // Redirect to voucher view page
    window.location.href = `vouchers.html?view=${voucherId}`;
}

// Print voucher
function printVoucher(voucherId) {
    // Redirect to voucher print page
    window.location.href = `vouchers.html?print=${voucherId}`;
}

// View invoice
function viewInvoice(invoiceId) {
    showMessage('عرض الفاتورة قيد التطوير', 'info');
}

// Edit invoice
function editInvoice(invoiceId) {
    showMessage('تعديل الفاتورة قيد التطوير', 'info');
}

// Print invoice
function printInvoice(invoiceId) {
    showMessage('طباعة الفاتورة قيد التطوير', 'info');
}

// Delete invoice
function deleteInvoice(invoiceId) {
    if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
        invoices = invoices.filter(inv => inv.id !== invoiceId);
        saveInvoices();
        renderInvoicesTable();
        updateStats();
        showMessage('تم حذف الفاتورة بنجاح', 'success');
    }
}

// Switch main tab
function switchMainTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.main-tab-content');
    tabContents.forEach(content => {
        content.classList.remove('active');
        content.style.display = 'none';
    });

    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.main-tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500');
    });

    // Show selected tab content
    const selectedTab = document.getElementById(tabName + 'Tab');
    if (selectedTab) {
        selectedTab.classList.add('active');
        selectedTab.style.display = 'block';
    }

    // Activate selected tab button
    const selectedButton = event.target;
    selectedButton.classList.remove('border-transparent', 'text-gray-500');
    selectedButton.classList.add('border-blue-500', 'text-blue-600');
}

// Filter sales invoices
function filterSalesInvoices() {
    // This function will be implemented when we have actual invoice data
    console.log('Filtering sales invoices...');
}

// Add sample invoice data for testing
function addSampleInvoices() {
    const sampleInvoices = [
        {
            id: 'INV-001',
            invoiceNumber: 'INV-2024-001',
            type: INVOICE_TYPES.SALES,
            date: new Date().toISOString(),
            customerId: 'CUST-001',
            customerName: 'أحمد محمد العلي',
            branchId: 1,
            branchName: 'الفرع الرئيسي',
            totalAmount: 1500,
            paidAmount: 1000,
            status: INVOICE_STATUS.PARTIAL,
            items: []
        },
        {
            id: 'INV-002',
            invoiceNumber: 'INV-2024-002',
            type: INVOICE_TYPES.SALES,
            date: new Date().toISOString(),
            customerId: 'CUST-002',
            customerName: 'فاطمة أحمد',
            branchId: 1,
            branchName: 'الفرع الرئيسي',
            totalAmount: 800,
            paidAmount: 800,
            status: INVOICE_STATUS.PAID,
            items: []
        },
        {
            id: 'INV-003',
            invoiceNumber: 'INV-2024-003',
            type: INVOICE_TYPES.SALES_RETURN,
            date: new Date().toISOString(),
            customerId: 'CUST-001',
            customerName: 'أحمد محمد العلي',
            branchId: 1,
            branchName: 'الفرع الرئيسي',
            totalAmount: 200,
            paidAmount: 0,
            status: INVOICE_STATUS.PENDING,
            items: []
        }
    ];

    invoices = sampleInvoices;
    saveInvoices();
    renderInvoicesTable();
    updateStats();
}

// Tab switching functions
function switchMainTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.main-tab-content');
    tabContents.forEach(content => content.classList.remove('active'));

    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.main-tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500');
    });

    // Show selected tab content
    document.getElementById(tabName + 'Tab').classList.add('active');

    // Activate selected tab button
    const activeButton = event.target;
    activeButton.classList.remove('border-transparent', 'text-gray-500');
    activeButton.classList.add('border-blue-500', 'text-blue-600');

    // Load data for the selected tab
    switch(tabName) {
        case 'sales':
            loadSalesInvoices();
            break;
        case 'purchases':
            loadPurchaseInvoices();
            break;
        case 'returns':
            loadReturns();
            break;
        case 'quick':
            loadPOSInterface();
            break;
    }
}

// Load sales invoices
function loadSalesInvoices() {
    renderInvoicesTable(); // This already filters sales invoices
}

// Load purchase invoices
function loadPurchaseInvoices() {
    const tableBody = document.getElementById('purchaseInvoicesTableBody');
    if (!tableBody) return;

    // Filter purchase invoices
    const purchaseInvoices = invoices.filter(invoice =>
        invoice.type === INVOICE_TYPES.PURCHASE
    );

    if (purchaseInvoices.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="11" class="px-6 py-12 text-center text-gray-500">
                    <div class="flex flex-col items-center">
                        <span class="text-4xl mb-4">🛒</span>
                        <p class="text-lg mb-2">لا توجد فواتير شراء حتى الآن</p>
                        <p class="text-sm">ابدأ بإنشاء فاتورة شراء جديدة</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    const currencySymbol = getCurrencySymbol();

    tableBody.innerHTML = purchaseInvoices.map(invoice => {
        const remainingAmount = (invoice.totalAmount || 0) - (invoice.paidAmount || 0);
        const statusBadge = getStatusBadge(invoice.status);
        const voucherButtons = getPurchaseVoucherButtons(invoice);

        return `
            <tr class="hover:bg-gray-50">
                <td class="px-4 py-3 text-sm font-medium text-gray-900">${invoice.invoiceNumber}</td>
                <td class="px-4 py-3 text-sm text-gray-500">${formatDate(invoice.date)}</td>
                <td class="px-4 py-3 text-sm text-gray-500">${invoice.supplierName || 'غير محدد'}</td>
                <td class="px-4 py-3 text-sm text-gray-500">${invoice.branchName || 'غير محدد'}</td>
                <td class="px-4 py-3 text-sm text-gray-500">${invoice.referenceNumber || '-'}</td>
                <td class="px-4 py-3 text-sm font-medium text-gray-900">${(invoice.totalAmount || 0).toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}</td>
                <td class="px-4 py-3 text-sm text-green-600 font-medium">${(invoice.paidAmount || 0).toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}</td>
                <td class="px-4 py-3 text-sm ${remainingAmount > 0 ? 'text-red-600' : 'text-green-600'} font-medium">${remainingAmount.toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}</td>
                <td class="px-4 py-3">${statusBadge}</td>
                <td class="px-4 py-3 text-center">${voucherButtons}</td>
                <td class="px-4 py-3 text-center">
                    <div class="flex justify-center space-x-1">
                        <button onclick="viewInvoice('${invoice.id}')" class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50" title="عرض">
                            👁️
                        </button>
                        <button onclick="editInvoice('${invoice.id}')" class="text-yellow-600 hover:text-yellow-900 p-1 rounded hover:bg-yellow-50" title="تعديل">
                            ✏️
                        </button>
                        <button onclick="printInvoice('${invoice.id}')" class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50" title="طباعة">
                            🖨️
                        </button>
                        <button onclick="deleteInvoice('${invoice.id}')" class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50" title="حذف">
                            🗑️
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// Get purchase voucher buttons
function getPurchaseVoucherButtons(invoice) {
    const remainingAmount = (invoice.totalAmount || 0) - (invoice.paidAmount || 0);
    const hasLinkedVouchers = checkLinkedVouchers(invoice.id);

    let buttons = '';

    // Payment voucher button (if there's remaining amount)
    if (remainingAmount > 0) {
        buttons += `
            <button onclick="createPurchasePaymentVoucher('${invoice.id}')"
                    class="bg-red-100 text-red-800 px-2 py-1 rounded text-xs hover:bg-red-200 mr-1"
                    title="إنشاء سند صرف">
                📤 صرف
            </button>
   