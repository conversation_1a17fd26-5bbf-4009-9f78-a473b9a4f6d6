// Suppliers Management System
let suppliers = [];
let filteredSuppliers = [];

// Check authentication
function checkAuth() {
    const session = localStorage.getItem('anwar_bakery_session') ||
                   sessionStorage.getItem('anwar_bakery_session');

    if (!session) {
        window.location.href = 'login.html';
        return null;
    }

    return JSON.parse(session);
}

// Load user info
function loadUserInfo() {
    const session = checkAuth();
    if (session) {
        document.getElementById('userFullName').textContent = session.fullName;
        document.getElementById('userDisplayName').textContent = session.fullName;
        document.getElementById('userRole').textContent = session.role;

        const loginTime = new Date(session.loginTime);
        document.getElementById('loginTime').textContent =
            'آخر دخول: ' + loginTime.toLocaleString('ar-SA');
    }
}

// Load company info
function loadCompanyInfo() {
    const savedData = localStorage.getItem('anwar_bakery_company');
    if (savedData) {
        const companyData = JSON.parse(savedData);
        if (companyData.companyNameAr) {
            document.getElementById('sidebarCompanyName').textContent = companyData.companyNameAr;
            document.title = `إدارة الموردين - ${companyData.companyNameAr}`;
        }
    }
}

// Load suppliers from localStorage
function loadSuppliers() {
    const savedSuppliers = localStorage.getItem('anwar_bakery_suppliers');
    if (savedSuppliers) {
        suppliers = JSON.parse(savedSuppliers);
    } else {
        // Create sample suppliers
        suppliers = [
            {
                id: 1,
                supplierCode: 'SUP001',
                supplierName: 'شركة الدقيق الذهبي',
                category: 'raw_materials',
                contactPerson: 'أحمد محمد',
                phone: '0501234567',
                email: '<EMAIL>',
                address: 'الرياض، المملكة العربية السعودية',
                branchId: 1,
                branchName: 'الفرع الرئيسي',
                taxNumber: '*********',
                commercialRegister: 'CR123456',
                paymentTerms: 30,
                creditLimit: 50000,
                openingBalance: 14000,
                currentBalance: 15000,
                isActive: true,
                notes: 'مورد رئيسي للدقيق والمواد الخام',
                createdAt: '2024-01-15'
            },
            {
                id: 2,
                supplierCode: 'SUP002',
                supplierName: 'مؤسسة التعبئة والتغليف',
                category: 'packaging',
                contactPerson: 'سارة أحمد',
                phone: '0507654321',
                email: '<EMAIL>',
                address: 'جدة، المملكة العربية السعودية',
                branchId: 2,
                branchName: 'فرع الملك فهد',
                taxNumber: '*********',
                commercialRegister: 'CR987654',
                paymentTerms: 15,
                creditLimit: 25000,
                openingBalance: 8000,
                currentBalance: 8500,
                isActive: true,
                notes: 'مورد مواد التعبئة والتغليف',
                createdAt: '2024-01-20'
            }
        ];
        saveSuppliers();
    }
    renderSuppliers();
    updateStats();
}

// Save suppliers to localStorage
function saveSuppliers() {
    localStorage.setItem('anwar_bakery_suppliers', JSON.stringify(suppliers));
}

// Update statistics
function updateStats() {
    const totalSuppliers = suppliers.length;
    const activeSuppliers = suppliers.filter(s => s.isActive).length;
    const inactiveSuppliers = suppliers.filter(s => !s.isActive).length;
    const totalDebt = suppliers.reduce((sum, s) => sum + (s.currentBalance || 0), 0);

    document.getElementById('totalSuppliers').textContent = totalSuppliers;
    document.getElementById('activeSuppliers').textContent = activeSuppliers;
    document.getElementById('inactiveSuppliers').textContent = inactiveSuppliers;
    document.getElementById('totalDebt').textContent = totalDebt.toFixed(2);
}

// Render suppliers table
function renderSuppliers(filteredSuppliers = suppliers) {
    const tbody = document.getElementById('suppliersTableBody');
    tbody.innerHTML = '';

    filteredSuppliers.forEach(supplier => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';

        const categoryNames = {
            'raw_materials': 'مواد خام',
            'packaging': 'مواد تعبئة',
            'equipment': 'معدات',
            'services': 'خدمات',
            'other': 'أخرى'
        };

        const statusClass = supplier.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
        const statusText = supplier.isActive ? 'نشط' : 'معطل';

        row.innerHTML = `
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">${supplier.supplierCode}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <div class="font-medium">${supplier.supplierName}</div>
                <div class="text-xs text-gray-500">${supplier.contactPerson || ''}</div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                    ${categoryNames[supplier.category] || 'غير محدد'}
                </span>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">${supplier.phone || '-'}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">${supplier.branchName || '-'}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">${supplier.email || '-'}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 ${supplier.currentBalance > 0 ? 'text-red-600 font-semibold' : 'text-green-600'}">
                ${(supplier.currentBalance || 0).toFixed(2)} ${getCurrencySymbol()}
            </td>
            <td class="px-4 py-3 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                    ${statusText}
                </span>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-center">
                <div class="flex justify-center space-x-1">
                    <button onclick="editSupplier(${supplier.id})" class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50" title="تعديل">
                        ✏️
                    </button>
                    <button onclick="viewSupplierDetails(${supplier.id})" class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50" title="عرض التفاصيل">
                        👁️
                    </button>
                    <button onclick="toggleSupplierStatus(${supplier.id})" class="text-yellow-600 hover:text-yellow-900 p-1 rounded hover:bg-yellow-50" title="${supplier.isActive ? 'إلغاء تفعيل' : 'تفعيل'}">
                        ${supplier.isActive ? '⏸️' : '▶️'}
                    </button>
                    <button onclick="deleteSupplier(${supplier.id})" class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50" title="حذف">
                        🗑️
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Filter suppliers
function filterSuppliers() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const branchFilter = document.getElementById('branchFilter').value;
    const categoryFilter = document.getElementById('categoryFilter').value;

    const filtered = suppliers.filter(supplier => {
        const matchesSearch = supplier.supplierName.toLowerCase().includes(searchTerm) ||
                            supplier.supplierCode.toLowerCase().includes(searchTerm) ||
                            (supplier.contactPerson && supplier.contactPerson.toLowerCase().includes(searchTerm));

        let matchesStatus = true;
        if (statusFilter === 'active') {
            matchesStatus = supplier.isActive;
        } else if (statusFilter === 'inactive') {
            matchesStatus = !supplier.isActive;
        }

        const matchesBranch = !branchFilter || supplier.branchId == branchFilter;
        const matchesCategory = !categoryFilter || supplier.category === categoryFilter;

        return matchesSearch && matchesStatus && matchesBranch && matchesCategory;
    });

    renderSuppliers(filtered);
}

// Load branches for filters
function loadBranches() {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    let branches = [];

    if (savedBranches) {
        branches = JSON.parse(savedBranches);
    } else {
        branches = [
            { id: 1, branchName: 'الفرع الرئيسي', isActive: true },
            { id: 2, branchName: 'فرع الملك فهد', isActive: true },
            { id: 3, branchName: 'فرع العليا', isActive: true }
        ];
        localStorage.setItem('anwar_bakery_branches', JSON.stringify(branches));
    }

    const branchFilter = document.getElementById('branchFilter');
    if (branchFilter) {
        branchFilter.innerHTML = '<option value="">جميع الفروع</option>';
        branches.filter(branch => branch.isActive).forEach(branch => {
            const option = document.createElement('option');
            option.value = branch.id;
            option.textContent = branch.branchName;
            branchFilter.appendChild(option);
        });
    }
}

// Toggle supplier status
function toggleSupplierStatus(id) {
    const supplier = suppliers.find(s => s.id === id);
    if (supplier) {
        supplier.isActive = !supplier.isActive;
        saveSuppliers();
        renderSuppliers();
        updateStats();
        showMessage(`تم ${supplier.isActive ? 'تفعيل' : 'إلغاء تفعيل'} المورد بنجاح!`, 'success');
    }
}

// Delete supplier
function deleteSupplier(id) {
    if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
        suppliers = suppliers.filter(s => s.id !== id);
        saveSuppliers();
        renderSuppliers();
        updateStats();
        showMessage('تم حذف المورد بنجاح!', 'success');
    }
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Update current date time
function updateDateTime() {
    const now = new Date();
    const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    document.getElementById('currentDateTime').textContent =
        now.toLocaleDateString('ar-SA', options);
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (sidebar.classList.contains('translate-x-full')) {
        sidebar.classList.remove('translate-x-full');
        overlay.classList.remove('hidden');
    } else {
        sidebar.classList.add('translate-x-full');
        overlay.classList.add('hidden');
    }
}

// Get currency symbol from company settings
function getCurrencySymbol() {
    const companyData = localStorage.getItem('anwar_bakery_company');
    if (companyData) {
        const company = JSON.parse(companyData);
        return company.currency || 'ر.س';
    }
    return 'ر.س';
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_session');
        sessionStorage.removeItem('anwar_bakery_session');
        window.location.href = 'login.html';
    }
}

// Populate branch dropdowns
function populateBranchDropdowns() {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    let branches = [];

    if (savedBranches) {
        branches = JSON.parse(savedBranches);
    } else {
        branches = [
            { id: 1, branchName: 'الفرع الرئيسي', isActive: true },
            { id: 2, branchName: 'فرع الملك فهد', isActive: true },
            { id: 3, branchName: 'فرع العليا', isActive: true }
        ];
        localStorage.setItem('anwar_bakery_branches', JSON.stringify(branches));
    }

    const dropdowns = ['supplierBranch', 'editSupplierBranch'];
    dropdowns.forEach(dropdownId => {
        const dropdown = document.getElementById(dropdownId);
        if (dropdown) {
            dropdown.innerHTML = '<option value="">اختر الفرع</option>';
            branches.filter(branch => branch.isActive).forEach(branch => {
                const option = document.createElement('option');
                option.value = branch.id;
                option.textContent = branch.branchName;
                dropdown.appendChild(option);
            });
        }
    });
}

// Open add supplier modal
function openAddModal() {
    document.getElementById('addSupplierModal').classList.remove('hidden');
    document.getElementById('addSupplierModal').style.display = 'block';
    generateSupplierCode();
    populateBranchDropdowns();
}

// Close add supplier modal
function closeAddModal() {
    document.getElementById('addSupplierModal').classList.add('hidden');
    document.getElementById('addSupplierModal').style.display = 'none';
    document.getElementById('addSupplierForm').reset();
}

// Generate supplier code
function generateSupplierCode() {
    const nextId = Math.max(...suppliers.map(s => s.id), 0) + 1;
    const code = `SUPP${nextId.toString().padStart(4, '0')}`;
    document.getElementById('supplierCode').value = code;
}

// Add supplier
function addSupplier(event) {
    event.preventDefault();

    const branchId = document.getElementById('supplierBranch').value;
    let branchName = '';

    if (branchId) {
        const savedBranches = localStorage.getItem('anwar_bakery_branches');
        if (savedBranches) {
            const branches = JSON.parse(savedBranches);
            const branch = branches.find(b => b.id == branchId);
            branchName = branch ? branch.branchName : '';
        }
    }

    const newSupplier = {
        id: Math.max(...suppliers.map(s => s.id), 0) + 1,
        supplierCode: document.getElementById('supplierCode').value,
        supplierName: document.getElementById('supplierName').value,
        category: document.getElementById('supplierCategory').value,
        phone: document.getElementById('supplierPhone').value,
        email: document.getElementById('supplierEmail').value,
        branchId: parseInt(branchId) || null,
        branchName: branchName,
        contactPerson: document.getElementById('contactPerson').value,
        taxNumber: document.getElementById('taxNumber').value,
        commercialReg: document.getElementById('commercialReg').value,
        city: document.getElementById('supplierCity').value,
        address: document.getElementById('supplierAddress').value,
        creditLimit: parseFloat(document.getElementById('creditLimit').value) || 0,
        openingBalance: 0,
        currentBalance: 0,
        notes: document.getElementById('supplierNotes').value,
        isActive: true,
        createdAt: new Date().toISOString().split('T')[0],
        accountCode: `2101${(Math.max(...suppliers.map(s => s.id), 0) + 1).toString().padStart(3, '0')}`
    };

    suppliers.push(newSupplier);
    saveSuppliers();
    renderSuppliers(suppliers);
    updateStats();
    closeAddModal();
    showMessage('تم إضافة المورد بنجاح!', 'success');
}

// Edit supplier
function editSupplier(id) {
    const supplier = suppliers.find(s => s.id === id);
    if (supplier) {
        document.getElementById('editSupplierId').value = supplier.id;
        document.getElementById('editSupplierCode').value = supplier.supplierCode;
        document.getElementById('editSupplierName').value = supplier.supplierName;
        document.getElementById('editSupplierCategory').value = supplier.category;
        document.getElementById('editSupplierPhone').value = supplier.phone;
        document.getElementById('editSupplierEmail').value = supplier.email || '';
        document.getElementById('editSupplierBranch').value = supplier.branchId || '';
        document.getElementById('editContactPerson').value = supplier.contactPerson || '';
        document.getElementById('editSupplierCity').value = supplier.city || '';
        document.getElementById('editSupplierAddress').value = supplier.address || '';
        document.getElementById('editCreditLimit').value = supplier.creditLimit || 0;
        document.getElementById('editSupplierNotes').value = supplier.notes || '';

        populateBranchDropdowns();
        document.getElementById('editSupplierModal').classList.remove('hidden');
        document.getElementById('editSupplierModal').style.display = 'block';
    }
}

// Close edit supplier modal
function closeEditModal() {
    document.getElementById('editSupplierModal').classList.add('hidden');
    document.getElementById('editSupplierModal').style.display = 'none';
}

// Update supplier
function updateSupplier(event) {
    event.preventDefault();

    const supplierId = parseInt(document.getElementById('editSupplierId').value);
    const branchId = document.getElementById('editSupplierBranch').value;
    let branchName = '';

    if (branchId) {
        const savedBranches = localStorage.getItem('anwar_bakery_branches');
        if (savedBranches) {
            const branches = JSON.parse(savedBranches);
            const branch = branches.find(b => b.id == branchId);
            branchName = branch ? branch.branchName : '';
        }
    }

    const supplierIndex = suppliers.findIndex(s => s.id === supplierId);
    if (supplierIndex !== -1) {
        suppliers[supplierIndex] = {
            ...suppliers[supplierIndex],
            supplierName: document.getElementById('editSupplierName').value,
            category: document.getElementById('editSupplierCategory').value,
            phone: document.getElementById('editSupplierPhone').value,
            email: document.getElementById('editSupplierEmail').value,
            branchId: parseInt(branchId) || null,
            branchName: branchName,
            contactPerson: document.getElementById('editContactPerson').value,
            city: document.getElementById('editSupplierCity').value,
            address: document.getElementById('editSupplierAddress').value,
            creditLimit: parseFloat(document.getElementById('editCreditLimit').value) || 0,
            notes: document.getElementById('editSupplierNotes').value
        };

        saveSuppliers();
        renderSuppliers(suppliers);
        updateStats();
        closeEditModal();
        showMessage('تم تحديث بيانات المورد بنجاح!', 'success');
    }
}

// View supplier details
function viewSupplierDetails(id) {
    const supplier = suppliers.find(s => s.id === id);
    if (supplier) {
        const currencySymbol = getCurrencySymbol();
        let details = `تفاصيل المورد: ${supplier.supplierName}\n\n`;
        details += `🆔 الكود: ${supplier.supplierCode}\n`;
        details += `📂 الفئة: ${getCategoryName(supplier.category)}\n`;
        details += `📱 الهاتف: ${supplier.phone}\n`;
        details += `📧 البريد: ${supplier.email || 'غير محدد'}\n`;
        details += `🏢 الفرع: ${supplier.branchName || 'غير محدد'}\n`;
        details += `👤 الشخص المسؤول: ${supplier.contactPerson || 'غير محدد'}\n`;
        details += `🏙️ المدينة: ${supplier.city || 'غير محدد'}\n`;
        details += `💰 حد الائتمان: ${supplier.creditLimit.toFixed(2)} ${currencySymbol}\n`;
        details += `💵 الرصيد الحالي: ${supplier.currentBalance.toFixed(2)} ${currencySymbol}\n`;
        details += `📊 الحالة: ${supplier.isActive ? 'نشط' : 'غير نشط'}\n`;
        if (supplier.notes) {
            details += `📝 ملاحظات: ${supplier.notes}\n`;
        }

        alert(details);
    }
}

// Get category name in Arabic
function getCategoryName(category) {
    const categories = {
        'raw_materials': 'مواد خام',
        'packaging': 'مواد تعبئة',
        'equipment': 'معدات',
        'services': 'خدمات',
        'other': 'أخرى'
    };
    return categories[category] || category;
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    checkAuth();
    loadUserInfo();
    loadCompanyInfo();
    loadBranches();
    loadSuppliers();
    updateDateTime();
    setInterval(updateDateTime, 60000);

    // Hide modals initially
    document.getElementById('addSupplierModal').style.display = 'none';
    document.getElementById('editSupplierModal').style.display = 'none';
});

// Close sidebar when clicking overlay
document.getElementById('mobileOverlay').addEventListener('click', toggleSidebar);
