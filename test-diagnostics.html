<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام التشخيص</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-6">اختبار نظام التشخيص</h1>
        
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">اختبار الوظائف</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button onclick="testBasicDiagnostics()" class="bg-blue-600 text-white p-3 rounded-lg hover:bg-blue-700">
                    🔍 اختبار الفحص الأساسي
                </button>
                <button onclick="testFullDiagnostics()" class="bg-green-600 text-white p-3 rounded-lg hover:bg-green-700">
                    🔧 اختبار الفحص الشامل
                </button>
                <button onclick="testAutoFix()" class="bg-orange-600 text-white p-3 rounded-lg hover:bg-orange-700">
                    ⚡ اختبار الإصلاح التلقائي
                </button>
                <button onclick="clearResults()" class="bg-gray-600 text-white p-3 rounded-lg hover:bg-gray-700">
                    🗑️ مسح النتائج
                </button>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">نتائج الاختبار</h2>
            <div id="testResults" class="space-y-3">
                <p class="text-gray-500">لم يتم تشغيل أي اختبار بعد</p>
            </div>
        </div>
    </div>

    <!-- Include system diagnostics -->
    <script src="system-diagnostics.js"></script>
    
    <script>
        // Test functions
        async function testBasicDiagnostics() {
            addResult('🔍 بدء اختبار الفحص الأساسي...', 'info');
            
            try {
                // Test if SystemDiagnostics class exists
                if (typeof SystemDiagnostics === 'undefined') {
                    addResult('❌ فئة SystemDiagnostics غير موجودة', 'error');
                    return;
                }
                
                // Test if global instance exists
                if (typeof window.systemDiagnostics === 'undefined') {
                    addResult('❌ المثيل العام systemDiagnostics غير موجود', 'error');
                    return;
                }
                
                addResult('✅ فئة SystemDiagnostics موجودة', 'success');
                addResult('✅ المثيل العام systemDiagnostics موجود', 'success');
                
                // Test basic methods
                const methods = [
                    'runFullDiagnostics',
                    'checkDataIntegrity',
                    'checkAccountingBalance',
                    'checkDatabaseConsistency',
                    'autoFixIssues'
                ];
                
                methods.forEach(method => {
                    if (typeof window.systemDiagnostics[method] === 'function') {
                        addResult(`✅ الدالة ${method} موجودة`, 'success');
                    } else {
                        addResult(`❌ الدالة ${method} مفقودة`, 'error');
                    }
                });
                
                addResult('✅ اكتمل اختبار الفحص الأساسي بنجاح', 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في الاختبار الأساسي: ${error.message}`, 'error');
            }
        }

        async function testFullDiagnostics() {
            addResult('🔧 بدء اختبار الفحص الشامل...', 'info');
            
            try {
                // Initialize some test data
                initializeTestData();
                
                // Run full diagnostics
                const report = await window.systemDiagnostics.runFullDiagnostics();
                
                addResult(`✅ تم الفحص الشامل بنجاح`, 'success');
                addResult(`📊 إجمالي المشاكل: ${report.totalIssues}`, 'info');
                addResult(`❌ أخطاء: ${report.errors}`, report.errors > 0 ? 'error' : 'success');
                addResult(`⚠️ تحذيرات: ${report.warnings}`, report.warnings > 0 ? 'warning' : 'success');
                addResult(`ℹ️ معلومات: ${report.info}`, 'info');
                addResult(`🔧 تم إصلاحها: ${report.autoFixed}`, 'success');
                
                // Display issues
                if (report.issues && report.issues.length > 0) {
                    addResult('📋 المشاكل المكتشفة:', 'info');
                    report.issues.forEach(issue => {
                        const type = issue.type === 'error' ? 'error' : 
                                   issue.type === 'warning' ? 'warning' : 'info';
                        addResult(`${getIssueIcon(issue.type)} ${issue.message}`, type);
                    });
                } else {
                    addResult('✅ لم يتم العثور على مشاكل', 'success');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في الفحص الشامل: ${error.message}`, 'error');
                console.error('Full diagnostics error:', error);
            }
        }

        async function testAutoFix() {
            addResult('⚡ بدء اختبار الإصلاح التلقائي...', 'info');
            
            try {
                // Create some test issues first
                createTestIssues();
                
                // Run diagnostics to detect issues
                await window.systemDiagnostics.runFullDiagnostics();
                
                // Run auto fix
                await window.systemDiagnostics.autoFixIssues();
                
                addResult('✅ تم اختبار الإصلاح التلقائي بنجاح', 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في اختبار الإصلاح التلقائي: ${error.message}`, 'error');
                console.error('Auto fix test error:', error);
            }
        }

        function initializeTestData() {
            // Initialize clean test data
            const testData = {
                'anwar_bakery_products': JSON.stringify([
                    { id: 1, name: 'خبز أبيض', price: 5, quantity: 100 },
                    { id: 2, name: 'كيك شوكولاتة', price: 30, quantity: 50 }
                ]),
                'anwar_bakery_customers': JSON.stringify([
                    { id: 1, name: 'أحمد محمد', phone: '0501234567' },
                    { id: 2, name: 'فاطمة علي', phone: '0507654321' }
                ]),
                'anwar_bakery_journal_entries': JSON.stringify([]),
                'anwar_bakery_invoices': JSON.stringify([]),
                'anwar_bakery_vouchers': JSON.stringify([])
            };
            
            Object.keys(testData).forEach(key => {
                localStorage.setItem(key, testData[key]);
            });
            
            addResult('📝 تم تهيئة بيانات الاختبار', 'info');
        }

        function createTestIssues() {
            // Create some test issues for auto-fix testing
            const corruptedData = {
                'anwar_bakery_test_corrupted': 'invalid json data {'
            };
            
            Object.keys(corruptedData).forEach(key => {
                localStorage.setItem(key, corruptedData[key]);
            });
            
            addResult('🐛 تم إنشاء مشاكل اختبارية', 'warning');
        }

        function getIssueIcon(type) {
            switch (type) {
                case 'error': return '❌';
                case 'warning': return '⚠️';
                case 'info': return 'ℹ️';
                default: return '📋';
            }
        }

        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultElement = document.createElement('div');
            
            let bgColor = 'bg-gray-50';
            let textColor = 'text-gray-700';
            
            switch (type) {
                case 'success':
                    bgColor = 'bg-green-50';
                    textColor = 'text-green-700';
                    break;
                case 'error':
                    bgColor = 'bg-red-50';
                    textColor = 'text-red-700';
                    break;
                case 'warning':
                    bgColor = 'bg-yellow-50';
                    textColor = 'text-yellow-700';
                    break;
                case 'info':
                    bgColor = 'bg-blue-50';
                    textColor = 'text-blue-700';
                    break;
            }
            
            resultElement.className = `p-3 rounded-lg ${bgColor} ${textColor} text-sm`;
            resultElement.innerHTML = `
                <span class="font-mono text-xs text-gray-500">[${new Date().toLocaleTimeString()}]</span>
                <span class="mr-2">${message}</span>
            `;
            
            // Clear "no tests" message if it exists
            if (resultsDiv.children.length === 1 && resultsDiv.children[0].textContent.includes('لم يتم تشغيل')) {
                resultsDiv.innerHTML = '';
            }
            
            resultsDiv.appendChild(resultElement);
            
            // Scroll to bottom
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<p class="text-gray-500">تم مسح النتائج</p>';
        }

        // Auto-run basic test on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                addResult('🚀 مرحباً بك في اختبار نظام التشخيص', 'info');
                addResult('💡 اضغط على الأزرار أعلاه لبدء الاختبارات', 'info');
            }, 500);
        });
    </script>
</body>
</html>
