// System Diagnostics and Auto-Fix Utilities

class SystemDiagnostics {
    constructor() {
        this.diagnosticResults = [];
        this.autoFixEnabled = true;
        this.backupBeforeFix = true;
    }

    // Main diagnostic function
    async runFullDiagnostics() {
        this.diagnosticResults = [];

        console.log('🔍 بدء فحص النظام الشامل...');

        // Run all diagnostic checks
        await this.checkDataIntegrity();
        await this.checkAccountingBalance();
        await this.checkDatabaseConsistency();
        await this.checkSystemPerformance();
        await this.checkSecurityIssues();
        await this.checkBusinessLogic();

        // Generate report
        const report = this.generateDiagnosticReport();

        // Auto-fix if enabled
        if (this.autoFixEnabled) {
            await this.autoFixIssues();
        }

        return report;
    }

    // Check data integrity
    async checkDataIntegrity() {
        console.log('📊 فحص سلامة البيانات...');

        try {
            // Check for corrupted localStorage data
            const corruptedKeys = [];
            for (let key in localStorage) {
                if (key.startsWith('anwar_bakery_')) {
                    try {
                        JSON.parse(localStorage[key]);
                    } catch (error) {
                        corruptedKeys.push(key);
                    }
                }
            }

            if (corruptedKeys.length > 0) {
                this.diagnosticResults.push({
                    type: 'error',
                    category: 'data_integrity',
                    message: `بيانات تالفة في: ${corruptedKeys.join(', ')}`,
                    autoFixable: true,
                    fixAction: () => this.fixCorruptedData(corruptedKeys)
                });
            }

            // Check for missing essential data
            const essentialKeys = [
                'anwar_bakery_company',
                'anwar_bakery_accounts',
                'anwar_bakery_products'
            ];

            const missingKeys = essentialKeys.filter(key => !localStorage.getItem(key));
            if (missingKeys.length > 0) {
                this.diagnosticResults.push({
                    type: 'warning',
                    category: 'data_integrity',
                    message: `بيانات أساسية مفقودة: ${missingKeys.join(', ')}`,
                    autoFixable: true,
                    fixAction: () => this.initializeMissingData(missingKeys)
                });
            }

            // Check for duplicate IDs
            await this.checkDuplicateIds();

        } catch (error) {
            this.diagnosticResults.push({
                type: 'error',
                category: 'data_integrity',
                message: `خطأ في فحص سلامة البيانات: ${error.message}`,
                autoFixable: false
            });
        }
    }

    // Check accounting balance
    async checkAccountingBalance() {
        console.log('⚖️ فحص التوازن المحاسبي...');

        try {
            const journalEntries = JSON.parse(localStorage.getItem('anwar_bakery_journal_entries') || '[]');
            let totalDebit = 0;
            let totalCredit = 0;
            const unbalancedEntries = [];

            journalEntries.forEach(entry => {
                if (entry.entries && Array.isArray(entry.entries)) {
                    let entryDebit = 0;
                    let entryCredit = 0;

                    entry.entries.forEach(line => {
                        entryDebit += parseFloat(line.debit || 0);
                        entryCredit += parseFloat(line.credit || 0);
                    });

                    totalDebit += entryDebit;
                    totalCredit += entryCredit;

                    // Check individual entry balance
                    if (Math.abs(entryDebit - entryCredit) > 0.01) {
                        unbalancedEntries.push({
                            id: entry.id,
                            date: entry.date,
                            debit: entryDebit,
                            credit: entryCredit,
                            difference: entryDebit - entryCredit
                        });
                    }
                }
            });

            // Check overall balance
            const totalDifference = Math.abs(totalDebit - totalCredit);
            if (totalDifference > 0.01) {
                this.diagnosticResults.push({
                    type: 'error',
                    category: 'accounting_balance',
                    message: `عدم توازن في الحسابات: الفرق ${totalDifference.toFixed(2)}`,
                    autoFixable: true,
                    fixAction: () => this.fixAccountingBalance(totalDebit, totalCredit)
                });
            }

            // Report unbalanced entries
            if (unbalancedEntries.length > 0) {
                this.diagnosticResults.push({
                    type: 'warning',
                    category: 'accounting_balance',
                    message: `قيود غير متوازنة: ${unbalancedEntries.length} قيد`,
                    autoFixable: true,
                    fixAction: () => this.fixUnbalancedEntries(unbalancedEntries)
                });
            }

        } catch (error) {
            this.diagnosticResults.push({
                type: 'error',
                category: 'accounting_balance',
                message: `خطأ في فحص التوازن المحاسبي: ${error.message}`,
                autoFixable: false
            });
        }
    }

    // Check database consistency
    async checkDatabaseConsistency() {
        console.log('🗄️ فحص اتساق قاعدة البيانات...');

        try {
            // Check foreign key relationships
            await this.checkForeignKeyIntegrity();

            // Check for orphaned records
            await this.checkOrphanedRecords();

            // Check data types and formats
            await this.checkDataFormats();

        } catch (error) {
            this.diagnosticResults.push({
                type: 'error',
                category: 'database_consistency',
                message: `خطأ في فحص اتساق قاعدة البيانات: ${error.message}`,
                autoFixable: false
            });
        }
    }

    // Check foreign key integrity
    async checkForeignKeyIntegrity() {
        console.log('🔗 فحص سلامة المفاتيح الخارجية...');

        try {
            const collections = [
                { name: 'products', foreignKeys: [{ field: 'category_id', ref: 'categories' }] },
                { name: 'invoices', foreignKeys: [{ field: 'customer_id', ref: 'customers' }, { field: 'supplier_id', ref: 'suppliers' }] },
                { name: 'vouchers', foreignKeys: [{ field: 'customer_id', ref: 'customers' }, { field: 'supplier_id', ref: 'suppliers' }] }
            ];

            collections.forEach(collection => {
                const data = JSON.parse(localStorage.getItem(`anwar_bakery_${collection.name}`) || '[]');

                collection.foreignKeys.forEach(fk => {
                    const refData = JSON.parse(localStorage.getItem(`anwar_bakery_${fk.ref}`) || '[]');
                    const refIds = refData.map(item => item.id);

                    const orphanedRecords = data.filter(item =>
                        item[fk.field] && !refIds.includes(item[fk.field])
                    );

                    if (orphanedRecords.length > 0) {
                        this.diagnosticResults.push({
                            type: 'warning',
                            category: 'database_consistency',
                            message: `سجلات معلقة في ${collection.name}: ${orphanedRecords.length} سجل`,
                            autoFixable: true,
                            fixAction: () => this.fixOrphanedRecords(collection.name, fk.field, orphanedRecords)
                        });
                    }
                });
            });

        } catch (error) {
            console.error('Error checking foreign key integrity:', error);
        }
    }

    // Check for orphaned records
    async checkOrphanedRecords() {
        console.log('🔍 فحص السجلات المعلقة...');

        try {
            // Check for invoices without valid customers/suppliers
            const invoices = JSON.parse(localStorage.getItem('anwar_bakery_invoices') || '[]');
            const customers = JSON.parse(localStorage.getItem('anwar_bakery_customers') || '[]');
            const suppliers = JSON.parse(localStorage.getItem('anwar_bakery_suppliers') || '[]');

            const customerIds = customers.map(c => c.id);
            const supplierIds = suppliers.map(s => s.id);

            const orphanedInvoices = invoices.filter(invoice => {
                if (invoice.type === 'sales' && invoice.customer_id && !customerIds.includes(invoice.customer_id)) {
                    return true;
                }
                if (invoice.type === 'purchase' && invoice.supplier_id && !supplierIds.includes(invoice.supplier_id)) {
                    return true;
                }
                return false;
            });

            if (orphanedInvoices.length > 0) {
                this.diagnosticResults.push({
                    type: 'warning',
                    category: 'database_consistency',
                    message: `فواتير معلقة: ${orphanedInvoices.length} فاتورة`,
                    autoFixable: true,
                    fixAction: () => this.fixOrphanedInvoices(orphanedInvoices)
                });
            }

        } catch (error) {
            console.error('Error checking orphaned records:', error);
        }
    }

    // Check data formats
    async checkDataFormats() {
        console.log('📋 فحص تنسيق البيانات...');

        try {
            const collections = ['products', 'customers', 'suppliers', 'invoices'];

            collections.forEach(collectionName => {
                const data = JSON.parse(localStorage.getItem(`anwar_bakery_${collectionName}`) || '[]');
                const invalidRecords = [];

                data.forEach(record => {
                    // Check for required fields
                    if (!record.id || !record.name) {
                        invalidRecords.push(record);
                    }

                    // Check numeric fields
                    if (collectionName === 'products') {
                        if (isNaN(record.price) || isNaN(record.quantity)) {
                            invalidRecords.push(record);
                        }
                    }

                    // Check date formats
                    if (record.date && !this.isValidDate(record.date)) {
                        invalidRecords.push(record);
                    }
                });

                if (invalidRecords.length > 0) {
                    this.diagnosticResults.push({
                        type: 'warning',
                        category: 'database_consistency',
                        message: `تنسيق بيانات غير صحيح في ${collectionName}: ${invalidRecords.length} سجل`,
                        autoFixable: true,
                        fixAction: () => this.fixInvalidRecords(collectionName, invalidRecords)
                    });
                }
            });

        } catch (error) {
            console.error('Error checking data formats:', error);
        }
    }

    // Helper function to validate dates
    isValidDate(dateString) {
        const date = new Date(dateString);
        return date instanceof Date && !isNaN(date);
    }

    // Fix orphaned records
    async fixOrphanedRecords(collectionName, fieldName, orphanedRecords) {
        try {
            const data = JSON.parse(localStorage.getItem(`anwar_bakery_${collectionName}`) || '[]');

            // Remove foreign key reference from orphaned records
            orphanedRecords.forEach(orphaned => {
                const index = data.findIndex(item => item.id === orphaned.id);
                if (index !== -1) {
                    data[index][fieldName] = null;
                }
            });

            localStorage.setItem(`anwar_bakery_${collectionName}`, JSON.stringify(data));
            console.log(`🔧 تم إصلاح السجلات المعلقة في ${collectionName}`);

        } catch (error) {
            console.error('Error fixing orphaned records:', error);
        }
    }

    // Fix orphaned invoices
    async fixOrphanedInvoices(orphanedInvoices) {
        try {
            const invoices = JSON.parse(localStorage.getItem('anwar_bakery_invoices') || '[]');

            orphanedInvoices.forEach(orphaned => {
                const index = invoices.findIndex(inv => inv.id === orphaned.id);
                if (index !== -1) {
                    // Remove invalid customer/supplier reference
                    if (orphaned.type === 'sales') {
                        invoices[index].customer_id = null;
                        invoices[index].customer_name = 'عميل محذوف';
                    } else if (orphaned.type === 'purchase') {
                        invoices[index].supplier_id = null;
                        invoices[index].supplier_name = 'مورد محذوف';
                    }
                }
            });

            localStorage.setItem('anwar_bakery_invoices', JSON.stringify(invoices));
            console.log('🔧 تم إصلاح الفواتير المعلقة');

        } catch (error) {
            console.error('Error fixing orphaned invoices:', error);
        }
    }

    // Fix invalid records
    async fixInvalidRecords(collectionName, invalidRecords) {
        try {
            const data = JSON.parse(localStorage.getItem(`anwar_bakery_${collectionName}`) || '[]');

            invalidRecords.forEach(invalid => {
                const index = data.findIndex(item => item.id === invalid.id);
                if (index !== -1) {
                    // Fix missing required fields
                    if (!data[index].id) {
                        data[index].id = Date.now() + Math.random();
                    }
                    if (!data[index].name) {
                        data[index].name = 'غير محدد';
                    }

                    // Fix numeric fields
                    if (collectionName === 'products') {
                        if (isNaN(data[index].price)) {
                            data[index].price = 0;
                        }
                        if (isNaN(data[index].quantity)) {
                            data[index].quantity = 0;
                        }
                    }

                    // Fix date fields
                    if (data[index].date && !this.isValidDate(data[index].date)) {
                        data[index].date = new Date().toISOString().split('T')[0];
                    }
                }
            });

            localStorage.setItem(`anwar_bakery_${collectionName}`, JSON.stringify(data));
            console.log(`🔧 تم إصلاح البيانات غير الصحيحة في ${collectionName}`);

        } catch (error) {
            console.error('Error fixing invalid records:', error);
        }
    }

    // Check system performance
    async checkSystemPerformance() {
        console.log('⚡ فحص أداء النظام...');

        try {
            // Check localStorage size
            let totalSize = 0;
            for (let key in localStorage) {
                if (key.startsWith('anwar_bakery_')) {
                    totalSize += localStorage[key].length;
                }
            }

            const sizeInMB = totalSize / (1024 * 1024);
            if (sizeInMB > 5) {
                this.diagnosticResults.push({
                    type: 'warning',
                    category: 'performance',
                    message: `حجم البيانات كبير: ${sizeInMB.toFixed(2)} MB`,
                    autoFixable: true,
                    fixAction: () => this.optimizeDataStorage()
                });
            }

            // Check for old backup files
            const backups = JSON.parse(localStorage.getItem('anwar_bakery_backup_list') || '[]');
            if (backups.length > 20) {
                this.diagnosticResults.push({
                    type: 'info',
                    category: 'performance',
                    message: `عدد كبير من النسخ الاحتياطية: ${backups.length}`,
                    autoFixable: true,
                    fixAction: () => this.cleanupOldBackups()
                });
            }

        } catch (error) {
            this.diagnosticResults.push({
                type: 'error',
                category: 'performance',
                message: `خطأ في فحص الأداء: ${error.message}`,
                autoFixable: false
            });
        }
    }

    // Check security issues
    async checkSecurityIssues() {
        console.log('🔒 فحص الأمان...');

        try {
            // Check for sensitive data exposure
            const sensitiveKeys = ['password', 'token', 'secret'];
            const exposedData = [];

            for (let key in localStorage) {
                if (key.startsWith('anwar_bakery_')) {
                    const data = localStorage[key];
                    sensitiveKeys.forEach(sensitive => {
                        if (data.toLowerCase().includes(sensitive)) {
                            exposedData.push(key);
                        }
                    });
                }
            }

            if (exposedData.length > 0) {
                this.diagnosticResults.push({
                    type: 'warning',
                    category: 'security',
                    message: `بيانات حساسة محتملة في: ${exposedData.join(', ')}`,
                    autoFixable: false
                });
            }

            // Check session validity
            const session = localStorage.getItem('anwar_bakery_session');
            if (session) {
                try {
                    const sessionData = JSON.parse(session);
                    const loginTime = new Date(sessionData.loginTime);
                    const now = new Date();
                    const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

                    if (hoursDiff > 24) {
                        this.diagnosticResults.push({
                            type: 'warning',
                            category: 'security',
                            message: 'جلسة قديمة، يُنصح بتسجيل الدخول مرة أخرى',
                            autoFixable: true,
                            fixAction: () => this.refreshSession()
                        });
                    }
                } catch (error) {
                    this.diagnosticResults.push({
                        type: 'error',
                        category: 'security',
                        message: 'جلسة تالفة',
                        autoFixable: true,
                        fixAction: () => this.fixCorruptedSession()
                    });
                }
            }

        } catch (error) {
            this.diagnosticResults.push({
                type: 'error',
                category: 'security',
                message: `خطأ في فحص الأمان: ${error.message}`,
                autoFixable: false
            });
        }
    }

    // Check business logic
    async checkBusinessLogic() {
        console.log('💼 فحص المنطق التجاري...');

        try {
            // Check for negative inventory
            const products = JSON.parse(localStorage.getItem('anwar_bakery_products') || '[]');
            const negativeInventory = products.filter(product => (product.quantity || 0) < 0);

            if (negativeInventory.length > 0) {
                this.diagnosticResults.push({
                    type: 'warning',
                    category: 'business_logic',
                    message: `مخزون سالب في ${negativeInventory.length} منتج`,
                    autoFixable: true,
                    fixAction: () => this.fixNegativeInventory(negativeInventory)
                });
            }

            // Check for future dates in transactions
            const invoices = JSON.parse(localStorage.getItem('anwar_bakery_invoices') || '[]');
            const futureInvoices = invoices.filter(invoice => {
                const invoiceDate = new Date(invoice.date);
                return invoiceDate > new Date();
            });

            if (futureInvoices.length > 0) {
                this.diagnosticResults.push({
                    type: 'warning',
                    category: 'business_logic',
                    message: `فواتير بتواريخ مستقبلية: ${futureInvoices.length}`,
                    autoFixable: true,
                    fixAction: () => this.fixFutureDates(futureInvoices)
                });
            }

            // Check for zero-value transactions
            const zeroValueInvoices = invoices.filter(invoice => (invoice.total || 0) === 0);
            if (zeroValueInvoices.length > 0) {
                this.diagnosticResults.push({
                    type: 'info',
                    category: 'business_logic',
                    message: `فواتير بقيمة صفر: ${zeroValueInvoices.length}`,
                    autoFixable: false
                });
            }

        } catch (error) {
            this.diagnosticResults.push({
                type: 'error',
                category: 'business_logic',
                message: `خطأ في فحص المنطق التجاري: ${error.message}`,
                autoFixable: false
            });
        }
    }

    // Auto-fix issues
    async autoFixIssues() {
        console.log('🔧 بدء الإصلاح التلقائي...');

        if (this.backupBeforeFix) {
            await this.createBackupBeforeFix();
        }

        const fixableIssues = this.diagnosticResults.filter(issue => issue.autoFixable && issue.fixAction);

        for (const issue of fixableIssues) {
            try {
                console.log(`🔧 إصلاح: ${issue.message}`);
                await issue.fixAction();
                issue.fixed = true;
            } catch (error) {
                console.error(`❌ فشل في إصلاح: ${issue.message}`, error);
                issue.fixError = error.message;
            }
        }

        console.log(`✅ تم إصلاح ${fixableIssues.filter(i => i.fixed).length} من ${fixableIssues.length} مشكلة`);
    }

    // Generate diagnostic report
    generateDiagnosticReport() {
        const report = {
            timestamp: new Date().toISOString(),
            totalIssues: this.diagnosticResults.length,
            errors: this.diagnosticResults.filter(r => r.type === 'error').length,
            warnings: this.diagnosticResults.filter(r => r.type === 'warning').length,
            info: this.diagnosticResults.filter(r => r.type === 'info').length,
            autoFixed: this.diagnosticResults.filter(r => r.fixed).length,
            categories: {},
            issues: this.diagnosticResults
        };

        // Group by category
        this.diagnosticResults.forEach(issue => {
            if (!report.categories[issue.category]) {
                report.categories[issue.category] = 0;
            }
            report.categories[issue.category]++;
        });

        return report;
    }

    // Fix functions
    async fixCorruptedData(corruptedKeys) {
        corruptedKeys.forEach(key => {
            localStorage.removeItem(key);
            console.log(`🗑️ تم حذف البيانات التالفة: ${key}`);
        });
    }

    async initializeMissingData(missingKeys) {
        const defaultData = {
            'anwar_bakery_company': JSON.stringify({
                companyNameAr: 'مخبز أنوار الحي',
                companyNameEn: 'Anwar Bakery',
                currency: 'SAR'
            }),
            'anwar_bakery_accounts': JSON.stringify([]),
            'anwar_bakery_products': JSON.stringify([])
        };

        missingKeys.forEach(key => {
            if (defaultData[key]) {
                localStorage.setItem(key, defaultData[key]);
                console.log(`✅ تم إنشاء البيانات الافتراضية: ${key}`);
            }
        });
    }

    async checkDuplicateIds() {
        const collections = ['products', 'customers', 'suppliers', 'accounts'];

        collections.forEach(collection => {
            const data = JSON.parse(localStorage.getItem(`anwar_bakery_${collection}`) || '[]');
            const ids = data.map(item => item.id).filter(id => id);
            const duplicates = ids.filter((id, index) => ids.indexOf(id) !== index);

            if (duplicates.length > 0) {
                this.diagnosticResults.push({
                    type: 'error',
                    category: 'data_integrity',
                    message: `معرفات مكررة في ${collection}: ${duplicates.join(', ')}`,
                    autoFixable: true,
                    fixAction: () => this.fixDuplicateIds(collection, data)
                });
            }
        });
    }

    async fixDuplicateIds(collection, data) {
        const seenIds = new Set();
        const fixedData = data.map(item => {
            if (seenIds.has(item.id)) {
                item.id = this.generateUniqueId();
            }
            seenIds.add(item.id);
            return item;
        });

        localStorage.setItem(`anwar_bakery_${collection}`, JSON.stringify(fixedData));
        console.log(`🔧 تم إصلاح المعرفات المكررة في ${collection}`);
    }

    generateUniqueId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    async createBackupBeforeFix() {
        const backupData = {};
        for (let key in localStorage) {
            if (key.startsWith('anwar_bakery_')) {
                backupData[key] = localStorage[key];
            }
        }

        const backupKey = `anwar_bakery_diagnostic_backup_${Date.now()}`;
        localStorage.setItem(backupKey, JSON.stringify({
            timestamp: new Date().toISOString(),
            data: backupData
        }));

        console.log('💾 تم إنشاء نسخة احتياطية قبل الإصلاح');
    }

    // Additional fix functions
    async fixAccountingBalance(totalDebit, totalCredit) {
        const difference = totalDebit - totalCredit;

        // Create balancing entry
        const balancingEntry = {
            id: Date.now(),
            date: new Date().toISOString().split('T')[0],
            description: 'قيد تسوية تلقائي لتوازن الحسابات',
            entries: []
        };

        if (difference > 0) {
            // Add credit entry to balance
            balancingEntry.entries.push({
                account: 'حساب التسوية',
                debit: 0,
                credit: Math.abs(difference)
            });
        } else {
            // Add debit entry to balance
            balancingEntry.entries.push({
                account: 'حساب التسوية',
                debit: Math.abs(difference),
                credit: 0
            });
        }

        const journalEntries = JSON.parse(localStorage.getItem('anwar_bakery_journal_entries') || '[]');
        journalEntries.push(balancingEntry);
        localStorage.setItem('anwar_bakery_journal_entries', JSON.stringify(journalEntries));

        console.log('🔧 تم إنشاء قيد تسوية لتوازن الحسابات');
    }

    async fixUnbalancedEntries(unbalancedEntries) {
        try {
            const journalEntries = JSON.parse(localStorage.getItem('anwar_bakery_journal_entries') || '[]');

            unbalancedEntries.forEach(unbalanced => {
                const index = journalEntries.findIndex(entry => entry.id === unbalanced.id);
                if (index !== -1) {
                    const entry = journalEntries[index];
                    const difference = unbalanced.difference;

                    if (Math.abs(difference) > 0.01) {
                        // Add balancing line
                        const balancingLine = {
                            account: 'حساب التسوية',
                            debit: difference > 0 ? 0 : Math.abs(difference),
                            credit: difference > 0 ? Math.abs(difference) : 0
                        };

                        entry.entries.push(balancingLine);
                        entry.description += ' (تم التسوية تلقائياً)';
                    }
                }
            });

            localStorage.setItem('anwar_bakery_journal_entries', JSON.stringify(journalEntries));
            console.log('🔧 تم إصلاح القيود غير المتوازنة');

        } catch (error) {
            console.error('Error fixing unbalanced entries:', error);
        }
    }

    async optimizeDataStorage() {
        try {
            // Remove old backup files
            const backups = JSON.parse(localStorage.getItem('anwar_bakery_backup_list') || '[]');
            const oldBackups = backups.filter(backup => {
                const backupDate = new Date(backup.date);
                const monthsAgo = new Date();
                monthsAgo.setMonth(monthsAgo.getMonth() - 3);
                return backupDate < monthsAgo;
            });

            oldBackups.forEach(backup => {
                localStorage.removeItem(backup.key);
            });

            const remainingBackups = backups.filter(backup => !oldBackups.includes(backup));
            localStorage.setItem('anwar_bakery_backup_list', JSON.stringify(remainingBackups));

            console.log('🔧 تم تحسين تخزين البيانات');

        } catch (error) {
            console.error('Error optimizing data storage:', error);
        }
    }

    async cleanupOldBackups() {
        try {
            const backups = JSON.parse(localStorage.getItem('anwar_bakery_backup_list') || '[]');

            // Keep only last 10 backups
            const sortedBackups = backups.sort((a, b) => new Date(b.date) - new Date(a.date));
            const backupsToKeep = sortedBackups.slice(0, 10);
            const backupsToDelete = sortedBackups.slice(10);

            // Delete old backups
            backupsToDelete.forEach(backup => {
                localStorage.removeItem(backup.key);
            });

            localStorage.setItem('anwar_bakery_backup_list', JSON.stringify(backupsToKeep));

            console.log(`🔧 تم حذف ${backupsToDelete.length} نسخة احتياطية قديمة`);

        } catch (error) {
            console.error('Error cleaning up old backups:', error);
        }
    }

    async refreshSession() {
        try {
            const session = JSON.parse(localStorage.getItem('anwar_bakery_session') || '{}');
            session.loginTime = new Date().toISOString();
            session.lastActivity = new Date().toISOString();
            localStorage.setItem('anwar_bakery_session', JSON.stringify(session));

            console.log('🔧 تم تحديث الجلسة');

        } catch (error) {
            console.error('Error refreshing session:', error);
        }
    }

    async fixCorruptedSession() {
        try {
            const newSession = {
                loginTime: new Date().toISOString(),
                lastActivity: new Date().toISOString(),
                user: 'admin',
                isValid: true
            };

            localStorage.setItem('anwar_bakery_session', JSON.stringify(newSession));
            console.log('🔧 تم إصلاح الجلسة التالفة');

        } catch (error) {
            console.error('Error fixing corrupted session:', error);
        }
    }

    async fixNegativeInventory(negativeProducts) {
        try {
            const products = JSON.parse(localStorage.getItem('anwar_bakery_products') || '[]');

            negativeProducts.forEach(negativeProduct => {
                const index = products.findIndex(p => p.id === negativeProduct.id);
                if (index !== -1) {
                    products[index].quantity = 0;
                    console.log(`🔧 تم تصفير المخزون السالب للمنتج: ${products[index].name}`);
                }
            });

            localStorage.setItem('anwar_bakery_products', JSON.stringify(products));
            console.log('🔧 تم إصلاح المخزون السالب');

        } catch (error) {
            console.error('Error fixing negative inventory:', error);
        }
    }

    async fixFutureDates(futureInvoices) {
        try {
            const invoices = JSON.parse(localStorage.getItem('anwar_bakery_invoices') || '[]');
            const today = new Date().toISOString().split('T')[0];

            futureInvoices.forEach(futureInvoice => {
                const index = invoices.findIndex(inv => inv.id === futureInvoice.id);
                if (index !== -1) {
                    invoices[index].date = today;
                    console.log(`🔧 تم تصحيح تاريخ الفاتورة: ${invoices[index].number}`);
                }
            });

            localStorage.setItem('anwar_bakery_invoices', JSON.stringify(invoices));
            console.log('🔧 تم إصلاح التواريخ المستقبلية');

        } catch (error) {
            console.error('Error fixing future dates:', error);
        }
    }
}

// Create global instance
window.systemDiagnostics = new SystemDiagnostics();

// Export main functions
window.runSystemDiagnostics = () => {
    return window.systemDiagnostics.runFullDiagnostics();
};

window.autoFixSystemIssues = () => {
    return window.systemDiagnostics.autoFixIssues();
};
