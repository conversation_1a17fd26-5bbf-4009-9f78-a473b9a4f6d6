// Damage Entry Management System
let damageEntries = [];
let filteredDamageEntries = [];
let items = [];
let warehouses = [];

// Load damage entries from localStorage
function loadDamageEntries() {
    const savedEntries = localStorage.getItem('anwar_bakery_damage_entries');
    if (savedEntries) {
        damageEntries = JSON.parse(savedEntries);
    } else {
        // Add sample data for testing
        addSampleDamageEntries();
    }
    filteredDamageEntries = [...damageEntries];
    renderDamageEntries();
    updateStats();
}

// Add sample damage entries for testing
function addSampleDamageEntries() {
    const sampleEntries = [
        {
            id: 1,
            entryNumber: 'DMG-BREAD001-001',
            date: '2024-01-15',
            itemId: 1,
            itemCode: 'BREAD001',
            itemName: 'خبز أبيض',
            warehouseId: 1,
            warehouseName: 'المخزن الرئيسي',
            unitType: 'piece',
            unitName: 'رغيف',
            conversionFactor: 1,
            quantity: 50,
            unitCost: 1.5,
            totalValue: 75.00,
            reason: 'expired',
            notes: 'منتهي الصلاحية - تم اكتشافه في الفحص الصباحي',
            createdBy: 'أحمد محمد',
            createdAt: '2024-01-15T08:30:00.000Z'
        },
        {
            id: 2,
            entryNumber: 'DMG-CAKE002-002',
            date: '2024-01-16',
            itemId: 2,
            itemCode: 'CAKE002',
            itemName: 'كيك شوكولاتة',
            warehouseId: 1,
            warehouseName: 'المخزن الرئيسي',
            unitType: 'piece',
            unitName: 'قطعة',
            conversionFactor: 1,
            quantity: 3,
            unitCost: 25.00,
            totalValue: 75.00,
            reason: 'damaged',
            notes: 'تلف أثناء النقل',
            createdBy: 'فاطمة علي',
            createdAt: '2024-01-16T14:20:00.000Z'
        }
    ];

    damageEntries = sampleEntries;
    localStorage.setItem('anwar_bakery_damage_entries', JSON.stringify(damageEntries));
}

// Load items from localStorage
function loadItems() {
    const savedItems = localStorage.getItem('anwar_bakery_items');
    if (savedItems) {
        items = JSON.parse(savedItems);
    } else {
        // Add default items if none exist
        items = [
            {
                id: 1,
                code: 'BREAD001',
                name: 'خبز أبيض',
                smallUnitName: 'رغيف',
                costPrice: 1.5,
                isActive: true
            },
            {
                id: 2,
                code: 'CAKE002',
                name: 'كيك شوكولاتة',
                smallUnitName: 'قطعة',
                costPrice: 25.0,
                isActive: true
            },
            {
                id: 3,
                code: 'PASTRY003',
                name: 'معجنات',
                smallUnitName: 'قطعة',
                costPrice: 3.0,
                isActive: true
            }
        ];
        localStorage.setItem('anwar_bakery_items', JSON.stringify(items));
    }
}

// Load warehouses from localStorage
function loadWarehouses() {
    const savedWarehouses = localStorage.getItem('anwar_bakery_warehouses');
    if (savedWarehouses) {
        warehouses = JSON.parse(savedWarehouses);
    } else {
        // Default warehouses
        warehouses = [
            { id: 1, name: 'المخزن الرئيسي', isActive: true },
            { id: 2, name: 'مخزن الفرع الثاني', isActive: true }
        ];
    }

    populateWarehouseFilter();
}

// Populate warehouse filter
function populateWarehouseFilter() {
    const warehouseFilter = document.getElementById('warehouseFilter');
    if (warehouseFilter) {
        warehouseFilter.innerHTML = '<option value="">جميع المخازن</option>';
        warehouses.filter(w => w.isActive).forEach(warehouse => {
            const option = document.createElement('option');
            option.value = warehouse.id;
            option.textContent = warehouse.name;
            warehouseFilter.appendChild(option);
        });
    }
}

// Load branches for filters
function loadBranches() {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    let branches = [];

    if (savedBranches) {
        branches = JSON.parse(savedBranches);
    } else {
        branches = [
            { id: 1, branchName: 'الفرع الرئيسي', isActive: true },
            { id: 2, branchName: 'فرع الملك فهد', isActive: true },
            { id: 3, branchName: 'فرع العليا', isActive: true }
        ];
        localStorage.setItem('anwar_bakery_branches', JSON.stringify(branches));
    }

    const branchFilter = document.getElementById('branchFilter');
    if (branchFilter) {
        branchFilter.innerHTML = '<option value="">جميع الفروع</option>';
        branches.filter(branch => branch.isActive).forEach(branch => {
            const option = document.createElement('option');
            option.value = branch.id;
            option.textContent = branch.branchName;
            branchFilter.appendChild(option);
        });
    }
}

// Render damage entries table
function renderDamageEntries() {
    const tableBody = document.getElementById('damageEntriesTableBody');
    if (!tableBody) return;

    if (filteredDamageEntries.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="px-6 py-12 text-center text-gray-500">
                    <div class="flex flex-col items-center">
                        <span class="text-4xl mb-4">🗑️</span>
                        <p class="text-lg mb-2">لا توجد قيود تالف حتى الآن</p>
                        <p class="text-sm">ابدأ بإنشاء قيد تالف جديد</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    const currencySymbol = getCurrencySymbol();
    tableBody.innerHTML = filteredDamageEntries.map(entry => `
        <tr class="hover:bg-gray-50">
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">${entry.entryNumber}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">${new Date(entry.date).toLocaleDateString('ar-SA')}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <div>
                    <div class="font-medium">${entry.itemName}</div>
                    <div class="text-xs text-gray-500">${entry.itemCode}</div>
                </div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">${entry.warehouseName}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">${entry.quantity} ${entry.unitName}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-red-600">${entry.totalValue.toFixed(2)} ${currencySymbol}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <span class="px-2 py-1 text-xs rounded-full ${getReasonBadgeClass(entry.reason)}">
                    ${getReasonText(entry.reason)}
                </span>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                <div class="flex space-x-2">
                    <button onclick="viewDamageEntry(${entry.id})" class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50" title="عرض">
                        👁️
                    </button>
                    <button onclick="printDamageEntry(${entry.id})" class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50" title="طباعة">
                        🖨️
                    </button>
                    <button onclick="deleteDamageEntry(${entry.id})" class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50" title="حذف">
                        🗑️
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// Get reason badge class
function getReasonBadgeClass(reason) {
    const classes = {
        'expired': 'bg-orange-100 text-orange-800',
        'damaged': 'bg-red-100 text-red-800',
        'contaminated': 'bg-purple-100 text-purple-800',
        'defective': 'bg-yellow-100 text-yellow-800',
        'other': 'bg-gray-100 text-gray-800'
    };
    return classes[reason] || 'bg-gray-100 text-gray-800';
}

// Get reason text in Arabic
function getReasonText(reason) {
    const texts = {
        'expired': 'منتهي الصلاحية',
        'damaged': 'تالف',
        'contaminated': 'ملوث',
        'defective': 'معيب',
        'other': 'أخرى'
    };
    return texts[reason] || 'غير محدد';
}

// Update statistics
function updateStats() {
    const totalEntries = damageEntries.length;
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    const monthlyEntries = damageEntries.filter(entry => {
        const entryDate = new Date(entry.date);
        return entryDate.getMonth() === currentMonth && entryDate.getFullYear() === currentYear;
    });

    const monthlyValue = monthlyEntries.reduce((sum, entry) => sum + entry.totalValue, 0);
    const totalValue = damageEntries.reduce((sum, entry) => sum + entry.totalValue, 0);

    const currencySymbol = getCurrencySymbol();

    document.getElementById('totalDamageEntries').textContent = totalEntries;
    document.getElementById('monthlyDamageEntries').textContent = monthlyEntries.length;
    document.getElementById('monthlyDamageValue').textContent = `${monthlyValue.toFixed(2)} ${currencySymbol}`;
    document.getElementById('totalDamageValue').textContent = `${totalValue.toFixed(2)} ${currencySymbol}`;
}

// Filter damage entries
function filterDamageEntries() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const branchFilter = document.getElementById('branchFilter').value;
    const warehouseFilter = document.getElementById('warehouseFilter').value;
    const reasonFilter = document.getElementById('reasonFilter').value;

    filteredDamageEntries = damageEntries.filter(entry => {
        const matchesSearch = entry.entryNumber.toLowerCase().includes(searchTerm) ||
                            entry.itemName.toLowerCase().includes(searchTerm) ||
                            entry.itemCode.toLowerCase().includes(searchTerm);

        const matchesBranch = !branchFilter || entry.branchId == branchFilter;
        const matchesWarehouse = !warehouseFilter || entry.warehouseId == warehouseFilter;
        const matchesReason = !reasonFilter || entry.reason === reasonFilter;

        return matchesSearch && matchesBranch && matchesWarehouse && matchesReason;
    });

    renderDamageEntries();
}

// Open new damage modal
function openNewDamageModal() {
    document.getElementById('newDamageModal').classList.add('active');
    document.getElementById('newDamageModal').style.display = 'flex';
    generateNewDamageEntryNumber();
    document.getElementById('newDamageDate').value = new Date().toISOString().split('T')[0];
    populateNewDamageItemsDropdown();
    populateNewDamageWarehousesDropdown();
}

// Close new damage modal
function closeNewDamageModal() {
    document.getElementById('newDamageModal').classList.remove('active');
    document.getElementById('newDamageModal').style.display = 'none';
    document.getElementById('newDamageForm').reset();
}

// Generate new damage entry number
function generateNewDamageEntryNumber() {
    const nextId = Math.max(...damageEntries.map(e => e.id), 0) + 1;
    const entryNumber = `DMG-${nextId.toString().padStart(6, '0')}`;
    document.getElementById('newDamageEntryNumber').value = entryNumber;
}

// Populate items dropdown for new damage entry
function populateNewDamageItemsDropdown() {
    const dropdown = document.getElementById('newDamageItemId');
    dropdown.innerHTML = '<option value="">اختر الصنف</option>';

    items.forEach(item => {
        if (item.isActive) {
            const option = document.createElement('option');
            option.value = item.id;
            option.textContent = `${item.name} (${item.code})`;
            option.dataset.unitName = item.smallUnitName || 'قطعة';
            option.dataset.unitCost = item.costPrice || 0;
            dropdown.appendChild(option);
        }
    });
}

// Populate warehouses dropdown for new damage entry
function populateNewDamageWarehousesDropdown() {
    const dropdown = document.getElementById('newDamageWarehouseId');
    dropdown.innerHTML = '<option value="">اختر المخزن</option>';

    warehouses.forEach(warehouse => {
        if (warehouse.isActive) {
            const option = document.createElement('option');
            option.value = warehouse.id;
            option.textContent = warehouse.name;
            dropdown.appendChild(option);
        }
    });
}

// Update item details for new damage entry
function updateNewDamageItemDetails() {
    const dropdown = document.getElementById('newDamageItemId');
    const selectedOption = dropdown.options[dropdown.selectedIndex];

    if (selectedOption.value) {
        document.getElementById('newDamageUnitName').value = selectedOption.dataset.unitName || '';
        document.getElementById('newDamageUnitCost').value = selectedOption.dataset.unitCost || 0;
        calculateNewDamageTotal();
    } else {
        document.getElementById('newDamageUnitName').value = '';
        document.getElementById('newDamageUnitCost').value = '';
        document.getElementById('newDamageTotalValue').value = '';
    }
}

// Calculate total for new damage entry
function calculateNewDamageTotal() {
    const quantity = parseFloat(document.getElementById('newDamageQuantity').value) || 0;
    const unitCost = parseFloat(document.getElementById('newDamageUnitCost').value) || 0;
    const total = quantity * unitCost;
    document.getElementById('newDamageTotalValue').value = total.toFixed(2);
}

// Save new damage entry
function saveNewDamageEntry(event) {
    event.preventDefault();

    const itemDropdown = document.getElementById('newDamageItemId');
    const warehouseDropdown = document.getElementById('newDamageWarehouseId');
    const selectedItem = itemDropdown.options[itemDropdown.selectedIndex];
    const selectedWarehouse = warehouseDropdown.options[warehouseDropdown.selectedIndex];

    if (!selectedItem.value || !selectedWarehouse.value) {
        showMessage('يرجى اختيار الصنف والمخزن', 'error');
        return;
    }

    const newEntry = {
        id: Math.max(...damageEntries.map(e => e.id), 0) + 1,
        entryNumber: document.getElementById('newDamageEntryNumber').value,
        date: document.getElementById('newDamageDate').value,
        itemId: parseInt(itemDropdown.value),
        itemCode: selectedItem.textContent.match(/\(([^)]+)\)/)?.[1] || 'N/A',
        itemName: selectedItem.textContent.split(' (')[0],
        warehouseId: parseInt(warehouseDropdown.value),
        warehouseName: selectedWarehouse.textContent,
        unitType: 'small',
        unitName: document.getElementById('newDamageUnitName').value,
        conversionFactor: 1,
        quantity: parseFloat(document.getElementById('newDamageQuantity').value),
        unitCost: parseFloat(document.getElementById('newDamageUnitCost').value),
        totalValue: parseFloat(document.getElementById('newDamageTotalValue').value),
        reason: document.getElementById('newDamageReason').value,
        notes: document.getElementById('newDamageNotes').value,
        createdBy: 'مدير النظام',
        createdAt: new Date().toISOString()
    };

    // Add to arrays and save
    damageEntries.push(newEntry);
    filteredDamageEntries = [...damageEntries];
    localStorage.setItem('anwar_bakery_damage_entries', JSON.stringify(damageEntries));

    // Update UI
    renderDamageEntries();
    updateStats();
    closeNewDamageModal();
    showMessage('تم حفظ قيد التالف بنجاح!', 'success');
}

// View damage entry details
function viewDamageEntry(id) {
    const entry = damageEntries.find(e => e.id === id);
    if (entry) {
        const currencySymbol = getCurrencySymbol();
        let details = `تفاصيل قيد التالف: ${entry.entryNumber}\n\n`;
        details += `📅 التاريخ: ${new Date(entry.date).toLocaleDateString('ar-SA')}\n`;
        details += `📦 الصنف: ${entry.itemName} (${entry.itemCode})\n`;
        details += `🏪 المخزن: ${entry.warehouseName}\n`;
        details += `📊 الكمية: ${entry.quantity} ${entry.unitName}\n`;
        details += `💰 تكلفة الوحدة: ${entry.unitCost.toFixed(2)} ${currencySymbol}\n`;
        details += `💸 إجمالي القيمة: ${entry.totalValue.toFixed(2)} ${currencySymbol}\n`;
        details += `❓ السبب: ${getReasonText(entry.reason)}\n`;
        details += `👤 المسجل بواسطة: ${entry.createdBy}\n`;
        if (entry.notes) {
            details += `📝 ملاحظات: ${entry.notes}\n`;
        }

        alert(details);
    }
}

// Print damage entry
function printDamageEntry(id) {
    showMessage('وظيفة الطباعة قيد التطوير...', 'info');
}

// Delete damage entry
function deleteDamageEntry(id) {
    if (confirm('هل أنت متأكد من حذف قيد التالف هذا؟')) {
        damageEntries = damageEntries.filter(e => e.id !== id);
        localStorage.setItem('anwar_bakery_damage_entries', JSON.stringify(damageEntries));
        filteredDamageEntries = filteredDamageEntries.filter(e => e.id !== id);
        renderDamageEntries();
        updateStats();
        showMessage('تم حذف قيد التالف بنجاح!', 'success');
    }
}

// Export damage entries to Excel
function exportDamageEntries() {
    const currencySymbol = getCurrencySymbol();
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += `رقم القيد,التاريخ,كود الصنف,اسم الصنف,المخزن,الكمية,الوحدة,تكلفة الوحدة (${currencySymbol}),إجمالي القيمة (${currencySymbol}),السبب,ملاحظات,المسجل بواسطة\n`;

    filteredDamageEntries.forEach(entry => {
        csvContent += `"${entry.entryNumber}","${entry.date}","${entry.itemCode}","${entry.itemName}","${entry.warehouseName}","${entry.quantity}","${entry.unitName}","${entry.unitCost}","${entry.totalValue}","${getReasonText(entry.reason)}","${entry.notes || ''}","${entry.createdBy}"\n`;
    });

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `damage_entries_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showMessage('تم تصدير قيود التالف بنجاح!', 'success');
}

// Get currency symbol from company settings
function getCurrencySymbol() {
    const companyData = localStorage.getItem('anwar_bakery_company');
    if (companyData) {
        const company = JSON.parse(companyData);
        return company.currency || 'ر.س';
    }
    return 'ر.س';
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700',
        'warning': 'bg-yellow-50 border-yellow-200 text-yellow-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (sidebar.classList.contains('translate-x-full')) {
        sidebar.classList.remove('translate-x-full');
        overlay.classList.remove('hidden');
    } else {
        sidebar.classList.add('translate-x-full');
        overlay.classList.add('hidden');
    }
}

// Update current date time
function updateDateTime() {
    const now = new Date();
    const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    document.getElementById('currentDateTime').textContent =
        now.toLocaleDateString('ar-SA', options);
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_session');
        sessionStorage.removeItem('anwar_bakery_session');
        window.location.href = 'login.html';
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadItems();
    loadWarehouses();
    loadBranches();
    loadDamageEntries();
    updateDateTime();
    setInterval(updateDateTime, 60000);

    // Ensure items are loaded for the modal
    if (items.length === 0) {
        // Add some sample items if none exist
        items = [
            {
                id: 1,
                code: 'BREAD001',
                name: 'خبز أبيض',
                smallUnitName: 'رغيف',
                costPrice: 1.5,
                isActive: true
            },
            {
                id: 2,
                code: 'CAKE002',
                name: 'كيك شوكولاتة',
                smallUnitName: 'قطعة',
                costPrice: 25.0,
                isActive: true
            }
        ];
        localStorage.setItem('anwar_bakery_items', JSON.stringify(items));
    }
});

// Close sidebar when clicking overlay
document.getElementById('mobileOverlay').addEventListener('click', toggleSidebar);
