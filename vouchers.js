// Vouchers Management System
let vouchers = [];
let filteredVouchers = [];

// Load vouchers from localStorage
function loadVouchers() {
    const savedVouchers = localStorage.getItem('anwar_bakery_vouchers');
    if (savedVouchers) {
        vouchers = JSON.parse(savedVouchers);
    }
    filteredVouchers = [...vouchers];
    renderVouchers();
    updateStatistics();
}

// Check authentication
function checkAuth() {
    const session = localStorage.getItem('anwar_bakery_session') ||
                   sessionStorage.getItem('anwar_bakery_session');

    if (!session) {
        window.location.href = 'login.html';
        return null;
    }

    return JSON.parse(session);
}

// Load user info
function loadUserInfo() {
    const session = checkAuth();
    if (session) {
        const userFullNameElement = document.getElementById('userFullName');
        const userRoleElement = document.getElementById('userRole');

        if (userFullNameElement) userFullNameElement.textContent = session.fullName;
        if (userRoleElement) userRoleElement.textContent = session.role;
    }
}

// Load company info
function loadCompanyInfo() {
    const savedData = localStorage.getItem('anwar_bakery_company');
    if (savedData) {
        const companyData = JSON.parse(savedData);
        const sidebarElement = document.getElementById('sidebarCompanyName');
        if (sidebarElement && companyData.companyNameAr) {
            sidebarElement.textContent = companyData.companyNameAr;
        }
    }
}

// Get currency symbol from settings
function getCurrencySymbol() {
    const savedSettings = localStorage.getItem('anwar_bakery_settings');
    if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        return settings.currency || '';
    }
    return '';
}

// Open receipt voucher
function openReceiptVoucher() {
    window.location.href = 'receipt-voucher.html';
}

// Open payment voucher
function openPaymentVoucher() {
    window.location.href = 'payment-voucher.html';
}

// Open journal entry
function openJournalEntry() {
    window.location.href = 'journal-entry.html';
}

// Filter vouchers
function filterVouchers() {
    const typeFilter = document.getElementById('voucherTypeFilter').value;

    filteredVouchers = vouchers.filter(voucher => {
        return !typeFilter || voucher.type === typeFilter;
    });

    renderVouchers();
}

// Render vouchers table
function renderVouchers() {
    const tableBody = document.getElementById('vouchersTableBody');

    if (filteredVouchers.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="px-4 py-12 text-center text-gray-500">
                    <div class="flex flex-col items-center">
                        <span class="text-4xl mb-4">📋</span>
                        <p class="text-lg mb-2">لا توجد سندات حتى الآن</p>
                        <p class="text-sm">ابدأ بإنشاء سند جديد</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    const currencySymbol = getCurrencySymbol();

    tableBody.innerHTML = filteredVouchers.map(voucher => {
        const typeColors = {
            'receipt': 'bg-green-100 text-green-800',
            'payment': 'bg-red-100 text-red-800',
            'journal': 'bg-purple-100 text-purple-800'
        };

        const typeNames = {
            'receipt': 'سند قبض',
            'payment': 'سند صرف',
            'journal': 'قيد محاسبي'
        };

        const typeIcons = {
            'receipt': '📥',
            'payment': '📤',
            'journal': '⚖️'
        };

        const statusColors = {
            'draft': 'bg-gray-100 text-gray-800',
            'posted': 'bg-blue-100 text-blue-800',
            'cancelled': 'bg-red-100 text-red-800'
        };

        const statusNames = {
            'draft': 'مسودة',
            'posted': 'مرحل',
            'cancelled': 'ملغي'
        };

        return `
            <tr class="hover:bg-gray-50">
                <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                    <div class="flex items-center">
                        <span class="ml-2">${typeIcons[voucher.type]}</span>
                        ${voucher.voucherNumber}
                    </div>
                </td>
                <td class="px-4 py-3 whitespace-nowrap">
                    <span class="px-2 py-1 text-xs font-medium rounded-full ${typeColors[voucher.type]}">
                        ${typeNames[voucher.type]}
                    </span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                    ${new Date(voucher.date).toLocaleDateString('ar-SA')}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 font-semibold">
                    ${voucher.amount ? voucher.amount.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '') : '-'}
                </td>
                <td class="px-4 py-3 text-sm text-gray-900 max-w-xs truncate">
                    ${voucher.description || '-'}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                    ${voucher.linkedInvoiceId ?
                        `<a href="invoices.html" class="text-blue-600 hover:text-blue-900 font-medium">${voucher.invoiceNumber || voucher.linkedInvoiceId}</a>` :
                        '<span class="text-gray-400">-</span>'
                    }
                </td>
                <td class="px-4 py-3 whitespace-nowrap">
                    <span class="px-2 py-1 text-xs font-medium rounded-full ${statusColors[voucher.status]}">
                        ${statusNames[voucher.status]}
                    </span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                        <button onclick="viewVoucher('${voucher.id}')" class="text-blue-600 hover:text-blue-900 text-xs">عرض</button>
                        <button onclick="editVoucher('${voucher.id}')" class="text-green-600 hover:text-green-900 text-xs">تعديل</button>
                        <button onclick="printVoucher('${voucher.id}')" class="text-purple-600 hover:text-purple-900 text-xs">طباعة</button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// Update statistics
function updateStatistics() {
    const today = new Date().toDateString();

    const todayReceipts = vouchers.filter(v =>
        v.type === 'receipt' &&
        new Date(v.date).toDateString() === today
    ).length;

    const todayPayments = vouchers.filter(v =>
        v.type === 'payment' &&
        new Date(v.date).toDateString() === today
    ).length;

    const todayJournals = vouchers.filter(v =>
        v.type === 'journal' &&
        new Date(v.date).toDateString() === today
    ).length;

    const todayReceiptsElement = document.getElementById('todayReceipts');
    const todayPaymentsElement = document.getElementById('todayPayments');
    const todayJournalsElement = document.getElementById('todayJournals');

    if (todayReceiptsElement) {
        todayReceiptsElement.textContent = todayReceipts;
    }
    if (todayPaymentsElement) {
        todayPaymentsElement.textContent = todayPayments;
    }
    if (todayJournalsElement) {
        todayJournalsElement.textContent = todayJournals;
    }
}

// View voucher details
function viewVoucher(id) {
    const voucher = vouchers.find(v => v.id == id);
    if (voucher) {
        const currencySymbol = getCurrencySymbol();

        const typeNames = {
            'receipt': 'سند قبض',
            'payment': 'سند صرف',
            'journal': 'قيد محاسبي'
        };

        const statusNames = {
            'draft': 'مسودة',
            'posted': 'مرحل',
            'cancelled': 'ملغي'
        };

        let message = `تفاصيل ${typeNames[voucher.type]}: ${voucher.voucherNumber}\n\n`;
        message += `📅 التاريخ: ${new Date(voucher.date).toLocaleDateString('ar-SA')}\n`;

        if (voucher.amount) {
            message += `💰 المبلغ: ${voucher.amount.toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}\n`;
        }

        message += `📝 البيان: ${voucher.description}\n`;
        message += `📊 الحالة: ${statusNames[voucher.status]}\n`;

        if (voucher.referenceNumber) {
            message += `🔗 رقم المرجع: ${voucher.referenceNumber}\n`;
        }

        if (voucher.journalEntries && voucher.journalEntries.length > 0) {
            message += `\n📋 القيود المحاسبية:\n`;
            voucher.journalEntries.forEach(entry => {
                if (entry.debit > 0) {
                    message += `من حـ/ ${entry.account.name}: ${entry.debit.toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}\n`;
                }
                if (entry.credit > 0) {
                    message += `إلى حـ/ ${entry.account.name}: ${entry.credit.toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}\n`;
                }
            });
        }

        alert(message);
    }
}

// Edit voucher
function editVoucher(id) {
    const voucher = vouchers.find(v => v.id == id);
    if (voucher) {
        if (voucher.status === 'posted') {
            if (confirm('هذا السند مرحل. هل تريد تعديله؟\nسيتم إنشاء قيد عكسي تلقائياً.')) {
                // TODO: Implement edit posted voucher
                showMessage('تعديل السندات المرحلة قيد التطوير...', 'info');
            }
        } else {
            // Redirect to edit page based on voucher type
            switch(voucher.type) {
                case 'receipt':
                    window.location.href = `receipt-voucher.html?edit=${id}`;
                    break;
                case 'payment':
                    window.location.href = `payment-voucher.html?edit=${id}`;
                    break;
                case 'journal':
                    window.location.href = `journal-entry.html?edit=${id}`;
                    break;
            }
        }
    }
}

// Print voucher
function printVoucher(id) {
    const voucher = vouchers.find(v => v.id == id);
    if (voucher) {
        // TODO: Implement printing
        showMessage('الطباعة قيد التطوير...', 'info');
    }
}

// View all vouchers
function viewAllVouchers() {
    // TODO: Implement detailed vouchers view
    showMessage('عرض جميع السندات قيد التطوير...', 'info');
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    if (!container) return;

    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (sidebar && overlay) {
        if (sidebar.classList.contains('translate-x-full')) {
            sidebar.classList.remove('translate-x-full');
            overlay.classList.remove('hidden');
        } else {
            sidebar.classList.add('translate-x-full');
            overlay.classList.add('hidden');
        }
    }
}

// Update current date time
function updateDateTime() {
    const now = new Date();
    const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    const dateTimeElement = document.getElementById('currentDateTime');
    if (dateTimeElement) {
        dateTimeElement.textContent = now.toLocaleDateString('ar-SA', options);
    }
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_session');
        sessionStorage.removeItem('anwar_bakery_session');
        window.location.href = 'login.html';
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadUserInfo();
    loadCompanyInfo();
    loadVouchers();
    updateDateTime();
    setInterval(updateDateTime, 60000);

    // Close sidebar when clicking overlay
    const overlay = document.getElementById('mobileOverlay');
    if (overlay) {
        overlay.addEventListener('click', toggleSidebar);
    }
});
