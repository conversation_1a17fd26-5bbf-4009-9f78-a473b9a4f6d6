// Chart of Accounts data structure
let accounts = []; // الحسابات الفعلية المستخدمة في الصفحة
let defaultAccounts = [
    // الأصول - Assets (1000-1999)
    {
        id: 1000,
        code: '1000',
        name: 'الأصول',
        type: 'assets',
        level: 0,
        parentId: null,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: true,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'الحساب الرئيسي للأصول'
    },
    {
        id: 1100,
        code: '1100',
        name: 'الأصول المتداولة',
        type: 'assets',
        level: 1,
        parentId: 1000,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: true,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'الأصول التي يمكن تحويلها إلى نقد خلال سنة'
    },
    {
        id: 1110,
        code: '1110',
        name: 'النقدية والبنوك',
        type: 'assets',
        level: 2,
        parentId: 1100,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: true,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'النقدية في الصناديق والحسابات البنكية'
    },
    {
        id: 1111,
        code: '1111',
        name: 'الصناديق',
        type: 'assets',
        level: 3,
        parentId: 1110,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: true,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'صناديق النقدية في الفروع'
    },
    {
        id: 1112,
        code: '1112',
        name: 'البنوك',
        type: 'assets',
        level: 3,
        parentId: 1110,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: true,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'الحسابات البنكية'
    },
    {
        id: 1120,
        code: '1120',
        name: 'المخزون',
        type: 'assets',
        level: 2,
        parentId: 1100,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: true,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'مخزون المواد والمنتجات'
    },
    {
        id: 1121,
        code: '1121',
        name: 'مخزون المواد الخام',
        type: 'assets',
        level: 3,
        parentId: 1120,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'مخزون المواد الخام والمكونات'
    },
    {
        id: 1122,
        code: '1122',
        name: 'مخزون المنتجات النهائية',
        type: 'assets',
        level: 3,
        parentId: 1120,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'مخزون المنتجات الجاهزة للبيع'
    },
    {
        id: 1123,
        code: '1123',
        name: 'مخزون نصف المصنعة',
        type: 'assets',
        level: 3,
        parentId: 1120,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'مخزون المنتجات نصف المصنعة'
    },
    {
        id: 1130,
        code: '1130',
        name: 'العملاء',
        type: 'assets',
        level: 2,
        parentId: 1100,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: true,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'حسابات العملاء والذمم المدينة'
    },

    // الخصوم - Liabilities (2000-2999)
    {
        id: 2000,
        code: '2000',
        name: 'الخصوم',
        type: 'liabilities',
        level: 0,
        parentId: null,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: true,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'الحساب الرئيسي للخصوم'
    },
    {
        id: 2100,
        code: '2100',
        name: 'الخصوم المتداولة',
        type: 'liabilities',
        level: 1,
        parentId: 2000,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: true,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'الخصوم المستحقة خلال سنة'
    },
    {
        id: 2110,
        code: '2110',
        name: 'الموردين',
        type: 'liabilities',
        level: 2,
        parentId: 2100,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: true,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'حسابات الموردين والذمم الدائنة'
    },
    {
        id: 2120,
        code: '2120',
        name: 'المصروفات المستحقة',
        type: 'liabilities',
        level: 2,
        parentId: 2100,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'المصروفات المستحقة الدفع'
    },
    {
        id: 2130,
        code: '2130',
        name: 'الرواتب والأجور المستحقة',
        type: 'liabilities',
        level: 2,
        parentId: 2100,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'رواتب وأجور الموظفين المستحقة'
    },

    // حقوق الملكية - Equity (3000-3999)
    {
        id: 3000,
        code: '3000',
        name: 'حقوق الملكية',
        type: 'equity',
        level: 0,
        parentId: null,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: true,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'الحساب الرئيسي لحقوق الملكية'
    },
    {
        id: 3100,
        code: '3100',
        name: 'رأس المال',
        type: 'equity',
        level: 1,
        parentId: 3000,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'رأس المال المدفوع'
    },
    {
        id: 3200,
        code: '3200',
        name: 'الأرباح المحتجزة',
        type: 'equity',
        level: 1,
        parentId: 3000,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'الأرباح المحتجزة من السنوات السابقة'
    },
    {
        id: 3300,
        code: '3300',
        name: 'أرباح السنة الحالية',
        type: 'equity',
        level: 1,
        parentId: 3000,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'صافي أرباح السنة الحالية'
    },

    // الإيرادات - Revenues (4000-4999)
    {
        id: 4000,
        code: '4000',
        name: 'الإيرادات',
        type: 'revenues',
        level: 0,
        parentId: null,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: true,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'الحساب الرئيسي للإيرادات'
    },
    {
        id: 4100,
        code: '4100',
        name: 'إيرادات المبيعات',
        type: 'revenues',
        level: 1,
        parentId: 4000,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: true,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'إيرادات مبيعات المنتجات'
    },
    {
        id: 4110,
        code: '4110',
        name: 'مبيعات الخبز',
        type: 'revenues',
        level: 2,
        parentId: 4100,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'إيرادات مبيعات الخبز والمعجنات'
    },
    {
        id: 4120,
        code: '4120',
        name: 'مبيعات الحلويات',
        type: 'revenues',
        level: 2,
        parentId: 4100,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'إيرادات مبيعات الحلويات والكيك'
    },
    {
        id: 4200,
        code: '4200',
        name: 'إيرادات الخدمات',
        type: 'revenues',
        level: 1,
        parentId: 4000,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: true,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'إيرادات الخدمات المقدمة'
    },
    {
        id: 4210,
        code: '4210',
        name: 'خدمات التزيين',
        type: 'revenues',
        level: 2,
        parentId: 4200,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'إيرادات خدمات تزيين الكيك'
    },
    {
        id: 4220,
        code: '4220',
        name: 'خدمات التوصيل',
        type: 'revenues',
        level: 2,
        parentId: 4200,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'إيرادات خدمات التوصيل'
    },

    // المصروفات - Expenses (5000-5999)
    {
        id: 5000,
        code: '5000',
        name: 'المصروفات',
        type: 'expenses',
        level: 0,
        parentId: null,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: true,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'الحساب الرئيسي للمصروفات'
    },
    {
        id: 5100,
        code: '5100',
        name: 'تكلفة البضاعة المباعة',
        type: 'expenses',
        level: 1,
        parentId: 5000,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'تكلفة المواد والمنتجات المباعة'
    },
    {
        id: 5200,
        code: '5200',
        name: 'المصروفات التشغيلية',
        type: 'expenses',
        level: 1,
        parentId: 5000,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: true,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'المصروفات التشغيلية اليومية'
    },
    {
        id: 5210,
        code: '5210',
        name: 'رواتب الموظفين',
        type: 'expenses',
        level: 2,
        parentId: 5200,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'رواتب وأجور الموظفين'
    },
    {
        id: 5220,
        code: '5220',
        name: 'إيجار المحل',
        type: 'expenses',
        level: 2,
        parentId: 5200,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'إيجار المحلات والفروع'
    },
    {
        id: 5230,
        code: '5230',
        name: 'الكهرباء والماء',
        type: 'expenses',
        level: 2,
        parentId: 5200,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'فواتير الكهرباء والماء'
    },
    {
        id: 5240,
        code: '5240',
        name: 'صيانة المعدات',
        type: 'expenses',
        level: 2,
        parentId: 5200,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'صيانة الأفران والمعدات'
    },
    {
        id: 5250,
        code: '5250',
        name: 'مصروفات التسويق',
        type: 'expenses',
        level: 2,
        parentId: 5200,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: true,
        canDelete: false,
        description: 'مصروفات الإعلان والتسويق'
    }
];

// Check authentication
function checkAuth() {
    const session = localStorage.getItem('anwar_bakery_session') ||
                   sessionStorage.getItem('anwar_bakery_session');

    if (!session) {
        window.location.href = 'login.html';
        return null;
    }

    return JSON.parse(session);
}

// Load user info
function loadUserInfo() {
    const session = checkAuth();
    if (session) {
        const userFullNameElement = document.getElementById('userFullName');
        const userRoleElement = document.getElementById('userRole');

        if (userFullNameElement) {
            userFullNameElement.textContent = session.fullName || 'أحمد محمد';
        }
        if (userRoleElement) {
            userRoleElement.textContent = session.role === 'admin' ? 'مدير النظام' : (session.role || 'مدير النظام');
        }
    } else {
        // Default values if no session
        const userFullNameElement = document.getElementById('userFullName');
        const userRoleElement = document.getElementById('userRole');

        if (userFullNameElement) {
            userFullNameElement.textContent = 'أحمد محمد';
        }
        if (userRoleElement) {
            userRoleElement.textContent = 'مدير النظام';
        }
    }
}

// Load company info
function loadCompanyInfo() {
    const savedCompany = localStorage.getItem('anwar_bakery_company');
    if (savedCompany) {
        const company = JSON.parse(savedCompany);
        document.getElementById('companyName').textContent = company.companyName;
        document.getElementById('companySlogan').textContent = company.slogan || 'جودة تستحق الثقة';
    }
}

// Load accounts from localStorage - FIXED TO READ FROM SALARY SYSTEM
function loadAccounts() {
    console.log('🔄 Loading accounts from database...');

    // محاولة قراءة من قاعدة البيانات المرتبطة بنظام المرتبات
    const savedAccounts = localStorage.getItem('anwar_bakery_accounts') || localStorage.getItem('anwar_bakery_chart_of_accounts');

    if (savedAccounts) {
        try {
            const loadedAccounts = JSON.parse(savedAccounts);
            console.log(`✅ Loaded ${loadedAccounts.length} accounts from database`);

            // دمج الحسابات المحملة مع الحسابات الافتراضية
            accounts = mergeAccountsWithDefaults(loadedAccounts);

        } catch (error) {
            console.error('❌ Error parsing saved accounts:', error);
            accounts = [...defaultAccounts];
            initializeDefaultAccounts();
        }
    } else {
        console.log('📝 No saved accounts found, using defaults');
        accounts = [...defaultAccounts];
        initializeDefaultAccounts();
    }

    renderAccountsTree();
    updateStats();

    // الاستماع لتحديثات من نظام المرتبات
    listenForAccountUpdates();
}

// دمج الحسابات المحملة مع الافتراضية
function mergeAccountsWithDefaults(loadedAccounts) {
    console.log('🔄 Merging loaded accounts with defaults...');

    // إنشاء خريطة للحسابات المحملة حسب الكود
    const loadedAccountsMap = {};
    loadedAccounts.forEach(acc => {
        loadedAccountsMap[acc.code] = acc;
    });

    // دمج الحسابات
    const mergedAccounts = [];

    // إضافة الحسابات الافتراضية أولاً
    defaultAccounts.forEach(defaultAcc => {
        if (loadedAccountsMap[defaultAcc.code]) {
            // الحساب موجود في البيانات المحملة، استخدم البيانات المحملة
            mergedAccounts.push(loadedAccountsMap[defaultAcc.code]);
            delete loadedAccountsMap[defaultAcc.code];
        } else {
            // الحساب غير موجود، استخدم الافتراضي
            mergedAccounts.push({...defaultAcc});
        }
    });

    // إضافة الحسابات الإضافية من البيانات المحملة (مثل حسابات الموظفين)
    Object.values(loadedAccountsMap).forEach(acc => {
        mergedAccounts.push(acc);
    });

    console.log(`✅ Merged ${mergedAccounts.length} accounts total`);
    return mergedAccounts;
}

// تهيئة الحسابات الافتراضية
function initializeDefaultAccounts() {
    addSampleSubAccounts();
    saveAccounts();
}

// الاستماع لتحديثات من نظام المرتبات
function listenForAccountUpdates() {
    // الاستماع للأحداث المخصصة
    window.addEventListener('chartOfAccountsUpdated', function() {
        console.log('📢 Received chart of accounts update event');
        setTimeout(() => {
            loadAccounts();
        }, 500);
    });

    // الاستماع لتغييرات localStorage
    window.addEventListener('storage', function(event) {
        if (event.key === 'anwar_bakery_accounts' || event.key === 'anwar_bakery_chart_of_accounts') {
            console.log('📢 Detected accounts update in localStorage');
            setTimeout(() => {
                loadAccounts();
            }, 500);
        }
    });
}

// Add sample sub-accounts for demonstration
function addSampleSubAccounts() {
    // Sample suppliers under الموردين (2110)
    const sampleSuppliers = [
        { name: 'مورد الدقيق والمواد الأساسية', code: '2111', balance: 15000 },
        { name: 'مورد منتجات الألبان', code: '2112', balance: 8500 },
        { name: 'مورد السكر والمحليات', code: '2113', balance: 5200 },
        { name: 'مورد المعدات والأدوات', code: '2114', balance: 12000 }
    ];

    // Sample customers under العملاء (1130)
    const sampleCustomers = [
        { name: 'عميل الجملة - المطاعم', code: '1131', balance: 25000 },
        { name: 'عميل الجملة - المقاهي', code: '1132', balance: 18000 },
        { name: 'عميل التجزئة - الأفراد', code: '1133', balance: 7500 }
    ];

    let maxId = Math.max(...accounts.map(acc => acc.id));

    // Add suppliers
    sampleSuppliers.forEach(supplier => {
        maxId++;
        accounts.push({
            id: maxId,
            code: supplier.code,
            name: supplier.name,
            type: 'liabilities',
            level: 3,
            parentId: 2110, // الموردين
            isActive: true,
            balance: supplier.balance,
            debitBalance: 0,
            creditBalance: supplier.balance,
            hasChildren: false,
            isExpanded: false,
            isDefault: false,
            canDelete: true,
            description: `حساب فرعي للمورد: ${supplier.name}`
        });
    });

    // Add customers
    sampleCustomers.forEach(customer => {
        maxId++;
        accounts.push({
            id: maxId,
            code: customer.code,
            name: customer.name,
            type: 'assets',
            level: 3,
            parentId: 1130, // العملاء
            isActive: true,
            balance: customer.balance,
            debitBalance: customer.balance,
            creditBalance: 0,
            hasChildren: false,
            isExpanded: false,
            isDefault: false,
            canDelete: true,
            description: `حساب فرعي للعميل: ${customer.name}`
        });
    });

    // Update parent accounts to have children
    const suppliersAccount = accounts.find(acc => acc.id === 2110);
    const customersAccount = accounts.find(acc => acc.id === 1130);

    if (suppliersAccount) {
        suppliersAccount.hasChildren = true;
        suppliersAccount.isExpanded = false;
    }

    if (customersAccount) {
        customersAccount.hasChildren = true;
        customersAccount.isExpanded = false;
    }

    saveAccounts();
}

// Save accounts to localStorage
function saveAccounts() {
    localStorage.setItem('anwar_bakery_accounts', JSON.stringify(accounts));
}

// Update statistics
function updateStats() {
    const totalAccounts = accounts.length;
    const assetsCount = accounts.filter(acc => acc.type === 'assets').length;
    const liabilitiesCount = accounts.filter(acc => acc.type === 'liabilities').length;
    const revenuesCount = accounts.filter(acc => acc.type === 'revenues').length;
    const expensesCount = accounts.filter(acc => acc.type === 'expenses').length;

    document.getElementById('totalAccounts').textContent = totalAccounts;
    document.getElementById('assetsCount').textContent = assetsCount;
    document.getElementById('liabilitiesCount').textContent = liabilitiesCount;
    document.getElementById('revenuesCount').textContent = revenuesCount;
    document.getElementById('expensesCount').textContent = expensesCount;
}

// Render accounts tree
function renderAccountsTree() {
    const treeContainer = document.getElementById('accountsTree');
    treeContainer.innerHTML = '';

    const rootAccounts = accounts.filter(acc => acc.level === 0);

    rootAccounts.forEach(account => {
        renderAccountNode(account, treeContainer);
    });
}

// Get currency symbol from settings
function getCurrencySymbol() {
    const savedSettings = localStorage.getItem('anwar_bakery_settings');
    if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        return settings.currency || '';
    }
    return '';
}

// Render single account node
function renderAccountNode(account, container) {
    const accountDiv = document.createElement('div');
    accountDiv.className = `account-level-${account.level} mb-1`;

    const typeColors = {
        'assets': 'text-blue-600',
        'liabilities': 'text-red-600',
        'equity': 'text-purple-600',
        'revenues': 'text-green-600',
        'expenses': 'text-orange-600'
    };

    const expandIcon = account.hasChildren ?
        (account.isExpanded ? '🔽' : '▶️') : '📄';

    const currencySymbol = getCurrencySymbol() || 'ر.ي';
    const isDefaultAccount = account.isDefault || false;
    const canDelete = account.canDelete !== false;

    // تأكد من وجود قيمة الرصيد وتنسيقها
    const balance = parseFloat(account.balance) || 0;
    const debitBalance = parseFloat(account.debitBalance) || 0;
    const creditBalance = parseFloat(account.creditBalance) || 0;

    // تسجيل للتتبع
    if (account.code === '5210' || account.code.startsWith('2120')) {
        console.log(`🔍 Rendering account ${account.code} - ${account.name}:`);
        console.log(`  - Balance: ${balance}`);
        console.log(`  - Debit: ${debitBalance}`);
        console.log(`  - Credit: ${creditBalance}`);
    }

    accountDiv.innerHTML = `
        <div class="account-item flex items-center justify-between p-3 rounded-lg cursor-pointer ${isDefaultAccount ? 'default-account' : 'hover:bg-gray-50'}"
             onclick="showAccountDetails(${account.id})"
             title="${account.description || ''}">
            <div class="flex items-center">
                <span class="ml-2 cursor-pointer text-lg" onclick="toggleAccount(${account.id}); event.stopPropagation();">${expandIcon}</span>
                <span class="font-mono text-xs text-gray-500 ml-3 min-w-[60px] bg-gray-100 px-2 py-1 rounded">${account.code}</span>
                <span class="font-medium ${typeColors[account.type]} text-sm ml-3">${account.name}</span>
                ${isDefaultAccount ? '<span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full ml-2">🔒 افتراضي</span>' : ''}
            </div>
            <div class="flex items-center space-x-2">
                <span class="text-sm font-semibold ${balance >= 0 ? 'text-green-600' : 'text-red-600'} min-w-[120px] text-left bg-gray-50 px-3 py-1 rounded border">
                    ${balance.toLocaleString()} ${currencySymbol}
                </span>
                <div class="action-buttons flex space-x-1">
                    <button onclick="editAccount(${account.id}); event.stopPropagation();"
                            class="text-blue-600 hover:text-blue-800 text-xs px-2 py-1 rounded hover:bg-blue-100 transition-colors">
                        ✏️ تعديل
                    </button>
                    <button onclick="addChildAccount(${account.id}); event.stopPropagation();"
                            class="text-green-600 hover:text-green-800 text-xs px-2 py-1 rounded hover:bg-green-100 transition-colors">
                        ➕ فرعي
                    </button>
                    ${canDelete ? `<button onclick="deleteAccount(${account.id}); event.stopPropagation();"
                                           class="text-red-600 hover:text-red-800 text-xs px-2 py-1 rounded hover:bg-red-100 transition-colors">
                                       🗑️ حذف
                                   </button>` : ''}
                </div>
            </div>
        </div>
    `;

    container.appendChild(accountDiv);

    // Render children if expanded
    if (account.hasChildren && account.isExpanded) {
        const childrenContainer = document.createElement('div');
        childrenContainer.className = 'mr-6 tree-line';

        const children = accounts.filter(acc => acc.parentId === account.id);
        children.forEach(child => {
            renderAccountNode(child, childrenContainer);
        });

        container.appendChild(childrenContainer);
    }
}

// Toggle account expansion
function toggleAccount(accountId) {
    const account = accounts.find(acc => acc.id === accountId);
    if (account && account.hasChildren) {
        account.isExpanded = !account.isExpanded;
        renderAccountsTree();
        saveAccounts();
    }
}

// Expand all accounts
function expandAllAccounts() {
    accounts.forEach(account => {
        if (account.hasChildren) {
            account.isExpanded = true;
        }
    });
    renderAccountsTree();
    saveAccounts();
}

// Switch main tabs
function switchMainTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.main-tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.main-tab-button').forEach(button => {
        button.classList.remove('border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500');
    });

    // Show selected tab content
    document.getElementById(tabName + 'Tab').classList.add('active');

    // Activate selected tab button
    event.target.classList.remove('border-transparent', 'text-gray-500');
    event.target.classList.add('border-blue-500', 'text-blue-600');
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_session');
        sessionStorage.removeItem('anwar_bakery_session');
        window.location.href = 'login.html';
    }
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Show account details and transactions
function showAccountDetails(accountId) {
    const account = accounts.find(acc => acc.id === accountId);
    if (!account) return;

    const currencySymbol = getCurrencySymbol();

    let message = `تفاصيل الحساب: ${account.name}\n\n`;
    message += `📋 الكود: ${account.code}\n`;
    message += `📝 الوصف: ${account.description}\n`;
    message += `💰 الرصيد الحالي: ${account.balance.toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}\n`;
    message += `📈 المدين: ${account.debitBalance.toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}\n`;
    message += `📉 الدائن: ${account.creditBalance.toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}\n`;
    message += `🏷️ النوع: ${getAccountTypeName(account.type)}\n`;
    message += `📊 المستوى: ${account.level + 1}\n`;

    if (account.isDefault) {
        message += `🔒 حساب افتراضي - لا يمكن حذفه\n`;
    }

    message += `\n📊 آخر العمليات:\n`;
    message += `قريباً - سيتم عرض آخر العمليات على هذا الحساب`;

    alert(message);
}

// Get account type name in Arabic
function getAccountTypeName(type) {
    const typeNames = {
        'assets': 'الأصول',
        'liabilities': 'الخصوم',
        'equity': 'حقوق الملكية',
        'revenues': 'الإيرادات',
        'expenses': 'المصروفات'
    };
    return typeNames[type] || type;
}

// Add child account
function addChildAccount(parentId) {
    const parentAccount = accounts.find(acc => acc.id === parentId);
    if (!parentAccount) return;

    const accountName = prompt(`إضافة حساب فرعي تحت: ${parentAccount.name}\n\nأدخل اسم الحساب الجديد:`);
    if (!accountName || accountName.trim() === '') return;

    const accountCode = prompt('أدخل كود الحساب:');
    if (!accountCode || accountCode.trim() === '') return;

    // Check if code already exists
    if (accounts.find(acc => acc.code === accountCode)) {
        alert('كود الحساب موجود مسبقاً!');
        return;
    }

    const newAccount = {
        id: Math.max(...accounts.map(acc => acc.id)) + 1,
        code: accountCode.trim(),
        name: accountName.trim(),
        type: parentAccount.type,
        level: parentAccount.level + 1,
        parentId: parentId,
        isActive: true,
        balance: 0,
        debitBalance: 0,
        creditBalance: 0,
        hasChildren: false,
        isExpanded: false,
        isDefault: false,
        canDelete: true,
        description: `حساب فرعي تحت ${parentAccount.name}`
    };

    accounts.push(newAccount);

    // Update parent to have children
    parentAccount.hasChildren = true;
    parentAccount.isExpanded = true;

    saveAccounts();
    renderAccountsTree();
    updateStats();
    showMessage('تم إضافة الحساب الفرعي بنجاح!', 'success');
}

// Edit account
function editAccount(accountId) {
    const account = accounts.find(acc => acc.id === accountId);
    if (!account) return;

    const newName = prompt(`تعديل اسم الحساب:\n\nالاسم الحالي: ${account.name}`, account.name);
    if (newName === null) return;

    if (newName.trim() === '') {
        alert('اسم الحساب لا يمكن أن يكون فارغاً!');
        return;
    }

    const newDescription = prompt(`تعديل وصف الحساب:\n\nالوصف الحالي: ${account.description || ''}`, account.description || '');

    account.name = newName.trim();
    if (newDescription !== null) {
        account.description = newDescription.trim();
    }

    saveAccounts();
    renderAccountsTree();
    showMessage('تم تحديث الحساب بنجاح!', 'success');
}

// Delete account
function deleteAccount(accountId) {
    const account = accounts.find(acc => acc.id === accountId);
    if (!account) return;

    if (account.isDefault || account.canDelete === false) {
        alert('لا يمكن حذف الحسابات الافتراضية!');
        return;
    }

    // Check if account has children
    const hasChildren = accounts.some(acc => acc.parentId === accountId);
    if (hasChildren) {
        alert('لا يمكن حذف حساب يحتوي على حسابات فرعية!');
        return;
    }

    // Check if account has balance
    if (account.balance !== 0) {
        const currencySymbol = getCurrencySymbol();
        if (!confirm(`الحساب يحتوي على رصيد ${account.balance.toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''}\n\nهل أنت متأكد من الحذف؟`)) {
            return;
        }
    }

    if (!confirm(`هل أنت متأكد من حذف الحساب: ${account.name}؟`)) {
        return;
    }

    // Remove account
    const accountIndex = accounts.findIndex(acc => acc.id === accountId);
    accounts.splice(accountIndex, 1);

    // Update parent if no more children
    const parentId = account.parentId;
    if (parentId) {
        const remainingChildren = accounts.filter(acc => acc.parentId === parentId);
        if (remainingChildren.length === 0) {
            const parent = accounts.find(acc => acc.id === parentId);
            if (parent) {
                parent.hasChildren = false;
                parent.isExpanded = false;
            }
        }
    }

    saveAccounts();
    renderAccountsTree();
    updateStats();
    showMessage('تم حذف الحساب بنجاح!', 'success');
}

// Open add account modal
function openAddAccountModal() {
    showMessage('نافذة إضافة حساب جديد قيد التطوير...', 'info');
}

// Expand all accounts
function expandAllAccounts() {
    accounts.forEach(account => {
        if (account.hasChildren) {
            account.isExpanded = true;
        }
    });
    renderAccountsTree();
    showMessage('تم توسيع جميع الحسابات', 'success');
}

// Collapse all accounts
function collapseAllAccounts() {
    accounts.forEach(account => {
        account.isExpanded = false;
    });
    renderAccountsTree();
    showMessage('تم طي جميع الحسابات', 'success');
}

// Filter accounts tree
function filterAccountsTree() {
    const searchTerm = document.getElementById('treeSearchInput').value.toLowerCase();
    const typeFilter = document.getElementById('accountTypeFilter').value;
    const levelFilter = document.getElementById('accountLevelFilter').value;

    // For now, just re-render the tree
    // Advanced filtering can be implemented later
    renderAccountsTree();

    if (searchTerm || typeFilter || levelFilter) {
        showMessage('البحث والفلترة قيد التطوير...', 'info');
    }
}

// Export accounts to Excel
function exportAccountsToExcel() {
    const currencySymbol = getCurrencySymbol();
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += `كود الحساب,اسم الحساب,النوع,المستوى,الرصيد${currencySymbol ? ' (' + currencySymbol + ')' : ''},الوصف,افتراضي\n`;

    accounts.forEach(account => {
        const typeNames = {
            'assets': 'الأصول',
            'liabilities': 'الخصوم',
            'equity': 'حقوق الملكية',
            'revenues': 'الإيرادات',
            'expenses': 'المصروفات'
        };

        const row = [
            account.code,
            account.name,
            typeNames[account.type],
            account.level + 1,
            account.balance.toLocaleString(),
            account.description || '',
            account.isDefault ? 'نعم' : 'لا'
        ].join(',');
        csvContent += row + "\n";
    });

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "chart_of_accounts.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showMessage('تم تصدير شجرة الحسابات بنجاح!', 'success');
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_current_user');
        window.location.href = 'login.html';
    }
}

// Toggle sidebar for mobile
function toggleSidebar() {
    // Mobile sidebar toggle functionality
    showMessage('وظيفة الشريط الجانبي للجوال قيد التطوير...', 'info');
}

// Debug function to check account balances
function debugAccountBalances() {
    console.log('🔍 Debugging account balances...');

    const salaryAccounts = accounts.filter(acc =>
        acc.code === '5210' ||
        acc.code === '2120' ||
        acc.code.startsWith('2120-') ||
        acc.code === '1111'
    );

    console.log('💰 Salary-related accounts:');
    salaryAccounts.forEach(acc => {
        console.log(`  ${acc.code} - ${acc.name}:`);
        console.log(`    Balance: ${acc.balance || 0}`);
        console.log(`    Debit: ${acc.debitBalance || 0}`);
        console.log(`    Credit: ${acc.creditBalance || 0}`);
    });

    // فحص البيانات في localStorage
    const savedAccounts = localStorage.getItem('anwar_bakery_accounts');
    const savedChart = localStorage.getItem('anwar_bakery_chart_of_accounts');

    console.log('💾 localStorage data:');
    console.log(`  anwar_bakery_accounts: ${savedAccounts ? 'exists' : 'missing'}`);
    console.log(`  anwar_bakery_chart_of_accounts: ${savedChart ? 'exists' : 'missing'}`);

    if (savedAccounts) {
        const parsed = JSON.parse(savedAccounts);
        const salaryAccountsInStorage = parsed.filter(acc =>
            acc.code === '5210' || acc.code.startsWith('2120')
        );
        console.log('💾 Salary accounts in localStorage:');
        salaryAccountsInStorage.forEach(acc => {
            console.log(`  ${acc.code} - ${acc.name}: Balance = ${acc.balance || 0}`);
        });
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadUserInfo();
    loadCompanyInfo();
    loadAccounts();

    // إضافة زر debug للاختبار
    setTimeout(() => {
        debugAccountBalances();
    }, 2000);
});
