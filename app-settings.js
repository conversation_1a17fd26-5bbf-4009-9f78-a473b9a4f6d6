// نظام الإعدادات المركزي للتطبيق
class AppSettings {
    constructor() {
        this.settings = this.loadSettings();
        this.listeners = [];
        this.initializeEventSystem();
    }

    // تحميل الإعدادات من localStorage
    loadSettings() {
        const defaultSettings = {
            // إعدادات الشركة
            company: {
                companyNameAr: 'مخبز أنور',
                companyNameEn: 'Anwar Bakery',
                logo: '',
                phone: '',
                email: '',
                website: '',
                address: '',
                city: '',
                country: 'السعودية',
                taxNumber: '',
                commercialRegister: ''
            },

            // إعدادات العملة والمالية
            financial: {
                baseCurrency: 'SAR',
                currencySymbol: 'ر.س',
                currencyPosition: 'after', // before, after
                decimalPlaces: 2,
                thousandsSeparator: ',',
                decimalSeparator: '.',
                taxRate: 15,
                enableTax: true,
                enableDiscount: true,
                enableMultiCurrency: false,
                supportedCurrencies: [
                    { code: 'SAR', name: 'ريال سعودي', symbol: 'ر.س' },
                    { code: 'YER', name: 'ريال يمني', symbol: 'ر.ي' },
                    { code: 'USD', name: 'دولار أمريكي', symbol: '$' },
                    { code: 'EUR', name: 'يورو', symbol: '€' },
                    { code: 'AED', name: 'درهم إماراتي', symbol: 'د.إ' },
                    { code: 'KWD', name: 'دينار كويتي', symbol: 'د.ك' },
                    { code: 'QAR', name: 'ريال قطري', symbol: 'ر.ق' }
                ]
            },

            // إعدادات التاريخ والوقت
            dateTime: {
                dateFormat: 'YYYY-MM-DD',
                timeFormat: '24h',
                timezone: 'Asia/Riyadh',
                weekStart: 'saturday',
                language: 'ar'
            },

            // إعدادات الفواتير
            invoicing: {
                invoicePrefix: 'INV',
                invoiceNumberLength: 6,
                autoInvoiceNumber: true,
                printAfterSave: false,
                defaultPaymentMethod: 'cash',
                enableBarcode: true,
                enableQRCode: true
            },

            // إعدادات المخزون
            inventory: {
                enableLowStockAlerts: true,
                lowStockThreshold: 10,
                enableExpiryAlerts: true,
                expiryAlertDays: 7,
                enableBatchTracking: false,
                enableSerialNumbers: false,
                defaultUnit: 'piece'
            },

            // إعدادات النظام
            system: {
                theme: 'light',
                sidebarCollapsed: false,
                itemsPerPage: 20,
                enableNotifications: true,
                enableSounds: false,
                autoSave: true,
                autoSaveInterval: 30000 // 30 seconds
            },

            // إعدادات الطباعة
            printing: {
                paperSize: 'A4',
                orientation: 'portrait',
                margins: { top: 20, right: 20, bottom: 20, left: 20 },
                fontSize: 12,
                fontFamily: 'Cairo',
                includeLogo: true,
                includeQRCode: true
            }
        };

        const savedSettings = localStorage.getItem('anwar_bakery_settings');
        if (savedSettings) {
            return this.mergeSettings(defaultSettings, JSON.parse(savedSettings));
        }

        return defaultSettings;
    }

    // دمج الإعدادات الافتراضية مع المحفوظة
    mergeSettings(defaultSettings, savedSettings) {
        const merged = { ...defaultSettings };

        for (const category in savedSettings) {
            if (merged[category]) {
                merged[category] = { ...merged[category], ...savedSettings[category] };
            }
        }

        return merged;
    }

    // حفظ الإعدادات
    saveSettings() {
        localStorage.setItem('anwar_bakery_settings', JSON.stringify(this.settings));
        this.notifyListeners('settingsChanged', this.settings);
    }

    // الحصول على إعداد معين
    get(category, key = null) {
        if (key) {
            return this.settings[category]?.[key];
        }
        return this.settings[category];
    }

    // تحديث إعداد معين
    set(category, key, value) {
        if (!this.settings[category]) {
            this.settings[category] = {};
        }

        this.settings[category][key] = value;
        this.saveSettings();

        // تطبيق التغيير فوراً
        this.applySettingChange(category, key, value);
    }

    // تحديث فئة كاملة من الإعدادات
    setCategory(category, values) {
        this.settings[category] = { ...this.settings[category], ...values };
        this.saveSettings();

        // تطبيق التغييرات فوراً
        for (const [key, value] of Object.entries(values)) {
            this.applySettingChange(category, key, value);
        }
    }

    // تطبيق التغيير فوراً على التطبيق
    applySettingChange(category, key, value) {
        switch (category) {
            case 'company':
                this.applyCompanySettings(key, value);
                break;
            case 'financial':
                this.applyFinancialSettings(key, value);
                break;
            case 'system':
                this.applySystemSettings(key, value);
                break;
            case 'dateTime':
                this.applyDateTimeSettings(key, value);
                break;
        }
    }

    // تطبيق إعدادات الشركة
    applyCompanySettings(key, value) {
        switch (key) {
            case 'companyNameAr':
                // تحديث اسم الشركة في جميع الصفحات
                const sidebarElements = document.querySelectorAll('#sidebarCompanyName');
                sidebarElements.forEach(el => el.textContent = value);

                // تحديث عنوان الصفحة
                const currentTitle = document.title;
                const titleParts = currentTitle.split(' - ');
                if (titleParts.length > 1) {
                    document.title = `${titleParts[0]} - ${value}`;
                }
                break;

            case 'logo':
                // تحديث الشعار في جميع الصفحات
                const logoElements = document.querySelectorAll('.company-logo');
                logoElements.forEach(el => {
                    if (value) {
                        el.src = value;
                        el.style.display = 'block';
                    } else {
                        el.style.display = 'none';
                    }
                });
                break;
        }
    }

    // تطبيق الإعدادات المالية
    applyFinancialSettings(key, value) {
        switch (key) {
            case 'baseCurrency':
            case 'currencySymbol':
                // تحديث العملة في جميع العناصر المالية
                this.updateCurrencyDisplay();
                break;

            case 'taxRate':
                // تحديث معدل الضريبة في الحسابات
                this.updateTaxCalculations();
                break;
        }
    }

    // تطبيق إعدادات النظام
    applySystemSettings(key, value) {
        switch (key) {
            case 'theme':
                this.applyTheme(value);
                break;

            case 'sidebarCollapsed':
                this.applySidebarState(value);
                break;

            case 'itemsPerPage':
                this.updatePagination(value);
                break;
        }
    }

    // تطبيق إعدادات التاريخ والوقت
    applyDateTimeSettings(key, value) {
        switch (key) {
            case 'dateFormat':
                this.updateDateDisplays();
                break;

            case 'language':
                this.updateLanguage(value);
                break;
        }
    }

    // تحديث عرض العملة
    updateCurrencyDisplay() {
        const currency = this.get('financial', 'baseCurrency');
        const symbol = this.get('financial', 'currencySymbol');
        const position = this.get('financial', 'currencyPosition');

        // تحديث جميع عناصر العملة في الصفحة
        const currencyElements = document.querySelectorAll('.currency-display');
        currencyElements.forEach(el => {
            const amount = el.dataset.amount || '0';
            if (position === 'before') {
                el.textContent = `${symbol} ${amount}`;
            } else {
                el.textContent = `${amount} ${symbol}`;
            }
        });
    }

    // تحديث حسابات الضريبة
    updateTaxCalculations() {
        const taxRate = this.get('financial', 'taxRate');

        // إشعار جميع النماذج بتحديث حسابات الضريبة
        this.notifyListeners('taxRateChanged', taxRate);
    }

    // تطبيق السمة
    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);

        if (theme === 'dark') {
            document.body.classList.add('dark');
        } else {
            document.body.classList.remove('dark');
        }
    }

    // تطبيق حالة الشريط الجانبي
    applySidebarState(collapsed) {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            if (collapsed) {
                sidebar.classList.add('collapsed');
            } else {
                sidebar.classList.remove('collapsed');
            }
        }
    }

    // تحديث التصفح
    updatePagination(itemsPerPage) {
        this.notifyListeners('paginationChanged', itemsPerPage);
    }

    // تحديث عرض التواريخ
    updateDateDisplays() {
        const format = this.get('dateTime', 'dateFormat');
        this.notifyListeners('dateFormatChanged', format);
    }

    // تحديث اللغة
    updateLanguage(language) {
        document.documentElement.lang = language;
        document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
    }

    // نظام الأحداث
    initializeEventSystem() {
        // إنشاء حدث مخصص للإعدادات
        this.settingsEvent = new CustomEvent('settingsUpdated');
    }

    // إضافة مستمع للأحداث
    addListener(eventType, callback) {
        if (!this.listeners[eventType]) {
            this.listeners[eventType] = [];
        }
        this.listeners[eventType].push(callback);
    }

    // إزالة مستمع
    removeListener(eventType, callback) {
        if (this.listeners[eventType]) {
            this.listeners[eventType] = this.listeners[eventType].filter(cb => cb !== callback);
        }
    }

    // إشعار المستمعين
    notifyListeners(eventType, data) {
        if (this.listeners[eventType]) {
            this.listeners[eventType].forEach(callback => callback(data));
        }

        // إرسال حدث عام للنافذة
        window.dispatchEvent(new CustomEvent(eventType, { detail: data }));
    }

    // تنسيق المبلغ حسب إعدادات العملة
    formatCurrency(amount) {
        const symbol = this.get('financial', 'currencySymbol');
        const position = this.get('financial', 'currencyPosition');
        const decimalPlaces = this.get('financial', 'decimalPlaces');
        const thousandsSeparator = this.get('financial', 'thousandsSeparator');
        const decimalSeparator = this.get('financial', 'decimalSeparator');

        // تنسيق الرقم
        const formattedAmount = Number(amount).toFixed(decimalPlaces)
            .replace('.', decimalSeparator)
            .replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator);

        // إضافة رمز العملة
        if (position === 'before') {
            return `${symbol} ${formattedAmount}`;
        } else {
            return `${formattedAmount} ${symbol}`;
        }
    }

    // تنسيق التاريخ
    formatDate(date) {
        const format = this.get('dateTime', 'dateFormat');
        const dateObj = new Date(date);

        switch (format) {
            case 'DD/MM/YYYY':
                return dateObj.toLocaleDateString('ar-SA');
            case 'MM/DD/YYYY':
                return dateObj.toLocaleDateString('en-US');
            case 'YYYY-MM-DD':
            default:
                return dateObj.toISOString().split('T')[0];
        }
    }

    // إعادة تعيين الإعدادات للافتراضية
    resetToDefaults() {
        localStorage.removeItem('anwar_bakery_settings');
        this.settings = this.loadSettings();
        this.saveSettings();

        // إعادة تحميل الصفحة لتطبيق جميع الإعدادات
        window.location.reload();
    }

    // تصدير الإعدادات
    exportSettings() {
        const dataStr = JSON.stringify(this.settings, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = 'anwar_bakery_settings.json';
        link.click();
    }

    // استيراد الإعدادات
    importSettings(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedSettings = JSON.parse(e.target.result);
                this.settings = this.mergeSettings(this.loadSettings(), importedSettings);
                this.saveSettings();

                // تطبيق جميع الإعدادات
                this.applyAllSettings();

                alert('تم استيراد الإعدادات بنجاح!');
            } catch (error) {
                alert('خطأ في استيراد الإعدادات: ' + error.message);
            }
        };
        reader.readAsText(file);
    }

    // تطبيق جميع الإعدادات
    applyAllSettings() {
        // تطبيق إعدادات الشركة
        this.applyCompanySettings('companyNameAr', this.get('company', 'companyNameAr'));
        this.applyCompanySettings('logo', this.get('company', 'logo'));

        // تطبيق الإعدادات المالية
        this.updateCurrencyDisplay();

        // تطبيق إعدادات النظام
        this.applySystemSettings('theme', this.get('system', 'theme'));
        this.applySystemSettings('sidebarCollapsed', this.get('system', 'sidebarCollapsed'));

        // تطبيق إعدادات التاريخ
        this.applyDateTimeSettings('language', this.get('dateTime', 'language'));
    }
}

// إنشاء مثيل عام للإعدادات
window.appSettings = new AppSettings();

// تطبيق الإعدادات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    window.appSettings.applyAllSettings();
});
