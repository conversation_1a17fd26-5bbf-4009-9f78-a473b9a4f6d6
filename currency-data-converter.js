// محول البيانات المالية حسب العملة - حل الكارثة المحاسبية
class CurrencyDataConverter {
    constructor() {
        this.exchangeRates = {
            // أسعار الصرف مقابل الريال السعودي (تحديث يومي مطلوب)
            'SAR': 1,           // الأساس
            'YER': 663.75,      // 1 ريال سعودي = 663.75 ريال يمني
            'USD': 0.267,       // 1 ريال سعودي = 0.267 دولار
            'EUR': 0.244,       // 1 ريال سعودي = 0.244 يورو
            'AED': 0.98,        // 1 ريال سعودي = 0.98 درهم إماراتي
            'KWD': 0.082,       // 1 ريال سعودي = 0.082 دينار كويتي
            'QAR': 0.97         // 1 ريال سعودي = 0.97 ريال قطري
        };
        
        this.init();
    }

    init() {
        console.log('💰 Currency Data Converter initialized');
        this.checkAndConvertData();
    }

    // فحص وتحويل البيانات المالية
    checkAndConvertData() {
        const currentCurrency = this.getCurrentCurrency();
        const storedCurrency = this.getStoredCurrency();
        
        console.log('Current currency:', currentCurrency);
        console.log('Stored currency:', storedCurrency);
        
        if (currentCurrency !== storedCurrency) {
            console.log('🚨 CURRENCY MISMATCH DETECTED! Converting data...');
            this.convertAllFinancialData(storedCurrency, currentCurrency);
        }
    }

    // تحويل جميع البيانات المالية
    convertAllFinancialData(fromCurrency, toCurrency) {
        console.log(`🔄 Converting from ${fromCurrency} to ${toCurrency}`);
        
        // تحويل رواتب الموظفين
        this.convertEmployeeSalaries(fromCurrency, toCurrency);
        
        // تحويل أرصدة الصناديق
        this.convertCashRegisterBalances(fromCurrency, toCurrency);
        
        // تحويل أرصدة البنوك
        this.convertBankBalances(fromCurrency, toCurrency);
        
        // تحويل أسعار المنتجات
        this.convertProductPrices(fromCurrency, toCurrency);
        
        // تحويل أرصدة العملاء والموردين
        this.convertCustomerSupplierBalances(fromCurrency, toCurrency);
        
        // حفظ العملة الجديدة كمرجع
        this.updateStoredCurrency(toCurrency);
        
        console.log('✅ All financial data converted successfully');
        
        // إعادة تحميل الصفحة لعرض البيانات الجديدة
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }

    // تحويل رواتب الموظفين
    convertEmployeeSalaries(fromCurrency, toCurrency) {
        const employees = JSON.parse(localStorage.getItem('anwar_bakery_employees') || '[]');
        const conversionRate = this.getConversionRate(fromCurrency, toCurrency);
        
        employees.forEach(employee => {
            if (employee.basicSalary) {
                const oldSalary = employee.basicSalary;
                employee.basicSalary = Math.round(employee.basicSalary * conversionRate);
                console.log(`Employee ${employee.fullName}: ${oldSalary} ${fromCurrency} → ${employee.basicSalary} ${toCurrency}`);
            }
        });
        
        localStorage.setItem('anwar_bakery_employees', JSON.stringify(employees));
        
        // تحويل سجلات الرواتب المدفوعة
        const salaries = JSON.parse(localStorage.getItem('anwar_bakery_salaries') || '[]');
        salaries.forEach(salary => {
            if (salary.basicSalary) {
                salary.basicSalary = Math.round(salary.basicSalary * conversionRate);
                salary.netSalary = Math.round(salary.netSalary * conversionRate);
                salary.allowances = Math.round(salary.allowances * conversionRate);
                salary.deductions = Math.round(salary.deductions * conversionRate);
                salary.penalties = Math.round(salary.penalties * conversionRate);
            }
        });
        localStorage.setItem('anwar_bakery_salaries', JSON.stringify(salaries));
    }

    // تحويل أرصدة الصناديق
    convertCashRegisterBalances(fromCurrency, toCurrency) {
        const cashRegisters = JSON.parse(localStorage.getItem('anwar_bakery_cash_registers') || '[]');
        const conversionRate = this.getConversionRate(fromCurrency, toCurrency);
        
        cashRegisters.forEach(register => {
            if (register.currentBalance) {
                const oldBalance = register.currentBalance;
                register.currentBalance = Math.round(register.currentBalance * conversionRate);
                register.openingBalance = Math.round(register.openingBalance * conversionRate);
                console.log(`Cash Register ${register.name}: ${oldBalance} ${fromCurrency} → ${register.currentBalance} ${toCurrency}`);
            }
        });
        
        localStorage.setItem('anwar_bakery_cash_registers', JSON.stringify(cashRegisters));
    }

    // تحويل أرصدة البنوك
    convertBankBalances(fromCurrency, toCurrency) {
        const banks = JSON.parse(localStorage.getItem('anwar_bakery_banks') || '[]');
        const conversionRate = this.getConversionRate(fromCurrency, toCurrency);
        
        banks.forEach(bank => {
            if (bank.currentBalance) {
                bank.currentBalance = Math.round(bank.currentBalance * conversionRate);
                bank.openingBalance = Math.round(bank.openingBalance * conversionRate);
            }
        });
        
        localStorage.setItem('anwar_bakery_banks', JSON.stringify(banks));
    }

    // تحويل أسعار المنتجات
    convertProductPrices(fromCurrency, toCurrency) {
        const products = JSON.parse(localStorage.getItem('anwar_bakery_products') || '[]');
        const conversionRate = this.getConversionRate(fromCurrency, toCurrency);
        
        products.forEach(product => {
            if (product.sellingPrice) {
                product.sellingPrice = Math.round(product.sellingPrice * conversionRate * 100) / 100; // دقة عشرية للأسعار
                product.costPrice = Math.round(product.costPrice * conversionRate * 100) / 100;
            }
        });
        
        localStorage.setItem('anwar_bakery_products', JSON.stringify(products));
    }

    // تحويل أرصدة العملاء والموردين
    convertCustomerSupplierBalances(fromCurrency, toCurrency) {
        const conversionRate = this.getConversionRate(fromCurrency, toCurrency);
        
        // تحويل أرصدة العملاء
        const customers = JSON.parse(localStorage.getItem('anwar_bakery_customers') || '[]');
        customers.forEach(customer => {
            if (customer.balance) {
                customer.balance = Math.round(customer.balance * conversionRate);
            }
        });
        localStorage.setItem('anwar_bakery_customers', JSON.stringify(customers));
        
        // تحويل أرصدة الموردين
        const suppliers = JSON.parse(localStorage.getItem('anwar_bakery_suppliers') || '[]');
        suppliers.forEach(supplier => {
            if (supplier.balance) {
                supplier.balance = Math.round(supplier.balance * conversionRate);
            }
        });
        localStorage.setItem('anwar_bakery_suppliers', JSON.stringify(suppliers));
    }

    // حساب معدل التحويل
    getConversionRate(fromCurrency, toCurrency) {
        const fromRate = this.exchangeRates[fromCurrency] || 1;
        const toRate = this.exchangeRates[toCurrency] || 1;
        
        // التحويل عبر الريال السعودي كعملة أساس
        const conversionRate = toRate / fromRate;
        
        console.log(`Conversion rate from ${fromCurrency} to ${toCurrency}: ${conversionRate}`);
        return conversionRate;
    }

    // الحصول على العملة الحالية
    getCurrentCurrency() {
        const companyData = JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
        return companyData.currency || 'SAR';
    }

    // الحصول على العملة المخزنة
    getStoredCurrency() {
        return localStorage.getItem('anwar_bakery_stored_currency') || 'SAR';
    }

    // تحديث العملة المخزنة
    updateStoredCurrency(currency) {
        localStorage.setItem('anwar_bakery_stored_currency', currency);
    }

    // تحديث أسعار الصرف (يجب استدعاؤها يومياً)
    updateExchangeRates(newRates) {
        this.exchangeRates = { ...this.exchangeRates, ...newRates };
        console.log('📈 Exchange rates updated:', this.exchangeRates);
    }
}

// إنشاء مثيل عام
window.currencyDataConverter = new CurrencyDataConverter();

// وظائف مساعدة
window.convertCurrencyData = (fromCurrency, toCurrency) => {
    window.currencyDataConverter.convertAllFinancialData(fromCurrency, toCurrency);
};

window.updateExchangeRates = (rates) => {
    window.currencyDataConverter.updateExchangeRates(rates);
};

console.log('🚨 Currency Data Converter loaded - Financial disaster prevention system active!');
