<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأصناف والمنتجات - مخبز أنور</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .modal {
            display: none;
            z-index: 9999 !important;
        }
        .modal.active {
            display: flex !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
        }
        .tab-content { display: none; }
        .tab-content.active { display: block; }

        /* تحسين مظهر النماذج */
        .modal .bg-white {
            max-height: 90vh;
        }

        /* تحسين الخط في حقول الإدخال */
        input[type="text"],
        input[type="number"],
        input[type="date"],
        select,
        textarea {
            font-size: 16px !important;
            line-height: 1.5;
        }

        /* تحسين التبويبات */
        .tab-button {
            font-size: 14px;
            padding: 8px 12px;
            transition: all 0.2s ease;
        }

        /* تحسين الأزرار */
        button {
            font-size: 14px;
            transition: all 0.2s ease;
        }

        /* تحسين حقول الإدخال في الجداول */
        table input[type="number"],
        table input[type="date"] {
            font-size: 14px !important;
            padding: 4px 8px !important;
        }

        /* تحسين المسافات */
        .modal form {
            max-height: calc(90vh - 120px);
            overflow-y: auto;
        }

        /* تحسين التبويبات الرئيسية */
        .main-tab-content { display: none; }
        .main-tab-content.active { display: block; }
        .main-tab-button {
            transition: all 0.2s ease;
        }

        /* تحسين جداول الإنتاج */
        .production-table th,
        .production-table td {
            padding: 8px 12px;
            text-align: right;
            border: 1px solid #e5e7eb;
        }

        .production-table th {
            background-color: #f9fafb;
            font-weight: 600;
        }

        /* تحسين بطاقات الإنتاج */
        .production-card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .production-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* تثبيت الشريط الجانبي */
        #sidebar {
            position: fixed !important;
            top: 0;
            right: 0;
            height: 100vh;
            z-index: 1000;
        }

        /* ضمان عدم تداخل المحتوى مع الشريط الجانبي */
        .main-content {
            margin-right: 256px; /* عرض الشريط الجانبي */
            min-height: 100vh;
        }

        /* تحسين التنقل بين التبويبات */
        .main-tab-content {
            display: none;
        }

        .main-tab-content.active {
            display: block;
        }

        /* تحسين أزرار التبويبات */
        .main-tab-button {
            transition: all 0.3s ease;
        }

        .main-tab-button:hover {
            color: #3b82f6;
            border-color: #93c5fd;
        }

        /* تحسين أزرار التبويبات في النافذة المنبثقة */
        .modal-tab-button {
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .modal-tab-button:hover {
            color: #374151 !important;
        }

        .modal-tab-button.active {
            border-color: #3b82f6 !important;
            color: #3b82f6 !important;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Mobile menu overlay -->
    <div id="mobileOverlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 lg:hidden hidden"></div>

    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform translate-x-full lg:translate-x-0 lg:static lg:inset-0 sidebar-transition flex flex-col">
            <div class="h-16 px-4 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between flex-shrink-0">
                <h1 id="sidebarCompanyName" class="text-white text-lg font-bold">مخبز أنور</h1>
                <button onclick="toggleSidebar()" class="lg:hidden text-white">
                    <span class="text-xl">✕</span>
                </button>
            </div>

            <nav class="flex-1 overflow-y-auto px-2 py-4 space-y-1">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    لوحة التحكم
                </a>
                <a href="users-management.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👥</span>
                    إدارة المستخدمين
                </a>
                <a href="company-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏢</span>
                    بيانات المنشأة
                </a>
                <a href="system-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">⚙️</span>
                    إعدادات النظام
                </a>
                <a href="warehouses.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏬</span>
                    المخازن
                </a>
                <a href="units.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📏</span>
                    وحدات القياس
                </a>
                <a href="chart-of-accounts.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🌳</span>
                    شجرة الحسابات
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="damage-entry.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🗑️</span>
                    قيد التالف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🚚</span>
                    الموردين
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="inventory.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📊</span>
                    المخزون
                </a>
                <a href="reports.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📈</span>
                    التقارير
                </a>

            </nav>

            <!-- User Info & Logout -->
            <div class="flex-shrink-0 p-4 border-t border-gray-200">
                <div class="bg-gray-50 rounded-lg p-3 mb-2">
                    <p id="userFullName" class="text-sm font-medium text-gray-900">مدير النظام</p>
                    <p id="userRole" class="text-xs text-gray-500">admin</p>
                    <p id="loginTime" class="text-xs text-gray-400"></p>
                </div>
                <button onclick="logout()" class="w-full text-right px-3 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50 flex items-center">
                    <span class="ml-3">🚪</span>
                    تسجيل الخروج
                </button>
            </div>
        </div>

        <!-- Main content -->
        <div class="flex-1 overflow-hidden main-content">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <button onclick="toggleSidebar()" class="lg:hidden text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">☰</span>
                            </button>
                            <span class="text-lg font-semibold text-gray-900">إدارة الأصناف والمنتجات</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button onclick="exportProductsToExcel()" class="bg-emerald-600 text-white px-3 py-2 rounded-md hover:bg-emerald-700 flex items-center text-sm">
                                <span class="ml-1">📊</span>
                                تصدير Excel
                            </button>
                            <button onclick="printProductsList()" class="bg-indigo-600 text-white px-3 py-2 rounded-md hover:bg-indigo-700 flex items-center text-sm">
                                <span class="ml-1">🖨️</span>
                                طباعة
                            </button>
                            <button onclick="openDamageEntryModal()" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 flex items-center">
                                <span class="ml-2">🗑️</span>
                                قيد التالف
                            </button>
                            <button onclick="openAddModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center">
                                <span class="ml-2">➕</span>
                                إضافة صنف جديد
                            </button>
                            <button onclick="openOpeningBalanceModal()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center">
                                <span class="ml-2">💰</span>
                                الأرصدة الافتتاحية
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2 flex items-center">
                        <span class="ml-3">📦</span>
                        إدارة الأصناف والخدمات
                    </h1>
                    <p class="text-gray-600">
                        إدارة شاملة للخامات والمنتجات والخدمات مع الأرصدة الافتتاحية والتكاليف
                    </p>
                </div>

                <!-- Success/Error Messages -->
                <div id="messageContainer" class="mb-6"></div>

                <!-- Main Tabs -->
                <div class="bg-white rounded-xl shadow-lg mb-6">
                    <div class="border-b border-gray-200">
                        <nav class="flex space-x-8 px-6">
                            <button onclick="switchMainTab('items')" id="itemsMainTab" class="main-tab-button py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
                                📦 إدارة الأصناف
                            </button>
                            <button onclick="switchMainTab('production')" id="productionMainTab" class="main-tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                                🏭 الإنتاج والتصنيع
                            </button>
                            <button onclick="switchMainTab('recipes')" id="recipesMainTab" class="main-tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                                📋 الوصفات
                            </button>
                            <button onclick="switchMainTab('quality')" id="qualityMainTab" class="main-tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                                ✅ مراقبة الجودة
                            </button>
                        </nav>
                    </div>
                </div>

                <!-- Items Management Tab Content -->
                <div id="itemsTabContent" class="main-tab-content active">
                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100 text-sm">إجمالي الأصناف</p>
                                <p class="text-3xl font-bold" id="totalItems">0</p>
                            </div>
                            <div class="text-3xl opacity-80">📦</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-100 text-sm">الخامات</p>
                                <p class="text-3xl font-bold" id="rawMaterials">0</p>
                            </div>
                            <div class="text-3xl opacity-80">🥄</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl shadow-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-100 text-sm">المنتجات</p>
                                <p class="text-3xl font-bold" id="finishedProducts">0</p>
                            </div>
                            <div class="text-3xl opacity-80">🍞</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl shadow-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-orange-100 text-sm">الخدمات</p>
                                <p class="text-3xl font-bold" id="servicesCount">0</p>
                            </div>
                            <div class="text-3xl opacity-80">🛠️</div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <input type="text" id="searchInput" placeholder="البحث بالاسم أو الكود..."
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   onkeyup="filterItems()">
                        </div>
                        <div>
                            <select id="categoryFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterItems()">
                                <option value="">جميع الفئات</option>
                                <option value="raw_material">خامات</option>
                                <option value="finished_product">منتجات نهائية</option>
                                <option value="semi_finished">نصف مصنعة</option>
                                <option value="service">خدمات</option>
                            </select>
                        </div>
                        <div>
                            <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterItems()">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="low_stock">نفاد مخزون</option>
                            </select>
                        </div>
                        <div>
                            <button onclick="exportToExcel()" class="w-full bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 flex items-center justify-center">
                                <span class="ml-2">📊</span>
                                تصدير Excel
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Items Table -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكود</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم الصنف</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">النوع</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المخازن والأرصدة</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوحدة الصغرى</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوحدة الكبرى</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">سعر التكلفة</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">سعر البيع</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية الحالية</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الرصيد الافتتاحي</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="itemsTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- Items will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
                </div>

                <!-- Production Management Tab Content -->
                <div id="productionTabContent" class="main-tab-content">
                    <!-- Production Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-xl shadow-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-indigo-100 text-sm">عمليات الإنتاج اليوم</p>
                                    <p class="text-3xl font-bold" id="todayProductions">0</p>
                                </div>
                                <div class="text-3xl opacity-80">🏭</div>
                            </div>
                        </div>
                        <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-xl shadow-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-emerald-100 text-sm">الإنتاج المكتمل</p>
                                    <p class="text-3xl font-bold" id="completedProductions">0</p>
                                </div>
                                <div class="text-3xl opacity-80">✅</div>
                            </div>
                        </div>
                        <div class="bg-gradient-to-r from-amber-500 to-amber-600 rounded-xl shadow-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-amber-100 text-sm">قيد الإنتاج</p>
                                    <p class="text-3xl font-bold" id="inProgressProductions">0</p>
                                </div>
                                <div class="text-3xl opacity-80">⏳</div>
                            </div>
                        </div>
                        <div class="bg-gradient-to-r from-rose-500 to-rose-600 rounded-xl shadow-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-rose-100 text-sm">إجمالي التكلفة اليوم</p>
                                    <p class="text-3xl font-bold" id="todayProductionCost">0.00</p>
                                </div>
                                <div class="text-3xl opacity-80">💰</div>
                            </div>
                        </div>
                    </div>

                    <!-- Production Controls -->
                    <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <span class="ml-2">🏭</span>
                                عمليات الإنتاج والتصنيع
                            </h3>
                            <div class="flex space-x-2">
                                <button onclick="openNewProductionModal()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center">
                                    <span class="ml-2">➕</span>
                                    بدء عملية إنتاج جديدة
                                </button>
                                <button onclick="openBatchProductionModal()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center">
                                    <span class="ml-2">📦</span>
                                    إنتاج بالدفعات
                                </button>
                                <button onclick="openProductionReportModal()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center">
                                    <span class="ml-2">📊</span>
                                    تقرير الإنتاج
                                </button>
                            </div>
                        </div>

                        <!-- Production Filters -->
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
                            <div>
                                <input type="text" id="productionSearchInput" placeholder="البحث في عمليات الإنتاج..."
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                       onkeyup="filterProductions()">
                            </div>
                            <div>
                                <select id="productionStatusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterProductions()">
                                    <option value="">جميع الحالات</option>
                                    <option value="planned">مخطط</option>
                                    <option value="in_progress">قيد التنفيذ</option>
                                    <option value="completed">مكتمل</option>
                                    <option value="paused">متوقف</option>
                                    <option value="cancelled">ملغي</option>
                                </select>
                            </div>
                            <div>
                                <select id="productionBranchFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterProductions()">
                                    <option value="">جميع الفروع</option>
                                </select>
                            </div>
                            <div>
                                <input type="date" id="productionDateFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterProductions()">
                            </div>
                            <div>
                                <select id="productionLineFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterProductions()">
                                    <option value="">جميع خطوط الإنتاج</option>
                                    <option value="line1">خط الإنتاج 1 - الخبز</option>
                                    <option value="line2">خط الإنتاج 2 - المعجنات</option>
                                    <option value="line3">خط الإنتاج 3 - الحلويات</option>
                                    <option value="line4">خط الإنتاج 4 - الكيك</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Active Productions -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-6">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <span class="ml-2">⚡</span>
                                عمليات الإنتاج النشطة
                            </h3>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="w-full production-table">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم العملية</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المنتج</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية المطلوبة</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوحدة</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">خط الإنتاج</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">وقت البدء</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوقت المتوقع</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">التقدم</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">التكلفة المقدرة</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                                        <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="activeProductionsTableBody" class="bg-white divide-y divide-gray-200">
                                    <!-- Active productions will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Production History -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <span class="ml-2">📋</span>
                                سجل عمليات الإنتاج
                            </h3>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="w-full production-table">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم العملية</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المنتج</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية المنتجة</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوحدة</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ الإنتاج</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">وقت الإنتاج الفعلي</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">التكلفة الفعلية</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">معدل الجودة</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                                        <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="productionHistoryTableBody" class="bg-white divide-y divide-gray-200">
                                    <!-- Production history will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Recipes Management Tab Content -->
                <div id="recipesTabContent" class="main-tab-content">
                    <!-- Recipes Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div class="bg-gradient-to-r from-teal-500 to-teal-600 rounded-xl shadow-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-teal-100 text-sm">إجمالي الوصفات</p>
                                    <p class="text-3xl font-bold" id="totalRecipes">0</p>
                                </div>
                                <div class="text-3xl opacity-80">📋</div>
                            </div>
                        </div>
                        <div class="bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-xl shadow-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-cyan-100 text-sm">وصفات نشطة</p>
                                    <p class="text-3xl font-bold" id="activeRecipes">0</p>
                                </div>
                                <div class="text-3xl opacity-80">✅</div>
                            </div>
                        </div>
                        <div class="bg-gradient-to-r from-lime-500 to-lime-600 rounded-xl shadow-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-lime-100 text-sm">منتجات لها وصفات</p>
                                    <p class="text-3xl font-bold" id="productsWithRecipes">0</p>
                                </div>
                                <div class="text-3xl opacity-80">🍞</div>
                            </div>
                        </div>
                        <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-xl shadow-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-yellow-100 text-sm">متوسط تكلفة الوصفة</p>
                                    <p class="text-3xl font-bold" id="averageRecipeCost">0.00</p>
                                </div>
                                <div class="text-3xl opacity-80">💰</div>
                            </div>
                        </div>
                    </div>

                    <!-- Recipe Controls -->
                    <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <span class="ml-2">📋</span>
                                إدارة الوصفات
                            </h3>
                            <div class="flex space-x-2">
                                <button onclick="openNewRecipeModal()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center">
                                    <span class="ml-2">➕</span>
                                    إضافة وصفة جديدة
                                </button>
                                <button onclick="importRecipesFromExcel()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center">
                                    <span class="ml-2">📊</span>
                                    استيراد من Excel
                                </button>
                                <button onclick="exportRecipesToExcel()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center">
                                    <span class="ml-2">📤</span>
                                    تصدير الوصفات
                                </button>
                            </div>
                        </div>

                        <!-- Recipe Filters -->
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
                            <div>
                                <input type="text" id="recipeSearchInput" placeholder="البحث في الوصفات..."
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                       onkeyup="filterRecipes()">
                            </div>
                            <div>
                                <select id="recipeProductFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterRecipes()">
                                    <option value="">جميع المنتجات</option>
                                </select>
                            </div>
                            <div>
                                <select id="recipeCategoryFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterRecipes()">
                                    <option value="">جميع الفئات</option>
                                    <option value="bread">خبز</option>
                                    <option value="pastry">معجنات</option>
                                    <option value="cake">كيك وحلويات</option>
                                    <option value="cookies">بسكويت</option>
                                </select>
                            </div>
                            <div>
                                <select id="recipeStatusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterRecipes()">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشطة</option>
                                    <option value="inactive">غير نشطة</option>
                                    <option value="draft">مسودة</option>
                                </select>
                            </div>
                            <div>
                                <select id="recipeDifficultyFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base" onchange="filterRecipes()">
                                    <option value="">جميع المستويات</option>
                                    <option value="easy">سهل</option>
                                    <option value="medium">متوسط</option>
                                    <option value="hard">صعب</option>
                                    <option value="expert">خبير</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Recipes Table -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="w-full production-table">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">كود الوصفة</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم الوصفة</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المنتج النهائي</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية المنتجة</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">عدد الخامات</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">وقت التحضير</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">وقت الطبخ</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">مدة الصلاحية</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">تكلفة الوصفة</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المستوى</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                                        <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="recipesTableBody" class="bg-white divide-y divide-gray-200">
                                    <!-- Recipes will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Quality Control Tab Content -->
                <div id="qualityTabContent" class="main-tab-content">
                    <!-- Quality Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-xl shadow-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-emerald-100 text-sm">فحوصات اليوم</p>
                                    <p class="text-3xl font-bold" id="todayInspections">0</p>
                                </div>
                                <div class="text-3xl opacity-80">🔍</div>
                            </div>
                        </div>
                        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-green-100 text-sm">معدل الجودة</p>
                                    <p class="text-3xl font-bold" id="qualityRate">95%</p>
                                </div>
                                <div class="text-3xl opacity-80">✅</div>
                            </div>
                        </div>
                        <div class="bg-gradient-to-r from-red-500 to-red-600 rounded-xl shadow-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-red-100 text-sm">منتجات مرفوضة</p>
                                    <p class="text-3xl font-bold" id="rejectedProducts">2</p>
                                </div>
                                <div class="text-3xl opacity-80">❌</div>
                            </div>
                        </div>
                        <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl shadow-lg p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-orange-100 text-sm">منتجات قاربت الانتهاء</p>
                                    <p class="text-3xl font-bold" id="nearExpiryProducts">5</p>
                                </div>
                                <div class="text-3xl opacity-80">⚠️</div>
                            </div>
                        </div>
                    </div>

                    <!-- Quality Control Tools -->
                    <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <span class="ml-2">✅</span>
                                مراقبة الجودة وتواريخ الانتهاء
                            </h3>
                            <div class="flex space-x-2">
                                <button onclick="openQualityInspectionModal()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center">
                                    <span class="ml-2">🔍</span>
                                    فحص جودة جديد
                                </button>
                                <button onclick="checkExpiryDates()" class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 flex items-center">
                                    <span class="ml-2">⏰</span>
                                    فحص تواريخ الانتهاء
                                </button>
                                <button onclick="generateQualityReport()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center">
                                    <span class="ml-2">📊</span>
                                    تقرير الجودة
                                </button>
                            </div>
                        </div>

                        <!-- Expiry Date Tracking -->
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                            <h4 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
                                <span class="ml-2">📅</span>
                                تتبع تواريخ الانتهاء
                            </h4>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="bg-red-50 border border-red-200 rounded-lg p-3">
                                    <h5 class="font-medium text-red-800 mb-2">منتهية الصلاحية</h5>
                                    <div id="expiredProductsList" class="text-sm text-red-700">
                                        <!-- Expired products will be listed here -->
                                    </div>
                                </div>
                                <div class="bg-orange-50 border border-orange-200 rounded-lg p-3">
                                    <h5 class="font-medium text-orange-800 mb-2">تنتهي خلال 3 أيام</h5>
                                    <div id="soonExpiryProductsList" class="text-sm text-orange-700">
                                        <!-- Soon to expire products will be listed here -->
                                    </div>
                                </div>
                                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                                    <h5 class="font-medium text-yellow-800 mb-2">تنتهي خلال أسبوع</h5>
                                    <div id="weekExpiryProductsList" class="text-sm text-yellow-700">
                                        <!-- Week expiry products will be listed here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products with Expiry Dates Table -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                <span class="ml-2">📦</span>
                                المنتجات وتواريخ الانتهاء
                            </h3>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="w-full production-table">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">كود المنتج</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم المنتج</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ الإنتاج</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">مدة الصلاحية</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ الانتهاء</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الأيام المتبقية</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المخزن</th>
                                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">حالة الصلاحية</th>
                                        <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="expiryTrackingTableBody" class="bg-white divide-y divide-gray-200">
                                    <!-- Products with expiry dates will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add/Edit Item Modal -->
    <div id="itemModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 id="modalTitle" class="text-lg font-medium text-gray-900">إضافة صنف جديد</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                    <span class="text-2xl">×</span>
                </button>
            </div>

            <form id="itemForm" class="px-4 py-3">
                <input type="hidden" id="itemId" value="">

                <!-- Tabs -->
                <div class="mb-4">
                    <div class="border-b border-gray-200">
                        <nav class="-mb-px flex space-x-6">
                            <button type="button" onclick="switchTab('basic', event)" class="modal-tab-button py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
                                المعلومات الأساسية
                            </button>
                            <button type="button" onclick="switchTab('pricing', event)" class="modal-tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                                الأسعار والتكاليف
                            </button>
                            <button type="button" onclick="switchTab('inventory', event)" class="modal-tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                                المخزون والوحدات
                            </button>
                            <button type="button" onclick="switchTab('opening', event)" class="modal-tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700">
                                الرصيد الافتتاحي
                            </button>
                            <button type="button" onclick="switchTab('service', event)" class="modal-tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" id="serviceTab" style="display:none;">
                                إعدادات الخدمة
                            </button>
                        </nav>
                    </div>
                </div>

                <!-- Basic Information Tab -->
                <div id="basicTab" class="tab-content active space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">كود الصنف *</label>
                            <input type="text" id="itemCode" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="مثال: RAW001">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الباركود</label>
                            <input type="text" id="barcode"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="اختياري">
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم الصنف *</label>
                            <input type="text" id="itemName" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="مثال: دقيق أبيض">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">نوع الصنف *</label>
                            <select id="itemType" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                    onchange="toggleServiceFields()">
                                <option value="">اختر النوع</option>
                                <option value="raw_material">خامة</option>
                                <option value="finished_product">منتج نهائي</option>
                                <option value="semi_finished">نصف مصنع</option>
                                <option value="service">خدمة</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                            <select id="category"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                                <option value="">اختر الفئة</option>
                                <!-- Product categories -->
                                <option value="flour" class="product-category">دقيق ومواد أساسية</option>
                                <option value="dairy" class="product-category">منتجات الألبان</option>
                                <option value="sugar" class="product-category">سكر ومحليات</option>
                                <option value="bread" class="product-category">خبز</option>
                                <option value="pastry" class="product-category">معجنات</option>
                                <option value="cake" class="product-category">كيك وحلويات</option>
                                <!-- Service categories -->
                                <option value="manufacturing" class="service-category" style="display:none;">تصنيع حسب الطلب</option>
                                <option value="decoration" class="service-category" style="display:none;">تزيين وتجهيز</option>
                                <option value="delivery" class="service-category" style="display:none;">توصيل</option>
                                <option value="packaging" class="service-category" style="display:none;">تغليف</option>
                                <option value="consultation" class="service-category" style="display:none;">استشارات</option>
                                <option value="events" class="service-category" style="display:none;">تنظيم مناسبات</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">المخزن الرئيسي</label>
                            <select id="warehouseId"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                                <option value="">اختر المخزن</option>
                                <!-- Warehouses will be populated from warehouses system -->
                            </select>
                            <p class="text-xs text-gray-500 mt-1">
                                <a href="warehouses.html" target="_blank" class="text-blue-600 hover:text-blue-800">إدارة المخازن</a>
                            </p>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                            <textarea id="description" rows="2"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                      placeholder="وصف تفصيلي للصنف..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- Pricing Tab -->
                <div id="pricingTab" class="tab-content space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">سعر التكلفة *</label>
                            <input type="number" id="costPrice" step="0.01" min="0" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="0.00" onchange="calculateProfitMargin()">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">سعر البيع *</label>
                            <input type="number" id="sellingPrice" step="0.01" min="0" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="0.00" onchange="calculateProfitMargin()">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">هامش الربح (%)</label>
                            <input type="number" id="profitMargin" step="0.01" min="0" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-base"
                                   placeholder="يحسب تلقائياً">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">العملة</label>
                            <select id="currency"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                                <option value="SAR">ريال سعودي</option>
                                <option value="YER">ريال يمني</option>
                                <option value="USD">دولار أمريكي</option>
                                <option value="EUR">يورو</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Inventory Tab -->
                <div id="inventoryTab" class="tab-content space-y-4">
                    <!-- Units Section -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                        <h4 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
                            <span class="ml-2">📏</span>
                            وحدات القياس
                        </h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الوحدة الصغرى (الأساسية) *</label>
                                <select id="smallUnit" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                                    <option value="">اختر الوحدة الصغرى</option>
                                    <!-- Units will be populated from units system -->
                                </select>
                                <p class="text-xs text-gray-500 mt-1">الوحدة الأساسية للحسابات والمخزون</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الوحدة الكبرى</label>
                                <select id="largeUnit"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                                    <option value="">اختر الوحدة الكبرى</option>
                                    <!-- Units will be populated from units system -->
                                </select>
                                <p class="text-xs text-gray-500 mt-1">وحدة للبيع بالجملة (اختيارية)</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">عدد الوحدات الصغرى في الكبرى</label>
                                <input type="number" id="conversionFactor" min="1" step="0.01"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                       placeholder="مثال: 12 (12 قطعة في الكرتون)">
                                <p class="text-xs text-gray-500 mt-1">كم وحدة صغرى تساوي وحدة كبرى واحدة</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">سعر الوحدة الصغرى</label>
                                <input type="number" id="smallUnitPrice" step="0.01" min="0"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                       placeholder="0.00" onchange="calculateLargeUnitPrice()">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">سعر الوحدة الكبرى</label>
                                <input type="number" id="largeUnitPrice" step="0.01" min="0"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                       placeholder="0.00" onchange="calculateSmallUnitPrice()">
                                <p class="text-xs text-gray-500 mt-1">يحسب تلقائياً أو يمكن تعديله</p>
                            </div>
                        </div>

                        <p class="text-xs text-gray-500 mt-2">
                            <a href="units.html" target="_blank" class="text-blue-600 hover:text-blue-800">إدارة وحدات القياس</a>
                        </p>
                    </div>

                    <!-- Warehouse Distribution -->
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                        <h4 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
                            <span class="ml-2">🏪</span>
                            توزيع المخازن
                        </h4>

                        <div class="mb-3">
                            <p class="text-sm text-gray-600 mb-2">يمكن أن يكون الصنف في عدة مخازن مختلفة:</p>
                            <button type="button" onclick="addWarehouseRow()" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">
                                + إضافة مخزن
                            </button>
                        </div>

                        <div id="warehousesContainer">
                            <!-- Warehouse rows will be added here -->
                        </div>
                    </div>

                    <!-- Stock Settings -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الحد الأدنى للمخزون (إجمالي)</label>
                            <input type="number" id="minStock" step="0.01" min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="0">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الحد الأقصى للمخزون (إجمالي)</label>
                            <input type="number" id="maxStock" step="0.01" min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="0">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">مدة الصلاحية (أيام)</label>
                            <input type="number" id="shelfLife" min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="0" onchange="calculateExpiryDate()">
                            <p class="text-xs text-gray-500 mt-1">عدد الأيام من تاريخ الإنتاج حتى انتهاء الصلاحية</p>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="isActive" checked
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="isActive" class="mr-2 block text-sm text-gray-900">الصنف نشط</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="trackExpiry" onchange="toggleExpiryFields()"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="trackExpiry" class="mr-2 block text-sm text-gray-900">تتبع تاريخ الانتهاء</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="allowNegativeStock"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="allowNegativeStock" class="mr-2 block text-sm text-gray-900">السماح بالمخزون السالب</label>
                        </div>
                    </div>

                    <!-- Expiry Date Tracking Section -->
                    <div id="expiryTrackingSection" class="bg-orange-50 border border-orange-200 rounded-lg p-4 mt-4" style="display:none;">
                        <h4 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
                            <span class="ml-2">📅</span>
                            إعدادات تتبع تواريخ الانتهاء
                        </h4>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ الإنتاج (للمنتجات الحالية)</label>
                                <input type="date" id="productionDate"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 text-base"
                                       onchange="calculateExpiryDate()">
                                <p class="text-xs text-gray-500 mt-1">تاريخ إنتاج الدفعة الحالية</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ الانتهاء المحسوب</label>
                                <input type="date" id="calculatedExpiryDate" readonly
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-base">
                                <p class="text-xs text-gray-500 mt-1">يحسب تلقائياً من تاريخ الإنتاج + مدة الصلاحية</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الأيام المتبقية</label>
                                <input type="text" id="daysRemaining" readonly
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-base">
                                <p class="text-xs text-gray-500 mt-1">عدد الأيام المتبقية حتى انتهاء الصلاحية</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">تحذير قبل انتهاء الصلاحية (أيام)</label>
                                <input type="number" id="expiryWarningDays" min="1" value="3"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 text-base"
                                       placeholder="3">
                                <p class="text-xs text-gray-500 mt-1">عدد الأيام قبل انتهاء الصلاحية لإظهار التحذير</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">إجراء عند انتهاء الصلاحية</label>
                                <select id="expiryAction"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 text-base">
                                    <option value="alert">تنبيه فقط</option>
                                    <option value="discount">تخفيض السعر تلقائياً</option>
                                    <option value="block">منع البيع</option>
                                    <option value="dispose">إتلاف تلقائي</option>
                                </select>
                            </div>
                        </div>

                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                            <div class="flex items-center mb-2">
                                <span class="text-yellow-600 ml-2">⚠️</span>
                                <h5 class="text-sm font-medium text-yellow-800">ملاحظات مهمة</h5>
                            </div>
                            <ul class="text-xs text-yellow-700 space-y-1">
                                <li>• سيتم حساب تاريخ الانتهاء تلقائياً للمنتجات الجديدة بناءً على تاريخ الإنتاج</li>
                                <li>• يمكن تتبع عدة دفعات بتواريخ إنتاج مختلفة لنفس المنتج</li>
                                <li>• سيظهر تحذير في لوحة التحكم عند اقتراب انتهاء الصلاحية</li>
                                <li>• يمكن تطبيق إجراءات تلقائية عند انتهاء الصلاحية</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Opening Balance Tab -->
                <div id="openingTab" class="tab-content space-y-4">
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                        <div class="flex items-center mb-2">
                            <span class="text-yellow-600 ml-2">⚠️</span>
                            <h4 class="text-lg font-medium text-yellow-800">الأرصدة الافتتاحية</h4>
                        </div>
                        <p class="text-sm text-yellow-700 mb-2">
                            الرصيد الافتتاحي يُدخل مرة واحدة فقط عند إنشاء الصنف أو في بداية السنة المالية
                        </p>
                        <p class="text-xs text-yellow-600">
                            يجب إدخال الرصيد الافتتاحي لكل مخزن منفصل إذا كان الصنف موزع على عدة مخازن
                        </p>
                    </div>

                    <!-- Opening Balance by Warehouse -->
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                        <h4 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
                            <span class="ml-2">💰</span>
                            الأرصدة الافتتاحية حسب المخزن
                        </h4>

                        <div class="mb-3">
                            <button type="button" onclick="addOpeningBalanceRow()" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">
                                + إضافة رصيد افتتاحي
                            </button>
                        </div>

                        <div id="openingBalancesContainer">
                            <!-- Opening balance rows will be added here -->
                        </div>
                    </div>

                    <!-- Summary -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
                            <span class="ml-2">📊</span>
                            ملخص الأرصدة الافتتاحية
                        </h4>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">إجمالي الكمية</label>
                                <input type="number" id="totalOpeningQuantity" readonly
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-base"
                                       placeholder="0">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">إجمالي القيمة</label>
                                <input type="number" id="totalOpeningValue" readonly
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-base"
                                       placeholder="0.00">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">متوسط سعر التكلفة</label>
                                <input type="number" id="averageOpeningCost" readonly
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-base"
                                       placeholder="0.00">
                            </div>
                        </div>

                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ الرصيد الافتتاحي</label>
                            <input type="date" id="openingDate"
                                   class="w-full md:w-1/3 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                            <p class="text-xs text-gray-500 mt-1">تاريخ بداية السنة المالية أو تاريخ إنشاء الصنف</p>
                        </div>
                    </div>

                    <!-- Smart Accounting Integration -->
                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                        <h4 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
                            <span class="ml-2">🧮</span>
                            الربط المحاسبي الذكي
                        </h4>

                        <!-- Auto Account Selection Info -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                            <div class="flex items-center mb-2">
                                <span class="text-blue-600 ml-2">🤖</span>
                                <h5 class="text-sm font-medium text-blue-800">اختيار الحسابات التلقائي</h5>
                            </div>
                            <p class="text-xs text-blue-700 mb-2">
                                سيتم اختيار الحسابات المحاسبية تلقائياً بناءً على:
                            </p>
                            <ul class="text-xs text-blue-600 list-disc list-inside space-y-1">
                                <li>نوع الصنف (خامة، منتج نهائي، خدمة)</li>
                                <li>الفرع المحدد</li>
                                <li>المخزن المحدد</li>
                                <li>إعدادات الربط المحاسبي المسبقة</li>
                            </ul>
                        </div>

                        <!-- Smart Account Mapping -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">حساب المخزون المقترح</label>
                                <div class="relative">
                                    <select id="inventoryAccount"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-base">
                                        <option value="">جاري تحديد الحساب...</option>
                                    </select>
                                    <div class="absolute left-2 top-2">
                                        <span id="inventoryAccountStatus" class="text-lg">⏳</span>
                                    </div>
                                </div>
                                <div id="inventoryAccountInfo" class="text-xs text-gray-500 mt-1"></div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">حساب تكلفة البضاعة المقترح</label>
                                <div class="relative">
                                    <select id="cogsAccount"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-base">
                                        <option value="">جاري تحديد الحساب...</option>
                                    </select>
                                    <div class="absolute left-2 top-2">
                                        <span id="cogsAccountStatus" class="text-lg">⏳</span>
                                    </div>
                                </div>
                                <div id="cogsAccountInfo" class="text-xs text-gray-500 mt-1"></div>
                            </div>
                        </div>

                        <!-- Additional Smart Accounts -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">حساب المشتريات</label>
                                <div class="relative">
                                    <select id="purchaseAccount"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-base">
                                        <option value="">جاري تحديد الحساب...</option>
                                    </select>
                                    <div class="absolute left-2 top-2">
                                        <span id="purchaseAccountStatus" class="text-lg">⏳</span>
                                    </div>
                                </div>
                                <div id="purchaseAccountInfo" class="text-xs text-gray-500 mt-1"></div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">حساب المبيعات</label>
                                <div class="relative">
                                    <select id="salesAccount"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-base">
                                        <option value="">جاري تحديد الحساب...</option>
                                    </select>
                                    <div class="absolute left-2 top-2">
                                        <span id="salesAccountStatus" class="text-lg">⏳</span>
                                    </div>
                                </div>
                                <div id="salesAccountInfo" class="text-xs text-gray-500 mt-1"></div>
                            </div>
                        </div>

                        <!-- Account Mapping Rules -->
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                            <h5 class="text-sm font-medium text-yellow-800 mb-2 flex items-center">
                                <span class="ml-1">⚙️</span>
                                قواعد الربط المحاسبي
                            </h5>
                            <div id="accountMappingRules" class="text-xs text-yellow-700 space-y-1">
                                <!-- Rules will be populated dynamically -->
                            </div>
                        </div>

                        <!-- Manual Override -->
                        <div class="border-t border-purple-200 pt-4">
                            <div class="flex items-center justify-between mb-3">
                                <h5 class="text-sm font-medium text-gray-700">إعدادات متقدمة</h5>
                                <button type="button" onclick="toggleManualAccountSelection()"
                                        class="text-xs text-purple-600 hover:text-purple-800">
                                    تخصيص الحسابات يدوياً
                                </button>
                            </div>

                            <div id="manualAccountSelection" class="hidden">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">حساب مخصص للمخزون</label>
                                        <select id="customInventoryAccount"
                                                class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
                                            <option value="">اختر حساب مخصص</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">حساب مخصص للتكلفة</label>
                                        <select id="customCogsAccount"
                                                class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
                                            <option value="">اختر حساب مخصص</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Auto Journal Entry Options -->
                        <div class="border-t border-purple-200 pt-4">
                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <input type="checkbox" id="autoCreateJournalEntry" checked
                                           class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                    <label for="autoCreateJournalEntry" class="mr-2 block text-sm text-gray-900">
                                        إنشاء قيد محاسبي تلقائياً للرصيد الافتتاحي
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="autoUpdateAccounts" checked
                                           class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                    <label for="autoUpdateAccounts" class="mr-2 block text-sm text-gray-900">
                                        تحديث الحسابات تلقائياً عند تغيير الفرع أو المخزن
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="validateAccountMapping"
                                           class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                    <label for="validateAccountMapping" class="mr-2 block text-sm text-gray-900">
                                        التحقق من صحة الربط المحاسبي قبل الحفظ
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Service Settings Tab -->
                <div id="serviceTab" class="tab-content space-y-4" style="display:none;">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                        <div class="flex items-center">
                            <span class="text-blue-600 ml-2">🛠️</span>
                            <p class="text-sm text-blue-700">
                                إعدادات خاصة بالخدمات - لا تحتاج لمخزون أو وحدات قياس
                            </p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">مدة تنفيذ الخدمة</label>
                            <div class="flex space-x-2">
                                <input type="number" id="serviceDuration" min="0" step="0.5"
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                       placeholder="0">
                                <select id="serviceDurationUnit"
                                        class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                                    <option value="hours">ساعة</option>
                                    <option value="days">يوم</option>
                                    <option value="weeks">أسبوع</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">نوع التسعير</label>
                            <select id="servicePricingType"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                    onchange="togglePricingFields()">
                                <option value="fixed">سعر ثابت</option>
                                <option value="hourly">بالساعة</option>
                                <option value="daily">باليوم</option>
                                <option value="quantity">حسب الكمية</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تكلفة العمالة</label>
                            <input type="number" id="serviceLaborCost" step="0.01" min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="0.00">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الحد الأدنى للطلب</label>
                            <input type="number" id="serviceMinOrder" step="0.01" min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="0">
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" id="serviceRequiresBooking"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="serviceRequiresBooking" class="mr-2 block text-sm text-gray-900">يتطلب حجز مسبق</label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" id="serviceAvailableOnline"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="serviceAvailableOnline" class="mr-2 block text-sm text-gray-900">متاح للطلب أونلاين</label>
                        </div>

                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">متطلبات خاصة</label>
                            <textarea id="serviceRequirements" rows="2"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                      placeholder="أي متطلبات خاصة لتنفيذ الخدمة..."></textarea>
                        </div>

                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">المواد المطلوبة</label>
                            <textarea id="serviceRequiredMaterials" rows="2"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                      placeholder="قائمة بالمواد أو الأصناف المطلوبة لتنفيذ الخدمة..."></textarea>
                        </div>
                    </div>
                </div>
            </form>

            <div class="px-4 py-3 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="closeModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                    إلغاء
                </button>
                <button onclick="saveItem()" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700">
                    حفظ الصنف
                </button>
            </div>
        </div>
    </div>

    <!-- Opening Balance Modal -->
    <div id="openingBalanceModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-5xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">إدارة الأرصدة الافتتاحية</h3>
                <button onclick="closeOpeningBalanceModal()" class="text-gray-400 hover:text-gray-600">
                    <span class="text-2xl">×</span>
                </button>
            </div>

            <div class="px-4 py-3">
                <div class="mb-4">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <div class="flex items-center">
                            <span class="text-blue-600 ml-2">ℹ️</span>
                            <p class="text-sm text-blue-700">
                                يمكنك هنا إدخال أو تعديل الأرصدة الافتتاحية لجميع الأصناف دفعة واحدة
                            </p>
                        </div>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">كود الصنف</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم الصنف</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوحدة</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية الافتتاحية</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">سعر التكلفة</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">القيمة الإجمالية</th>
                                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">تاريخ الرصيد</th>
                            </tr>
                        </thead>
                        <tbody id="openingBalanceTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- Opening balances will be populated here -->
                        </tbody>
                    </table>
                </div>

                <div class="mt-4 flex justify-between items-center">
                    <div class="text-base font-semibold text-gray-900">
                        إجمالي قيمة الأرصدة الافتتاحية: <span id="totalOpeningValue" class="text-blue-600">0.00</span> ر.س
                    </div>
                    <button onclick="saveAllOpeningBalances()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 text-sm">
                        حفظ جميع الأرصدة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Damage Entry Modal -->
    <div id="damageModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: none;">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900 flex items-center">
                        <span class="text-2xl ml-2">🗑️</span>
                        قيد إتلاف الأصناف
                    </h3>
                    <button onclick="closeDamageModal()" class="text-gray-400 hover:text-gray-600">
                        <span class="text-xl">×</span>
                    </button>
                </div>

                <!-- Item Information -->
                <div id="damageItemInfo" class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <!-- Item details will be populated here -->
                </div>

                <!-- Damage Entry Form -->
                <form id="damageForm" onsubmit="processDamageEntry(event)">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <!-- Warehouse Selection -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">المخزن *</label>
                            <select id="damageWarehouse" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                                    onchange="updateAvailableStock()">
                                <option value="">اختر المخزن</option>
                            </select>
                            <div id="warehouseStockInfo" class="text-xs text-gray-500 mt-1"></div>
                        </div>

                        <!-- Unit Selection -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الوحدة *</label>
                            <select id="damageUnit" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                                    onchange="updateUnitPrice()">
                                <option value="">اختر الوحدة</option>
                            </select>
                            <div id="unitPriceInfo" class="text-xs text-gray-500 mt-1"></div>
                        </div>

                        <!-- Damage Quantity -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">كمية الإتلاف *</label>
                            <input type="number" id="damageQuantity" required min="0.01" step="0.01"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                                   placeholder="0.00" onchange="calculateDamageValue()">
                            <div id="quantityValidation" class="text-xs mt-1"></div>
                        </div>

                        <!-- Unit Cost -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تكلفة الوحدة</label>
                            <input type="number" id="damageUnitCost" step="0.01" min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                                   placeholder="0.00" onchange="calculateDamageValue()">
                            <div class="text-xs text-gray-500 mt-1">سيتم استخدام متوسط التكلفة إذا ترك فارغاً</div>
                        </div>

                        <!-- Total Damage Value -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">إجمالي قيمة الإتلاف</label>
                            <input type="number" id="totalDamageValue" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 font-medium"
                                   placeholder="0.00">
                        </div>

                        <!-- Damage Date -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ الإتلاف *</label>
                            <input type="date" id="damageDate" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500">
                        </div>
                    </div>

                    <!-- Damage Reason -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">سبب الإتلاف *</label>
                        <select id="damageReason" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 mb-2">
                            <option value="">اختر سبب الإتلاف</option>
                            <option value="expired">انتهاء الصلاحية</option>
                            <option value="damaged">تلف أثناء التخزين</option>
                            <option value="contaminated">تلوث</option>
                            <option value="quality_issue">مشكلة في الجودة</option>
                            <option value="accident">حادث</option>
                            <option value="theft">سرقة</option>
                            <option value="other">أخرى</option>
                        </select>

                        <textarea id="damageNotes" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                                  placeholder="تفاصيل إضافية حول سبب الإتلاف..."></textarea>
                    </div>

                    <!-- Accounting Integration -->
                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-4">
                        <h4 class="text-md font-medium text-gray-900 mb-3 flex items-center">
                            <span class="ml-2">🧮</span>
                            الربط المحاسبي للإتلاف
                        </h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">حساب الإتلاف</label>
                                <select id="damageExpenseAccount"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500">
                                    <option value="">سيتم تحديده تلقائياً</option>
                                </select>
                                <div id="damageAccountInfo" class="text-xs text-gray-500 mt-1"></div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">حساب المخزون</label>
                                <select id="damageInventoryAccount"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500">
                                    <option value="">سيتم تحديده تلقائياً</option>
                                </select>
                                <div id="inventoryAccountInfo" class="text-xs text-gray-500 mt-1"></div>
                            </div>
                        </div>

                        <div class="mt-3 flex items-center">
                            <input type="checkbox" id="autoCreateDamageEntry" checked
                                   class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                            <label for="autoCreateDamageEntry" class="mr-2 block text-sm text-gray-900">
                                إنشاء قيد محاسبي تلقائياً للإتلاف
                            </label>
                        </div>
                    </div>

                    <!-- Approval Section -->
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                        <h4 class="text-md font-medium text-gray-900 mb-3 flex items-center">
                            <span class="ml-2">✅</span>
                            اعتماد الإتلاف
                        </h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">المعتمد من</label>
                                <input type="text" id="approvedBy"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
                                       placeholder="اسم المعتمد">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">رقم الإذن</label>
                                <input type="text" id="approvalNumber"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
                                       placeholder="رقم إذن الإتلاف">
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeDamageModal()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                            إلغاء
                        </button>
                        <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                            تسجيل الإتلاف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="products.js"></script>
    <script>
        // Additional functions that need to be in the main file

        // Calculate profit margin
        function calculateProfitMargin() {
            const costPrice = parseFloat(document.getElementById('costPrice').value) || 0;
            const sellingPrice = parseFloat(document.getElementById('sellingPrice').value) || 0;

            if (costPrice > 0) {
                const margin = ((sellingPrice - costPrice) / costPrice) * 100;
                document.getElementById('profitMargin').value = margin.toFixed(2);
            }
        }

        // Calculate opening value
        function calculateOpeningValue() {
            const quantity = parseFloat(document.getElementById('openingQuantity').value) || 0;
            const costPrice = parseFloat(document.getElementById('costPrice').value) || 0;

            const value = quantity * costPrice;
            document.getElementById('openingValue').value = value.toFixed(2);
            document.getElementById('avgCostPrice').value = costPrice.toFixed(2);
        }

        // Show message
        function showMessage(message, type) {
            const container = document.getElementById('messageContainer');
            const alertClass = {
                'success': 'bg-green-50 border-green-200 text-green-700',
                'error': 'bg-red-50 border-red-200 text-red-700',
                'info': 'bg-blue-50 border-blue-200 text-blue-700'
            };

            container.innerHTML = `
                <div class="border rounded-lg p-4 ${alertClass[type]}">
                    <div class="flex items-center">
                        <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                        ${message}
                    </div>
                </div>
            `;

            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }

        // Toggle service fields based on item type
        function toggleServiceFields() {
            const itemType = document.getElementById('itemType').value;
            const serviceTab = document.getElementById('serviceTab');
            const serviceTabContent = document.getElementById('serviceTab');
            const inventoryTab = document.querySelector('button[onclick="switchTab(\'inventory\')"]');
            const openingTab = document.querySelector('button[onclick="switchTab(\'opening\')"]');

            // Show/hide service categories
            const productCategories = document.querySelectorAll('.product-category');
            const serviceCategories = document.querySelectorAll('.service-category');

            if (itemType === 'service') {
                // Show service tab and hide inventory/opening tabs
                serviceTab.style.display = 'block';
                inventoryTab.style.display = 'none';
                openingTab.style.display = 'none';

                // Show service categories, hide product categories
                productCategories.forEach(cat => cat.style.display = 'none');
                serviceCategories.forEach(cat => cat.style.display = 'block');

                // Switch to service tab if currently on inventory/opening
                const activeTab = document.querySelector('.tab-content.active');
                if (activeTab && (activeTab.id === 'inventoryTab' || activeTab.id === 'openingTab')) {
                    switchTab('service');
                }
            } else {
                // Hide service tab and show inventory/opening tabs
                serviceTab.style.display = 'none';
                inventoryTab.style.display = 'block';
                openingTab.style.display = 'block';

                // Show product categories, hide service categories
                productCategories.forEach(cat => cat.style.display = 'block');
                serviceCategories.forEach(cat => cat.style.display = 'none');

                // Switch to basic tab if currently on service tab
                const activeTab = document.querySelector('.tab-content.active');
                if (activeTab && activeTab.id === 'serviceTab') {
                    switchTab('basic');
                }
            }

            // Clear category selection
            document.getElementById('category').value = '';
        }

        // Toggle pricing fields based on pricing type
        function togglePricingFields() {
            const pricingType = document.getElementById('servicePricingType').value;
            // Additional logic can be added here for different pricing types
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadUserInfo();
            loadCompanyInfo();
            loadItems();
            initializeSidebar();
        });

        // Initialize sidebar to stay fixed
        function initializeSidebar() {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                // Ensure sidebar stays fixed
                sidebar.style.position = 'fixed';
                sidebar.style.top = '0';
                sidebar.style.right = '0';
                sidebar.style.height = '100vh';
                sidebar.style.zIndex = '1000';
                sidebar.style.width = '256px';

                // Ensure main content has proper margin
                const mainContent = document.querySelector('.main-content');
                if (mainContent) {
                    mainContent.style.marginRight = '256px';
                }
            }
        }

        // Override any functions that might hide the sidebar
        function toggleSidebar() {
            // For mobile only - desktop sidebar should always be visible
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobileOverlay');

            if (window.innerWidth < 1024) { // Mobile breakpoint
                sidebar.classList.toggle('translate-x-full');
                overlay.classList.toggle('hidden');
            }
            // On desktop, do nothing - sidebar stays fixed
        }

        // Main Tab Switching Functions
        function switchMainTab(tabName) {
            // Hide all main tab contents
            const tabContents = document.querySelectorAll('.main-tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // Remove active class from all main tab buttons
            const tabButtons = document.querySelectorAll('.main-tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('border-blue-500', 'text-blue-600');
                button.classList.add('border-transparent', 'text-gray-500');
            });

            // Show selected tab content
            document.getElementById(tabName + 'TabContent').classList.add('active');

            // Activate selected tab button
            const activeButton = document.getElementById(tabName + 'MainTab');
            activeButton.classList.remove('border-transparent', 'text-gray-500');
            activeButton.classList.add('border-blue-500', 'text-blue-600');

            // Ensure sidebar stays fixed after tab switch
            setTimeout(() => {
                initializeSidebar();
            }, 100);
        }

        // Modal Tab Switching Functions (for item modal) - handled in products.js

        // Production Functions
        function openNewProductionModal() {
            document.getElementById('newProductionModal').classList.add('active');
            generateProductionNumber();
            loadProductsForProduction();
            setDefaultProductionDate();
        }

        function closeProductionModal() {
            document.getElementById('newProductionModal').classList.remove('active');
            document.getElementById('productionForm').reset();
        }

        function generateProductionNumber() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const time = String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0');

            const productionNumber = `PROD-${year}${month}${day}-${time}`;
            document.getElementById('productionNumber').value = productionNumber;
        }

        function loadProductsForProduction() {
            const productSelect = document.getElementById('productToManufacture');
            productSelect.innerHTML = '<option value="">اختر المنتج</option>';

            // Load finished products only - prioritize products with recipes
            const finishedProducts = items.filter(item => item.itemType === 'finished_product' && item.isActive);

            // Sort: products with recipes first
            finishedProducts.sort((a, b) => {
                if (a.hasRecipe && !b.hasRecipe) return -1;
                if (!a.hasRecipe && b.hasRecipe) return 1;
                return a.itemName.localeCompare(b.itemName);
            });

            finishedProducts.forEach(item => {
                const option = document.createElement('option');
                option.value = item.id;
                const statusIcon = item.hasRecipe ? '📋' : '❌';
                const statusText = item.hasRecipe ? '' : ' (بدون وصفة)';
                option.textContent = `${statusIcon} ${item.itemName} (${item.itemCode})${statusText}`;
                productSelect.appendChild(option);
            });
        }

        function setDefaultProductionDate() {
            const now = new Date();
            const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
            document.getElementById('productionStartDate').value = localDateTime;
        }

        function loadProductRecipe() {
            const productId = document.getElementById('productToManufacture').value;
            if (!productId) {
                document.getElementById('recipeContainer').innerHTML = `
                    <div class="text-center text-gray-500 py-8">
                        <span class="text-4xl">📝</span>
                        <p class="mt-2">اختر المنتج أولاً لعرض الوصفة والخامات المطلوبة</p>
                    </div>
                `;
                document.getElementById('rawMaterialsTable').style.display = 'none';
                return;
            }

            // Show recipe info
            document.getElementById('recipeContainer').innerHTML = `
                <div class="bg-white border border-gray-200 rounded-lg p-4">
                    <h5 class="font-medium text-gray-900 mb-2">وصفة الإنتاج</h5>
                    <p class="text-sm text-gray-600">سيتم تحميل الوصفة والخامات المطلوبة...</p>
                </div>
            `;

            document.getElementById('rawMaterialsTable').style.display = 'block';

            // Load raw materials (sample data)
            loadRawMaterialsForProduction(productId);
        }

        function loadRawMaterialsForProduction(productId) {
            // Sample recipe data - in real app this would come from recipes system
            const sampleRecipe = [
                { id: 1, name: 'دقيق أبيض', requiredQty: 5, unit: 'كيلو', available: 50, unitCost: 3.50, status: 'available' },
                { id: 2, name: 'سكر', requiredQty: 1, unit: 'كيلو', available: 20, unitCost: 4.00, status: 'available' },
                { id: 3, name: 'زيت نباتي', requiredQty: 0.5, unit: 'لتر', available: 0.2, unitCost: 8.00, status: 'insufficient' },
                { id: 4, name: 'خميرة', requiredQty: 0.1, unit: 'كيلو', available: 0, unitCost: 15.00, status: 'unavailable' }
            ];

            const tbody = document.getElementById('rawMaterialsTableBody');
            tbody.innerHTML = '';

            let totalRawMaterialsCost = 0;

            sampleRecipe.forEach(material => {
                const totalCost = material.requiredQty * material.unitCost;
                totalRawMaterialsCost += totalCost;

                let statusClass = 'bg-green-100 text-green-800';
                let statusText = 'متوفر';

                if (material.status === 'insufficient') {
                    statusClass = 'bg-yellow-100 text-yellow-800';
                    statusText = 'غير كافي';
                } else if (material.status === 'unavailable') {
                    statusClass = 'bg-red-100 text-red-800';
                    statusText = 'غير متوفر';
                }

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-4 py-3 text-sm font-medium text-gray-900">${material.name}</td>
                    <td class="px-4 py-3 text-sm text-gray-900 font-semibold">${material.requiredQty}</td>
                    <td class="px-4 py-3 text-sm text-gray-900">${material.unit}</td>
                    <td class="px-4 py-3 text-sm text-gray-900">${material.available}</td>
                    <td class="px-4 py-3 text-sm text-gray-900">${material.unitCost.toFixed(2)}</td>
                    <td class="px-4 py-3 text-sm text-gray-900 font-semibold">${totalCost.toFixed(2)}</td>
                    <td class="px-4 py-3">
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                            ${statusText}
                        </span>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // Update raw materials cost
            document.getElementById('rawMaterialsCost').value = totalRawMaterialsCost.toFixed(2);
            calculateTotalProductionCost();
        }

        function addServiceCost() {
            const tbody = document.getElementById('serviceCostsTableBody');
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-4 py-3">
                    <select class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
                        <option value="labor">عمالة</option>
                        <option value="electricity">كهرباء</option>
                        <option value="gas">غاز</option>
                        <option value="maintenance">صيانة</option>
                        <option value="packaging">تغليف</option>
                        <option value="other">أخرى</option>
                    </select>
                </td>
                <td class="px-4 py-3">
                    <input type="text" class="w-full px-2 py-1 border border-gray-300 rounded text-sm" placeholder="وصف الخدمة">
                </td>
                <td class="px-4 py-3">
                    <input type="number" step="0.01" class="w-full px-2 py-1 border border-gray-300 rounded text-sm service-cost" placeholder="0.00" onchange="calculateServiceTotal(this)">
                </td>
                <td class="px-4 py-3">
                    <select class="w-full px-2 py-1 border border-gray-300 rounded text-sm">
                        <option value="fixed">ثابت</option>
                        <option value="per_unit">لكل وحدة</option>
                        <option value="per_hour">لكل ساعة</option>
                    </select>
                </td>
                <td class="px-4 py-3">
                    <input type="number" step="0.01" value="1" class="w-full px-2 py-1 border border-gray-300 rounded text-sm service-quantity" onchange="calculateServiceTotal(this)">
                </td>
                <td class="px-4 py-3">
                    <input type="number" readonly class="w-full px-2 py-1 border border-gray-300 rounded bg-gray-50 text-sm service-total" placeholder="0.00">
                </td>
                <td class="px-4 py-3 text-center">
                    <button type="button" onclick="removeServiceCost(this)" class="text-red-600 hover:text-red-800">🗑️</button>
                </td>
            `;
            tbody.appendChild(row);
        }

        function removeServiceCost(button) {
            button.closest('tr').remove();
            calculateTotalServicesCost();
        }

        function calculateServiceTotal(input) {
            const row = input.closest('tr');
            const cost = parseFloat(row.querySelector('.service-cost').value) || 0;
            const quantity = parseFloat(row.querySelector('.service-quantity').value) || 1;
            const total = cost * quantity;

            row.querySelector('.service-total').value = total.toFixed(2);
            calculateTotalServicesCost();
        }

        function calculateTotalServicesCost() {
            const serviceTotals = document.querySelectorAll('.service-total');
            let totalServices = 0;

            serviceTotals.forEach(input => {
                totalServices += parseFloat(input.value) || 0;
            });

            document.getElementById('servicesCost').value = totalServices.toFixed(2);
            calculateTotalProductionCost();
        }

        function calculateTotalProductionCost() {
            const rawMaterialsCost = parseFloat(document.getElementById('rawMaterialsCost').value) || 0;
            const servicesCost = parseFloat(document.getElementById('servicesCost').value) || 0;
            const laborCost = parseFloat(document.getElementById('laborCost').value) || 0;

            const totalCost = rawMaterialsCost + servicesCost + laborCost;
            document.getElementById('totalProductionCost').value = totalCost.toFixed(2);

            // Calculate unit cost
            const quantity = parseFloat(document.getElementById('productionQuantity').value) || 1;
            const unitCost = totalCost / quantity;
            document.getElementById('unitCost').value = unitCost.toFixed(2);

            // Calculate suggested selling price
            calculateSuggestedPrice();
        }

        function calculateSuggestedPrice() {
            const unitCost = parseFloat(document.getElementById('unitCost').value) || 0;
            const profitMargin = parseFloat(document.getElementById('profitMarginSuggested').value) || 25;

            const suggestedPrice = unitCost * (1 + profitMargin / 100);
            document.getElementById('suggestedSellingPrice').value = suggestedPrice.toFixed(2);
        }

        function calculateProductionCost() {
            const quantity = parseFloat(document.getElementById('productionQuantity').value) || 0;
            const unit = document.getElementById('productionUnit').value;

            // Update quantity in small units
            if (unit === 'large') {
                // Assume conversion factor of 12 for demo
                const smallQuantity = quantity * 12;
                document.getElementById('productionQuantitySmall').value = smallQuantity.toFixed(2);
            } else {
                document.getElementById('productionQuantitySmall').value = quantity.toFixed(2);
            }

            // Estimate production time (sample calculation)
            const estimatedMinutes = quantity * 15; // 15 minutes per unit
            const hours = Math.floor(estimatedMinutes / 60);
            const minutes = estimatedMinutes % 60;
            document.getElementById('estimatedProductionTime').value = `${hours}س ${minutes}د`;

            calculateTotalProductionCost();
        }

        function saveProductionAsDraft() {
            // Save production as draft
            alert('تم حفظ عملية الإنتاج كمسودة');
            closeProductionModal();
        }

        // Placeholder functions for other production features
        function openBatchProductionModal() {
            alert('نافذة الإنتاج بالدفعات قيد التطوير...');
        }

        function openProductionReportModal() {
            alert('نافذة تقرير الإنتاج قيد التطوير...');
        }

        function filterProductions() {
            // Filter productions logic
            console.log('Filtering productions...');
        }

        // Recipe Management Functions
        let recipes = [];

        function openNewRecipeModal() {
            document.getElementById('newRecipeModal').classList.add('active');
            generateRecipeCode();
            loadUnitsForRecipe();
            addIngredientRow(); // Add first ingredient row
            addInstructionStep(); // Add first instruction step
        }

        function closeRecipeModal() {
            document.getElementById('newRecipeModal').classList.remove('active');
            document.getElementById('recipeForm').reset();
            document.getElementById('ingredientsTableBody').innerHTML = '';
            document.getElementById('instructionsContainer').innerHTML = '';
        }

        function generateRecipeCode() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const sequence = String(recipes.length + 1).padStart(3, '0');

            const recipeCode = `RCP-${year}${month}${day}-${sequence}`;
            document.getElementById('recipeCode').value = recipeCode;
        }

        function checkProductExists() {
            const productName = document.getElementById('recipeProductName').value.trim();
            const warningDiv = document.getElementById('productExistsWarning');

            if (productName) {
                // Check if product already exists
                const existingProduct = items.find(item =>
                    item.itemName.toLowerCase() === productName.toLowerCase() &&
                    item.itemType === 'finished_product'
                );

                if (existingProduct) {
                    warningDiv.style.display = 'block';
                } else {
                    warningDiv.style.display = 'none';
                }
            } else {
                warningDiv.style.display = 'none';
            }
        }

        function loadUnitsForRecipe() {
            // Load units for recipe yield
            const unitSelect = document.getElementById('recipeYieldUnit');
            unitSelect.innerHTML = '<option value="">اختر الوحدة</option>';

            // Sample units - in real app this would come from units system
            const sampleUnits = [
                { id: 'kg', name: 'كيلوجرام' },
                { id: 'g', name: 'جرام' },
                { id: 'l', name: 'لتر' },
                { id: 'ml', name: 'مليلتر' },
                { id: 'piece', name: 'قطعة' },
                { id: 'dozen', name: 'دزينة' }
            ];

            sampleUnits.forEach(unit => {
                const option = document.createElement('option');
                option.value = unit.id;
                option.textContent = unit.name;
                unitSelect.appendChild(option);
            });
        }

        function addIngredientRow() {
            const tbody = document.getElementById('ingredientsTableBody');
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-4 py-3">
                    <select class="w-full px-2 py-1 border border-gray-300 rounded text-sm ingredient-select" onchange="loadIngredientCost(this)" required>
                        <option value="">اختر الخامة</option>
                        ${items.filter(item => item.itemType === 'raw_material' && item.isActive).map(item =>
                            `<option value="${item.id}" data-cost="${item.costPrice || 0}">${item.itemName} (${item.itemCode})</option>`
                        ).join('')}
                    </select>
                </td>
                <td class="px-4 py-3">
                    <input type="number" step="0.01" min="0.01" class="w-full px-2 py-1 border border-gray-300 rounded text-sm ingredient-quantity" placeholder="0.00" onchange="calculateIngredientCost(this)" required>
                </td>
                <td class="px-4 py-3">
                    <select class="w-full px-2 py-1 border border-gray-300 rounded text-sm ingredient-unit" required>
                        <option value="">اختر الوحدة</option>
                        <option value="kg">كيلوجرام</option>
                        <option value="g">جرام</option>
                        <option value="l">لتر</option>
                        <option value="ml">مليلتر</option>
                        <option value="piece">قطعة</option>
                        <option value="cup">كوب</option>
                        <option value="tbsp">ملعقة كبيرة</option>
                        <option value="tsp">ملعقة صغيرة</option>
                    </select>
                </td>
                <td class="px-4 py-3">
                    <input type="number" step="0.01" readonly class="w-full px-2 py-1 border border-gray-300 rounded bg-gray-50 text-sm ingredient-unit-cost" placeholder="0.00">
                </td>
                <td class="px-4 py-3">
                    <input type="number" step="0.01" readonly class="w-full px-2 py-1 border border-gray-300 rounded bg-gray-50 text-sm ingredient-total-cost" placeholder="0.00">
                </td>
                <td class="px-4 py-3">
                    <input type="text" class="w-full px-2 py-1 border border-gray-300 rounded text-sm ingredient-notes" placeholder="ملاحظات...">
                </td>
                <td class="px-4 py-3 text-center">
                    <button type="button" onclick="removeIngredientRow(this)" class="text-red-600 hover:text-red-800">🗑️</button>
                </td>
            `;
            tbody.appendChild(row);
        }

        function removeIngredientRow(button) {
            button.closest('tr').remove();
            calculateRecipeCost();
        }

        function loadIngredientCost(select) {
            const row = select.closest('tr');
            const selectedOption = select.options[select.selectedIndex];
            const cost = selectedOption.getAttribute('data-cost') || 0;

            row.querySelector('.ingredient-unit-cost').value = parseFloat(cost).toFixed(2);
            calculateIngredientCost(row.querySelector('.ingredient-quantity'));
        }

        function calculateIngredientCost(input) {
            const row = input.closest('tr');
            const quantity = parseFloat(row.querySelector('.ingredient-quantity').value) || 0;
            const unitCost = parseFloat(row.querySelector('.ingredient-unit-cost').value) || 0;
            const totalCost = quantity * unitCost;

            row.querySelector('.ingredient-total-cost').value = totalCost.toFixed(2);
            calculateRecipeCost();
        }

        function addInstructionStep() {
            const container = document.getElementById('instructionsContainer');
            const stepNumber = container.children.length + 1;

            const stepDiv = document.createElement('div');
            stepDiv.className = 'mb-4 p-4 border border-gray-200 rounded-lg';
            stepDiv.innerHTML = `
                <div class="flex justify-between items-center mb-2">
                    <h5 class="font-medium text-gray-900">الخطوة ${stepNumber}</h5>
                    <button type="button" onclick="removeInstructionStep(this)" class="text-red-600 hover:text-red-800 text-sm">🗑️ حذف</button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الوقت المطلوب (دقيقة)</label>
                        <input type="number" min="0" class="w-full px-2 py-1 border border-gray-300 rounded text-sm step-time" placeholder="0">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">درجة الحرارة (°م)</label>
                        <input type="number" min="0" class="w-full px-2 py-1 border border-gray-300 rounded text-sm step-temperature" placeholder="0">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">نوع الخطوة</label>
                        <select class="w-full px-2 py-1 border border-gray-300 rounded text-sm step-type">
                            <option value="prep">تحضير</option>
                            <option value="mix">خلط</option>
                            <option value="bake">خبز</option>
                            <option value="cool">تبريد</option>
                            <option value="decorate">تزيين</option>
                            <option value="package">تغليف</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">وصف الخطوة *</label>
                    <textarea class="w-full px-2 py-1 border border-gray-300 rounded text-sm step-description" rows="3" placeholder="اكتب وصف تفصيلي للخطوة..." required></textarea>
                </div>
            `;
            container.appendChild(stepDiv);
        }

        function removeInstructionStep(button) {
            button.closest('.mb-4').remove();
            // Renumber steps
            const container = document.getElementById('instructionsContainer');
            Array.from(container.children).forEach((step, index) => {
                step.querySelector('h5').textContent = `الخطوة ${index + 1}`;
            });
        }

        function calculateTotalTime() {
            const prepTime = parseInt(document.getElementById('prepTime').value) || 0;
            const cookTime = parseInt(document.getElementById('cookTime').value) || 0;
            const totalMinutes = prepTime + cookTime;

            const hours = Math.floor(totalMinutes / 60);
            const minutes = totalMinutes % 60;

            let timeText = '';
            if (hours > 0) timeText += `${hours}س `;
            if (minutes > 0) timeText += `${minutes}د`;

            document.getElementById('totalTime').value = timeText || '0د';
        }

        function calculateRecipeCost() {
            // Calculate ingredients cost
            const ingredientCosts = document.querySelectorAll('.ingredient-total-cost');
            let totalIngredientsCost = 0;

            ingredientCosts.forEach(input => {
                totalIngredientsCost += parseFloat(input.value) || 0;
            });

            document.getElementById('recipeIngredientsCost').value = totalIngredientsCost.toFixed(2);

            // Calculate total recipe cost
            const laborCost = parseFloat(document.getElementById('recipeLaborCost').value) || 0;
            const overheadCost = parseFloat(document.getElementById('recipeOverheadCost').value) || 0;
            const totalCost = totalIngredientsCost + laborCost + overheadCost;

            document.getElementById('recipeTotalCost').value = totalCost.toFixed(2);

            // Calculate unit cost
            const yield = parseFloat(document.getElementById('recipeYield').value) || 1;
            const unitCost = totalCost / yield;
            document.getElementById('recipeUnitCost').value = unitCost.toFixed(2);

            // Calculate suggested selling price
            calculateRecipeSellingPrice();
        }

        function calculateRecipeSellingPrice() {
            const unitCost = parseFloat(document.getElementById('recipeUnitCost').value) || 0;
            const profitMargin = parseFloat(document.getElementById('recipeProfit').value) || 25;

            const suggestedPrice = unitCost * (1 + profitMargin / 100);
            document.getElementById('recipeSuggestedPrice').value = suggestedPrice.toFixed(2);
        }

        function saveRecipeAsDraft() {
            // Save recipe as draft
            alert('تم حفظ الوصفة كمسودة');
            closeRecipeModal();
        }

        // Save Recipe and Create Product
        function saveRecipe() {
            const recipeData = collectRecipeData();

            if (validateRecipeData(recipeData)) {
                // Create or update product automatically
                createProductFromRecipe(recipeData);

                // Save recipe
                recipes.push(recipeData);

                // Update UI
                loadRecipesTable();
                loadItems(); // Refresh items table to show new product

                alert('تم حفظ الوصفة وإنشاء المنتج بنجاح!');
                closeRecipeModal();
            }
        }

        function collectRecipeData() {
            // Collect all recipe form data
            const recipeData = {
                id: Date.now(),
                code: document.getElementById('recipeCode').value,
                name: document.getElementById('recipeName').value,
                productName: document.getElementById('recipeProductName').value,
                yield: parseFloat(document.getElementById('recipeYield').value) || 0,
                yieldUnit: document.getElementById('recipeYieldUnit').value,
                prepTime: parseInt(document.getElementById('prepTime').value) || 0,
                cookTime: parseInt(document.getElementById('cookTime').value) || 0,
                shelfLife: parseInt(document.getElementById('recipeShelfLife').value) || 0,
                difficulty: document.getElementById('recipeDifficulty').value,
                category: document.getElementById('recipeCategory').value,
                totalCost: parseFloat(document.getElementById('recipeTotalCost').value) || 0,
                unitCost: parseFloat(document.getElementById('recipeUnitCost').value) || 0,
                suggestedPrice: parseFloat(document.getElementById('recipeSuggestedPrice').value) || 0,
                notes: document.getElementById('recipeNotes').value,
                ingredients: collectIngredients(),
                instructions: collectInstructions(),
                createdAt: new Date().toISOString(),
                status: 'active'
            };

            return recipeData;
        }

        function collectIngredients() {
            const ingredients = [];
            const rows = document.querySelectorAll('#ingredientsTableBody tr');

            rows.forEach(row => {
                const ingredient = {
                    itemId: row.querySelector('.ingredient-select').value,
                    itemName: row.querySelector('.ingredient-select option:checked').textContent,
                    quantity: parseFloat(row.querySelector('.ingredient-quantity').value) || 0,
                    unit: row.querySelector('.ingredient-unit').value,
                    unitCost: parseFloat(row.querySelector('.ingredient-unit-cost').value) || 0,
                    totalCost: parseFloat(row.querySelector('.ingredient-total-cost').value) || 0,
                    notes: row.querySelector('.ingredient-notes').value
                };

                if (ingredient.itemId && ingredient.quantity > 0) {
                    ingredients.push(ingredient);
                }
            });

            return ingredients;
        }

        function collectInstructions() {
            const instructions = [];
            const steps = document.querySelectorAll('#instructionsContainer .mb-4');

            steps.forEach((step, index) => {
                const instruction = {
                    stepNumber: index + 1,
                    time: parseInt(step.querySelector('.step-time').value) || 0,
                    temperature: parseInt(step.querySelector('.step-temperature').value) || 0,
                    type: step.querySelector('.step-type').value,
                    description: step.querySelector('.step-description').value
                };

                if (instruction.description.trim()) {
                    instructions.push(instruction);
                }
            });

            return instructions;
        }

        function validateRecipeData(recipeData) {
            if (!recipeData.name.trim()) {
                alert('يرجى إدخال اسم الوصفة');
                return false;
            }

            if (!recipeData.productName.trim()) {
                alert('يرجى إدخال اسم المنتج النهائي');
                return false;
            }

            if (recipeData.yield <= 0) {
                alert('يرجى إدخال كمية الإنتاج');
                return false;
            }

            if (!recipeData.yieldUnit) {
                alert('يرجى اختيار وحدة الإنتاج');
                return false;
            }

            if (recipeData.ingredients.length === 0) {
                alert('يرجى إضافة خامة واحدة على الأقل');
                return false;
            }

            if (recipeData.instructions.length === 0) {
                alert('يرجى إضافة خطوة واحدة على الأقل');
                return false;
            }

            return true;
        }

        function createProductFromRecipe(recipeData) {
            // Check if product already exists
            const existingProduct = items.find(item =>
                item.itemName.toLowerCase() === recipeData.productName.toLowerCase() &&
                item.itemType === 'finished_product'
            );

            if (existingProduct) {
                // Update existing product with recipe data
                existingProduct.costPrice = recipeData.unitCost;
                existingProduct.sellingPrice = recipeData.suggestedPrice;
                existingProduct.shelfLife = recipeData.shelfLife;
                existingProduct.hasRecipe = true;
                existingProduct.recipeId = recipeData.id;
                existingProduct.productionStatus = 'ready_to_produce'; // Ready to produce

                console.log('تم تحديث المنتج الموجود:', existingProduct);
            } else {
                // Create new product
                const newProduct = {
                    id: Date.now() + Math.random(),
                    itemCode: generateProductCode(),
                    itemName: recipeData.productName,
                    itemType: 'finished_product',
                    category: recipeData.category,
                    branchId: '', // Will be set during production
                    warehouseId: '', // Will be set during production
                    description: `منتج تم إنشاؤه من الوصفة: ${recipeData.name}`,
                    costPrice: recipeData.unitCost,
                    sellingPrice: recipeData.suggestedPrice,
                    currency: 'SAR',
                    smallUnit: recipeData.yieldUnit,
                    largeUnit: '',
                    conversionFactor: 1,
                    currentQuantity: 0,
                    openingBalance: 0,
                    minStock: 0,
                    maxStock: 0,
                    shelfLife: recipeData.shelfLife,
                    isActive: true,
                    trackExpiry: recipeData.shelfLife > 0,
                    allowNegativeStock: false,
                    hasRecipe: true,
                    recipeId: recipeData.id,
                    productionStatus: 'awaiting_production', // Awaiting production
                    createdAt: new Date().toISOString(),
                    createdFrom: 'recipe'
                };

                // Add to items array
                items.push(newProduct);

                console.log('تم إنشاء منتج جديد:', newProduct);
            }
        }

        function generateProductCode() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const sequence = String(items.filter(item => item.itemType === 'finished_product').length + 1).padStart(3, '0');

            return `PRD-${year}${month}${day}-${sequence}`;
        }

        function getProductionStatusBadge(item) {
            if (item.itemType !== 'finished_product') {
                return '<span class="text-gray-400 text-xs">-</span>';
            }

            const status = item.productionStatus || 'no_recipe';

            switch (status) {
                case 'awaiting_production':
                    return '<span class="px-2 py-1 text-xs font-medium rounded-full bg-orange-100 text-orange-800">🔄 سيتم إنتاجه</span>';
                case 'ready_to_produce':
                    return '<span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">⚡ جاهز للإنتاج</span>';
                case 'in_production':
                    return '<span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">🏭 قيد الإنتاج</span>';
                case 'produced':
                    return '<span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">✅ تم إنتاجه</span>';
                case 'no_recipe':
                default:
                    return '<span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">❌ بدون وصفة</span>';
            }
        }

        function loadRecipesTable() {
            const tbody = document.getElementById('recipesTableBody');
            tbody.innerHTML = '';

            recipes.forEach(recipe => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-4 py-3 text-sm font-medium text-gray-900">${recipe.code}</td>
                    <td class="px-4 py-3 text-sm text-gray-900">${recipe.name}</td>
                    <td class="px-4 py-3 text-sm text-gray-900">${recipe.productName}</td>
                    <td class="px-4 py-3 text-sm text-gray-900">${recipe.yield} ${recipe.yieldUnit}</td>
                    <td class="px-4 py-3 text-sm text-gray-900">${recipe.ingredients.length}</td>
                    <td class="px-4 py-3 text-sm text-gray-900">${recipe.prepTime}د</td>
                    <td class="px-4 py-3 text-sm text-gray-900">${recipe.cookTime}د</td>
                    <td class="px-4 py-3 text-sm text-gray-900">${recipe.shelfLife} أيام</td>
                    <td class="px-4 py-3 text-sm text-gray-900 font-semibold">${recipe.totalCost.toFixed(2)}</td>
                    <td class="px-4 py-3">
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${getDifficultyClass(recipe.difficulty)}">
                            ${getDifficultyText(recipe.difficulty)}
                        </span>
                    </td>
                    <td class="px-4 py-3">
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${recipe.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                            ${recipe.status === 'active' ? 'نشطة' : 'غير نشطة'}
                        </span>
                    </td>
                    <td class="px-4 py-3 text-center">
                        <div class="flex justify-center space-x-1">
                            <button onclick="viewRecipe(${recipe.id})" class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50" title="عرض">
                                👁️
                            </button>
                            <button onclick="editRecipe(${recipe.id})" class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50" title="تعديل">
                                ✏️
                            </button>
                            <button onclick="duplicateRecipe(${recipe.id})" class="text-purple-600 hover:text-purple-900 p-1 rounded hover:bg-purple-50" title="نسخ">
                                📋
                            </button>
                            <button onclick="deleteRecipe(${recipe.id})" class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50" title="حذف">
                                🗑️
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // Update recipe stats
            updateRecipeStats();
        }

        function getDifficultyClass(difficulty) {
            switch (difficulty) {
                case 'easy': return 'bg-green-100 text-green-800';
                case 'medium': return 'bg-yellow-100 text-yellow-800';
                case 'hard': return 'bg-orange-100 text-orange-800';
                case 'expert': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getDifficultyText(difficulty) {
            switch (difficulty) {
                case 'easy': return 'سهل';
                case 'medium': return 'متوسط';
                case 'hard': return 'صعب';
                case 'expert': return 'خبير';
                default: return 'غير محدد';
            }
        }

        function updateRecipeStats() {
            document.getElementById('totalRecipes').textContent = recipes.length;
            document.getElementById('activeRecipes').textContent = recipes.filter(r => r.status === 'active').length;
            document.getElementById('productsWithRecipes').textContent = new Set(recipes.map(r => r.productName)).size;

            const avgCost = recipes.length > 0 ?
                recipes.reduce((sum, r) => sum + r.totalCost, 0) / recipes.length : 0;
            document.getElementById('averageRecipeCost').textContent = avgCost.toFixed(2);
        }

        // Placeholder functions for recipe actions
        function viewRecipe(recipeId) {
            const recipe = recipes.find(r => r.id === recipeId);
            if (recipe) {
                alert(`عرض الوصفة: ${recipe.name}\nالمنتج: ${recipe.productName}\nالتكلفة: ${recipe.totalCost.toFixed(2)}`);
            }
        }

        function editRecipe(recipeId) {
            alert(`تعديل الوصفة ${recipeId} قيد التطوير...`);
        }

        function duplicateRecipe(recipeId) {
            alert(`نسخ الوصفة ${recipeId} قيد التطوير...`);
        }

        function deleteRecipe(recipeId) {
            if (confirm('هل أنت متأكد من حذف هذه الوصفة؟')) {
                const index = recipes.findIndex(r => r.id === recipeId);
                if (index > -1) {
                    recipes.splice(index, 1);
                    loadRecipesTable();
                    alert('تم حذف الوصفة بنجاح');
                }
            }
        }

        // Expiry Date Tracking Functions
        function checkExpiryDates() {
            // Check expiry dates for all products
            const today = new Date();
            const threeDaysFromNow = new Date(today.getTime() + (3 * 24 * 60 * 60 * 1000));
            const oneWeekFromNow = new Date(today.getTime() + (7 * 24 * 60 * 60 * 1000));

            // Sample data - in real app this would come from production records
            const productsWithExpiry = [
                { id: 1, name: 'خبز أبيض', productionDate: '2024-01-10', shelfLife: 3, quantity: 50, warehouse: 'المخزن الرئيسي' },
                { id: 2, name: 'كيك شوكولاتة', productionDate: '2024-01-12', shelfLife: 7, quantity: 20, warehouse: 'مخزن الحلويات' },
                { id: 3, name: 'معجنات', productionDate: '2024-01-08', shelfLife: 5, quantity: 30, warehouse: 'المخزن الرئيسي' }
            ];

            const expiredProducts = [];
            const soonExpiryProducts = [];
            const weekExpiryProducts = [];

            productsWithExpiry.forEach(product => {
                const productionDate = new Date(product.productionDate);
                const expiryDate = new Date(productionDate.getTime() + (product.shelfLife * 24 * 60 * 60 * 1000));

                if (expiryDate < today) {
                    expiredProducts.push(product);
                } else if (expiryDate <= threeDaysFromNow) {
                    soonExpiryProducts.push(product);
                } else if (expiryDate <= oneWeekFromNow) {
                    weekExpiryProducts.push(product);
                }
            });

            // Update UI
            updateExpiryLists(expiredProducts, soonExpiryProducts, weekExpiryProducts);
            loadExpiryTrackingTable(productsWithExpiry);
        }

        function updateExpiryLists(expired, soonExpiry, weekExpiry) {
            // Update expired products list
            const expiredList = document.getElementById('expiredProductsList');
            expiredList.innerHTML = expired.length > 0 ?
                expired.map(p => `<div class="mb-1">• ${p.name} (${p.quantity} وحدة)</div>`).join('') :
                '<div class="text-gray-500">لا توجد منتجات منتهية الصلاحية</div>';

            // Update soon expiry list
            const soonList = document.getElementById('soonExpiryProductsList');
            soonList.innerHTML = soonExpiry.length > 0 ?
                soonExpiry.map(p => `<div class="mb-1">• ${p.name} (${p.quantity} وحدة)</div>`).join('') :
                '<div class="text-gray-500">لا توجد منتجات تنتهي قريباً</div>';

            // Update week expiry list
            const weekList = document.getElementById('weekExpiryProductsList');
            weekList.innerHTML = weekExpiry.length > 0 ?
                weekExpiry.map(p => `<div class="mb-1">• ${p.name} (${p.quantity} وحدة)</div>`).join('') :
                '<div class="text-gray-500">لا توجد منتجات تنتهي خلال أسبوع</div>';
        }

        function loadExpiryTrackingTable(products) {
            const tbody = document.getElementById('expiryTrackingTableBody');
            tbody.innerHTML = '';

            products.forEach(product => {
                const productionDate = new Date(product.productionDate);
                const expiryDate = new Date(productionDate.getTime() + (product.shelfLife * 24 * 60 * 60 * 1000));
                const today = new Date();
                const daysRemaining = Math.ceil((expiryDate - today) / (24 * 60 * 60 * 1000));

                let statusClass = 'bg-green-100 text-green-800';
                let statusText = 'صالح';

                if (daysRemaining < 0) {
                    statusClass = 'bg-red-100 text-red-800';
                    statusText = 'منتهي الصلاحية';
                } else if (daysRemaining <= 3) {
                    statusClass = 'bg-orange-100 text-orange-800';
                    statusText = 'ينتهي قريباً';
                } else if (daysRemaining <= 7) {
                    statusClass = 'bg-yellow-100 text-yellow-800';
                    statusText = 'ينتهي خلال أسبوع';
                }

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-4 py-3 text-sm font-medium text-gray-900">PROD${product.id.toString().padStart(3, '0')}</td>
                    <td class="px-4 py-3 text-sm text-gray-900">${product.name}</td>
                    <td class="px-4 py-3 text-sm text-gray-900">${new Date(product.productionDate).toLocaleDateString('ar-SA')}</td>
                    <td class="px-4 py-3 text-sm text-gray-900">${product.shelfLife} أيام</td>
                    <td class="px-4 py-3 text-sm text-gray-900">${expiryDate.toLocaleDateString('ar-SA')}</td>
                    <td class="px-4 py-3 text-sm text-gray-900 font-semibold ${daysRemaining < 0 ? 'text-red-600' : daysRemaining <= 3 ? 'text-orange-600' : 'text-green-600'}">
                        ${daysRemaining < 0 ? 'منتهي' : `${daysRemaining} يوم`}
                    </td>
                    <td class="px-4 py-3 text-sm text-gray-900">${product.quantity}</td>
                    <td class="px-4 py-3 text-sm text-gray-900">${product.warehouse}</td>
                    <td class="px-4 py-3">
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                            ${statusText}
                        </span>
                    </td>
                    <td class="px-4 py-3 text-center">
                        <div class="flex justify-center space-x-1">
                            <button onclick="extendShelfLife(${product.id})" class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50" title="تمديد الصلاحية">
                                ⏰
                            </button>
                            <button onclick="markAsDiscounted(${product.id})" class="text-yellow-600 hover:text-yellow-900 p-1 rounded hover:bg-yellow-50" title="تخفيض السعر">
                                💰
                            </button>
                            <button onclick="disposeProduct(${product.id})" class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50" title="إتلاف">
                                🗑️
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Placeholder functions for quality control
        function openQualityInspectionModal() {
            alert('نافذة فحص الجودة قيد التطوير...');
        }

        function generateQualityReport() {
            alert('تقرير الجودة قيد التطوير...');
        }

        function extendShelfLife(productId) {
            alert(`تمديد صلاحية المنتج ${productId} قيد التطوير...`);
        }

        function markAsDiscounted(productId) {
            alert(`تخفيض سعر المنتج ${productId} قيد التطوير...`);
        }

        function disposeProduct(productId) {
            if (confirm('هل أنت متأكد من إتلاف هذا المنتج؟')) {
                alert(`إتلاف المنتج ${productId} قيد التطوير...`);
            }
        }

        // Placeholder functions for recipes
        function filterRecipes() {
            console.log('Filtering recipes...');
        }

        function importRecipesFromExcel() {
            alert('استيراد الوصفات من Excel قيد التطوير...');
        }

        function exportRecipesToExcel() {
            alert('تصدير الوصفات إلى Excel قيد التطوير...');
        }

        function importIngredientsFromTemplate() {
            alert('استيراد المكونات من قالب قيد التطوير...');
        }



        // Expiry Date Calculation Functions
        function toggleExpiryFields() {
            const trackExpiry = document.getElementById('trackExpiry').checked;
            const expirySection = document.getElementById('expiryTrackingSection');

            if (trackExpiry) {
                expirySection.style.display = 'block';
                // Set today as default production date
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('productionDate').value = today;
                calculateExpiryDate();
            } else {
                expirySection.style.display = 'none';
            }
        }

        function calculateExpiryDate() {
            const productionDate = document.getElementById('productionDate').value;
            const shelfLife = parseInt(document.getElementById('shelfLife').value) || 0;

            if (productionDate && shelfLife > 0) {
                const production = new Date(productionDate);
                const expiry = new Date(production.getTime() + (shelfLife * 24 * 60 * 60 * 1000));

                // Set calculated expiry date
                document.getElementById('calculatedExpiryDate').value = expiry.toISOString().split('T')[0];

                // Calculate days remaining
                const today = new Date();
                const daysRemaining = Math.ceil((expiry - today) / (24 * 60 * 60 * 1000));

                let daysText = '';
                let colorClass = '';

                if (daysRemaining < 0) {
                    daysText = `منتهي منذ ${Math.abs(daysRemaining)} يوم`;
                    colorClass = 'text-red-600 font-bold';
                } else if (daysRemaining === 0) {
                    daysText = 'ينتهي اليوم';
                    colorClass = 'text-red-600 font-bold';
                } else if (daysRemaining <= 3) {
                    daysText = `${daysRemaining} أيام متبقية`;
                    colorClass = 'text-orange-600 font-semibold';
                } else if (daysRemaining <= 7) {
                    daysText = `${daysRemaining} أيام متبقية`;
                    colorClass = 'text-yellow-600';
                } else {
                    daysText = `${daysRemaining} يوم متبقي`;
                    colorClass = 'text-green-600';
                }

                const daysField = document.getElementById('daysRemaining');
                daysField.value = daysText;
                daysField.className = daysField.className.replace(/text-\w+-\d+/g, '') + ' ' + colorClass;
            } else {
                document.getElementById('calculatedExpiryDate').value = '';
                document.getElementById('daysRemaining').value = '';
            }
        }

        // Initialize expiry checking on page load
        document.addEventListener('DOMContentLoaded', function() {
            // ... existing initialization code ...

            // Check expiry dates if on quality tab
            if (window.location.hash === '#quality') {
                checkExpiryDates();
            }
        });
    </script>

    <!-- New Production Modal -->
    <div id="newProductionModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-5xl w-full mx-4 max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <span class="ml-2">🏭</span>
                    بدء عملية إنتاج جديدة
                </h3>
                <button onclick="closeProductionModal()" class="text-gray-400 hover:text-gray-600">
                    <span class="text-2xl">×</span>
                </button>
            </div>

            <form id="productionForm" class="px-6 py-4">
                <input type="hidden" id="productionId" value="">

                <!-- Production Basic Info -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">رقم العملية *</label>
                        <input type="text" id="productionNumber" required readonly
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-base"
                               placeholder="سيتم إنشاؤه تلقائياً">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">المنتج المراد إنتاجه *</label>
                        <select id="productToManufacture" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                onchange="loadProductRecipe()">
                            <option value="">اختر المنتج</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">خط الإنتاج *</label>
                        <select id="productionLine" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                            <option value="">اختر خط الإنتاج</option>
                            <option value="line1">خط الإنتاج 1 - الخبز</option>
                            <option value="line2">خط الإنتاج 2 - المعجنات</option>
                            <option value="line3">خط الإنتاج 3 - الحلويات</option>
                            <option value="line4">خط الإنتاج 4 - الكيك</option>
                        </select>
                    </div>
                </div>

                <!-- Production Quantity and Units -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
                        <span class="ml-2">📏</span>
                        كمية الإنتاج والوحدات
                    </h4>

                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الكمية المطلوبة *</label>
                            <input type="number" id="productionQuantity" step="0.01" min="0.01" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="0.00" onchange="calculateProductionCost()">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">وحدة الإنتاج *</label>
                            <select id="productionUnit" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                    onchange="calculateProductionCost()">
                                <option value="">اختر الوحدة</option>
                                <option value="small">الوحدة الصغرى</option>
                                <option value="large">الوحدة الكبرى</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الكمية بالوحدة الصغرى</label>
                            <input type="number" id="productionQuantitySmall" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-base"
                                   placeholder="0.00">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الوقت المتوقع للإنتاج</label>
                            <input type="text" id="estimatedProductionTime" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-base"
                                   placeholder="سيحسب تلقائياً">
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-4 pt-4 border-t border-gray-200">
                    <button type="button" onclick="closeProductionModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300">
                        إلغاء
                    </button>
                    <button type="button" onclick="saveProductionAsDraft()" class="px-4 py-2 text-sm font-medium text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200">
                        حفظ كمسودة
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700">
                        بدء الإنتاج
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- New Recipe Modal -->
    <div id="newRecipeModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <span class="ml-2">📋</span>
                    إضافة وصفة جديدة
                </h3>
                <button onclick="closeRecipeModal()" class="text-gray-400 hover:text-gray-600">
                    <span class="text-2xl">×</span>
                </button>
            </div>

            <form id="recipeForm" class="px-6 py-4">
                <input type="hidden" id="recipeId" value="">

                <!-- Recipe Basic Info -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">كود الوصفة *</label>
                        <input type="text" id="recipeCode" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                               placeholder="مثال: RCP001">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم الوصفة *</label>
                        <input type="text" id="recipeName" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                               placeholder="مثال: وصفة خبز أبيض">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم المنتج النهائي *</label>
                        <input type="text" id="recipeProductName" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                               placeholder="مثال: كيك شوكولاتة بالكريمة"
                               onchange="checkProductExists()">
                        <p class="text-xs text-gray-500 mt-1">سيتم إنشاء هذا المنتج تلقائياً في قائمة الأصناف عند حفظ الوصفة</p>
                        <div id="productExistsWarning" class="text-xs text-orange-600 mt-1" style="display:none;">
                            ⚠️ يوجد منتج بهذا الاسم مسبقاً - سيتم ربط الوصفة بالمنتج الموجود
                        </div>
                    </div>
                </div>

                <!-- Recipe Details -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
                        <span class="ml-2">📝</span>
                        تفاصيل الوصفة
                    </h4>

                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الكمية المنتجة *</label>
                            <input type="number" id="recipeYield" step="0.01" min="0.01" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="0.00" onchange="calculateRecipeCost()">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">وحدة الإنتاج *</label>
                            <select id="recipeYieldUnit" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                                <option value="">اختر الوحدة</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">وقت التحضير (دقيقة)</label>
                            <input type="number" id="prepTime" min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="30">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">وقت الطبخ (دقيقة)</label>
                            <input type="number" id="cookTime" min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="45" onchange="calculateTotalTime()">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">إجمالي الوقت</label>
                            <input type="text" id="totalTime" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-base"
                                   placeholder="سيحسب تلقائياً">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">مدة الصلاحية (أيام) *</label>
                            <input type="number" id="recipeShelfLife" min="1" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="7">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">مستوى الصعوبة</label>
                            <select id="recipeDifficulty"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                                <option value="easy">سهل</option>
                                <option value="medium">متوسط</option>
                                <option value="hard">صعب</option>
                                <option value="expert">خبير</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">فئة الوصفة</label>
                            <select id="recipeCategory"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base">
                                <option value="bread">خبز</option>
                                <option value="pastry">معجنات</option>
                                <option value="cake">كيك وحلويات</option>
                                <option value="cookies">بسكويت</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Recipe Ingredients -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
                        <span class="ml-2">🥄</span>
                        الخامات والمكونات
                    </h4>

                    <div class="mb-3">
                        <button type="button" onclick="addIngredientRow()" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">
                            + إضافة خامة
                        </button>
                        <button type="button" onclick="importIngredientsFromTemplate()" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 mr-2">
                            📋 استيراد من قالب
                        </button>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full production-table">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الخامة *</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية *</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوحدة *</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">تكلفة الوحدة</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">إجمالي التكلفة</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">ملاحظات</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">إجراء</th>
                                </tr>
                            </thead>
                            <tbody id="ingredientsTableBody">
                                <!-- Ingredients will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Recipe Instructions -->
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
                        <span class="ml-2">📖</span>
                        خطوات التحضير
                    </h4>

                    <div class="mb-3">
                        <button type="button" onclick="addInstructionStep()" class="bg-purple-600 text-white px-3 py-1 rounded text-sm hover:bg-purple-700">
                            + إضافة خطوة
                        </button>
                    </div>

                    <div id="instructionsContainer">
                        <!-- Instructions will be populated here -->
                    </div>
                </div>

                <!-- Recipe Cost Summary -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
                        <span class="ml-2">💰</span>
                        ملخص تكاليف الوصفة
                    </h4>

                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">تكلفة الخامات</label>
                            <input type="number" id="recipeIngredientsCost" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-base"
                                   placeholder="0.00">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">تكلفة العمالة</label>
                            <input type="number" id="recipeLaborCost" step="0.01" min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="0.00" onchange="calculateRecipeCost()">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">تكاليف إضافية</label>
                            <input type="number" id="recipeOverheadCost" step="0.01" min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   placeholder="0.00" onchange="calculateRecipeCost()">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">إجمالي تكلفة الوصفة</label>
                            <input type="number" id="recipeTotalCost" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-yellow-100 text-base font-bold"
                                   placeholder="0.00">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">تكلفة الوحدة الواحدة</label>
                            <input type="number" id="recipeUnitCost" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-base"
                                   placeholder="0.00">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">هامش الربح المقترح (%)</label>
                            <input type="number" id="recipeProfit" step="0.01" min="0" value="25"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                                   onchange="calculateRecipeSellingPrice()">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">سعر البيع المقترح</label>
                            <input type="number" id="recipeSuggestedPrice" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-green-100 text-base font-bold"
                                   placeholder="0.00">
                        </div>
                    </div>
                </div>

                <!-- Recipe Notes -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات الوصفة</label>
                    <textarea id="recipeNotes" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-base"
                              placeholder="أي ملاحظات خاصة بالوصفة، نصائح، أو تحذيرات..."></textarea>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-4 pt-4 border-t border-gray-200">
                    <button type="button" onclick="closeRecipeModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300">
                        إلغاء
                    </button>
                    <button type="button" onclick="saveRecipeAsDraft()" class="px-4 py-2 text-sm font-medium text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200">
                        حفظ كمسودة
                    </button>
                    <button type="button" onclick="saveRecipe()" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700">
                        حفظ الوصفة وإنشاء المنتج
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Damage Entry Modal -->
    <div id="damageEntryModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <span class="ml-2">🗑️</span>
                    قيد تالف جديد
                </h3>
                <button onclick="closeDamageEntryModal()" class="text-gray-400 hover:text-gray-600">
                    <span class="text-xl">✕</span>
                </button>
            </div>

            <form id="damageEntryForm" onsubmit="saveDamageEntry(event)" class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">رقم القيد</label>
                        <input type="text" id="damageEntryNumber" readonly
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">التاريخ</label>
                        <input type="date" id="damageDate" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الصنف *</label>
                        <select id="damageItemId" required onchange="updateDamageItemDetails()"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500">
                            <option value="">اختر الصنف</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">المخزن *</label>
                        <select id="damageWarehouseId" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500">
                            <option value="">اختر المخزن</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الكمية *</label>
                        <input type="number" id="damageQuantity" step="0.01" min="0.01" required onchange="calculateDamageTotal()"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الوحدة</label>
                        <input type="text" id="damageUnitName" readonly
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">تكلفة الوحدة</label>
                        <input type="number" id="damageUnitCost" step="0.01" readonly
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">سبب التالف *</label>
                        <select id="damageReason" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500">
                            <option value="">اختر السبب</option>
                            <option value="expired">منتهي الصلاحية</option>
                            <option value="damaged">تالف</option>
                            <option value="contaminated">ملوث</option>
                            <option value="defective">معيب</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">إجمالي القيمة</label>
                        <input type="number" id="damageTotalValue" step="0.01" readonly
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 font-bold text-red-600">
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                    <textarea id="damageNotes" rows="3" placeholder="أدخل أي ملاحظات إضافية..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeDamageEntryModal()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300">
                        إلغاء
                    </button>
                    <button type="submit"
                            class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700">
                        حفظ قيد التالف
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Include advanced libraries -->
    <script src="advanced-excel.js"></script>
    <script src="advanced-print.js"></script>
    <script src="excel-utils.js"></script>

    <script src="products.js"></script>
</body>
</html>