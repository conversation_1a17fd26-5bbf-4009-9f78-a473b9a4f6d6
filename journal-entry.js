// Journal Entry Management System
let currentEntry = {
    type: 'journal',
    entries: []
};

let entryCounter = 1;
let accounts = [];
let rowCounter = 0;

// Load initial data
function loadInitialData() {
    loadUserInfo();
    loadCompanyInfo();
    generateEntryNumber();
    setCurrentDate();
    loadBranches();
    loadAccounts();
    addInitialRows();
}

// Generate entry number
function generateEntryNumber() {
    const savedCounter = localStorage.getItem('anwar_bakery_journal_counter');
    if (savedCounter) {
        entryCounter = parseInt(savedCounter) + 1;
    }

    const entryNumber = `JE-${new Date().getFullYear()}-${entryCounter.toString().padStart(3, '0')}`;
    document.getElementById('entryNumber').value = entryNumber;
}

// Set current date
function setCurrentDate() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('entryDate').value = today;
}

// Load branches
function loadBranches() {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    const branchSelect = document.getElementById('branchId');

    branchSelect.innerHTML = '<option value="">اختر الفرع</option>';

    if (savedBranches) {
        const branches = JSON.parse(savedBranches);
        branches.filter(branch => branch.isActive).forEach(branch => {
            const option = document.createElement('option');
            option.value = branch.id;
            option.textContent = branch.branchName;
            branchSelect.appendChild(option);
        });
    }
}

// Load accounts from chart of accounts
function loadAccounts() {
    const savedAccounts = localStorage.getItem('anwar_bakery_accounts');
    if (savedAccounts) {
        accounts = JSON.parse(savedAccounts);
    }
}

// Add initial rows
function addInitialRows() {
    addEntryRow(); // First debit row
    addEntryRow(); // First credit row
}

// Add entry row
function addEntryRow() {
    rowCounter++;
    const tableBody = document.getElementById('entriesTableBody');

    const row = document.createElement('tr');
    row.className = 'entry-row';
    row.id = `row-${rowCounter}`;

    row.innerHTML = `
        <td class="px-3 py-2 text-center text-sm border-b">
            <span class="text-gray-500">${rowCounter}</span>
        </td>
        <td class="px-3 py-2 border-b">
            <select class="account-select w-full px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-purple-500 text-sm"
                    onchange="updateTotals()" required>
                <option value="">اختر الحساب</option>
                ${accounts.map(account =>
                    `<option value="${account.id}" data-code="${account.code}" data-name="${account.name}">
                        ${account.name} (${account.code})
                    </option>`
                ).join('')}
            </select>
        </td>
        <td class="px-3 py-2 border-b">
            <input type="text" class="description-input w-full px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-purple-500 text-sm"
                   placeholder="بيان السطر...">
        </td>
        <td class="px-3 py-2 border-b">
            <input type="number" class="debit-input w-full px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-purple-500 text-sm text-center"
                   min="0" step="0.01" placeholder="0.00" onchange="handleDebitChange(this)" onkeyup="updateTotals()">
        </td>
        <td class="px-3 py-2 border-b">
            <input type="number" class="credit-input w-full px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-purple-500 text-sm text-center"
                   min="0" step="0.01" placeholder="0.00" onchange="handleCreditChange(this)" onkeyup="updateTotals()">
        </td>
        <td class="px-3 py-2 border-b text-center">
            <button onclick="removeEntryRow(${rowCounter})" class="delete-btn text-red-600 hover:text-red-900 text-sm px-2 py-1 rounded hover:bg-red-50">
                🗑️
            </button>
        </td>
    `;

    tableBody.appendChild(row);
    updateTotals();
}

// Handle debit change (clear credit if debit is entered)
function handleDebitChange(debitInput) {
    const row = debitInput.closest('tr');
    const creditInput = row.querySelector('.credit-input');

    if (parseFloat(debitInput.value) > 0) {
        creditInput.value = '';
        creditInput.disabled = true;
        creditInput.classList.add('bg-gray-100');
    } else {
        creditInput.disabled = false;
        creditInput.classList.remove('bg-gray-100');
    }

    updateTotals();
}

// Handle credit change (clear debit if credit is entered)
function handleCreditChange(creditInput) {
    const row = creditInput.closest('tr');
    const debitInput = row.querySelector('.debit-input');

    if (parseFloat(creditInput.value) > 0) {
        debitInput.value = '';
        debitInput.disabled = true;
        debitInput.classList.add('bg-gray-100');
    } else {
        debitInput.disabled = false;
        debitInput.classList.remove('bg-gray-100');
    }

    updateTotals();
}

// Remove entry row
function removeEntryRow(rowId) {
    const rows = document.querySelectorAll('.entry-row');
    if (rows.length <= 2) {
        showMessage('يجب أن يحتوي القيد على سطرين على الأقل', 'error');
        return;
    }

    const row = document.getElementById(`row-${rowId}`);
    if (row) {
        row.remove();
        updateTotals();
        updateRowNumbers();
    }
}

// Update row numbers after deletion
function updateRowNumbers() {
    const rows = document.querySelectorAll('.entry-row');
    rows.forEach((row, index) => {
        const numberCell = row.querySelector('td:first-child span');
        if (numberCell) {
            numberCell.textContent = index + 1;
        }
    });
}

// Add quick entry based on account type
function addQuickEntry(accountType) {
    const accountTypeMap = {
        'cash': { codes: ['1110'], name: 'النقدية' },
        'bank': { codes: ['1120'], name: 'البنوك' },
        'customer': { codes: ['1130'], name: 'العملاء' },
        'supplier': { codes: ['2110'], name: 'الموردين' },
        'expense': { codes: ['5'], name: 'المصروفات' },
        'revenue': { codes: ['4'], name: 'الإيرادات' }
    };

    const typeInfo = accountTypeMap[accountType];
    if (!typeInfo) return;

    // Find accounts matching the type
    const matchingAccounts = accounts.filter(account =>
        typeInfo.codes.some(code => account.code.startsWith(code))
    );

    if (matchingAccounts.length === 0) {
        showMessage(`لا توجد حسابات من نوع ${typeInfo.name}`, 'info');
        return;
    }

    // Add new row and select first matching account
    addEntryRow();
    const lastRow = document.querySelector('.entry-row:last-child');
    const accountSelect = lastRow.querySelector('.account-select');
    accountSelect.value = matchingAccounts[0].id;

    // Focus on the appropriate amount field
    if (['expense', 'customer'].includes(accountType)) {
        lastRow.querySelector('.debit-input').focus();
    } else {
        lastRow.querySelector('.credit-input').focus();
    }

    updateTotals();
}

// Update totals and balance status
function updateTotals() {
    const rows = document.querySelectorAll('.entry-row');
    let totalDebit = 0;
    let totalCredit = 0;

    rows.forEach(row => {
        const debitInput = row.querySelector('.debit-input');
        const creditInput = row.querySelector('.credit-input');

        const debitValue = parseFloat(debitInput.value) || 0;
        const creditValue = parseFloat(creditInput.value) || 0;

        totalDebit += debitValue;
        totalCredit += creditValue;
    });

    const currencySymbol = getCurrencySymbol();
    const difference = Math.abs(totalDebit - totalCredit);

    // Update totals display
    document.getElementById('totalDebit').textContent = totalDebit.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '');
    document.getElementById('totalCredit').textContent = totalCredit.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '');
    document.getElementById('difference').textContent = difference.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '');

    // Update balance status
    updateBalanceStatus(totalDebit, totalCredit, difference);

    // Enable/disable save button
    const saveButton = document.getElementById('saveButton');
    if (difference === 0 && totalDebit > 0 && totalCredit > 0) {
        saveButton.disabled = false;
        saveButton.classList.remove('opacity-50', 'cursor-not-allowed');
    } else {
        saveButton.disabled = true;
        saveButton.classList.add('opacity-50', 'cursor-not-allowed');
    }

    // Show smart suggestions
    showSmartSuggestions(totalDebit, totalCredit, difference);
}

// Update balance status
function updateBalanceStatus(totalDebit, totalCredit, difference) {
    const statusDiv = document.getElementById('balanceStatus');
    const iconSpan = document.getElementById('balanceIcon');
    const textSpan = document.getElementById('balanceText');

    statusDiv.style.display = 'block';
    statusDiv.className = 'mb-6';

    if (difference === 0 && totalDebit > 0) {
        statusDiv.classList.add('balance-balanced');
        iconSpan.textContent = '✅';
        textSpan.textContent = 'القيد متوازن ومجهز للحفظ';
    } else if (difference > 0) {
        statusDiv.classList.add('balance-warning');
        iconSpan.textContent = '⚠️';
        textSpan.textContent = `القيد غير متوازن - الفرق: ${difference.toLocaleString()}${getCurrencySymbol() ? ' ' + getCurrencySymbol() : ''}`;
    } else {
        statusDiv.classList.remove('balance-warning', 'balance-balanced');
        statusDiv.classList.add('border-gray-200', 'text-gray-600');
        iconSpan.textContent = '⚖️';
        textSpan.textContent = 'أدخل المبالغ لمعاينة توازن القيد';
    }
}

// Show smart suggestions
function showSmartSuggestions(totalDebit, totalCredit, difference) {
    const suggestionsDiv = document.getElementById('smartSuggestions');
    const contentDiv = document.getElementById('suggestionsContent');

    if (difference === 0 || (totalDebit === 0 && totalCredit === 0)) {
        suggestionsDiv.style.display = 'none';
        return;
    }

    suggestionsDiv.style.display = 'block';
    contentDiv.innerHTML = '';

    const currencySymbol = getCurrencySymbol();

    if (totalDebit > totalCredit) {
        contentDiv.innerHTML = `
            <div class="bg-blue-50 border border-blue-200 rounded p-3 text-sm">
                <strong>💡 اقتراح:</strong> تحتاج إلى إضافة مبلغ ${difference.toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''} في الجانب الدائن
            </div>
            <div class="text-xs text-gray-600">
                جرب إضافة: حساب بنك، مورد، أو إيراد
            </div>
        `;
    } else {
        contentDiv.innerHTML = `
            <div class="bg-orange-50 border border-orange-200 rounded p-3 text-sm">
                <strong>💡 اقتراح:</strong> تحتاج إلى إضافة مبلغ ${difference.toLocaleString()}${currencySymbol ? ' ' + currencySymbol : ''} في الجانب المدين
            </div>
            <div class="text-xs text-gray-600">
                جرب إضافة: حساب نقدية، عميل، أو مصروف
            </div>
        `;
    }
}

// Validate entry
function validateEntry() {
    const errors = [];

    // Check required fields
    if (!document.getElementById('entryDate').value) {
        errors.push('التاريخ مطلوب');
    }

    if (!document.getElementById('branchId').value) {
        errors.push('الفرع مطلوب');
    }

    if (!document.getElementById('description').value.trim()) {
        errors.push('البيان مطلوب');
    }

    // Check entries
    const rows = document.querySelectorAll('.entry-row');
    let validEntries = 0;
    let totalDebit = 0;
    let totalCredit = 0;

    rows.forEach((row, index) => {
        const accountSelect = row.querySelector('.account-select');
        const debitInput = row.querySelector('.debit-input');
        const creditInput = row.querySelector('.credit-input');

        const accountValue = accountSelect.value;
        const debitValue = parseFloat(debitInput.value) || 0;
        const creditValue = parseFloat(creditInput.value) || 0;

        if (accountValue && (debitValue > 0 || creditValue > 0)) {
            validEntries++;
            totalDebit += debitValue;
            totalCredit += creditValue;
        } else if (accountValue || debitValue > 0 || creditValue > 0) {
            errors.push(`السطر ${index + 1}: يجب اختيار الحساب وإدخال المبلغ`);
        }
    });

    if (validEntries < 2) {
        errors.push('يجب أن يحتوي القيد على سطرين صحيحين على الأقل');
    }

    if (Math.abs(totalDebit - totalCredit) > 0.01) {
        errors.push('القيد غير متوازن - إجمالي المدين يجب أن يساوي إجمالي الدائن');
    }

    if (errors.length > 0) {
        showMessage('أخطاء في القيد:\n' + errors.join('\n'), 'error');
        return false;
    }

    showMessage('القيد صحيح ومتوازن ✅', 'success');
    return true;
}

// Preview entry
function previewEntry() {
    if (!validateEntry()) return;

    const entryData = collectEntryData();
    populatePrintTemplate(entryData);

    // Open preview in new window
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>معاينة القيد المحاسبي</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                    @media print { body { margin: 0; } }
                </style>
            </head>
            <body>
                ${document.getElementById('printTemplate').innerHTML}
                <div style="text-align: center; margin-top: 20px; page-break-inside: avoid;">
                    <button onclick="window.print()" style="padding: 10px 20px; font-size: 16px;">طباعة</button>
                    <button onclick="window.close()" style="padding: 10px 20px; font-size: 16px; margin-right: 10px;">إغلاق</button>
                </div>
            </body>
        </html>
    `);
    printWindow.document.close();
}

// Save draft
function saveDraft() {
    if (!validateEntry()) return;

    const entryData = collectEntryData();
    entryData.status = 'draft';
    saveEntry(entryData);
    showMessage('تم حفظ مسودة القيد بنجاح', 'success');
}

// Save and post
function saveAndPost() {
    if (!validateEntry()) return;

    const entryData = collectEntryData();
    entryData.status = 'posted';
    saveEntry(entryData);
    showMessage('تم حفظ وترحيل القيد بنجاح', 'success');

    // Redirect after short delay
    setTimeout(() => {
        window.location.href = 'vouchers.html';
    }, 2000);
}

// Get currency symbol from settings
function getCurrencySymbol() {
    const savedSettings = localStorage.getItem('anwar_bakery_settings');
    if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        return settings.currency || '';
    }
    return '';
}

// Load user info
function loadUserInfo() {
    const savedUser = localStorage.getItem('anwar_bakery_current_user');
    if (savedUser) {
        const user = JSON.parse(savedUser);
        const userFullNameElement = document.getElementById('userFullName');
        const userRoleElement = document.getElementById('userRole');

        if (userFullNameElement) {
            userFullNameElement.textContent = user.fullName || 'أحمد محمد';
        }
        if (userRoleElement) {
            userRoleElement.textContent = user.role === 'admin' ? 'مدير النظام' : (user.role || 'مدير النظام');
        }
    }
}

// Load company info
function loadCompanyInfo() {
    const savedCompany = localStorage.getItem('anwar_bakery_company');
    if (savedCompany) {
        const company = JSON.parse(savedCompany);
        const companyNameElement = document.getElementById('companyName');
        const companySloganElement = document.getElementById('companySlogan');

        if (companyNameElement) {
            companyNameElement.textContent = company.companyName || 'مخبز أنوار الحي';
        }
        if (companySloganElement) {
            companySloganElement.textContent = company.slogan || 'جودة تستحق الثقة';
        }
    }
}

// Collect entry data
function collectEntryData() {
    const rows = document.querySelectorAll('.entry-row');
    const entries = [];

    rows.forEach((row, index) => {
        const accountSelect = row.querySelector('.account-select');
        const descriptionInput = row.querySelector('.description-input');
        const debitInput = row.querySelector('.debit-input');
        const creditInput = row.querySelector('.credit-input');

        const accountId = accountSelect.value;
        const debitValue = parseFloat(debitInput.value) || 0;
        const creditValue = parseFloat(creditInput.value) || 0;

        if (accountId && (debitValue > 0 || creditValue > 0)) {
            const selectedOption = accountSelect.options[accountSelect.selectedIndex];
            entries.push({
                lineNumber: index + 1,
                accountId: accountId,
                account: {
                    id: accountId,
                    code: selectedOption.dataset.code,
                    name: selectedOption.dataset.name
                },
                description: descriptionInput.value.trim() || document.getElementById('description').value,
                debit: debitValue,
                credit: creditValue
            });
        }
    });

    return {
        id: Date.now(),
        entryNumber: document.getElementById('entryNumber').value,
        type: 'journal',
        date: document.getElementById('entryDate').value,
        branchId: parseInt(document.getElementById('branchId').value),
        entryType: document.getElementById('entryType').value,
        description: document.getElementById('description').value,
        entries: entries,
        totalDebit: entries.reduce((sum, entry) => sum + entry.debit, 0),
        totalCredit: entries.reduce((sum, entry) => sum + entry.credit, 0),
        createdBy: 'current_user', // TODO: Get from session
        createdAt: new Date().toISOString()
    };
}

// Save entry
function saveEntry(entryData) {
    // Save to journal entries
    let journalEntries = [];
    const savedEntries = localStorage.getItem('anwar_bakery_journal_entries');
    if (savedEntries) {
        journalEntries = JSON.parse(savedEntries);
    }
    journalEntries.push(entryData);
    localStorage.setItem('anwar_bakery_journal_entries', JSON.stringify(journalEntries));

    // Update counter
    localStorage.setItem('anwar_bakery_journal_counter', entryCounter.toString());

    // TODO: Update account balances if posted
    // TODO: Create audit trail
}

// Populate print template
function populatePrintTemplate(entryData) {
    const savedUser = localStorage.getItem('anwar_bakery_current_user');
    const savedCompany = localStorage.getItem('anwar_bakery_company');
    const savedBranches = localStorage.getItem('anwar_bakery_branches');

    let currentUser = { fullName: 'غير محدد' };
    let company = { companyName: 'مخبز أنوار الحي', slogan: 'جودة تستحق الثقة' };
    let branchName = 'غير محدد';

    if (savedUser) currentUser = JSON.parse(savedUser);
    if (savedCompany) company = JSON.parse(savedCompany);
    if (savedBranches) {
        const branches = JSON.parse(savedBranches);
        const branch = branches.find(b => b.id == entryData.branchId);
        if (branch) branchName = branch.branchName;
    }

    const currencySymbol = getCurrencySymbol();
    const entryTypeNames = {
        'general': 'قيد عام',
        'adjustment': 'قيد تسوية',
        'closing': 'قيد إقفال',
        'opening': 'قيد افتتاحي'
    };

    // Populate template
    document.getElementById('printCompanyName').textContent = company.companyName;
    document.getElementById('printCompanySlogan').textContent = company.slogan;
    document.getElementById('printEntryNumber').textContent = entryData.entryNumber;
    document.getElementById('printDate').textContent = new Date(entryData.date).toLocaleDateString('ar-SA');
    document.getElementById('printBranch').textContent = branchName;
    document.getElementById('printEntryType').textContent = entryTypeNames[entryData.entryType];
    document.getElementById('printCreatedBy').textContent = currentUser.fullName;
    document.getElementById('printDescription').textContent = entryData.description;
    document.getElementById('printUserName').textContent = currentUser.fullName;
    document.getElementById('printCreatedAt').textContent = new Date().toLocaleString('ar-SA');

    // Populate entries
    const entriesHtml = entryData.entries.map(entry => `
        <tr>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">${entry.lineNumber}</td>
            <td style="padding: 8px; border: 1px solid #ddd;">${entry.account.name} (${entry.account.code})</td>
            <td style="padding: 8px; border: 1px solid #ddd;">${entry.description}</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: center; ${entry.debit > 0 ? 'font-weight: bold; color: #059669;' : ''}">${entry.debit > 0 ? entry.debit.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '') : '-'}</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: center; ${entry.credit > 0 ? 'font-weight: bold; color: #dc2626;' : ''}">${entry.credit > 0 ? entry.credit.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '') : '-'}</td>
        </tr>
    `).join('');
    document.getElementById('printEntries').innerHTML = entriesHtml;

    // Populate totals
    document.getElementById('printTotalDebit').textContent = entryData.totalDebit.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '');
    document.getElementById('printTotalCredit').textContent = entryData.totalCredit.toLocaleString() + (currencySymbol ? ' ' + currencySymbol : '');
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    if (!container) return;

    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                <div style="white-space: pre-line;">${message}</div>
            </div>
        </div>
    `;

    // Add shake animation for errors
    if (type === 'error') {
        container.firstElementChild.classList.add('validation-error');
    }

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_current_user');
        window.location.href = 'login.html';
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl + Enter to save and post
    if (e.ctrlKey && e.key === 'Enter') {
        e.preventDefault();
        saveAndPost();
    }

    // Ctrl + S to save draft
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        saveDraft();
    }

    // Ctrl + N to add new row
    if (e.ctrlKey && e.key === 'n') {
        e.preventDefault();
        addEntryRow();
    }

    // Ctrl + P to preview
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        previewEntry();
    }
});

// Auto-save draft every 2 minutes
setInterval(() => {
    const rows = document.querySelectorAll('.entry-row');
    let hasData = false;

    rows.forEach(row => {
        const accountSelect = row.querySelector('.account-select');
        const debitInput = row.querySelector('.debit-input');
        const creditInput = row.querySelector('.credit-input');

        if (accountSelect.value || parseFloat(debitInput.value) > 0 || parseFloat(creditInput.value) > 0) {
            hasData = true;
        }
    });

    if (hasData && document.getElementById('description').value.trim()) {
        try {
            const entryData = collectEntryData();
            entryData.status = 'auto_draft';
            entryData.autoSavedAt = new Date().toISOString();

            localStorage.setItem('anwar_bakery_journal_auto_draft', JSON.stringify(entryData));

            // Show subtle notification
            const notification = document.createElement('div');
            notification.className = 'fixed bottom-4 left-4 bg-gray-800 text-white px-3 py-2 rounded text-sm opacity-75';
            notification.textContent = 'تم الحفظ التلقائي';
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 2000);
        } catch (error) {
            console.log('Auto-save failed:', error);
        }
    }
}, 120000); // Every 2 minutes

// Load auto-saved draft on page load
function loadAutoDraft() {
    const autoDraft = localStorage.getItem('anwar_bakery_journal_auto_draft');
    if (autoDraft) {
        try {
            const draftData = JSON.parse(autoDraft);
            const draftAge = Date.now() - new Date(draftData.autoSavedAt).getTime();

            // Only load if draft is less than 24 hours old
            if (draftAge < 24 * 60 * 60 * 1000) {
                if (confirm('تم العثور على مسودة محفوظة تلقائياً. هل تريد استعادتها؟')) {
                    restoreAutoDraft(draftData);
                }
            } else {
                // Remove old draft
                localStorage.removeItem('anwar_bakery_journal_auto_draft');
            }
        } catch (error) {
            console.log('Failed to load auto-draft:', error);
        }
    }
}

// Restore auto-saved draft
function restoreAutoDraft(draftData) {
    // Set basic fields
    document.getElementById('entryDate').value = draftData.date;
    document.getElementById('branchId').value = draftData.branchId;
    document.getElementById('entryType').value = draftData.entryType;
    document.getElementById('description').value = draftData.description;

    // Clear existing rows
    document.getElementById('entriesTableBody').innerHTML = '';
    rowCounter = 0;

    // Add rows from draft
    draftData.entries.forEach(entry => {
        addEntryRow();
        const lastRow = document.querySelector('.entry-row:last-child');
        lastRow.querySelector('.account-select').value = entry.accountId;
        lastRow.querySelector('.description-input').value = entry.description;
        lastRow.querySelector('.debit-input').value = entry.debit || '';
        lastRow.querySelector('.credit-input').value = entry.credit || '';

        // Handle disabled state
        if (entry.debit > 0) {
            lastRow.querySelector('.credit-input').disabled = true;
            lastRow.querySelector('.credit-input').classList.add('bg-gray-100');
        } else if (entry.credit > 0) {
            lastRow.querySelector('.debit-input').disabled = true;
            lastRow.querySelector('.debit-input').classList.add('bg-gray-100');
        }
    });

    updateTotals();
    showMessage('تم استعادة المسودة المحفوظة تلقائياً', 'info');
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadInitialData();

    // Load auto-draft after a short delay
    setTimeout(() => {
        loadAutoDraft();
    }, 1000);
});
