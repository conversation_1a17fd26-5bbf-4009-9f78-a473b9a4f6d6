// Excel Import/Export Utilities for Anwar Bakery System

class ExcelUtils {
    constructor() {
        this.loadSheetJS();
    }

    // Load SheetJS library dynamically
    loadSheetJS() {
        if (typeof XLSX === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
            script.onload = () => {
                console.log('SheetJS loaded successfully');
            };
            document.head.appendChild(script);
        }
    }

    // Export data to Excel
    exportToExcel(data, filename, sheetName = 'البيانات') {
        try {
            if (!data || data.length === 0) {
                throw new Error('لا توجد بيانات للتصدير');
            }

            // Create workbook
            const wb = XLSX.utils.book_new();
            
            // Convert data to worksheet
            const ws = XLSX.utils.json_to_sheet(data);
            
            // Set column widths
            const colWidths = this.calculateColumnWidths(data);
            ws['!cols'] = colWidths;
            
            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, sheetName);
            
            // Generate filename with timestamp
            const timestamp = new Date().toISOString().split('T')[0];
            const fullFilename = `${filename}_${timestamp}.xlsx`;
            
            // Save file
            XLSX.writeFile(wb, fullFilename);
            
            return { success: true, message: 'تم تصدير البيانات بنجاح' };
        } catch (error) {
            console.error('Excel export error:', error);
            return { success: false, message: 'خطأ في تصدير البيانات: ' + error.message };
        }
    }

    // Import data from Excel
    importFromExcel(file, callback) {
        const reader = new FileReader();
        
        reader.onload = (e) => {
            try {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
                
                // Get first worksheet
                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];
                
                // Convert to JSON
                const jsonData = XLSX.utils.sheet_to_json(worksheet);
                
                callback({ success: true, data: jsonData, sheetName: firstSheetName });
            } catch (error) {
                console.error('Excel import error:', error);
                callback({ success: false, message: 'خطأ في قراءة الملف: ' + error.message });
            }
        };
        
        reader.onerror = () => {
            callback({ success: false, message: 'خطأ في قراءة الملف' });
        };
        
        reader.readAsArrayBuffer(file);
    }

    // Calculate optimal column widths
    calculateColumnWidths(data) {
        if (!data || data.length === 0) return [];
        
        const headers = Object.keys(data[0]);
        const widths = [];
        
        headers.forEach(header => {
            let maxWidth = header.length;
            
            data.forEach(row => {
                const cellValue = String(row[header] || '');
                maxWidth = Math.max(maxWidth, cellValue.length);
            });
            
            // Set minimum and maximum widths
            widths.push({ wch: Math.min(Math.max(maxWidth, 10), 50) });
        });
        
        return widths;
    }

    // Export multiple sheets
    exportMultipleSheets(sheetsData, filename) {
        try {
            const wb = XLSX.utils.book_new();
            
            sheetsData.forEach(({ data, sheetName }) => {
                if (data && data.length > 0) {
                    const ws = XLSX.utils.json_to_sheet(data);
                    const colWidths = this.calculateColumnWidths(data);
                    ws['!cols'] = colWidths;
                    XLSX.utils.book_append_sheet(wb, ws, sheetName);
                }
            });
            
            const timestamp = new Date().toISOString().split('T')[0];
            const fullFilename = `${filename}_${timestamp}.xlsx`;
            
            XLSX.writeFile(wb, fullFilename);
            
            return { success: true, message: 'تم تصدير البيانات بنجاح' };
        } catch (error) {
            console.error('Multi-sheet export error:', error);
            return { success: false, message: 'خطأ في تصدير البيانات: ' + error.message };
        }
    }

    // Export with formatting
    exportWithFormatting(data, filename, options = {}) {
        try {
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(data);
            
            // Apply formatting
            if (options.headerStyle) {
                this.applyHeaderStyle(ws, data);
            }
            
            if (options.numberFormat) {
                this.applyNumberFormat(ws, data, options.numberFormat);
            }
            
            // Set column widths
            const colWidths = this.calculateColumnWidths(data);
            ws['!cols'] = colWidths;
            
            XLSX.utils.book_append_sheet(wb, ws, options.sheetName || 'البيانات');
            
            const timestamp = new Date().toISOString().split('T')[0];
            const fullFilename = `${filename}_${timestamp}.xlsx`;
            
            XLSX.writeFile(wb, fullFilename);
            
            return { success: true, message: 'تم تصدير البيانات مع التنسيق بنجاح' };
        } catch (error) {
            console.error('Formatted export error:', error);
            return { success: false, message: 'خطأ في تصدير البيانات: ' + error.message };
        }
    }

    // Apply header styling
    applyHeaderStyle(worksheet, data) {
        if (!data || data.length === 0) return;
        
        const headers = Object.keys(data[0]);
        const headerStyle = {
            font: { bold: true, color: { rgb: "FFFFFF" } },
            fill: { fgColor: { rgb: "366092" } },
            alignment: { horizontal: "center", vertical: "center" }
        };
        
        headers.forEach((header, index) => {
            const cellAddress = XLSX.utils.encode_cell({ r: 0, c: index });
            if (worksheet[cellAddress]) {
                worksheet[cellAddress].s = headerStyle;
            }
        });
    }

    // Apply number formatting
    applyNumberFormat(worksheet, data, formatColumns) {
        if (!data || data.length === 0) return;
        
        const headers = Object.keys(data[0]);
        
        formatColumns.forEach(column => {
            const colIndex = headers.indexOf(column);
            if (colIndex !== -1) {
                for (let row = 1; row <= data.length; row++) {
                    const cellAddress = XLSX.utils.encode_cell({ r: row, c: colIndex });
                    if (worksheet[cellAddress]) {
                        worksheet[cellAddress].z = '#,##0.00';
                    }
                }
            }
        });
    }

    // Create Excel template for import
    createImportTemplate(templateName, headers, sampleData = []) {
        try {
            const wb = XLSX.utils.book_new();
            
            // Create template data
            const templateData = [
                headers.reduce((obj, header) => {
                    obj[header] = '';
                    return obj;
                }, {}),
                ...sampleData
            ];
            
            const ws = XLSX.utils.json_to_sheet(templateData);
            
            // Style headers
            this.applyHeaderStyle(ws, templateData);
            
            // Set column widths
            const colWidths = headers.map(header => ({ wch: Math.max(header.length, 15) }));
            ws['!cols'] = colWidths;
            
            XLSX.utils.book_append_sheet(wb, ws, 'قالب البيانات');
            
            // Add instructions sheet
            const instructions = [
                { 'التعليمات': 'يرجى ملء البيانات في الأعمدة المحددة' },
                { 'التعليمات': 'لا تقم بتغيير أسماء الأعمدة' },
                { 'التعليمات': 'تأكد من صحة البيانات قبل الحفظ' },
                { 'التعليمات': 'احفظ الملف بصيغة Excel (.xlsx)' }
            ];
            
            const instructionsWs = XLSX.utils.json_to_sheet(instructions);
            XLSX.utils.book_append_sheet(wb, instructionsWs, 'التعليمات');
            
            const filename = `قالب_${templateName}_${new Date().toISOString().split('T')[0]}.xlsx`;
            XLSX.writeFile(wb, filename);
            
            return { success: true, message: 'تم إنشاء قالب الاستيراد بنجاح' };
        } catch (error) {
            console.error('Template creation error:', error);
            return { success: false, message: 'خطأ في إنشاء القالب: ' + error.message };
        }
    }

    // Validate imported data
    validateImportData(data, requiredFields = []) {
        const errors = [];
        const warnings = [];
        
        if (!data || data.length === 0) {
            errors.push('الملف فارغ أو لا يحتوي على بيانات');
            return { valid: false, errors, warnings };
        }
        
        // Check required fields
        const headers = Object.keys(data[0]);
        requiredFields.forEach(field => {
            if (!headers.includes(field)) {
                errors.push(`العمود المطلوب "${field}" غير موجود`);
            }
        });
        
        // Check for empty rows
        data.forEach((row, index) => {
            const isEmpty = Object.values(row).every(value => !value || value.toString().trim() === '');
            if (isEmpty) {
                warnings.push(`الصف ${index + 2} فارغ`);
            }
        });
        
        // Check for duplicate entries (if ID field exists)
        if (headers.includes('id') || headers.includes('كود') || headers.includes('رقم')) {
            const idField = headers.find(h => ['id', 'كود', 'رقم'].includes(h));
            const ids = data.map(row => row[idField]).filter(id => id);
            const duplicateIds = ids.filter((id, index) => ids.indexOf(id) !== index);
            
            if (duplicateIds.length > 0) {
                errors.push(`توجد قيم مكررة في ${idField}: ${duplicateIds.join(', ')}`);
            }
        }
        
        return {
            valid: errors.length === 0,
            errors,
            warnings,
            rowCount: data.length
        };
    }

    // Convert CSV to Excel
    csvToExcel(csvContent, filename) {
        try {
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet(
                csvContent.split('\n').map(row => row.split(','))
            );
            
            XLSX.utils.book_append_sheet(wb, ws, 'البيانات');
            
            const timestamp = new Date().toISOString().split('T')[0];
            const fullFilename = `${filename}_${timestamp}.xlsx`;
            
            XLSX.writeFile(wb, fullFilename);
            
            return { success: true, message: 'تم تحويل CSV إلى Excel بنجاح' };
        } catch (error) {
            console.error('CSV to Excel conversion error:', error);
            return { success: false, message: 'خطأ في التحويل: ' + error.message };
        }
    }
}

// Create global instance
window.excelUtils = new ExcelUtils();

// Export functions for use in other scripts
window.exportToExcel = (data, filename, sheetName) => {
    return window.excelUtils.exportToExcel(data, filename, sheetName);
};

window.importFromExcel = (file, callback) => {
    return window.excelUtils.importFromExcel(file, callback);
};

window.createImportTemplate = (templateName, headers, sampleData) => {
    return window.excelUtils.createImportTemplate(templateName, headers, sampleData);
};

window.validateImportData = (data, requiredFields) => {
    return window.excelUtils.validateImportData(data, requiredFields);
};
