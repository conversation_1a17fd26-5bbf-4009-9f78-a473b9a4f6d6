// حل بسيط لمشكلة العملة
(function() {
    'use strict';

    // الحصول على رمز العملة الصحيح
    function getCorrectCurrencySymbol() {
        try {
            const companyData = localStorage.getItem('anwar_bakery_company');
            if (companyData) {
                const company = JSON.parse(companyData);

                // إذا كان هناك رمز محفوظ، استخدمه
                if (company.currencySymbol) {
                    return company.currencySymbol;
                }

                // وإلا حول كود العملة إلى رمز
                const currencySymbols = {
                    'SAR': 'ر.س',
                    'YER': '﷼',
                    'USD': '$',
                    'EUR': '€',
                    'AED': 'د.إ',
                    'KWD': 'د.ك',
                    'QAR': 'ر.ق'
                };

                return currencySymbols[company.currency] || 'ر.س';
            }
        } catch (error) {
            console.error('Error getting currency symbol:', error);
        }

        return 'ر.س';
    }

    // إصلاح العملة في الصفحة
    function fixCurrencyInPage() {
        const correctSymbol = getCorrectCurrencySymbol();

        // إصلاح النصوص التي تحتوي على أكواد العملة
        const currencyCodes = ['SAR', 'YER', 'USD', 'EUR', 'AED', 'KWD', 'QAR'];

        // البحث في جميع عناصر النص
        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        const textNodes = [];
        let node;

        while (node = walker.nextNode()) {
            textNodes.push(node);
        }

        textNodes.forEach(textNode => {
            let text = textNode.textContent;
            let changed = false;

            currencyCodes.forEach(code => {
                if (text.includes(' ' + code)) {
                    text = text.replace(new RegExp(' ' + code, 'g'), ' ' + correctSymbol);
                    changed = true;
                }
            });

            if (changed) {
                textNode.textContent = text;
            }
        });

        // إصلاح خاص للإحصائيات
        const totalSalariesElement = document.getElementById('totalSalaries');
        if (totalSalariesElement) {
            let text = totalSalariesElement.textContent;
            currencyCodes.forEach(code => {
                if (text.includes(' ' + code)) {
                    text = text.replace(new RegExp(' ' + code, 'g'), ' ' + correctSymbol);
                    totalSalariesElement.textContent = text;
                }
            });
        }

        console.log('✅ Currency fixed in page with symbol:', correctSymbol);
    }

    // تطبيق الإصلاح عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(fixCurrencyInPage, 1000);
        });
    } else {
        setTimeout(fixCurrencyInPage, 1000);
    }

    // تطبيق الإصلاح عند تحديث بيانات الشركة
    window.addEventListener('storage', function(event) {
        if (event.key === 'anwar_bakery_company') {
            setTimeout(fixCurrencyInPage, 500);
        }
    });

    // إضافة وظيفة عامة للإصلاح اليدوي
    window.fixCurrency = fixCurrencyInPage;

})();

console.log('✅ Simple Currency Fix loaded');
