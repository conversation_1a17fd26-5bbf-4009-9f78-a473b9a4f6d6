<?php
// Test PHP syntax compatibility
echo "PHP Version: " . PHP_VERSION . "\n";

// Test array syntax
$test1 = array('a', 'b', 'c');
echo "Array syntax test: " . implode(', ', $test1) . "\n";

// Test JSON encoding
$test2 = array('success' => true, 'message' => 'Test message');
echo "JSON test: " . json_encode($test2) . "\n";

// Test database connection constants
if (defined('DB_HOST')) {
    echo "DB_HOST is defined: " . DB_HOST . "\n";
} else {
    echo "DB_HOST is not defined\n";
}

echo "PHP syntax test completed successfully!\n";
?>
