# 🏪 نظام إدارة مخبز أنوار الحي - البروميت الشامل
## Anwar Bakery Management System - Complete Application Prompt

---

## 📋 **نظرة عامة على التطبيق**

**نظام إدارة مخبز أنوار الحي** هو نظام إدارة شامل ومتكامل للمخابز والحلويات، مطور بتقنيات الويب الحديثة مع دعم العمل أوفلاين وأونلاين. النظام مصمم خصيصاً للبيئة العربية مع دعم كامل للغة العربية واتجاه RTL.

---

## 🎯 **الهدف الأساسي**

تطوير نظام إدارة احترافي يغطي جميع احتياجات المخابز من:
- إدارة المبيعات والمشتريات
- إدارة المخزون والمنتجات
- النظام المحاسبي المتكامل
- إدارة العملاء والموردين والموظفين
- التقارير والتحليلات المالية
- نظام نقطة البيع (POS)

---

## 🏗️ **البنية التقنية**

### **التقنيات المستخدمة:**
- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **UI Framework**: Tailwind CSS
- **Database**: MySQL + LocalStorage (Hybrid)
- **Backend**: PHP (للمزامنة)
- **Icons**: Unicode Emojis + Custom Icons
- **Printing**: Browser Print API
- **Export**: SheetJS (Excel)

### **نمط التطوير:**
- **Single Page Applications** مع تنقل سلس
- **Progressive Web App** مع دعم العمل أوفلاين
- **Responsive Design** يعمل على جميع الأجهزة
- **Component-Based** مع إعادة استخدام الكود

---

## 📁 **هيكل المشروع**

```
AnwarBakery/
├── 📄 dashboard.html              # لوحة التحكم الرئيسية
├── 📄 login.html                  # صفحة تسجيل الدخول
├── 📄 system-settings.html        # إعدادات النظام
├── 📄 company-settings.html       # إعدادات الشركة
├── 📄 system-admin.html           # إدارة النظام المتقدمة
├── 📄 users-management.html       # إدارة المستخدمين
├── 📄 branches-warehouses.html    # الفروع والمخازن
├── 📄 products.html               # إدارة المنتجات
├── 📄 customers.html              # إدارة العملاء
├── 📄 suppliers.html              # إدارة الموردين
├── 📄 employees.html              # إدارة الموظفين
├── 📄 invoices.html               # الفواتير الرئيسية
├── 📄 sales-invoice.html          # فواتير المبيعات
├── 📄 purchase-invoice.html       # فواتير المشتريات
├── 📄 sales-return.html           # مرتجعات المبيعات
├── 📄 purchase-return.html        # مرتجعات المشتريات
├── 📄 pos-system.html             # نظام نقطة البيع
├── 📄 quick-sale.html             # البيع السريع
├── 📄 cash-registers.html         # إدارة الصناديق
├── 📄 banks.html                  # إدارة البنوك
├── 📄 chart-of-accounts.html      # شجرة الحسابات
├── 📄 vouchers.html               # السندات المالية
├── 📄 receipt-voucher.html        # سندات القبض
├── 📄 payment-voucher.html        # سندات الدفع
├── 📄 journal-entry.html          # القيود المحاسبية
├── 📄 reports.html                # التقارير
├── 📄 invoices-reports.html       # تقارير الفواتير
├── 📄 invoices-analytics.html     # تحليلات الفواتير
├── 📄 inventory.html              # تقارير المخزون
├── 📄 money-transfer.html         # تحويل الأموال
├── 📄 damage-entry.html           # قيد التالف
├── 📄 owners.html                 # إدارة الملاك
├── 📄 units.html                  # وحدات القياس
├── 📄 test-system.html            # اختبار النظام
├── 📄 test-diagnostics.html       # تشخيص النظام
├── 📂 api/                        # واجهات برمجية
│   ├── auth.php                   # المصادقة
│   ├── products.php               # منتجات API
│   ├── sync-manager.php           # مدير المزامنة
│   └── config/database.php        # إعداد قاعدة البيانات
├── 📄 database-schema.sql         # هيكل قاعدة البيانات
├── 📄 config.json                 # إعدادات التطبيق
└── 📄 *.js                        # ملفات JavaScript المختلفة
```

---

## 🎨 **تصميم الواجهة**

### **المبادئ الأساسية:**
- **تصميم عربي أصيل** مع دعم RTL كامل
- **ألوان احترافية** (أزرق، بنفسجي، رمادي)
- **تنقل سهل** مع sidebar ثابت
- **استجابة كاملة** لجميع أحجام الشاشات
- **أيقونات واضحة** مع نصوص عربية

### **نظام الألوان:**
- **الأساسي**: أزرق (#3B82F6) إلى بنفسجي (#8B5CF6)
- **النجاح**: أخضر (#10B981)
- **التحذير**: أصفر (#F59E0B)
- **الخطر**: أحمر (#EF4444)
- **الخلفية**: أبيض (#FFFFFF) ورمادي فاتح (#F9FAFB)

---

## 🔧 **الوظائف الأساسية**

### **1. إدارة المبيعات:**
- فواتير مبيعات شاملة
- نظام نقطة البيع (POS)
- البيع السريع
- مرتجعات المبيعات
- ربط مع العملاء والمخزون

### **2. إدارة المشتريات:**
- فواتير مشتريات مفصلة
- ربط مع الموردين
- مرتجعات المشتريات
- تحديث المخزون تلقائياً

### **3. إدارة المخزون:**
- تتبع المنتجات والكميات
- تنبيهات نفاد المخزون
- قيد التالف والفقدان
- تقارير حركة المخزون

### **4. النظام المحاسبي:**
- شجرة حسابات شاملة
- سندات القبض والدفع
- القيود المحاسبية
- الميزانية والأرباح والخسائر

### **5. إدارة الأشخاص:**
- العملاء مع حسابات جارية
- الموردين مع تتبع المستحقات
- الموظفين مع الرواتب والجزاءات
- الملاك مع حساباتهم

---

## 📊 **نظام التقارير**

### **التقارير المالية:**
- تقارير المبيعات اليومية/الشهرية/السنوية
- تقارير المشتريات والمصروفات
- تقارير الأرباح والخسائر
- تقارير حركة الصناديق

### **تقارير المخزون:**
- تقرير المخزون الحالي
- تقرير حركة الأصناف
- تقرير الأصناف الراكدة
- تقرير التالف والفقدان

### **تقارير العملاء والموردين:**
- كشف حساب العملاء
- أعمار الديون
- تقرير أفضل العملاء
- تقرير مستحقات الموردين

---

## 🚀 **الميزات المتقدمة**

### **1. قاعدة البيانات الحقيقية:**
- مزامنة ثنائية الاتجاه
- دعم أجهزة متعددة
- نسخ احتياطية تلقائية
- مزامنة في الخلفية

### **2. نظام الطباعة المتقدم:**
- طباعة فواتير احترافية A4
- طباعة إيصالات حرارية
- تخصيص التنسيق والألوان
- طباعة متعددة النسخ

### **3. تصدير Excel متقدم:**
- تقارير بأوراق متعددة
- تنسيق احترافي مع ألوان
- رسوم بيانية جاهزة
- دعم RTL للعربية

---

## 💾 **إدارة البيانات**

### **التخزين المحلي:**
- LocalStorage للبيانات الأساسية
- SessionStorage للجلسات
- IndexedDB للملفات الكبيرة
- Cache API للأداء

### **قاعدة البيانات:**
- MySQL للبيانات المشتركة
- هيكل محاسبي احترافي
- فهرسة محسنة للأداء
- علاقات مترابطة

---

## 🔐 **الأمان والصلاحيات**

### **نظام المصادقة:**
- تسجيل دخول آمن
- جلسات محمية
- انتهاء صلاحية تلقائي
- حماية من CSRF

### **مستويات الصلاحيات:**
- **مدير النظام**: صلاحيات كاملة
- **المدير**: إدارة العمليات
- **الكاشير**: المبيعات والمدفوعات
- **المشاهد**: عرض التقارير فقط

---

## 📱 **التوافق والاستجابة**

### **المتصفحات المدعومة:**
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### **الأجهزة المدعومة:**
- أجهزة سطح المكتب
- الأجهزة اللوحية
- الهواتف الذكية
- أجهزة نقطة البيع

---

## 🎯 **أهداف التطوير**

### **الأولويات:**
1. **الاستقرار والموثوقية** - نظام خالي من الأخطاء
2. **سهولة الاستخدام** - واجهة بديهية وسريعة
3. **الأداء العالي** - استجابة سريعة
4. **التوافق الكامل** - يعمل في جميع البيئات
5. **الأمان المتقدم** - حماية البيانات

### **المعايير:**
- **كود نظيف ومنظم** مع تعليقات عربية
- **اختبارات شاملة** لجميع الوظائف
- **توثيق مفصل** لكل ميزة
- **دعم فني متكامل** مع أدلة الاستخدام

---

## 🔄 **دورة التطوير**

### **مراحل التطوير:**
1. **التحليل والتصميم** - فهم المتطلبات
2. **التطوير والبرمجة** - كتابة الكود
3. **الاختبار والمراجعة** - ضمان الجودة
4. **النشر والتشغيل** - إطلاق النظام
5. **الصيانة والتطوير** - التحسين المستمر

### **أدوات الجودة:**
- **مراجعة الكود** قبل كل تحديث
- **اختبارات تلقائية** للوظائف الحرجة
- **مراقبة الأداء** المستمرة
- **تحليل الأخطاء** وإصلاحها فوراً

---

## 📈 **خطة التطوير المستقبلي**

### **الإصدارات القادمة:**
- **v2.0**: تطبيق موبايل أصلي
- **v2.1**: ذكاء اصطناعي للتنبؤات
- **v2.2**: تكامل مع أنظمة خارجية
- **v2.3**: تقارير BI متقدمة

### **التحسينات المخططة:**
- واجهة مستخدم محسنة
- أداء أسرع ومحسن
- ميزات أمان إضافية
- دعم لغات متعددة

---

## ✅ **معايير النجاح**

النظام يعتبر ناجحاً عندما:
- ✅ يعمل بدون أخطاء في جميع المتصفحات
- ✅ يدعم جميع العمليات المحاسبية المطلوبة
- ✅ يوفر تقارير دقيقة وشاملة
- ✅ يحافظ على البيانات بأمان
- ✅ يوفر تجربة مستخدم ممتازة

---

---

## 🛠️ **التفاصيل التقنية المتقدمة**

### **نمط البرمجة:**
```javascript
// مثال على البنية المعيارية للكود
class InvoiceManager {
    constructor() {
        this.invoices = this.loadInvoices();
        this.initializeEventListeners();
    }

    // حفظ فاتورة جديدة
    saveInvoice(invoiceData) {
        // التحقق من صحة البيانات
        if (!this.validateInvoice(invoiceData)) {
            throw new Error('بيانات الفاتورة غير صحيحة');
        }

        // إضافة معرف فريد
        invoiceData.id = this.generateInvoiceId();

        // حفظ في التخزين المحلي
        this.invoices.push(invoiceData);
        this.saveToStorage();

        // تحديث المخزون
        this.updateInventory(invoiceData.items);

        // إنشاء قيد محاسبي
        this.createJournalEntry(invoiceData);

        return invoiceData;
    }
}
```

### **إدارة الحالة (State Management):**
```javascript
// نظام إدارة الحالة المركزي
const AppState = {
    user: null,
    company: null,
    settings: null,

    // تحديث بيانات المستخدم
    setUser(userData) {
        this.user = userData;
        this.notifySubscribers('user', userData);
    },

    // الاشتراك في تغييرات الحالة
    subscribe(key, callback) {
        if (!this.subscribers[key]) {
            this.subscribers[key] = [];
        }
        this.subscribers[key].push(callback);
    }
};
```

### **نظام التحقق من البيانات:**
```javascript
// مثال على التحقق من صحة البيانات
const ValidationRules = {
    invoice: {
        customerName: { required: true, minLength: 2 },
        items: { required: true, minItems: 1 },
        totalAmount: { required: true, min: 0 }
    },

    product: {
        name: { required: true, minLength: 2 },
        price: { required: true, min: 0 },
        stock: { required: true, min: 0 }
    }
};
```

---

## 📋 **دليل الاستخدام السريع**

### **للمطورين:**

#### **إضافة صفحة جديدة:**
1. إنشاء ملف HTML جديد
2. تضمين الملفات المشتركة (CSS, JS)
3. إضافة رابط في الـ sidebar
4. تطبيق نمط التصميم الموحد

#### **إضافة وظيفة جديدة:**
1. تعريف الوظيفة في ملف JS منفصل
2. إضافة التحقق من البيانات
3. ربط مع قاعدة البيانات
4. إضافة واجهة المستخدم
5. اختبار شامل للوظيفة

#### **تخصيص التصميم:**
```css
/* متغيرات CSS للألوان */
:root {
    --primary-color: #3B82F6;
    --secondary-color: #8B5CF6;
    --success-color: #10B981;
    --warning-color: #F59E0B;
    --danger-color: #EF4444;
    --background-color: #F9FAFB;
}
```

### **للمستخدمين:**

#### **البدء السريع:**
1. فتح `login.html` في المتصفح
2. تسجيل الدخول (admin/admin)
3. إعداد بيانات الشركة
4. إضافة المنتجات والعملاء
5. بدء إنشاء الفواتير

#### **العمليات اليومية:**
- **المبيعات**: استخدام نظام POS أو فواتير المبيعات
- **المشتريات**: إدخال فواتير الموردين
- **المخزون**: مراقبة الكميات والتنبيهات
- **التقارير**: مراجعة الأداء اليومي

---

## 🔍 **نظام التشخيص والإصلاح**

### **أدوات التشخيص المدمجة:**
```javascript
// فحص سلامة النظام
function systemDiagnostics() {
    const checks = [
        checkLocalStorage(),
        checkDataIntegrity(),
        checkCurrencySettings(),
        checkAccountingBalance(),
        checkInventoryConsistency()
    ];

    return {
        status: checks.every(check => check.passed),
        details: checks,
        recommendations: generateRecommendations(checks)
    };
}
```

### **الإصلاح التلقائي:**
- إصلاح البيانات التالفة
- إعادة بناء الفهارس
- تصحيح الأرصدة المحاسبية
- إصلاح روابط البيانات

---

## 📊 **مؤشرات الأداء الرئيسية (KPIs)**

### **المؤشرات المالية:**
- إجمالي المبيعات اليومية/الشهرية
- هامش الربح الإجمالي
- معدل دوران المخزون
- متوسط قيمة الفاتورة

### **مؤشرات العملاء:**
- عدد العملاء الجدد
- معدل تكرار الشراء
- قيمة العميل مدى الحياة
- رضا العملاء

### **مؤشرات التشغيل:**
- سرعة معالجة الفواتير
- دقة المخزون
- معدل الأخطاء
- وقت الاستجابة

---

## 🌐 **التكامل مع الأنظمة الخارجية**

### **APIs المدعومة:**
- أنظمة الدفع الإلكتروني
- خدمات الشحن والتوصيل
- أنظمة المحاسبة الخارجية
- منصات التجارة الإلكترونية

### **تصدير البيانات:**
- تصدير إلى QuickBooks
- تصدير إلى Excel/CSV
- تصدير إلى PDF
- APIs للتكامل المخصص

---

## 🔒 **الأمان والخصوصية**

### **حماية البيانات:**
- تشفير البيانات الحساسة
- نسخ احتياطية مشفرة
- سجل مراجعة شامل
- حماية من SQL Injection

### **الامتثال للمعايير:**
- GDPR للخصوصية
- PCI DSS للمدفوعات
- ISO 27001 للأمان
- معايير المحاسبة المحلية

---

## 📱 **دعم الأجهزة المحمولة**

### **التصميم المتجاوب:**
- واجهة محسنة للهواتف
- لمس وإيماءات طبيعية
- تحميل سريع على الشبكات البطيئة
- وضع العمل أوفلاين

### **ميزات الموبايل:**
- مسح الباركود بالكاميرا
- إشعارات push
- مزامنة في الخلفية
- دعم الطابعات المحمولة

---

## 🎓 **التدريب والدعم**

### **مواد التدريب:**
- دليل المستخدم المصور
- فيديوهات تعليمية
- ورش عمل تفاعلية
- اختبارات الكفاءة

### **الدعم الفني:**
- دعم 24/7 عبر الهاتف
- دردشة مباشرة
- تذاكر الدعم الفني
- قاعدة معرفة شاملة

---

## 🚀 **خارطة الطريق التقنية**

### **الإصدار الحالي (v1.0):**
- ✅ جميع الوظائف الأساسية
- ✅ واجهة مستخدم كاملة
- ✅ نظام محاسبي متكامل
- ✅ تقارير شاملة

### **الإصدارات القادمة:**
- **v1.1**: تحسينات الأداء والأمان
- **v1.2**: ميزات ذكية بالذكاء الاصطناعي
- **v2.0**: تطبيق موبايل أصلي
- **v2.1**: تكامل مع أنظمة ERP

---

## 💡 **أفضل الممارسات**

### **للمطورين:**
- استخدام أسماء متغيرات واضحة بالعربية
- كتابة تعليقات مفصلة
- اختبار شامل قبل النشر
- مراجعة الكود بانتظام

### **للمستخدمين:**
- نسخ احتياطية يومية
- تحديث كلمات المرور بانتظام
- مراجعة التقارير المالية
- تدريب الموظفين الجدد

---

## 📞 **معلومات الاتصال والدعم**

### **الدعم الفني:**
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-11-234-5678
- **الموقع**: www.anwar-bakery.com
- **ساعات العمل**: 24/7

### **التطوير والتخصيص:**
- **فريق التطوير**: <EMAIL>
- **طلبات الميزات**: <EMAIL>
- **الاستشارات**: <EMAIL>

---

---

## 🗄️ **هيكل قاعدة البيانات المفصل**

### **الجداول الأساسية:**

#### **1. جدول الشركة (anwar_bakery_company):**
```sql
CREATE TABLE anwar_bakery_company (
    id INT PRIMARY KEY AUTO_INCREMENT,
    companyNameAr VARCHAR(255) NOT NULL,
    companyNameEn VARCHAR(255),
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    city VARCHAR(100),
    currency VARCHAR(10) DEFAULT 'ر.س',
    currencySymbol VARCHAR(5) DEFAULT 'ر.س',
    taxNumber VARCHAR(50),
    logo TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **2. جدول المستخدمين (anwar_bakery_users):**
```sql
CREATE TABLE anwar_bakery_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    fullName VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    role ENUM('admin', 'manager', 'cashier', 'viewer') NOT NULL,
    isActive BOOLEAN DEFAULT TRUE,
    lastLogin TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **3. جدول الفروع (anwar_bakery_branches):**
```sql
CREATE TABLE anwar_bakery_branches (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    manager VARCHAR(255),
    isActive BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **4. جدول المنتجات (anwar_bakery_products):**
```sql
CREATE TABLE anwar_bakery_products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100),
    unit VARCHAR(50) DEFAULT 'قطعة',
    purchasePrice DECIMAL(10,2) DEFAULT 0.00,
    salePrice DECIMAL(10,2) DEFAULT 0.00,
    stock DECIMAL(10,2) DEFAULT 0.00,
    minStock DECIMAL(10,2) DEFAULT 0.00,
    barcode VARCHAR(100),
    isActive BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **5. جدول العملاء (anwar_bakery_customers):**
```sql
CREATE TABLE anwar_bakery_customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    city VARCHAR(100),
    creditLimit DECIMAL(15,2) DEFAULT 0.00,
    currentBalance DECIMAL(15,2) DEFAULT 0.00,
    isActive BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **6. جدول الموردين (anwar_bakery_suppliers):**
```sql
CREATE TABLE anwar_bakery_suppliers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    city VARCHAR(100),
    currentBalance DECIMAL(15,2) DEFAULT 0.00,
    isActive BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **7. جدول الموظفين (anwar_bakery_employees):**
```sql
CREATE TABLE anwar_bakery_employees (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employeeCode VARCHAR(50) UNIQUE NOT NULL,
    fullName VARCHAR(255) NOT NULL,
    position VARCHAR(100),
    department VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    hireDate DATE,
    salary DECIMAL(10,2) DEFAULT 0.00,
    branchId INT,
    isActive BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **8. جدول فواتير المبيعات (anwar_bakery_sales_invoices):**
```sql
CREATE TABLE anwar_bakery_sales_invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoiceNumber VARCHAR(50) UNIQUE NOT NULL,
    date DATE NOT NULL,
    customerId INT,
    customerName VARCHAR(255),
    branchId INT,
    subtotalAmount DECIMAL(15,2) DEFAULT 0.00,
    taxAmount DECIMAL(15,2) DEFAULT 0.00,
    discountAmount DECIMAL(15,2) DEFAULT 0.00,
    totalAmount DECIMAL(15,2) DEFAULT 0.00,
    paidAmount DECIMAL(15,2) DEFAULT 0.00,
    paymentMethod ENUM('cash', 'bank', 'credit') DEFAULT 'cash',
    status ENUM('draft', 'confirmed', 'paid', 'cancelled') DEFAULT 'draft',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **9. جدول أصناف الفواتير (anwar_bakery_invoice_items):**
```sql
CREATE TABLE anwar_bakery_invoice_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoiceId INT NOT NULL,
    invoiceType ENUM('sales', 'purchase', 'sales_return', 'purchase_return'),
    productId INT,
    productCode VARCHAR(50),
    productName VARCHAR(255),
    quantity DECIMAL(10,2) NOT NULL,
    unit VARCHAR(50),
    price DECIMAL(10,2) NOT NULL,
    total DECIMAL(15,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **10. جدول الصناديق (anwar_bakery_cash_registers):**
```sql
CREATE TABLE anwar_bakery_cash_registers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    branchId INT,
    openingBalance DECIMAL(15,2) DEFAULT 0.00,
    currentBalance DECIMAL(15,2) DEFAULT 0.00,
    isActive BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **11. جدول شجرة الحسابات (anwar_bakery_chart_of_accounts):**
```sql
CREATE TABLE anwar_bakery_chart_of_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    accountCode VARCHAR(20) UNIQUE NOT NULL,
    accountName VARCHAR(255) NOT NULL,
    accountType ENUM('assets', 'liabilities', 'equity', 'revenue', 'expense') NOT NULL,
    parentId INT NULL,
    level INT DEFAULT 1,
    openingBalance DECIMAL(15,2) DEFAULT 0.00,
    currentBalance DECIMAL(15,2) DEFAULT 0.00,
    isActive BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **12. جدول السندات المالية (anwar_bakery_vouchers):**
```sql
CREATE TABLE anwar_bakery_vouchers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    voucherNumber VARCHAR(50) UNIQUE NOT NULL,
    type ENUM('receipt', 'payment') NOT NULL,
    date DATE NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    fromAccount VARCHAR(255),
    toAccount VARCHAR(255),
    description TEXT,
    linkedInvoiceId VARCHAR(50),
    cashRegisterId INT,
    status ENUM('draft', 'confirmed', 'cancelled') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🎨 **مواصفات التصميم التفصيلية**

### **1. الـ Sidebar (الشريط الجانبي):**
```html
<div class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg">
    <div class="h-16 px-4 bg-gradient-to-r from-blue-600 to-purple-600">
        <h1 class="text-white text-lg font-bold">مخبز أنوار الحي</h1>
    </div>
    <nav class="flex-1 overflow-y-auto px-2 py-4 space-y-1">
        <!-- قائمة التنقل -->
    </nav>
</div>
```

### **2. تصميم الفواتير:**
```html
<div class="bg-white rounded-lg shadow-md p-6">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">رقم الفاتورة</label>
            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">التاريخ</label>
            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">العميل</label>
            <select class="w-full px-3 py-2 border border-gray-300 rounded-md">
                <option>اختر العميل</option>
            </select>
        </div>
    </div>
</div>
```

### **3. جدول الأصناف:**
```html
<div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الصنف</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">السعر</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجمالي</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">إجراءات</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            <!-- صفوف الأصناف -->
        </tbody>
    </table>
</div>
```

---

## 🔧 **الوظائف الأساسية المطلوبة**

### **1. نظام تسجيل الدخول:**
```javascript
// وظيفة تسجيل الدخول
function login(username, password) {
    // التحقق من بيانات المستخدم
    const users = JSON.parse(localStorage.getItem('anwar_bakery_users') || '[]');
    const user = users.find(u => u.username === username && u.password === password);

    if (user) {
        // حفظ جلسة المستخدم
        const session = {
            userId: user.id,
            fullName: user.fullName,
            role: user.role,
            loginTime: new Date().toISOString()
        };
        localStorage.setItem('anwar_bakery_session', JSON.stringify(session));

        // تحديث آخر تسجيل دخول
        user.lastLogin = new Date().toISOString();
        localStorage.setItem('anwar_bakery_users', JSON.stringify(users));

        return { success: true, user: session };
    }

    return { success: false, message: 'اسم المستخدم أو كلمة المرور غير صحيحة' };
}
```

### **2. إدارة المنتجات:**
```javascript
// إضافة منتج جديد
function addProduct(productData) {
    const products = JSON.parse(localStorage.getItem('anwar_bakery_products') || '[]');

    // التحقق من عدم تكرار الكود
    if (products.find(p => p.code === productData.code)) {
        throw new Error('كود المنتج موجود مسبقاً');
    }

    // إضافة معرف فريد
    productData.id = Date.now();
    productData.created_at = new Date().toISOString();

    products.push(productData);
    localStorage.setItem('anwar_bakery_products', JSON.stringify(products));

    return productData;
}

// تحديث مخزون المنتج
function updateProductStock(productId, quantity, operation = 'subtract') {
    const products = JSON.parse(localStorage.getItem('anwar_bakery_products') || '[]');
    const productIndex = products.findIndex(p => p.id === productId);

    if (productIndex !== -1) {
        if (operation === 'add') {
            products[productIndex].stock += quantity;
        } else {
            products[productIndex].stock -= quantity;
        }

        localStorage.setItem('anwar_bakery_products', JSON.stringify(products));
    }
}
```

### **3. إدارة الفواتير:**
```javascript
// إنشاء فاتورة مبيعات
function createSalesInvoice(invoiceData) {
    const invoices = JSON.parse(localStorage.getItem('anwar_bakery_sales_invoices') || '[]');

    // إنشاء رقم فاتورة تلقائي
    const invoiceNumber = generateInvoiceNumber('sales');

    const invoice = {
        id: Date.now(),
        invoiceNumber: invoiceNumber,
        type: 'sales',
        date: invoiceData.date,
        customerId: invoiceData.customerId,
        customerName: invoiceData.customerName,
        branchId: invoiceData.branchId,
        items: invoiceData.items,
        subtotalAmount: calculateSubtotal(invoiceData.items),
        taxAmount: calculateTax(invoiceData.items),
        totalAmount: calculateTotal(invoiceData.items),
        paidAmount: invoiceData.paidAmount || 0,
        paymentMethod: invoiceData.paymentMethod || 'cash',
        status: 'confirmed',
        notes: invoiceData.notes || '',
        created_at: new Date().toISOString()
    };

    // حفظ الفاتورة
    invoices.push(invoice);
    localStorage.setItem('anwar_bakery_sales_invoices', JSON.stringify(invoices));

    // تحديث المخزون
    updateInventoryFromInvoice(invoice.items, 'subtract');

    // تحديث رصيد العميل
    updateCustomerBalance(invoice.customerId, invoice.totalAmount - invoice.paidAmount);

    // إنشاء قيد محاسبي
    createJournalEntry(invoice);

    return invoice;
}
```

### **4. نظام نقطة البيع (POS):**
```javascript
// إضافة صنف إلى سلة POS
function addToPOSCart(productId, quantity = 1) {
    const products = JSON.parse(localStorage.getItem('anwar_bakery_products') || '[]');
    const product = products.find(p => p.id === productId);

    if (!product) {
        throw new Error('المنتج غير موجود');
    }

    if (product.stock < quantity) {
        throw new Error('الكمية المطلوبة غير متوفرة في المخزون');
    }

    let cart = JSON.parse(localStorage.getItem('pos_cart') || '[]');
    const existingItem = cart.find(item => item.productId === productId);

    if (existingItem) {
        existingItem.quantity += quantity;
        existingItem.total = existingItem.quantity * existingItem.price;
    } else {
        cart.push({
            productId: productId,
            name: product.name,
            code: product.code,
            price: product.salePrice,
            quantity: quantity,
            unit: product.unit,
            total: quantity * product.salePrice
        });
    }

    localStorage.setItem('pos_cart', JSON.stringify(cart));
    updatePOSDisplay();
}
```

### **5. النظام المحاسبي:**
```javascript
// إنشاء قيد محاسبي
function createJournalEntry(invoiceData) {
    const entries = JSON.parse(localStorage.getItem('anwar_bakery_journal_entries') || '[]');

    const entry = {
        id: Date.now(),
        date: invoiceData.date,
        description: `فاتورة ${invoiceData.type} رقم ${invoiceData.invoiceNumber}`,
        reference: invoiceData.invoiceNumber,
        debits: [],
        credits: [],
        totalAmount: invoiceData.totalAmount,
        created_at: new Date().toISOString()
    };

    if (invoiceData.type === 'sales') {
        // مدين: الصندوق أو العميل
        if (invoiceData.paidAmount > 0) {
            entry.debits.push({
                accountCode: '1001', // الصندوق
                accountName: 'الصندوق',
                amount: invoiceData.paidAmount
            });
        }

        if (invoiceData.totalAmount > invoiceData.paidAmount) {
            entry.debits.push({
                accountCode: '1201', // العملاء
                accountName: invoiceData.customerName,
                amount: invoiceData.totalAmount - invoiceData.paidAmount
            });
        }

        // دائن: المبيعات
        entry.credits.push({
            accountCode: '4001', // المبيعات
            accountName: 'مبيعات',
            amount: invoiceData.totalAmount
        });
    }

    entries.push(entry);
    localStorage.setItem('anwar_bakery_journal_entries', JSON.stringify(entries));

    // تحديث أرصدة الحسابات
    updateAccountBalances(entry);
}
```

---

## 📊 **نظام التقارير المطلوب**

### **1. تقرير المبيعات اليومي:**
```javascript
function generateDailySalesReport(date) {
    const invoices = JSON.parse(localStorage.getItem('anwar_bakery_sales_invoices') || '[]');
    const dailyInvoices = invoices.filter(inv => inv.date === date);

    const report = {
        date: date,
        totalInvoices: dailyInvoices.length,
        totalAmount: dailyInvoices.reduce((sum, inv) => sum + inv.totalAmount, 0),
        totalPaid: dailyInvoices.reduce((sum, inv) => sum + inv.paidAmount, 0),
        totalCredit: dailyInvoices.reduce((sum, inv) => sum + (inv.totalAmount - inv.paidAmount), 0),
        paymentMethods: {
            cash: dailyInvoices.filter(inv => inv.paymentMethod === 'cash').length,
            bank: dailyInvoices.filter(inv => inv.paymentMethod === 'bank').length,
            credit: dailyInvoices.filter(inv => inv.paymentMethod === 'credit').length
        },
        topProducts: getTopSellingProducts(dailyInvoices),
        hourlyBreakdown: getHourlyBreakdown(dailyInvoices)
    };

    return report;
}
```

### **2. تقرير المخزون:**
```javascript
function generateInventoryReport() {
    const products = JSON.parse(localStorage.getItem('anwar_bakery_products') || '[]');

    const report = {
        totalProducts: products.length,
        totalValue: products.reduce((sum, p) => sum + (p.stock * p.purchasePrice), 0),
        lowStockItems: products.filter(p => p.stock <= p.minStock),
        outOfStockItems: products.filter(p => p.stock <= 0),
        topValueItems: products.sort((a, b) => (b.stock * b.purchasePrice) - (a.stock * a.purchasePrice)).slice(0, 10),
        categoryBreakdown: getCategoryBreakdown(products)
    };

    return report;
}
```

---

## 🎯 **متطلبات الواجهة المحددة**

### **1. لوحة التحكم الرئيسية:**
- عرض إحصائيات سريعة (مبيعات اليوم، المخزون، العملاء)
- رسوم بيانية للمبيعات الأسبوعية
- قائمة بآخر الفواتير
- تنبيهات المخزون المنخفض
- ساعة وتاريخ مباشر

### **2. صفحة الفواتير:**
- جدول بجميع الفواتير مع إمكانية البحث والفلترة
- أزرار لإنشاء فاتورة جديدة
- عرض تفاصيل الفاتورة في نافذة منبثقة
- طباعة وتصدير الفواتير
- تغيير حالة الفاتورة

### **3. نظام نقطة البيع:**
- شاشة منقسمة: منتجات على اليسار، سلة على اليمين
- بحث سريع بالاسم أو الباركود
- حاسبة مدمجة
- اختيار طريقة الدفع
- طباعة فورية للإيصال

### **4. إدارة المنتجات:**
- جدول بجميع المنتجات
- نموذج إضافة/تعديل منتج
- رفع صور المنتجات
- إدارة الفئات
- تتبع حركة المخزون

---

## 🔒 **متطلبات الأمان**

### **1. المصادقة:**
- تشفير كلمات المرور
- جلسات محدودة الوقت
- تسجيل خروج تلقائي
- حماية من هجمات القوة الغاشمة

### **2. الصلاحيات:**
- مدير النظام: جميع الصلاحيات
- المدير: إدارة البيانات والتقارير
- الكاشير: المبيعات والمدفوعات فقط
- المشاهد: عرض التقارير فقط

### **3. حماية البيانات:**
- نسخ احتياطية تلقائية
- تشفير البيانات الحساسة
- سجل مراجعة للعمليات
- استرداد البيانات المحذوفة

---

**🎉 نظام إدارة مخبز أنوار الحي - الحل الأمثل والأكثر تطوراً لإدارة المخابز والحلويات في العالم العربي! 🚀**

**💫 مطور بعناية فائقة ليجمع بين البساطة في الاستخدام والقوة في الأداء والشمولية في الوظائف! ✨**

**📋 هذا البروميت يحتوي على جميع التفاصيل اللازمة لإعادة بناء النظام من الصفر بنفس المواصفات والجودة! 🎯**
