// Sample data
let branches = [
    {
        id: 1,
        branchCode: 'BR001',
        branchName: 'فرع الرياض الرئيسي',
        city: 'الرياض',
        district: 'العليا',
        address: 'شارع الملك فهد، حي العليا، الرياض',
        manager: 'أحم<PERSON> محمد السعد',
        phone: '0112345678',
        email: '<EMAIL>',
        openingDate: '2020-01-15',
        isActive: true,
        notes: 'الفرع الرئيسي للشركة',
        createdAt: '2020-01-15'
    },
    {
        id: 2,
        branchCode: 'BR002',
        branchName: 'فرع جدة',
        city: 'جدة',
        district: 'الروضة',
        address: 'شارع الأمير سلطان، حي الروضة، جدة',
        manager: 'فاطمة علي الزهراني',
        phone: '0122345678',
        email: '<EMAIL>',
        openingDate: '2021-03-10',
        isActive: true,
        notes: 'فرع جدة الجديد',
        createdAt: '2021-03-10'
    }
];

let warehouses = [
    {
        id: 1,
        warehouseCode: 'WH001',
        warehouseName: 'المخزن الرئيسي - الرياض',
        branchId: 1,
        branchName: 'فرع الرياض الرئيسي',
        type: 'main',
        capacity: 1000,
        area: 500,
        unit: 'متر مربع',
        isActive: true,
        notes: 'المخزن الرئيسي للفرع',
        createdAt: '2020-01-15'
    },
    {
        id: 2,
        warehouseCode: 'WH002',
        warehouseName: 'مخزن المبردات - الرياض',
        branchId: 1,
        branchName: 'فرع الرياض الرئيسي',
        type: 'cold',
        capacity: 200,
        area: 100,
        unit: 'متر مربع',
        isActive: true,
        notes: 'مخزن المواد المبردة',
        createdAt: '2020-01-15'
    },
    {
        id: 3,
        warehouseCode: 'WH003',
        warehouseName: 'المخزن الرئيسي - جدة',
        branchId: 2,
        branchName: 'فرع جدة',
        type: 'main',
        capacity: 800,
        area: 400,
        unit: 'متر مربع',
        isActive: true,
        notes: 'المخزن الرئيسي لفرع جدة',
        createdAt: '2021-03-10'
    }
];

let inventoryDistribution = [
    {
        id: 1,
        itemCode: 'RAW001',
        itemName: 'دقيق أبيض',
        branchId: 1,
        branchName: 'فرع الرياض الرئيسي',
        warehouseId: 1,
        warehouseName: 'المخزن الرئيسي - الرياض',
        quantity: 150,
        unit: 'كيلوجرام',
        lastUpdated: '2024-01-15'
    },
    {
        id: 2,
        itemCode: 'RAW001',
        itemName: 'دقيق أبيض',
        branchId: 2,
        branchName: 'فرع جدة',
        warehouseId: 3,
        warehouseName: 'المخزن الرئيسي - جدة',
        quantity: 100,
        unit: 'كيلوجرام',
        lastUpdated: '2024-01-15'
    },
    {
        id: 3,
        itemCode: 'FIN001',
        itemName: 'خبز أبيض',
        branchId: 1,
        branchName: 'فرع الرياض الرئيسي',
        warehouseId: 1,
        warehouseName: 'المخزن الرئيسي - الرياض',
        quantity: 85,
        unit: 'قطعة',
        lastUpdated: '2024-01-15'
    }
];

// Cashboxes data
let cashboxes = [
    {
        id: 1,
        cashboxCode: 'CB001',
        cashboxName: 'الصندوق الرئيسي - الرياض',
        branchId: 1,
        branchName: 'فرع الرياض الرئيسي',
        currentBalance: 50000,
        currency: 'SAR',
        responsible: 'أحمد محمد السعد',
        isActive: true,
        notes: 'الصندوق الرئيسي للفرع',
        createdAt: '2020-01-15'
    },
    {
        id: 2,
        cashboxCode: 'CB002',
        cashboxName: 'صندوق المبيعات - الرياض',
        branchId: 1,
        branchName: 'فرع الرياض الرئيسي',
        currentBalance: 25000,
        currency: 'SAR',
        responsible: 'سارة أحمد الفهد',
        isActive: true,
        notes: 'صندوق مبيعات الفرع',
        createdAt: '2020-01-15'
    },
    {
        id: 3,
        cashboxCode: 'CB003',
        cashboxName: 'الصندوق الرئيسي - جدة',
        branchId: 2,
        branchName: 'فرع جدة',
        currentBalance: 35000,
        currency: 'SAR',
        responsible: 'فاطمة علي الزهراني',
        isActive: true,
        notes: 'الصندوق الرئيسي لفرع جدة',
        createdAt: '2021-03-10'
    }
];

// Bank accounts data
let bankAccounts = [
    {
        id: 1,
        accountNumber: '************',
        accountName: 'مخبز أنوار الحي - حساب جاري',
        bankName: 'الراجحي',
        accountType: 'current',
        currentBalance: 150000,
        currency: 'SAR',
        iban: 'SA************3456789012',
        swiftCode: 'RJHISARI',
        isActive: true,
        notes: 'الحساب الرئيسي للشركة',
        createdAt: '2020-01-15'
    },
    {
        id: 2,
        accountNumber: '************',
        accountName: 'مخبز أنوار الحي - حساب توفير',
        bankName: 'الأهلي',
        accountType: 'savings',
        currentBalance: 75000,
        currency: 'SAR',
        iban: 'SA************7654321098',
        swiftCode: 'NCBKSAJE',
        isActive: true,
        notes: 'حساب التوفير للطوارئ',
        createdAt: '2020-06-01'
    },
    {
        id: 3,
        accountNumber: '************',
        accountName: 'مخبز أنوار الحي - فرع جدة',
        bankName: 'سامبا',
        accountType: 'current',
        currentBalance: 85000,
        currency: 'SAR',
        iban: 'SA************5556667778',
        swiftCode: 'SAMBSARI',
        isActive: true,
        notes: 'حساب خاص بفرع جدة',
        createdAt: '2021-03-10'
    }
];

// Show message function
function showMessage(message, type = 'info') {
    const messageContainer = document.getElementById('messageContainer');
    if (!messageContainer) return;

    const alertClass = {
        'success': 'bg-green-100 border-green-400 text-green-700',
        'error': 'bg-red-100 border-red-400 text-red-700',
        'warning': 'bg-yellow-100 border-yellow-400 text-yellow-700',
        'info': 'bg-blue-100 border-blue-400 text-blue-700'
    };

    const iconClass = {
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'info': 'ℹ️'
    };

    messageContainer.innerHTML = `
        <div class="border-l-4 p-4 rounded ${alertClass[type]} mb-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <span class="text-lg">${iconClass[type]}</span>
                </div>
                <div class="mr-3">
                    <p class="text-sm font-medium">${message}</p>
                </div>
            </div>
        </div>
    `;

    // Auto hide after 5 seconds
    setTimeout(() => {
        if (messageContainer) {
            messageContainer.innerHTML = '';
        }
    }, 5000);
}

// Check authentication
function checkAuth() {
    const session = localStorage.getItem('anwar_bakery_session') ||
                   sessionStorage.getItem('anwar_bakery_session');

    if (!session) {
        window.location.href = 'login.html';
        return null;
    }

    return JSON.parse(session);
}

// Load user info
function loadUserInfo() {
    const session = checkAuth();
    if (session) {
        document.getElementById('userFullName').textContent = session.fullName;
        document.getElementById('userRole').textContent = session.role;
    }
}

// Load company info
function loadCompanyInfo() {
    // Use new settings system if available
    if (window.appSettings) {
        const companyName = window.appSettings.get('company', 'companyNameAr');
        if (companyName) {
            document.getElementById('sidebarCompanyName').textContent = companyName;
            document.title = `إدارة الفروع والمخازن - ${companyName}`;
        }
    } else {
        // Fallback to old system
        const savedData = localStorage.getItem('anwar_bakery_company');
        if (savedData) {
            const companyData = JSON.parse(savedData);

            if (companyData.companyNameAr) {
                document.getElementById('sidebarCompanyName').textContent = companyData.companyNameAr;
                document.title = `إدارة الفروع والمخازن - ${companyData.companyNameAr}`;
            }
        }
    }
}

// Load branches from localStorage
function loadBranches() {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    if (savedBranches) {
        branches = JSON.parse(savedBranches);
    }
    renderBranches();
    updateStats();
    populateBranchFilters();
}

// Save branches to localStorage
function saveBranches() {
    localStorage.setItem('anwar_bakery_branches', JSON.stringify(branches));
}

// Load warehouses from localStorage
function loadWarehouses() {
    const savedWarehouses = localStorage.getItem('anwar_bakery_warehouses');
    if (savedWarehouses) {
        warehouses = JSON.parse(savedWarehouses);
    }
    renderWarehouses();
    updateStats();
}

// Save warehouses to localStorage
function saveWarehouses() {
    localStorage.setItem('anwar_bakery_warehouses', JSON.stringify(warehouses));
}

// Load inventory distribution
function loadInventoryDistribution() {
    const savedInventory = localStorage.getItem('anwar_bakery_inventory_distribution');
    if (savedInventory) {
        inventoryDistribution = JSON.parse(savedInventory);
    }
    renderInventoryDistribution();
}

// Save inventory distribution
function saveInventoryDistribution() {
    localStorage.setItem('anwar_bakery_inventory_distribution', JSON.stringify(inventoryDistribution));
}

// Load cashboxes from localStorage
function loadCashboxes() {
    const savedCashboxes = localStorage.getItem('anwar_bakery_cash_registers');
    if (savedCashboxes) {
        cashboxes = JSON.parse(savedCashboxes);
    }
    renderCashboxes();
    updateStats();
    populateCashboxFilters();
}

// Save cashboxes to localStorage
function saveCashboxes() {
    localStorage.setItem('anwar_bakery_cash_registers', JSON.stringify(cashboxes));
}

// Update statistics
function updateStats() {
    const totalBranches = branches.length;
    const totalWarehouses = warehouses.length;
    const activeBranches = branches.filter(branch => branch.isActive).length;
    const activeWarehouses = warehouses.filter(warehouse => warehouse.isActive).length;

    // Get products count from localStorage
    const savedProducts = localStorage.getItem('anwar_bakery_products');
    let totalProducts = 0;
    if (savedProducts) {
        try {
            const products = JSON.parse(savedProducts);
            totalProducts = products.filter(product => product.isActive !== false).length;
        } catch (error) {
            console.error('Error parsing products:', error);
        }
    }

    document.getElementById('totalBranches').textContent = totalBranches;
    document.getElementById('totalWarehouses').textContent = totalWarehouses;
    document.getElementById('activeBranches').textContent = activeBranches;
    document.getElementById('activeWarehouses').textContent = activeWarehouses;
    document.getElementById('totalProducts').textContent = totalProducts;
}

// Render branches table
function renderBranches(filteredBranches = branches) {
    const tbody = document.getElementById('branchesTableBody');
    tbody.innerHTML = '';

    filteredBranches.forEach(branch => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';

        const warehouseCount = warehouses.filter(w => w.branchId === branch.id).length;
        const statusClass = branch.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
        const statusText = branch.isActive ? 'نشط' : 'غير نشط';

        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${branch.branchCode}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${branch.branchName}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${branch.city}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${branch.manager || '-'}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${branch.phone || '-'}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${warehouseCount}</td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                    ${statusText}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button onclick="editBranch(${branch.id})" class="text-blue-600 hover:text-blue-900">تعديل</button>
                <button onclick="toggleBranchStatus(${branch.id})" class="text-yellow-600 hover:text-yellow-900">
                    ${branch.isActive ? 'إلغاء تفعيل' : 'تفعيل'}
                </button>
                <button onclick="deleteBranch(${branch.id})" class="text-red-600 hover:text-red-900">حذف</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Render warehouses table
function renderWarehouses(filteredWarehouses = warehouses) {
    const tbody = document.getElementById('warehousesTableBody');
    tbody.innerHTML = '';

    filteredWarehouses.forEach(warehouse => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';

        const typeNames = {
            'main': 'رئيسي',
            'secondary': 'فرعي',
            'cold': 'مبردات',
            'dry': 'جاف',
            'finished': 'منتجات نهائية'
        };

        const statusClass = warehouse.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
        const statusText = warehouse.isActive ? 'نشط' : 'غير نشط';

        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${warehouse.warehouseCode}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${warehouse.warehouseName}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${warehouse.branchName}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${typeNames[warehouse.type]}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${warehouse.capacity} ${warehouse.unit}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${warehouse.area} ${warehouse.unit}</td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                    ${statusText}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button onclick="editWarehouse(${warehouse.id})" class="text-blue-600 hover:text-blue-900">تعديل</button>
                <button onclick="toggleWarehouseStatus(${warehouse.id})" class="text-yellow-600 hover:text-yellow-900">
                    ${warehouse.isActive ? 'إلغاء تفعيل' : 'تفعيل'}
                </button>
                <button onclick="deleteWarehouse(${warehouse.id})" class="text-red-600 hover:text-red-900">حذف</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Render inventory distribution table
function renderInventoryDistribution(filteredInventory = inventoryDistribution) {
    const tbody = document.getElementById('inventoryTableBody');
    tbody.innerHTML = '';

    filteredInventory.forEach(item => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';

        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${item.itemCode}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.itemName}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.branchName}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.warehouseName}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.quantity}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.unit}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.lastUpdated}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button onclick="editInventoryItem(${item.id})" class="text-blue-600 hover:text-blue-900">تعديل</button>
                <button onclick="transferInventory(${item.id})" class="text-purple-600 hover:text-purple-900">نقل</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Populate branch filters
function populateBranchFilters() {
    const warehouseBranchFilter = document.getElementById('warehouseBranchFilter');
    const inventoryBranchFilter = document.getElementById('inventoryBranchFilter');
    const cashboxBranchFilter = document.getElementById('cashboxBranchFilter');

    // Clear existing options (keep first option)
    if (warehouseBranchFilter) {
        warehouseBranchFilter.innerHTML = '<option value="">جميع الفروع</option>';
    }
    if (inventoryBranchFilter) {
        inventoryBranchFilter.innerHTML = '<option value="">جميع الفروع</option>';
    }
    if (cashboxBranchFilter) {
        cashboxBranchFilter.innerHTML = '<option value="">جميع الفروع</option>';
    }

    branches.forEach(branch => {
        if (warehouseBranchFilter) {
            const option1 = document.createElement('option');
            option1.value = branch.id;
            option1.textContent = branch.branchName;
            warehouseBranchFilter.appendChild(option1);
        }

        if (inventoryBranchFilter) {
            const option2 = document.createElement('option');
            option2.value = branch.id;
            option2.textContent = branch.branchName;
            inventoryBranchFilter.appendChild(option2);
        }

        if (cashboxBranchFilter) {
            const option3 = document.createElement('option');
            option3.value = branch.id;
            option3.textContent = branch.branchName;
            cashboxBranchFilter.appendChild(option3);
        }
    });
}

// Populate cashbox modal branches
function populateCashboxBranches() {
    const cashboxBranch = document.getElementById('cashboxBranch');
    if (!cashboxBranch) return;

    cashboxBranch.innerHTML = '<option value="">اختر الفرع</option>';
    branches.forEach(branch => {
        const option = document.createElement('option');
        option.value = branch.id;
        option.textContent = branch.branchName;
        option.dataset.branchName = branch.branchName;
        cashboxBranch.appendChild(option);
    });
}

// Update employees based on selected branch
function updateCashboxEmployees() {
    const branchId = document.getElementById('cashboxBranch').value;
    const employeeSelect = document.getElementById('cashboxResponsible');

    if (!employeeSelect) return;

    employeeSelect.innerHTML = '<option value="">اختر الموظف المسؤول</option>';

    if (!branchId) return;

    // Load employees from localStorage
    const savedEmployees = localStorage.getItem('anwar_bakery_employees');
    if (savedEmployees) {
        try {
            const employees = JSON.parse(savedEmployees);
            const branchEmployees = employees.filter(emp =>
                emp.branchId == branchId && emp.status === 'active'
            );

            branchEmployees.forEach(employee => {
                const option = document.createElement('option');
                option.value = employee.id;
                option.textContent = `${employee.fullName} (${employee.employeeNumber})`;
                option.dataset.employeeName = employee.fullName;
                employeeSelect.appendChild(option);
            });

            if (branchEmployees.length === 0) {
                const option = document.createElement('option');
                option.value = '';
                option.textContent = 'لا يوجد موظفين نشطين في هذا الفرع';
                option.disabled = true;
                employeeSelect.appendChild(option);
            }
        } catch (error) {
            console.error('Error loading employees:', error);
        }
    }
}

// Main tab switching
function switchMainTab(tabName) {
    console.log('Switching to tab:', tabName);

    // Hide all tab contents
    document.querySelectorAll('.main-tab-content').forEach(tab => {
        tab.classList.remove('active');
        tab.style.display = 'none';
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.main-tab-button').forEach(button => {
        button.classList.remove('border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500');
    });

    // Show selected tab content
    const selectedTab = document.getElementById(tabName + 'Tab');
    if (selectedTab) {
        selectedTab.classList.add('active');
        selectedTab.style.display = 'block';
        console.log('Tab activated:', tabName + 'Tab');
    } else {
        console.error('Tab not found:', tabName + 'Tab');
    }

    // Activate selected tab button - find by onclick attribute
    document.querySelectorAll('.main-tab-button').forEach(button => {
        if (button.getAttribute('onclick') && button.getAttribute('onclick').includes(tabName)) {
            button.classList.remove('border-transparent', 'text-gray-500');
            button.classList.add('border-blue-500', 'text-blue-600');
        }
    });

    // Load data for the selected tab
    switch(tabName) {
        case 'warehouses':
            loadWarehouses();
            break;
        case 'inventory':
            loadInventoryDistribution();
            break;
        default:
            loadBranches();
    }
}

// Filter functions
function filterBranches() {
    const searchTerm = document.getElementById('branchSearchInput').value.toLowerCase();
    const statusFilter = document.getElementById('branchStatusFilter').value;

    const filtered = branches.filter(branch => {
        const matchesSearch = branch.branchName.toLowerCase().includes(searchTerm) ||
                            branch.branchCode.toLowerCase().includes(searchTerm) ||
                            branch.city.toLowerCase().includes(searchTerm);
        let matchesStatus = true;

        if (statusFilter === 'active') {
            matchesStatus = branch.isActive;
        } else if (statusFilter === 'inactive') {
            matchesStatus = !branch.isActive;
        }

        return matchesSearch && matchesStatus;
    });

    renderBranches(filtered);
}

function filterWarehouses() {
    const searchTerm = document.getElementById('warehouseSearchInput').value.toLowerCase();
    const branchFilter = document.getElementById('warehouseBranchFilter').value;
    const statusFilter = document.getElementById('warehouseStatusFilter').value;

    const filtered = warehouses.filter(warehouse => {
        const matchesSearch = warehouse.warehouseName.toLowerCase().includes(searchTerm) ||
                            warehouse.warehouseCode.toLowerCase().includes(searchTerm);
        const matchesBranch = !branchFilter || warehouse.branchId == branchFilter;
        let matchesStatus = true;

        if (statusFilter === 'active') {
            matchesStatus = warehouse.isActive;
        } else if (statusFilter === 'inactive') {
            matchesStatus = !warehouse.isActive;
        }

        return matchesSearch && matchesBranch && matchesStatus;
    });

    renderWarehouses(filtered);
}

function filterInventory() {
    const searchTerm = document.getElementById('inventorySearchInput').value.toLowerCase();
    const branchFilter = document.getElementById('inventoryBranchFilter').value;
    const warehouseFilter = document.getElementById('inventoryWarehouseFilter').value;

    const filtered = inventoryDistribution.filter(item => {
        const matchesSearch = item.itemName.toLowerCase().includes(searchTerm) ||
                            item.itemCode.toLowerCase().includes(searchTerm);
        const matchesBranch = !branchFilter || item.branchId == branchFilter;
        const matchesWarehouse = !warehouseFilter || item.warehouseId == warehouseFilter;

        return matchesSearch && matchesBranch && matchesWarehouse;
    });

    renderInventoryDistribution(filtered);
}

// Render cashboxes table
function renderCashboxes(filteredCashboxes = cashboxes) {
    const tbody = document.getElementById('cashboxesTableBody');
    tbody.innerHTML = '';

    filteredCashboxes.forEach(cashbox => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';

        const statusClass = cashbox.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
        const statusText = cashbox.isActive ? 'نشط' : 'غير نشط';

        // Get company currency
        const company = getCompanyInfo();
        const currency = company.currency || 'ر.س';

        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${cashbox.cashboxCode || cashbox.code || '-'}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${cashbox.cashboxName || cashbox.name || '-'}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${cashbox.branchName || '-'}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold text-blue-600">${(cashbox.openingBalance || 0).toLocaleString()} ${currency}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold ${(cashbox.currentBalance || 0) >= 0 ? 'text-green-600' : 'text-red-600'}">${(cashbox.currentBalance || 0).toLocaleString()} ${currency}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${cashbox.currency || currency}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${cashbox.responsibleEmployeeName || cashbox.responsible || '-'}</td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                    ${statusText}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button onclick="editCashbox(${cashbox.id})" class="text-blue-600 hover:text-blue-900">تعديل</button>
                <button onclick="cashboxTransactions(${cashbox.id})" class="text-green-600 hover:text-green-900">العمليات</button>
                <button onclick="toggleCashboxStatus(${cashbox.id})" class="text-yellow-600 hover:text-yellow-900">
                    ${cashbox.isActive ? 'إلغاء تفعيل' : 'تفعيل'}
                </button>
                <button onclick="deleteCashbox(${cashbox.id})" class="text-red-600 hover:text-red-900">حذف</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Get company info helper
function getCompanyInfo() {
    try {
        return JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
    } catch {
        return {};
    }
}

// Render bank accounts table
function renderBankAccounts(filteredAccounts = bankAccounts) {
    const tbody = document.getElementById('bankAccountsTableBody');
    tbody.innerHTML = '';

    const accountTypeNames = {
        'current': 'جاري',
        'savings': 'توفير',
        'investment': 'استثماري',
        'loan': 'قرض'
    };

    filteredAccounts.forEach(account => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';

        const statusClass = account.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
        const statusText = account.isActive ? 'نشط' : 'غير نشط';

        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${account.accountNumber}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${account.accountName}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${account.bankName}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${accountTypeNames[account.accountType]}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold ${account.currentBalance >= 0 ? 'text-green-600' : 'text-red-600'}">${account.currentBalance.toLocaleString()}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${account.currency}</td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                    ${statusText}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button onclick="editBankAccount(${account.id})" class="text-blue-600 hover:text-blue-900">تعديل</button>
                <button onclick="bankTransactions(${account.id})" class="text-green-600 hover:text-green-900">العمليات</button>
                <button onclick="toggleBankAccountStatus(${account.id})" class="text-yellow-600 hover:text-yellow-900">
                    ${account.isActive ? 'إلغاء تفعيل' : 'تفعيل'}
                </button>
                <button onclick="deleteBankAccount(${account.id})" class="text-red-600 hover:text-red-900">حذف</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Filter cashboxes
function filterCashboxes() {
    const searchTerm = document.getElementById('cashboxSearchInput').value.toLowerCase();
    const branchFilter = document.getElementById('cashboxBranchFilter').value;
    const statusFilter = document.getElementById('cashboxStatusFilter').value;

    const filtered = cashboxes.filter(cashbox => {
        const matchesSearch = cashbox.cashboxName.toLowerCase().includes(searchTerm) ||
                            cashbox.cashboxCode.toLowerCase().includes(searchTerm) ||
                            cashbox.responsible.toLowerCase().includes(searchTerm);
        const matchesBranch = !branchFilter || cashbox.branchId == branchFilter;
        let matchesStatus = true;

        if (statusFilter === 'active') {
            matchesStatus = cashbox.isActive;
        } else if (statusFilter === 'inactive') {
            matchesStatus = !cashbox.isActive;
        }

        return matchesSearch && matchesBranch && matchesStatus;
    });

    renderCashboxes(filtered);
}

// Filter bank accounts
function filterBankAccounts() {
    const searchTerm = document.getElementById('bankSearchInput').value.toLowerCase();
    const bankFilter = document.getElementById('bankNameFilter').value;
    const statusFilter = document.getElementById('bankStatusFilter').value;

    const filtered = bankAccounts.filter(account => {
        const matchesSearch = account.accountName.toLowerCase().includes(searchTerm) ||
                            account.accountNumber.toLowerCase().includes(searchTerm) ||
                            account.iban.toLowerCase().includes(searchTerm);
        const matchesBank = !bankFilter || account.bankName.includes(bankFilter);
        let matchesStatus = true;

        if (statusFilter === 'active') {
            matchesStatus = account.isActive;
        } else if (statusFilter === 'inactive') {
            matchesStatus = !account.isActive;
        }

        return matchesSearch && matchesBank && matchesStatus;
    });

    renderBankAccounts(filtered);
}

// Branch modal functions
function openAddBranchModal() {
    document.getElementById('branchModalTitle').textContent = 'إضافة فرع جديد';
    document.getElementById('branchForm').reset();
    document.getElementById('branchId').value = '';
    document.getElementById('branchIsActive').checked = true;
    document.getElementById('branchModal').classList.add('active');
}

function editBranch(id) {
    const branch = branches.find(b => b.id === id);
    if (branch) {
        document.getElementById('branchModalTitle').textContent = 'تعديل الفرع';
        document.getElementById('branchId').value = branch.id;
        document.getElementById('branchCode').value = branch.branchCode;
        document.getElementById('branchName').value = branch.branchName;
        document.getElementById('branchCity').value = branch.city;
        document.getElementById('branchDistrict').value = branch.district || '';
        document.getElementById('branchAddress').value = branch.address || '';
        document.getElementById('branchManager').value = branch.manager || '';
        document.getElementById('branchPhone').value = branch.phone || '';
        document.getElementById('branchEmail').value = branch.email || '';
        document.getElementById('branchOpeningDate').value = branch.openingDate || '';
        document.getElementById('branchIsActive').checked = branch.isActive;
        document.getElementById('branchNotes').value = branch.notes || '';

        document.getElementById('branchModal').classList.add('active');
    }
}

function closeBranchModal() {
    document.getElementById('branchModal').classList.remove('active');
}

function saveBranch() {
    const form = document.getElementById('branchForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const branchId = document.getElementById('branchId').value;
    const branchData = {
        branchCode: document.getElementById('branchCode').value,
        branchName: document.getElementById('branchName').value,
        city: document.getElementById('branchCity').value,
        district: document.getElementById('branchDistrict').value,
        address: document.getElementById('branchAddress').value,
        manager: document.getElementById('branchManager').value,
        phone: document.getElementById('branchPhone').value,
        email: document.getElementById('branchEmail').value,
        openingDate: document.getElementById('branchOpeningDate').value,
        isActive: document.getElementById('branchIsActive').checked,
        notes: document.getElementById('branchNotes').value
    };

    // Check if branch code already exists
    const existingBranch = branches.find(b => b.branchCode === branchData.branchCode && b.id != branchId);
    if (existingBranch) {
        showMessage('كود الفرع موجود مسبقاً!', 'error');
        return;
    }

    if (branchId) {
        // Edit existing branch
        const branchIndex = branches.findIndex(b => b.id == branchId);
        if (branchIndex !== -1) {
            branches[branchIndex] = {
                ...branches[branchIndex],
                ...branchData
            };
        }
    } else {
        // Add new branch
        const newBranch = {
            id: Math.max(...branches.map(b => b.id), 0) + 1,
            ...branchData,
            createdAt: new Date().toISOString().split('T')[0]
        };
        branches.push(newBranch);
    }

    saveBranches();
    renderBranches();
    updateStats();
    populateBranchFilters();
    closeBranchModal();
    showMessage('تم حفظ الفرع بنجاح!', 'success');
}

// Toggle branch status
function toggleBranchStatus(id) {
    const branch = branches.find(b => b.id === id);
    if (branch) {
        branch.isActive = !branch.isActive;
        saveBranches();
        renderBranches();
        updateStats();
        showMessage(`تم ${branch.isActive ? 'تفعيل' : 'إلغاء تفعيل'} الفرع بنجاح!`, 'success');
    }
}

// Delete branch
function deleteBranch(id) {
    if (confirm('هل أنت متأكد من حذف هذا الفرع؟ سيتم حذف جميع المخازن التابعة له أيضاً.')) {
        branches = branches.filter(b => b.id !== id);
        warehouses = warehouses.filter(w => w.branchId !== id);
        inventoryDistribution = inventoryDistribution.filter(i => i.branchId !== id);

        saveBranches();
        saveWarehouses();
        saveInventoryDistribution();
        renderBranches();
        renderWarehouses();
        renderInventoryDistribution();
        updateStats();
        populateBranchFilters();
        showMessage('تم حذف الفرع وجميع مخازنه بنجاح!', 'success');
    }
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (sidebar.classList.contains('translate-x-full')) {
        sidebar.classList.remove('translate-x-full');
        overlay.classList.remove('hidden');
    } else {
        sidebar.classList.add('translate-x-full');
        overlay.classList.add('hidden');
    }
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_session');
        sessionStorage.removeItem('anwar_bakery_session');
        window.location.href = 'login.html';
    }
}

// Warehouse modal functions
function openAddWarehouseModal() {
    document.getElementById('warehouseModalTitle').textContent = 'إضافة مخزن جديد';
    document.getElementById('warehouseForm').reset();
    document.getElementById('warehouseId').value = '';
    document.getElementById('warehouseIsActive').checked = true;
    document.getElementById('warehouseUnit').value = 'متر مربع';
    populateWarehouseBranches();
    document.getElementById('warehouseModal').classList.add('active');
}

function editWarehouse(id) {
    const warehouse = warehouses.find(w => w.id === id);
    if (warehouse) {
        document.getElementById('warehouseModalTitle').textContent = 'تعديل المخزن';
        document.getElementById('warehouseId').value = warehouse.id;
        document.getElementById('warehouseCode').value = warehouse.warehouseCode;
        document.getElementById('warehouseName').value = warehouse.warehouseName;
        document.getElementById('warehouseBranch').value = warehouse.branchId;
        document.getElementById('warehouseType').value = warehouse.type;
        document.getElementById('warehouseCapacity').value = warehouse.capacity || '';
        document.getElementById('warehouseArea').value = warehouse.area || '';
        document.getElementById('warehouseUnit').value = warehouse.unit || 'متر مربع';
        document.getElementById('warehouseTemperature').value = warehouse.temperature || '';
        document.getElementById('warehouseIsActive').checked = warehouse.isActive;
        document.getElementById('warehouseLocation').value = warehouse.location || '';
        document.getElementById('warehouseNotes').value = warehouse.notes || '';

        populateWarehouseBranches();
        document.getElementById('warehouseModal').classList.add('active');
    }
}

function closeWarehouseModal() {
    document.getElementById('warehouseModal').classList.remove('active');
}

function populateWarehouseBranches() {
    const warehouseBranchSelect = document.getElementById('warehouseBranch');
    warehouseBranchSelect.innerHTML = '<option value="">اختر الفرع</option>';

    branches.filter(branch => branch.isActive).forEach(branch => {
        const option = document.createElement('option');
        option.value = branch.id;
        option.textContent = branch.branchName;
        warehouseBranchSelect.appendChild(option);
    });
}

function saveWarehouse() {
    const form = document.getElementById('warehouseForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const warehouseId = document.getElementById('warehouseId').value;
    const branchId = parseInt(document.getElementById('warehouseBranch').value);
    const branch = branches.find(b => b.id === branchId);

    const warehouseData = {
        warehouseCode: document.getElementById('warehouseCode').value,
        warehouseName: document.getElementById('warehouseName').value,
        branchId: branchId,
        branchName: branch ? branch.branchName : '',
        type: document.getElementById('warehouseType').value,
        capacity: parseFloat(document.getElementById('warehouseCapacity').value) || 0,
        area: parseFloat(document.getElementById('warehouseArea').value) || 0,
        unit: document.getElementById('warehouseUnit').value,
        temperature: document.getElementById('warehouseTemperature').value,
        isActive: document.getElementById('warehouseIsActive').checked,
        location: document.getElementById('warehouseLocation').value,
        notes: document.getElementById('warehouseNotes').value
    };

    // Check if warehouse code already exists
    const existingWarehouse = warehouses.find(w => w.warehouseCode === warehouseData.warehouseCode && w.id != warehouseId);
    if (existingWarehouse) {
        showMessage('كود المخزن موجود مسبقاً!', 'error');
        return;
    }

    if (warehouseId) {
        // Edit existing warehouse
        const warehouseIndex = warehouses.findIndex(w => w.id == warehouseId);
        if (warehouseIndex !== -1) {
            warehouses[warehouseIndex] = {
                ...warehouses[warehouseIndex],
                ...warehouseData
            };
        }
    } else {
        // Add new warehouse
        const newWarehouse = {
            id: Math.max(...warehouses.map(w => w.id), 0) + 1,
            ...warehouseData,
            createdAt: new Date().toISOString().split('T')[0]
        };
        warehouses.push(newWarehouse);
    }

    saveWarehouses();
    renderWarehouses();
    updateStats();
    closeWarehouseModal();
    showMessage('تم حفظ المخزن بنجاح!', 'success');
}

function toggleWarehouseStatus(id) {
    const warehouse = warehouses.find(w => w.id === id);
    if (warehouse) {
        warehouse.isActive = !warehouse.isActive;
        saveWarehouses();
        renderWarehouses();
        updateStats();
        showMessage(`تم ${warehouse.isActive ? 'تفعيل' : 'إلغاء تفعيل'} المخزن بنجاح!`, 'success');
    }
}

function deleteWarehouse(id) {
    if (confirm('هل أنت متأكد من حذف هذا المخزن؟ سيتم حذف جميع المخزون المرتبط به أيضاً.')) {
        warehouses = warehouses.filter(w => w.id !== id);
        inventoryDistribution = inventoryDistribution.filter(i => i.warehouseId !== id);

        saveWarehouses();
        saveInventoryDistribution();
        renderWarehouses();
        renderInventoryDistribution();
        updateStats();
        showMessage('تم حذف المخزن وجميع مخزونه بنجاح!', 'success');
    }
}

// Transfer modal functions
function openTransferModal() {
    document.getElementById('transferForm').reset();
    document.getElementById('transferDate').value = new Date().toISOString().split('T')[0];
    populateTransferData();
    document.getElementById('transferModal').classList.add('active');
}

function closeTransferModal() {
    document.getElementById('transferModal').classList.remove('active');
}

function populateTransferData() {
    // Populate items
    const transferItemSelect = document.getElementById('transferItem');
    transferItemSelect.innerHTML = '<option value="">اختر الصنف</option>';

    // Get unique items from inventory distribution
    const uniqueItems = [...new Map(inventoryDistribution.map(item => [item.itemCode, item])).values()];
    uniqueItems.forEach(item => {
        const option = document.createElement('option');
        option.value = item.itemCode;
        option.textContent = `${item.itemCode} - ${item.itemName}`;
        transferItemSelect.appendChild(option);
    });

    // Populate branches
    const fromBranchSelect = document.getElementById('fromBranch');
    const toBranchSelect = document.getElementById('toBranch');

    fromBranchSelect.innerHTML = '<option value="">اختر الفرع</option>';
    toBranchSelect.innerHTML = '<option value="">اختر الفرع</option>';

    branches.filter(branch => branch.isActive).forEach(branch => {
        const option1 = document.createElement('option');
        option1.value = branch.id;
        option1.textContent = branch.branchName;
        fromBranchSelect.appendChild(option1);

        const option2 = document.createElement('option');
        option2.value = branch.id;
        option2.textContent = branch.branchName;
        toBranchSelect.appendChild(option2);
    });
}

function updateTransferItemInfo() {
    const itemCode = document.getElementById('transferItem').value;
    if (itemCode) {
        const itemInventory = inventoryDistribution.filter(item => item.itemCode === itemCode);
        const totalQuantity = itemInventory.reduce((sum, item) => sum + item.quantity, 0);
        document.getElementById('availableQuantity').value = `${totalQuantity} (إجمالي في جميع المخازن)`;
    } else {
        document.getElementById('availableQuantity').value = '';
    }
}

function updateFromWarehouses() {
    const branchId = document.getElementById('fromBranch').value;
    const fromWarehouseSelect = document.getElementById('fromWarehouse');

    fromWarehouseSelect.innerHTML = '<option value="">اختر المخزن</option>';

    if (branchId) {
        const branchWarehouses = warehouses.filter(w => w.branchId == branchId && w.isActive);
        branchWarehouses.forEach(warehouse => {
            const option = document.createElement('option');
            option.value = warehouse.id;
            option.textContent = warehouse.warehouseName;
            fromWarehouseSelect.appendChild(option);
        });
    }
}

function updateToWarehouses() {
    const branchId = document.getElementById('toBranch').value;
    const toWarehouseSelect = document.getElementById('toWarehouse');

    toWarehouseSelect.innerHTML = '<option value="">اختر المخزن</option>';

    if (branchId) {
        const branchWarehouses = warehouses.filter(w => w.branchId == branchId && w.isActive);
        branchWarehouses.forEach(warehouse => {
            const option = document.createElement('option');
            option.value = warehouse.id;
            option.textContent = warehouse.warehouseName;
            toWarehouseSelect.appendChild(option);
        });
    }
}

function executeTransfer() {
    const form = document.getElementById('transferForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const itemCode = document.getElementById('transferItem').value;
    const fromWarehouseId = parseInt(document.getElementById('fromWarehouse').value);
    const toWarehouseId = parseInt(document.getElementById('toWarehouse').value);
    const transferQuantity = parseFloat(document.getElementById('transferQuantity').value);

    if (fromWarehouseId === toWarehouseId) {
        showMessage('لا يمكن النقل إلى نفس المخزن!', 'error');
        return;
    }

    // Find source inventory item
    const sourceItem = inventoryDistribution.find(item =>
        item.itemCode === itemCode && item.warehouseId === fromWarehouseId
    );

    if (!sourceItem) {
        showMessage('الصنف غير موجود في المخزن المصدر!', 'error');
        return;
    }

    if (sourceItem.quantity < transferQuantity) {
        showMessage('الكمية المطلوبة أكبر من المتاح في المخزن المصدر!', 'error');
        return;
    }

    // Get warehouse info
    const fromWarehouse = warehouses.find(w => w.id === fromWarehouseId);
    const toWarehouse = warehouses.find(w => w.id === toWarehouseId);

    // Update source inventory
    sourceItem.quantity -= transferQuantity;
    sourceItem.lastUpdated = new Date().toISOString().split('T')[0];

    // Find or create destination inventory item
    let destinationItem = inventoryDistribution.find(item =>
        item.itemCode === itemCode && item.warehouseId === toWarehouseId
    );

    if (destinationItem) {
        destinationItem.quantity += transferQuantity;
        destinationItem.lastUpdated = new Date().toISOString().split('T')[0];
    } else {
        // Create new inventory item for destination
        const newInventoryItem = {
            id: Math.max(...inventoryDistribution.map(i => i.id), 0) + 1,
            itemCode: sourceItem.itemCode,
            itemName: sourceItem.itemName,
            branchId: toWarehouse.branchId,
            branchName: toWarehouse.branchName,
            warehouseId: toWarehouseId,
            warehouseName: toWarehouse.warehouseName,
            quantity: transferQuantity,
            unit: sourceItem.unit,
            lastUpdated: new Date().toISOString().split('T')[0]
        };
        inventoryDistribution.push(newInventoryItem);
    }

    // Remove source item if quantity becomes 0
    if (sourceItem.quantity === 0) {
        inventoryDistribution = inventoryDistribution.filter(item => item.id !== sourceItem.id);
    }

    saveInventoryDistribution();
    renderInventoryDistribution();
    closeTransferModal();
    showMessage(`تم نقل ${transferQuantity} ${sourceItem.unit} من ${sourceItem.itemName} من ${fromWarehouse.warehouseName} إلى ${toWarehouse.warehouseName} بنجاح!`, 'success');
}

function editInventoryItem(id) {
    showMessage('تعديل توزيع المخزون قيد التطوير...', 'info');
}

function transferInventory(id) {
    const item = inventoryDistribution.find(i => i.id === id);
    if (item) {
        openTransferModal();
        document.getElementById('transferItem').value = item.itemCode;
        updateTransferItemInfo();
    }
}

function exportBranchesToExcel() {
    showMessage('تصدير الفروع قيد التطوير...', 'info');
}

function exportWarehousesToExcel() {
    showMessage('تصدير المخازن قيد التطوير...', 'info');
}

// Load cashboxes from localStorage
function loadCashboxes() {
    const savedCashboxes = localStorage.getItem('anwar_bakery_cash_registers');
    if (savedCashboxes) {
        cashboxes = JSON.parse(savedCashboxes);
    }
    renderCashboxes();
    populateCashboxBranchFilter();
}

// Save cashboxes to localStorage
function saveCashboxes() {
    localStorage.setItem('anwar_bakery_cash_registers', JSON.stringify(cashboxes));
}

// Load bank accounts from localStorage
function loadBankAccounts() {
    const savedBankAccounts = localStorage.getItem('anwar_bakery_bank_accounts');
    if (savedBankAccounts) {
        bankAccounts = JSON.parse(savedBankAccounts);
    }
    renderBankAccounts();
}

// Save bank accounts to localStorage
function saveBankAccounts() {
    localStorage.setItem('anwar_bakery_bank_accounts', JSON.stringify(bankAccounts));
}

// Populate cashbox branch filter
function populateCashboxBranchFilter() {
    const cashboxBranchFilter = document.getElementById('cashboxBranchFilter');
    if (cashboxBranchFilter) {
        cashboxBranchFilter.innerHTML = '<option value="">جميع الفروع</option>';

        branches.forEach(branch => {
            const option = document.createElement('option');
            option.value = branch.id;
            option.textContent = branch.branchName;
            cashboxBranchFilter.appendChild(option);
        });
    }
}

// Cashbox modal functions
function openAddCashboxModal() {
    document.getElementById('cashboxModalTitle').textContent = 'إضافة صندوق جديد';
    document.getElementById('cashboxForm').reset();
    document.getElementById('cashboxId').value = '';
    document.getElementById('cashboxIsActive').checked = true;

    // Generate cashbox code
    const nextNumber = cashboxes.length + 1;
    document.getElementById('cashboxCode').value = `CB${nextNumber.toString().padStart(3, '0')}`;

    // Set default currency from company settings
    const company = getCompanyInfo();
    if (company.currency) {
        document.getElementById('cashboxCurrency').value = company.currency;
    }

    populateCashboxBranches();
    document.getElementById('cashboxModal').classList.add('active');
}

function closeCashboxModal() {
    document.getElementById('cashboxModal').classList.remove('active');
}

function saveCashbox() {
    const form = document.getElementById('cashboxForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const cashboxId = document.getElementById('cashboxId').value;
    const branchId = document.getElementById('cashboxBranch').value;
    const employeeId = document.getElementById('cashboxResponsible').value;

    // Get branch and employee names
    const selectedBranch = branches.find(b => b.id == branchId);
    const branchName = selectedBranch ? selectedBranch.branchName : '';

    let employeeName = '';
    if (employeeId) {
        const savedEmployees = localStorage.getItem('anwar_bakery_employees');
        if (savedEmployees) {
            const employees = JSON.parse(savedEmployees);
            const selectedEmployee = employees.find(e => e.id == employeeId);
            employeeName = selectedEmployee ? selectedEmployee.fullName : '';
        }
    }

    const cashboxData = {
        cashboxCode: document.getElementById('cashboxCode').value,
        cashboxName: document.getElementById('cashboxName').value,
        branchId: parseInt(branchId),
        branchName: branchName,
        responsibleEmployeeId: employeeId ? parseInt(employeeId) : null,
        responsibleEmployeeName: employeeName,
        openingBalance: parseFloat(document.getElementById('cashboxOpeningBalance').value) || 0,
        currentBalance: parseFloat(document.getElementById('cashboxOpeningBalance').value) || 0,
        currency: document.getElementById('cashboxCurrency').value,
        isActive: document.getElementById('cashboxIsActive').checked,
        notes: document.getElementById('cashboxNotes').value
    };

    // Check if cashbox code already exists
    const existingCashbox = cashboxes.find(c => c.cashboxCode === cashboxData.cashboxCode && c.id != cashboxId);
    if (existingCashbox) {
        showMessage('كود الصندوق موجود مسبقاً!', 'error');
        return;
    }

    if (cashboxId) {
        // Edit existing cashbox
        const cashboxIndex = cashboxes.findIndex(c => c.id == cashboxId);
        if (cashboxIndex !== -1) {
            cashboxes[cashboxIndex] = {
                ...cashboxes[cashboxIndex],
                ...cashboxData
            };
        }
    } else {
        // Add new cashbox
        const newCashbox = {
            id: Math.max(...cashboxes.map(c => c.id), 0) + 1,
            ...cashboxData,
            createdAt: new Date().toISOString().split('T')[0]
        };
        cashboxes.push(newCashbox);
    }

    saveCashboxes();
    renderCashboxes();
    updateStats();
    closeCashboxModal();
    showMessage('تم حفظ بيانات الصندوق بنجاح!', 'success');
}

function editCashbox(id) {
    const cashbox = cashboxes.find(c => c.id === id);
    if (cashbox) {
        document.getElementById('cashboxModalTitle').textContent = 'تعديل الصندوق';
        document.getElementById('cashboxId').value = cashbox.id;
        document.getElementById('cashboxCode').value = cashbox.cashboxCode || '';
        document.getElementById('cashboxName').value = cashbox.cashboxName || '';
        document.getElementById('cashboxOpeningBalance').value = cashbox.openingBalance || 0;
        document.getElementById('cashboxCurrency').value = cashbox.currency || 'SAR';
        document.getElementById('cashboxIsActive').checked = cashbox.isActive;
        document.getElementById('cashboxNotes').value = cashbox.notes || '';

        populateCashboxBranches();

        // Set branch
        if (cashbox.branchId) {
            document.getElementById('cashboxBranch').value = cashbox.branchId;
            updateCashboxEmployees();

            // Set employee after a short delay to ensure employees are loaded
            setTimeout(() => {
                if (cashbox.responsibleEmployeeId) {
                    document.getElementById('cashboxResponsible').value = cashbox.responsibleEmployeeId;
                }
            }, 100);
        }

        document.getElementById('cashboxModal').classList.add('active');
    }
}

function cashboxTransactions(id) {
    showMessage('عمليات الصندوق قيد التطوير...', 'info');
}

function toggleCashboxStatus(id) {
    const cashbox = cashboxes.find(c => c.id === id);
    if (cashbox) {
        cashbox.isActive = !cashbox.isActive;
        saveCashboxes();
        renderCashboxes();
        updateStats();
        showMessage(`تم ${cashbox.isActive ? 'تفعيل' : 'إلغاء تفعيل'} الصندوق بنجاح!`, 'success');
    }
}

function deleteCashbox(id) {
    const cashbox = cashboxes.find(c => c.id === id);
    if (cashbox && confirm(`هل أنت متأكد من حذف الصندوق: ${cashbox.cashboxName}؟`)) {
        cashboxes = cashboxes.filter(c => c.id !== id);
        saveCashboxes();
        renderCashboxes();
        updateStats();
        showMessage('تم حذف الصندوق بنجاح!', 'success');
    }
}

// Bank account modal functions
function openAddBankAccountModal() {
    showMessage('إضافة حساب بنكي جديد قيد التطوير...', 'info');
}

function editBankAccount(id) {
    showMessage('تعديل الحساب البنكي قيد التطوير...', 'info');
}

function bankTransactions(id) {
    showMessage('عمليات الحساب البنكي قيد التطوير...', 'info');
}

function toggleBankAccountStatus(id) {
    const account = bankAccounts.find(a => a.id === id);
    if (account) {
        account.isActive = !account.isActive;
        saveBankAccounts();
        renderBankAccounts();
        updateStats();
        showMessage(`تم ${account.isActive ? 'تفعيل' : 'إلغاء تفعيل'} الحساب البنكي بنجاح!`, 'success');
    }
}

function deleteBankAccount(id) {
    if (confirm('هل أنت متأكد من حذف هذا الحساب البنكي؟')) {
        bankAccounts = bankAccounts.filter(a => a.id !== id);
        saveBankAccounts();
        renderBankAccounts();
        updateStats();
        showMessage('تم حذف الحساب البنكي بنجاح!', 'success');
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadUserInfo();
    loadCompanyInfo();
    loadBranches();
    loadWarehouses();
    loadInventoryDistribution();
    loadCashboxes();
    loadBankAccounts();
});