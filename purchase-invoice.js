// Purchase Invoice Management System
let currentInvoice = {
    items: [],
    subtotal: 0,
    tax: 0,
    total: 0
};

let invoiceCounter = 1;
let allItems = []; // Store all items for search
let selectedItem = null; // Currently selected item
let searchTimeout = null; // For debounced search

// Load initial data
function loadInitialData() {
    loadUserInfo();
    loadCompanyInfo();
    generateInvoiceNumber();
    setCurrentDate();
    loadBranches();
}

// Generate invoice number
function generateInvoiceNumber() {
    const savedCounter = localStorage.getItem('anwar_bakery_purchase_counter');
    if (savedCounter) {
        invoiceCounter = parseInt(savedCounter) + 1;
    }

    const invoiceNumber = `PUR-${new Date().getFullYear()}-${invoiceCounter.toString().padStart(3, '0')}`;
    document.getElementById('invoiceNumber').value = invoiceNumber;
}

// Set current date
function setCurrentDate() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('invoiceDate').value = today;
}

// Load branches
function loadBranches() {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    const branchSelect = document.getElementById('branchId');

    branchSelect.innerHTML = '<option value="">اختر الفرع</option>';

    if (savedBranches) {
        const branches = JSON.parse(savedBranches);
        branches.filter(branch => branch.isActive).forEach(branch => {
            const option = document.createElement('option');
            option.value = branch.id;
            option.textContent = branch.branchName;
            branchSelect.appendChild(option);
        });
    }
}

// Load branch data when branch is selected
function loadBranchData() {
    const branchId = document.getElementById('branchId').value;

    if (branchId) {
        loadCashRegisters(branchId);
        loadWarehouses(branchId);
        loadBranchItems(branchId);
        // Reset seller selection when branch changes
        document.getElementById('sellerId').innerHTML = '<option value="">اختر المورد</option>';
        loadSellers(branchId);
    } else {
        // Clear dependent dropdowns
        clearDependentDropdowns();
    }
}

// Load cash registers for selected branch
function loadCashRegisters(branchId) {
    const savedCashRegisters = localStorage.getItem('anwar_bakery_cash_registers');
    const cashRegisterSelect = document.getElementById('cashRegisterId');

    cashRegisterSelect.innerHTML = '<option value="">اختر الصندوق</option>';

    if (savedCashRegisters) {
        const cashRegisters = JSON.parse(savedCashRegisters);
        const branchCashRegisters = cashRegisters.filter(register =>
            register.branchId == branchId && register.isActive
        );

        branchCashRegisters.forEach(register => {
            const option = document.createElement('option');
            option.value = register.id;
            option.textContent = `${register.registerName} (${register.currentBalance.toLocaleString()})`;

            // Mark default cash register
            if (register.isDefault) {
                option.selected = true;
                option.textContent += ' - افتراضي';
            }

            cashRegisterSelect.appendChild(option);
        });
    }
}

// Load warehouses for selected branch
function loadWarehouses(branchId) {
    const savedWarehouses = localStorage.getItem('anwar_bakery_warehouses');
    const warehouseSelect = document.getElementById('warehouseId');

    warehouseSelect.innerHTML = '<option value="">اختر المخزن</option>';

    if (savedWarehouses) {
        const warehouses = JSON.parse(savedWarehouses);
        const branchWarehouses = warehouses.filter(warehouse =>
            warehouse.branchId == branchId && warehouse.isActive
        );

        branchWarehouses.forEach(warehouse => {
            const option = document.createElement('option');
            option.value = warehouse.id;
            option.textContent = warehouse.warehouseName;
            warehouseSelect.appendChild(option);
        });
    }
}

// Load items for selected branch
function loadBranchItems(branchId) {
    const savedItems = localStorage.getItem('anwar_bakery_items');

    if (savedItems) {
        const items = JSON.parse(savedItems);
        allItems = items.filter(item =>
            item.branchId == branchId && item.isActive
        );

        // Add sample items if none exist
        if (allItems.length === 0) {
            allItems = createSampleItems(branchId);
        }
    } else {
        // Create sample items for testing
        allItems = createSampleItems(branchId);
    }
}

// Create sample items for testing
function createSampleItems(branchId) {
    const sampleItems = [
        {
            id: 'ITEM-001',
            itemCode: 'FL001',
            barcode: '1234567890123',
            itemName: 'دقيق أبيض فاخر',
            itemType: 'raw_materials',
            category: 'خامات',
            branchId: branchId,
            units: [
                { name: 'كيس 50 كيلو', factor: 50, price: 85.00, isDefault: true },
                { name: 'كيلو', factor: 1, price: 1.80, isDefault: false }
            ],
            currentStock: 120,
            minStock: 20,
            maxStock: 500,
            costPrice: 85.00,
            sellingPrice: 95.00,
            isActive: true,
            warehouseId: 1
        },
        {
            id: 'ITEM-002',
            itemCode: 'SG002',
            barcode: '1234567890124',
            itemName: 'سكر أبيض ناعم',
            itemType: 'raw_materials',
            category: 'خامات',
            branchId: branchId,
            units: [
                { name: 'كيس 50 كيلو', factor: 50, price: 120.00, isDefault: true },
                { name: 'كيلو', factor: 1, price: 2.50, isDefault: false }
            ],
            currentStock: 80,
            minStock: 15,
            maxStock: 300,
            costPrice: 120.00,
            sellingPrice: 135.00,
            isActive: true,
            warehouseId: 1
        },
        {
            id: 'ITEM-003',
            itemCode: 'YS003',
            barcode: '1234567890125',
            itemName: 'خميرة فورية',
            itemType: 'raw_materials',
            category: 'خامات',
            branchId: branchId,
            units: [
                { name: 'علبة 500 جرام', factor: 0.5, price: 15.00, isDefault: true },
                { name: 'جرام', factor: 0.001, price: 0.03, isDefault: false }
            ],
            currentStock: 45,
            minStock: 10,
            maxStock: 100,
            costPrice: 15.00,
            sellingPrice: 18.00,
            isActive: true,
            warehouseId: 1
        },
        {
            id: 'ITEM-004',
            itemCode: 'BR004',
            barcode: '1234567890126',
            itemName: 'خبز أبيض كبير',
            itemType: 'products',
            category: 'منتجات',
            branchId: branchId,
            units: [
                { name: 'رغيف', factor: 1, price: 2.00, isDefault: true },
                { name: 'كرتون 20 رغيف', factor: 20, price: 38.00, isDefault: false }
            ],
            currentStock: 200,
            minStock: 50,
            maxStock: 1000,
            costPrice: 1.20,
            sellingPrice: 2.00,
            isActive: true,
            warehouseId: 2
        },
        {
            id: 'ITEM-005',
            itemCode: 'CK005',
            barcode: '1234567890127',
            itemName: 'كيك شوكولاتة',
            itemType: 'products',
            category: 'حلويات',
            branchId: branchId,
            units: [
                { name: 'قطعة', factor: 1, price: 25.00, isDefault: true },
                { name: 'علبة 6 قطع', factor: 6, price: 140.00, isDefault: false }
            ],
            currentStock: 30,
            minStock: 5,
            maxStock: 100,
            costPrice: 15.00,
            sellingPrice: 25.00,
            isActive: true,
            warehouseId: 2
        },
        {
            id: 'ITEM-006',
            itemCode: 'SV006',
            barcode: '1234567890128',
            itemName: 'خدمة توصيل',
            itemType: 'services',
            category: 'خدمات',
            branchId: branchId,
            units: [
                { name: 'مرة واحدة', factor: 1, price: 10.00, isDefault: true }
            ],
            currentStock: 0, // Services don't have stock
            minStock: 0,
            maxStock: 0,
            costPrice: 5.00,
            sellingPrice: 10.00,
            isActive: true,
            warehouseId: null
        }
    ];

    // Save sample items
    localStorage.setItem('anwar_bakery_items', JSON.stringify(sampleItems));
    return sampleItems;
}

// Smart search function
function searchItems() {
    const searchInput = document.getElementById('itemSearchInput');
    const searchTerm = searchInput.value.trim().toLowerCase();
    const resultsDiv = document.getElementById('searchResults');

    // Clear previous timeout
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }

    if (searchTerm.length < 2) {
        resultsDiv.style.display = 'none';
        return;
    }

    // Debounce search
    searchTimeout = setTimeout(() => {
        const filteredItems = allItems.filter(item => {
            return (
                item.itemName.toLowerCase().includes(searchTerm) ||
                item.itemCode.toLowerCase().includes(searchTerm) ||
                item.barcode.includes(searchTerm) ||
                item.category.toLowerCase().includes(searchTerm)
            );
        });

        displaySearchResults(filteredItems);
    }, 300);
}

// Display search results
function displaySearchResults(items) {
    const resultsDiv = document.getElementById('searchResults');

    if (items.length === 0) {
        resultsDiv.innerHTML = `
            <div class="p-3 text-center text-gray-500">
                <span class="text-lg">🔍</span>
                <p class="text-sm">لا توجد نتائج مطابقة</p>
            </div>
        `;
        resultsDiv.style.display = 'block';
        return;
    }

    const currencySymbol = getCurrencySymbol();

    resultsDiv.innerHTML = items.map(item => {
        const stockColor = item.currentStock <= item.minStock ? 'text-red-600' : 'text-green-600';
        const typeIcon = getItemTypeIcon(item.itemType);

        return `
            <div class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100" onclick="selectItem('${item.id}')">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="text-lg ml-2">${typeIcon}</span>
                        <div>
                            <div class="font-medium text-gray-900">${item.itemName}</div>
                            <div class="text-xs text-gray-500">
                                كود: ${item.itemCode} | باركود: ${item.barcode}
                            </div>
                        </div>
                    </div>
                    <div class="text-left">
                        <div class="text-sm font-medium text-blue-600">
                            ${item.costPrice.toFixed(2)}${currencySymbol ? ' ' + currencySymbol : ''}
                        </div>
                        <div class="text-xs ${stockColor}">
                            مخزون: ${item.currentStock}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    resultsDiv.style.display = 'block';
}

// Get item type icon
function getItemTypeIcon(itemType) {
    const icons = {
        'raw_materials': '🌾',
        'products': '🍞',
        'services': '🔧',
        'supplies': '📦'
    };
    return icons[itemType] || '📦';
}

// Select item from search results
function selectItem(itemId) {
    selectedItem = allItems.find(item => item.id === itemId);

    if (selectedItem) {
        // Hide search results
        document.getElementById('searchResults').style.display = 'none';

        // Clear search input
        document.getElementById('itemSearchInput').value = selectedItem.itemName;

        // Show selected item details
        showSelectedItemDetails();

        // Populate units dropdown
        populateUnitsDropdown();

        // Set default values
        setDefaultItemValues();
    }
}

// Show selected item details
function showSelectedItemDetails() {
    const detailsDiv = document.getElementById('selectedItemDetails');
    const currencySymbol = getCurrencySymbol();

    document.getElementById('selectedItemIcon').textContent = getItemTypeIcon(selectedItem.itemType);
    document.getElementById('selectedItemName').textContent = selectedItem.itemName;
    document.getElementById('selectedItemCode').textContent = `كود: ${selectedItem.itemCode} | باركود: ${selectedItem.barcode}`;

    const stockColor = selectedItem.currentStock <= selectedItem.minStock ? 'text-red-600' : 'text-green-600';
    document.getElementById('availableStock').innerHTML = `<span class="${stockColor}">${selectedItem.currentStock} ${selectedItem.units[0].name}</span>`;

    document.getElementById('lastPurchasePrice').textContent = selectedItem.costPrice.toFixed(2) + (currencySymbol ? ' ' + currencySymbol : '');

    detailsDiv.style.display = 'block';
}

// Populate units dropdown
function populateUnitsDropdown() {
    const unitSelect = document.getElementById('newItemUnit');

    unitSelect.innerHTML = '<option value="">اختر الوحدة</option>';

    selectedItem.units.forEach(unit => {
        const option = document.createElement('option');
        option.value = JSON.stringify(unit);
        option.textContent = unit.name;
        option.dataset.price = unit.price;
        option.dataset.factor = unit.factor;

        if (unit.isDefault) {
            option.selected = true;
        }

        unitSelect.appendChild(option);
    });
}

// Set default item values
function setDefaultItemValues() {
    const defaultUnit = selectedItem.units.find(unit => unit.isDefault);
    if (defaultUnit) {
        document.getElementById('newItemPrice').value = defaultUnit.price;
        updateUnitPrice();
    }
}

// Update unit price when unit changes
function updateUnitPrice() {
    const unitSelect = document.getElementById('newItemUnit');
    const selectedOption = unitSelect.options[unitSelect.selectedIndex];

    if (selectedOption.value) {
        const price = parseFloat(selectedOption.dataset.price);
        document.getElementById('newItemPrice').value = price.toFixed(2);
        calculateItemTotal();
    }
}

// Handle search keydown events
function handleSearchKeydown(event) {
    const resultsDiv = document.getElementById('searchResults');

    if (event.key === 'Escape') {
        resultsDiv.style.display = 'none';
        event.target.blur();
    } else if (event.key === 'Enter') {
        event.preventDefault();
        const firstResult = resultsDiv.querySelector('[onclick]');
        if (firstResult) {
            firstResult.click();
        }
    }
}

// Add quick item by type
function addQuickItem(itemType) {
    const filteredItems = allItems.filter(item => item.itemType === itemType);

    if (filteredItems.length === 0) {
        showMessage(`لا توجد أصناف من نوع ${getItemTypeName(itemType)}`, 'info');
        return;
    }

    // Show filtered results
    displaySearchResults(filteredItems);
    document.getElementById('searchResults').style.display = 'block';

    // Focus on search input
    document.getElementById('itemSearchInput').focus();
}

// Get item type name
function getItemTypeName(itemType) {
    const names = {
        'raw_materials': 'الخامات',
        'products': 'المنتجات',
        'services': 'الخدمات',
        'supplies': 'المستلزمات'
    };
    return names[itemType] || 'غير محدد';
}

// Toggle seller type (supplier/customer)
function toggleSellerType() {
    const sellerType = document.querySelector('input[name="sellerType"]:checked').value;
    const sellerLabel = document.getElementById('sellerLabel');
    const branchId = document.getElementById('branchId').value;

    if (sellerType === 'supplier') {
        sellerLabel.textContent = 'المورد *';
    } else {
        sellerLabel.textContent = 'العميل *';
    }

    // Reload sellers based on type and branch
    if (branchId) {
        loadSellers(branchId);
    }
}

// Load suppliers or customers based on selected type and branch
function loadSellers(branchId) {
    const sellerType = document.querySelector('input[name="sellerType"]:checked').value;
    const sellerSelect = document.getElementById('sellerId');

    if (sellerType === 'supplier') {
        sellerSelect.innerHTML = '<option value="">اختر المورد</option>';
        loadSuppliers(branchId);
    } else {
        sellerSelect.innerHTML = '<option value="">اختر العميل</option>';
        loadCustomers(branchId);
    }
}

// Load suppliers for the branch
function loadSuppliers(branchId) {
    const savedSuppliers = localStorage.getItem('anwar_bakery_suppliers');
    const sellerSelect = document.getElementById('sellerId');

    if (savedSuppliers) {
        const suppliers = JSON.parse(savedSuppliers);
        // Filter suppliers that serve this branch or are general
        const branchSuppliers = suppliers.filter(supplier =>
            supplier.isActive && (
                !supplier.branchId || // General suppliers
                supplier.branchId == branchId // Branch-specific suppliers
            )
        );

        branchSuppliers.forEach(supplier => {
            const option = document.createElement('option');
            option.value = supplier.id;
            option.textContent = supplier.supplierName;
            option.dataset.type = 'supplier';
            sellerSelect.appendChild(option);
        });
    }
}

// Load customers for the branch
function loadCustomers(branchId) {
    const savedCustomers = localStorage.getItem('anwar_bakery_customers');
    const sellerSelect = document.getElementById('sellerId');

    if (savedCustomers) {
        const customers = JSON.parse(savedCustomers);
        // Filter customers that belong to this branch or are general
        const branchCustomers = customers.filter(customer =>
            customer.isActive && (
                !customer.branchId || // General customers
                customer.branchId == branchId // Branch-specific customers
            )
        );

        branchCustomers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.id;
            option.textContent = customer.customerName;
            option.dataset.type = 'customer';
            sellerSelect.appendChild(option);
        });
    }
}

// Load seller data when seller is selected
function loadSellerData() {
    const sellerId = document.getElementById('sellerId').value;
    const sellerType = document.querySelector('input[name="sellerType"]:checked').value;

    if (sellerId) {
        // Here you can load additional seller data if needed
        // For example, payment terms, credit limit, etc.
        console.log(`Selected ${sellerType}: ${sellerId}`);
    }
}

// Clear dependent dropdowns
function clearDependentDropdowns() {
    document.getElementById('cashRegisterId').innerHTML = '<option value="">اختر الصندوق</option>';
    document.getElementById('warehouseId').innerHTML = '<option value="">اختر المخزن</option>';
    document.getElementById('sellerId').innerHTML = '<option value="">اختر المورد</option>';
    document.getElementById('newItemId').innerHTML = '<option value="">اختر الصنف</option>';
}

// Add invoice item
function addInvoiceItem() {
    const addItemRow = document.getElementById('addItemRow');
    addItemRow.style.display = 'block';

    // Clear previous values
    document.getElementById('newItemId').value = '';
    document.getElementById('newItemQuantity').value = '';
    document.getElementById('newItemUnit').value = '';
    document.getElementById('newItemPrice').value = '';
    document.getElementById('newItemTotal').value = '';
}

// Load item data when item is selected
function loadItemData() {
    const itemSelect = document.getElementById('newItemId');
    const selectedOption = itemSelect.options[itemSelect.selectedIndex];

    if (selectedOption.value) {
        document.getElementById('newItemUnit').value = selectedOption.dataset.unit || '';
        document.getElementById('newItemPrice').value = selectedOption.dataset.price || '';
        calculateItemTotal();
    } else {
        document.getElementById('newItemUnit').value = '';
        document.getElementById('newItemPrice').value = '';
        document.getElementById('newItemTotal').value = '';
    }
}

// Calculate item total
function calculateItemTotal() {
    const quantity = parseFloat(document.getElementById('newItemQuantity').value) || 0;
    const price = parseFloat(document.getElementById('newItemPrice').value) || 0;
    const discount = parseFloat(document.getElementById('newItemDiscount').value) || 0;

    const subtotal = quantity * price;
    const total = subtotal - discount;

    document.getElementById('newItemTotal').value = total.toFixed(2);
}

// Confirm add item
function confirmAddItem() {
    if (!selectedItem) {
        showMessage('يرجى اختيار صنف من نتائج البحث', 'error');
        return;
    }

    const quantity = parseFloat(document.getElementById('newItemQuantity').value);
    const price = parseFloat(document.getElementById('newItemPrice').value);
    const discount = parseFloat(document.getElementById('newItemDiscount').value) || 0;
    const unitSelect = document.getElementById('newItemUnit');

    if (!quantity || !price || !unitSelect.value) {
        showMessage('يرجى ملء جميع البيانات المطلوبة (الكمية، السعر، الوحدة)', 'error');
        return;
    }

    const selectedUnit = JSON.parse(unitSelect.value);
    const warehouseId = document.getElementById('warehouseId').value || selectedItem.warehouseId;

    const item = {
        id: Date.now(), // Temporary ID
        itemId: selectedItem.id,
        itemCode: selectedItem.itemCode,
        barcode: selectedItem.barcode,
        itemName: selectedItem.itemName,
        itemType: selectedItem.itemType,
        category: selectedItem.category,
        quantity: quantity,
        unit: selectedUnit.name,
        unitFactor: selectedUnit.factor,
        price: price,
        discount: discount,
        subtotal: quantity * price,
        total: (quantity * price) - discount,
        warehouseId: warehouseId,
        currentStock: selectedItem.currentStock
    };

    // Check if item already exists in invoice
    const existingItemIndex = currentInvoice.items.findIndex(invItem =>
        invItem.itemId === item.itemId && invItem.unit === item.unit
    );

    if (existingItemIndex !== -1) {
        // Update existing item
        currentInvoice.items[existingItemIndex].quantity += item.quantity;
        currentInvoice.items[existingItemIndex].subtotal = currentInvoice.items[existingItemIndex].quantity * currentInvoice.items[existingItemIndex].price;
        currentInvoice.items[existingItemIndex].total = currentInvoice.items[existingItemIndex].subtotal - currentInvoice.items[existingItemIndex].discount;
        showMessage('تم تحديث كمية الصنف الموجود', 'success');
    } else {
        // Add new item
        currentInvoice.items.push(item);
        showMessage('تم إضافة الصنف بنجاح', 'success');
    }

    renderInvoiceItems();
    calculateInvoiceTotals();
    cancelAddItem();
}

// Cancel add item
function cancelAddItem() {
    document.getElementById('addItemRow').style.display = 'none';

    // Clear search and selection
    document.getElementById('itemSearchInput').value = '';
    document.getElementById('searchResults').style.display = 'none';
    document.getElementById('selectedItemDetails').style.display = 'none';

    // Clear form fields
    document.getElementById('newItemQuantity').value = '';
    document.getElementById('newItemUnit').innerHTML = '<option value="">اختر الوحدة</option>';
    document.getElementById('newItemPrice').value = '';
    document.getElementById('newItemDiscount').value = '';
    document.getElementById('newItemTotal').value = '';

    // Reset selected item
    selectedItem = null;
}

// Render invoice items
function renderInvoiceItems() {
    const tableBody = document.getElementById('invoiceItemsTable');

    if (currentInvoice.items.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="9" class="px-2 py-6 text-center text-gray-500">
                    <span class="text-xl mb-2 block">📦</span>
                    <p class="text-sm">لا توجد أصناف في الفاتورة</p>
                    <p class="text-xs mt-1">استخدم البحث الذكي لإضافة الأصناف</p>
                </td>
            </tr>
        `;
        return;
    }

    const currencySymbol = getCurrencySymbol();

    tableBody.innerHTML = currentInvoice.items.map(item => {
        const typeIcon = getItemTypeIcon(item.itemType);
        const stockColor = item.currentStock <= 20 ? 'text-red-600' : 'text-green-600';

        return `
            <tr class="invoice-item-row hover:bg-gray-50">
                <td class="px-2 py-2 text-sm">
                    <div class="flex items-center">
                        <span class="text-lg ml-2">${typeIcon}</span>
                        <div>
                            <div class="font-medium text-gray-900">${item.itemName}</div>
                            <div class="text-xs text-gray-500">${item.category || 'غير محدد'}</div>
                        </div>
                    </div>
                </td>
                <td class="px-2 py-2 text-xs text-gray-500 text-center">
                    <div>${item.itemCode || 'غير محدد'}</div>
                    <div class="text-xs">${item.barcode || 'غير محدد'}</div>
                </td>
                <td class="px-2 py-2 text-sm text-gray-900 text-center font-medium">${item.quantity}</td>
                <td class="px-2 py-2 text-sm text-gray-900 text-center">${item.unit}</td>
                <td class="px-2 py-2 text-sm text-gray-900 text-center">${item.price.toFixed(2)}${currencySymbol ? ' ' + currencySymbol : ''}</td>
                <td class="px-2 py-2 text-sm text-center text-orange-600">${(item.discount || 0) > 0 ? (item.discount || 0).toFixed(2) + (currencySymbol ? ' ' + currencySymbol : '') : '-'}</td>
                <td class="px-2 py-2 text-sm text-gray-900 font-semibold text-center text-blue-600">${item.total.toFixed(2)}${currencySymbol ? ' ' + currencySymbol : ''}</td>
                <td class="px-2 py-2 text-xs text-center">
                    <div class="${stockColor}">مخزون: ${item.currentStock || 0}</div>
                    <div class="text-gray-500">مخزن: ${item.warehouseId || 'غير محدد'}</div>
                </td>
                <td class="px-2 py-2 text-center">
                    <div class="flex justify-center space-x-1">
                        <button onclick="editInvoiceItem(${item.id})" class="text-blue-600 hover:text-blue-900 text-sm p-1 rounded hover:bg-blue-50" title="تعديل">
                            ✏️
                        </button>
                        <button onclick="removeInvoiceItem(${item.id})" class="text-red-600 hover:text-red-900 text-sm p-1 rounded hover:bg-red-50" title="حذف">
                            🗑️
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// Edit invoice item
function editInvoiceItem(itemId) {
    const item = currentInvoice.items.find(item => item.id === itemId);
    if (!item) return;

    // Find the original item data
    selectedItem = allItems.find(originalItem => originalItem.id === item.itemId);
    if (!selectedItem) {
        showMessage('لا يمكن العثور على بيانات الصنف الأصلية', 'error');
        return;
    }

    // Show add item row
    document.getElementById('addItemRow').style.display = 'block';

    // Populate fields with current item data
    document.getElementById('itemSearchInput').value = item.itemName;
    showSelectedItemDetails();
    populateUnitsDropdown();

    // Set current values
    document.getElementById('newItemQuantity').value = item.quantity;
    document.getElementById('newItemPrice').value = item.price;
    document.getElementById('newItemDiscount').value = item.discount || 0;

    // Set unit
    const unitSelect = document.getElementById('newItemUnit');
    for (let option of unitSelect.options) {
        if (option.textContent === item.unit) {
            option.selected = true;
            break;
        }
    }

    calculateItemTotal();

    // Remove the item temporarily (will be re-added when confirmed)
    currentInvoice.items = currentInvoice.items.filter(invItem => invItem.id !== itemId);
    renderInvoiceItems();
    calculateInvoiceTotals();

    showMessage('جاري تعديل الصنف...', 'info');
}

// Remove invoice item
function removeInvoiceItem(itemId) {
    if (confirm('هل أنت متأكد من حذف هذا الصنف؟')) {
        currentInvoice.items = currentInvoice.items.filter(item => item.id !== itemId);
        renderInvoiceItems();
        calculateInvoiceTotals();
        showMessage('تم حذف الصنف', 'success');
    }
}

// Calculate invoice totals
function calculateInvoiceTotals() {
    const subtotal = currentInvoice.items.reduce((sum, item) => sum + item.total, 0);
    const taxRate = 0.15; // 15% tax
    const tax = subtotal * taxRate;
    const total = subtotal + tax;

    currentInvoice.subtotal = subtotal;
    currentInvoice.tax = tax;
    currentInvoice.total = total;

    const currencySymbol = getCurrencySymbol();

    document.getElementById('subtotalAmount').textContent = subtotal.toFixed(2) + (currencySymbol ? ' ' + currencySymbol : '');
    document.getElementById('taxAmount').textContent = tax.toFixed(2) + (currencySymbol ? ' ' + currencySymbol : '');
    document.getElementById('totalAmount').textContent = total.toFixed(2) + (currencySymbol ? ' ' + currencySymbol : '');
}

// Get currency symbol from settings - FIXED TO USE CORRECT SETTINGS
function getCurrencySymbol() {
    try {
        // First try to get from window.appSettings (the correct way)
        if (window.appSettings) {
            const financial = window.appSettings.get('financial');
            if (financial && financial.currencySymbol) {
                return financial.currencySymbol;
            }
            if (financial && financial.baseCurrency) {
                const currencySymbols = {
                    'SAR': 'ر.ي',
                    'YER': 'ر.ي',
                    'USD': '$',
                    'EUR': '€',
                    'AED': 'د.إ',
                    'KWD': 'د.ك',
                    'QAR': 'ر.ق'
                };
                return currencySymbols[financial.baseCurrency] || 'ر.ي';
            }
        }

        // Fallback: try to get from localStorage directly
        const savedSettings = localStorage.getItem('anwar_bakery_settings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);
            if (settings.financial && settings.financial.currencySymbol) {
                return settings.financial.currencySymbol;
            }
            if (settings.financial && settings.financial.baseCurrency) {
                const currencySymbols = {
                    'SAR': 'ر.ي',
                    'YER': 'ر.ي',
                    'USD': '$',
                    'EUR': '€',
                    'AED': 'د.إ',
                    'KWD': 'د.ك',
                    'QAR': 'ر.ق'
                };
                return currencySymbols[settings.financial.baseCurrency] || 'ر.ي';
            }
        }

        // Last fallback to company data
        const companyData = localStorage.getItem('anwar_bakery_company');
        if (companyData) {
            const company = JSON.parse(companyData);
            if (company.currencySymbol) {
                return company.currencySymbol;
            }
            if (company.currency) {
                const currencySymbols = {
                    'SAR': 'ر.ي',
                    'YER': 'ر.ي',
                    'USD': '$',
                    'EUR': '€',
                    'AED': 'د.إ',
                    'KWD': 'د.ك',
                    'QAR': 'ر.ق'
                };
                return currencySymbols[company.currency] || 'ر.ي';
            }
        }
    } catch (error) {
        console.error('Error getting currency symbol:', error);
    }

    return 'ر.ي'; // Default currency symbol
}

// Load user info
function loadUserInfo() {
    const savedUser = localStorage.getItem('anwar_bakery_current_user');
    if (savedUser) {
        const user = JSON.parse(savedUser);
        const userFullNameElement = document.getElementById('userFullName');
        const userRoleElement = document.getElementById('userRole');

        if (userFullNameElement) {
            userFullNameElement.textContent = user.fullName || 'أحمد محمد';
        }
        if (userRoleElement) {
            userRoleElement.textContent = user.role === 'admin' ? 'مدير النظام' : (user.role || 'مدير النظام');
        }
    }
}

// Load company info
function loadCompanyInfo() {
    const savedCompany = localStorage.getItem('anwar_bakery_company');
    if (savedCompany) {
        const company = JSON.parse(savedCompany);
        const companyNameElement = document.getElementById('companyName');
        const companySloganElement = document.getElementById('companySlogan');

        if (companyNameElement) {
            companyNameElement.textContent = company.companyName || 'مخبز أنوار الحي';
        }
        if (companySloganElement) {
            companySloganElement.textContent = company.slogan || 'جودة تستحق الثقة';
        }
    }
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    if (!container) return;

    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Save draft
function saveDraft() {
    showMessage('حفظ المسودة قيد التطوير...', 'info');
}

// Save and print
function saveAndPrint() {
    showMessage('حفظ وطباعة الفاتورة قيد التطوير...', 'info');
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_current_user');
        window.location.href = 'login.html';
    }
}

// Show add raw material modal
function showAddRawMaterialModal() {
    document.getElementById('addRawMaterialModal').style.display = 'block';

    // Clear form
    clearRawMaterialForm();

    // Focus on name field
    setTimeout(() => {
        document.getElementById('rawMaterialName').focus();
    }, 100);
}

// Close add raw material modal
function closeAddRawMaterialModal() {
    document.getElementById('addRawMaterialModal').style.display = 'none';
    clearRawMaterialForm();
}

// Clear raw material form
function clearRawMaterialForm() {
    document.getElementById('rawMaterialForm').reset();
    document.getElementById('nameValidation').innerHTML = '';
    document.getElementById('codeValidation').innerHTML = '';
    document.getElementById('barcodeValidation').innerHTML = '';

    // Reset units to default
    const unitsContainer = document.getElementById('unitsContainer');
    unitsContainer.innerHTML = `
        <div class="unit-row grid grid-cols-4 gap-2 mb-2">
            <div>
                <input type="text" placeholder="اسم الوحدة" class="unit-name w-full px-2 py-1 border border-gray-300 rounded text-sm" value="كيلو">
            </div>
            <div>
                <input type="number" placeholder="المعامل" class="unit-factor w-full px-2 py-1 border border-gray-300 rounded text-sm" value="1" step="0.001">
            </div>
            <div>
                <input type="number" placeholder="السعر" class="unit-price w-full px-2 py-1 border border-gray-300 rounded text-sm" step="0.01">
            </div>
            <div class="flex items-center">
                <label class="flex items-center text-xs">
                    <input type="checkbox" class="unit-default ml-1" checked>
                    افتراضي
                </label>
            </div>
        </div>
    `;
}

// Add unit row
function addUnitRow() {
    const unitsContainer = document.getElementById('unitsContainer');
    const unitRow = document.createElement('div');
    unitRow.className = 'unit-row grid grid-cols-4 gap-2 mb-2';
    unitRow.innerHTML = `
        <div>
            <input type="text" placeholder="اسم الوحدة" class="unit-name w-full px-2 py-1 border border-gray-300 rounded text-sm">
        </div>
        <div>
            <input type="number" placeholder="المعامل" class="unit-factor w-full px-2 py-1 border border-gray-300 rounded text-sm" step="0.001">
        </div>
        <div>
            <input type="number" placeholder="السعر" class="unit-price w-full px-2 py-1 border border-gray-300 rounded text-sm" step="0.01">
        </div>
        <div class="flex items-center">
            <label class="flex items-center text-xs">
                <input type="checkbox" class="unit-default ml-1">
                افتراضي
            </label>
            <button type="button" onclick="removeUnitRow(this)" class="text-red-600 hover:text-red-900 text-xs mr-2">
                🗑️
            </button>
        </div>
    `;
    unitsContainer.appendChild(unitRow);
}

// Remove unit row
function removeUnitRow(button) {
    const unitRows = document.querySelectorAll('.unit-row');
    if (unitRows.length > 1) {
        button.closest('.unit-row').remove();
    } else {
        showMessage('يجب أن تحتوي الخامة على وحدة واحدة على الأقل', 'error');
    }
}

// Validate raw material data
function validateRawMaterial() {
    const name = document.getElementById('rawMaterialName').value.trim();
    const code = document.getElementById('rawMaterialCode').value.trim();
    const barcode = document.getElementById('rawMaterialBarcode').value.trim();

    let isValid = true;

    // Validate name
    if (!name) {
        document.getElementById('nameValidation').innerHTML = '<span class="text-red-600">اسم الخامة مطلوب</span>';
        isValid = false;
    } else if (checkNameExists(name)) {
        document.getElementById('nameValidation').innerHTML = '<span class="text-red-600">اسم الخامة موجود مسبقاً</span>';
        isValid = false;
    } else {
        document.getElementById('nameValidation').innerHTML = '<span class="text-green-600">✓ اسم متاح</span>';
    }

    // Validate code
    if (!code) {
        document.getElementById('codeValidation').innerHTML = '<span class="text-red-600">كود الخامة مطلوب</span>';
        isValid = false;
    } else if (checkCodeExists(code)) {
        document.getElementById('codeValidation').innerHTML = '<span class="text-red-600">كود الخامة موجود مسبقاً</span>';
        isValid = false;
    } else {
        document.getElementById('codeValidation').innerHTML = '<span class="text-green-600">✓ كود متاح</span>';
    }

    // Validate barcode (if provided)
    if (barcode && checkBarcodeExists(barcode)) {
        document.getElementById('barcodeValidation').innerHTML = '<span class="text-red-600">الباركود موجود مسبقاً</span>';
        isValid = false;
    } else if (barcode) {
        document.getElementById('barcodeValidation').innerHTML = '<span class="text-green-600">✓ باركود متاح</span>';
    } else {
        document.getElementById('barcodeValidation').innerHTML = '';
    }

    return isValid;
}

// Check if name exists
function checkNameExists(name) {
    return allItems.some(item =>
        item.itemName.toLowerCase() === name.toLowerCase()
    );
}

// Check if code exists
function checkCodeExists(code) {
    return allItems.some(item =>
        item.itemCode.toLowerCase() === code.toLowerCase()
    );
}

// Check if barcode exists
function checkBarcodeExists(barcode) {
    return allItems.some(item =>
        item.barcode === barcode
    );
}

// Save raw material
function saveRawMaterial(event) {
    event.preventDefault();

    if (!validateRawMaterial()) {
        showMessage('يرجى تصحيح الأخطاء قبل الحفظ', 'error');
        return;
    }

    // Collect units data
    const unitRows = document.querySelectorAll('.unit-row');
    const units = [];
    let hasDefault = false;

    unitRows.forEach(row => {
        const name = row.querySelector('.unit-name').value.trim();
        const factor = parseFloat(row.querySelector('.unit-factor').value) || 1;
        const price = parseFloat(row.querySelector('.unit-price').value) || 0;
        const isDefault = row.querySelector('.unit-default').checked;

        if (name) {
            units.push({
                name: name,
                factor: factor,
                price: price,
                isDefault: isDefault
            });

            if (isDefault) hasDefault = true;
        }
    });

    if (units.length === 0) {
        showMessage('يجب إضافة وحدة واحدة على الأقل', 'error');
        return;
    }

    if (!hasDefault) {
        units[0].isDefault = true; // Make first unit default if none selected
    }

    // Create new raw material
    const branchId = document.getElementById('branchId').value;
    const newRawMaterial = {
        id: 'ITEM-' + Date.now(),
        itemCode: document.getElementById('rawMaterialCode').value.trim(),
        barcode: document.getElementById('rawMaterialBarcode').value.trim() || '',
        itemName: document.getElementById('rawMaterialName').value.trim(),
        itemType: 'raw_materials',
        category: document.getElementById('rawMaterialCategory').value,
        branchId: parseInt(branchId),
        units: units,
        currentStock: 0, // New item starts with 0 stock
        minStock: parseFloat(document.getElementById('rawMaterialMinStock').value) || 0,
        maxStock: parseFloat(document.getElementById('rawMaterialMaxStock').value) || 1000,
        costPrice: units.find(u => u.isDefault).price,
        sellingPrice: units.find(u => u.isDefault).price * 1.2, // 20% markup
        isActive: true,
        warehouseId: parseInt(document.getElementById('rawMaterialWarehouse').value),
        description: document.getElementById('rawMaterialDescription').value.trim(),
        createdAt: new Date().toISOString(),
        createdBy: 'current_user' // TODO: Get from session
    };

    // Add to items list
    allItems.push(newRawMaterial);

    // Save to localStorage
    const savedItems = localStorage.getItem('anwar_bakery_items');
    let items = savedItems ? JSON.parse(savedItems) : [];
    items.push(newRawMaterial);
    localStorage.setItem('anwar_bakery_items', JSON.stringify(items));

    // Close modal
    closeAddRawMaterialModal();

    // Auto-select the new item
    selectedItem = newRawMaterial;
    document.getElementById('itemSearchInput').value = newRawMaterial.itemName;
    showSelectedItemDetails();
    populateUnitsDropdown();
    setDefaultItemValues();

    // Show add item row
    document.getElementById('addItemRow').style.display = 'block';

    showMessage(`تم إضافة الخامة "${newRawMaterial.itemName}" بنجاح`, 'success');
}

// Add real-time validation
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for real-time validation
    const nameInput = document.getElementById('rawMaterialName');
    const codeInput = document.getElementById('rawMaterialCode');
    const barcodeInput = document.getElementById('rawMaterialBarcode');

    if (nameInput) {
        nameInput.addEventListener('input', function() {
            const name = this.value.trim();
            if (name.length > 2) {
                if (checkNameExists(name)) {
                    document.getElementById('nameValidation').innerHTML = '<span class="text-red-600">اسم الخامة موجود مسبقاً</span>';
                } else {
                    document.getElementById('nameValidation').innerHTML = '<span class="text-green-600">✓ اسم متاح</span>';
                }
            } else {
                document.getElementById('nameValidation').innerHTML = '';
            }
        });
    }

    if (codeInput) {
        codeInput.addEventListener('input', function() {
            const code = this.value.trim();
            if (code.length > 1) {
                if (checkCodeExists(code)) {
                    document.getElementById('codeValidation').innerHTML = '<span class="text-red-600">كود الخامة موجود مسبقاً</span>';
                } else {
                    document.getElementById('codeValidation').innerHTML = '<span class="text-green-600">✓ كود متاح</span>';
                }
            } else {
                document.getElementById('codeValidation').innerHTML = '';
            }
        });
    }

    if (barcodeInput) {
        barcodeInput.addEventListener('input', function() {
            const barcode = this.value.trim();
            if (barcode.length > 5) {
                if (checkBarcodeExists(barcode)) {
                    document.getElementById('barcodeValidation').innerHTML = '<span class="text-red-600">الباركود موجود مسبقاً</span>';
                } else {
                    document.getElementById('barcodeValidation').innerHTML = '<span class="text-green-600">✓ باركود متاح</span>';
                }
            } else {
                document.getElementById('barcodeValidation').innerHTML = '';
            }
        });
    }
});

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadInitialData();
});
