<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير الفواتير - مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .custom-scroll {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 #f7fafc;
        }

        .custom-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scroll::-webkit-scrollbar-track {
            background: #f7fafc;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        .report-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .report-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .chart-container {
            position: relative;
            height: 300px;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="bg-white w-64 shadow-lg flex flex-col">
            <div class="p-4 flex-shrink-0">
                <div class="flex items-center mb-8">
                    <span class="text-2xl ml-3">🍞</span>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800" id="companyName">مخبز أنوار الحي</h1>
                        <p class="text-sm text-gray-600" id="companySlogan">جودة تستحق الثقة</p>
                    </div>
                </div>
            </div>

            <nav class="flex-1 overflow-y-auto px-4 pb-4 custom-scroll">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    الرئيسية
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-indigo-600 bg-indigo-50 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏭</span>
                    الموردين
                </a>

                <!-- User Info & Logout -->
                <div class="mt-auto p-4 border-t border-gray-200">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                            أ
                        </div>
                        <div class="mr-3 flex-1">
                            <p id="userFullName" class="text-sm font-medium text-gray-900">أحمد محمد</p>
                            <p id="userRole" class="text-xs text-gray-500">مدير النظام</p>
                        </div>
                        <button onclick="logout()" class="text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50" title="تسجيل الخروج">
                            <span class="text-lg">🚪</span>
                        </button>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col min-h-0">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <a href="invoices.html" class="text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">←</span>
                            </a>
                            <h2 class="text-xl font-semibold text-gray-900">تقارير الفواتير</h2>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="exportReports()" class="bg-indigo-600 text-white px-4 py-2 rounded text-sm hover:bg-indigo-700">
                                📊 تصدير التقارير
                            </button>
                            <button onclick="window.history.back()" class="text-gray-600 hover:text-gray-800 px-2 py-2 rounded text-sm">
                                ← رجوع
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6 custom-scroll">
                <!-- Report Filters -->
                <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">فترة التقرير</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                            <input type="date" id="dateFrom"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm"
                                   onchange="updateReports()">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                            <input type="date" id="dateTo"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm"
                                   onchange="updateReports()">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الفرع</label>
                            <select id="branchFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm" onchange="updateReports()">
                                <option value="">جميع الفروع</option>
                                <option value="main">الفرع الرئيسي</option>
                                <option value="branch1">فرع الملك فهد</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">نوع التقرير</label>
                            <select id="reportType" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm" onchange="updateReports()">
                                <option value="summary">ملخص عام</option>
                                <option value="detailed">تفصيلي</option>
                                <option value="comparison">مقارنة</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Summary Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <span class="text-2xl">🛒</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">إجمالي المشتريات</p>
                                <p class="text-2xl font-bold text-gray-900" id="totalPurchases">125,000</p>
                                <p class="text-xs text-gray-500" id="purchasesCurrency">ر.ي</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <span class="text-2xl">💰</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">إجمالي المبيعات</p>
                                <p class="text-2xl font-bold text-gray-900" id="totalSales">185,500</p>
                                <p class="text-xs text-gray-500" id="salesCurrency">ر.ي</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                                <span class="text-2xl">↩️</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">إجمالي المرتجعات</p>
                                <p class="text-2xl font-bold text-gray-900" id="totalReturns">8,750</p>
                                <p class="text-xs text-gray-500" id="returnsCurrency">ر.ي</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                                <span class="text-2xl">📈</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">صافي الربح</p>
                                <p class="text-2xl font-bold text-gray-900" id="netProfit">51,750</p>
                                <p class="text-xs text-gray-500" id="profitCurrency">ر.ي</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Report Types Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                    <!-- Sales Report -->
                    <div class="report-card bg-white rounded-xl shadow-lg overflow-hidden" onclick="generateReport('sales')">
                        <div class="bg-gradient-to-r from-green-500 to-green-600 p-4 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-bold">تقرير المبيعات</h3>
                                    <p class="text-green-100 text-sm">تفاصيل جميع المبيعات</p>
                                </div>
                                <div class="text-3xl opacity-80">💰</div>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="space-y-2">
                                <div class="flex justify-between text-sm">
                                    <span>عدد الفواتير:</span>
                                    <span class="font-medium">156</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span>إجمالي المبلغ:</span>
                                    <span class="font-medium" id="salesReportTotal">185,500 ر.ي</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Purchase Report -->
                    <div class="report-card bg-white rounded-xl shadow-lg overflow-hidden" onclick="generateReport('purchases')">
                        <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-4 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-bold">تقرير المشتريات</h3>
                                    <p class="text-blue-100 text-sm">تفاصيل جميع المشتريات</p>
                                </div>
                                <div class="text-3xl opacity-80">🛒</div>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="space-y-2">
                                <div class="flex justify-between text-sm">
                                    <span>عدد الفواتير:</span>
                                    <span class="font-medium">89</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span>إجمالي المبلغ:</span>
                                    <span class="font-medium" id="purchasesReportTotal">125,000 ر.ي</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Returns Report -->
                    <div class="report-card bg-white rounded-xl shadow-lg overflow-hidden" onclick="generateReport('returns')">
                        <div class="bg-gradient-to-r from-orange-500 to-orange-600 p-4 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-bold">تقرير المرتجعات</h3>
                                    <p class="text-orange-100 text-sm">تفاصيل جميع المرتجعات</p>
                                </div>
                                <div class="text-3xl opacity-80">↩️</div>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="space-y-2">
                                <div class="flex justify-between text-sm">
                                    <span>عدد المرتجعات:</span>
                                    <span class="font-medium">23</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span>إجمالي المبلغ:</span>
                                    <span class="font-medium" id="returnsReportTotal">8,750 ر.ي</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- POS Report -->
                    <div class="report-card bg-white rounded-xl shadow-lg overflow-hidden" onclick="generateReport('pos')">
                        <div class="bg-gradient-to-r from-purple-500 to-purple-600 p-4 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-bold">تقرير نقطة البيع</h3>
                                    <p class="text-purple-100 text-sm">مبيعات POS</p>
                                </div>
                                <div class="text-3xl opacity-80">⚡</div>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="space-y-2">
                                <div class="flex justify-between text-sm">
                                    <span>عدد المبيعات:</span>
                                    <span class="font-medium">342</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span>إجمالي المبلغ:</span>
                                    <span class="font-medium" id="posReportTotal">45,200 ر.ي</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Profit Report -->
                    <div class="report-card bg-white rounded-xl shadow-lg overflow-hidden" onclick="generateReport('profit')">
                        <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 p-4 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-bold">تقرير الأرباح</h3>
                                    <p class="text-indigo-100 text-sm">تحليل الربحية</p>
                                </div>
                                <div class="text-3xl opacity-80">📈</div>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="space-y-2">
                                <div class="flex justify-between text-sm">
                                    <span>هامش الربح:</span>
                                    <span class="font-medium">28%</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span>صافي الربح:</span>
                                    <span class="font-medium" id="profitReportTotal">51,750 ر.ي</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tax Report -->
                    <div class="report-card bg-white rounded-xl shadow-lg overflow-hidden" onclick="generateReport('tax')">
                        <div class="bg-gradient-to-r from-red-500 to-red-600 p-4 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-bold">تقرير الضرائب</h3>
                                    <p class="text-red-100 text-sm">ضريبة القيمة المضافة</p>
                                </div>
                                <div class="text-3xl opacity-80">🧾</div>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="space-y-2">
                                <div class="flex justify-between text-sm">
                                    <span>ضريبة المبيعات:</span>
                                    <span class="font-medium" id="salesTaxTotal">27,825 ر.ي</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span>ضريبة المشتريات:</span>
                                    <span class="font-medium" id="purchasesTaxTotal">18,750 ر.ي</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chart Section -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">الرسم البياني للمبيعات والمشتريات</h3>
                    <div class="chart-container">
                        <div class="flex items-center justify-center h-full text-gray-500">
                            <div class="text-center">
                                <span class="text-6xl">📊</span>
                                <p class="mt-2">سيتم عرض الرسم البياني هنا</p>
                                <p class="text-sm">يمكن استخدام مكتبة Chart.js أو أي مكتبة رسوم بيانية أخرى</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Get currency symbol from settings - FIXED FOR REPORTS
        function getCurrencySymbol() {
            try {
                // First try to get from window.appSettings (the correct way)
                if (window.appSettings) {
                    const financial = window.appSettings.get('financial');
                    if (financial && financial.currencySymbol) {
                        return financial.currencySymbol;
                    }
                    if (financial && financial.baseCurrency) {
                        const currencySymbols = {
                            'SAR': 'ر.س',
                            'YER': 'ر.ي',
                            'USD': '$',
                            'EUR': '€',
                            'AED': 'د.إ',
                            'KWD': 'د.ك',
                            'QAR': 'ر.ق'
                        };
                        return currencySymbols[financial.baseCurrency] || 'ر.ي';
                    }
                }

                // Fallback: try to get from localStorage directly
                const savedSettings = localStorage.getItem('anwar_bakery_settings');
                if (savedSettings) {
                    const settings = JSON.parse(savedSettings);
                    if (settings.financial && settings.financial.currencySymbol) {
                        return settings.financial.currencySymbol;
                    }
                    if (settings.financial && settings.financial.baseCurrency) {
                        const currencySymbols = {
                            'SAR': 'ر.س',
                            'YER': 'ر.ي',
                            'USD': '$',
                            'EUR': '€',
                            'AED': 'د.إ',
                            'KWD': 'د.ك',
                            'QAR': 'ر.ق'
                        };
                        return currencySymbols[settings.financial.baseCurrency] || 'ر.ي';
                    }
                }

                // Last fallback to company data
                const companyData = localStorage.getItem('anwar_bakery_company');
                if (companyData) {
                    const company = JSON.parse(companyData);
                    if (company.currencySymbol) {
                        return company.currencySymbol;
                    }
                    if (company.currency) {
                        const currencySymbols = {
                            'SAR': 'ر.س',
                            'YER': 'ر.ي',
                            'USD': '$',
                            'EUR': '€',
                            'AED': 'د.إ',
                            'KWD': 'د.ك',
                            'QAR': 'ر.ق'
                        };
                        return currencySymbols[company.currency] || 'ر.ي';
                    }
                }
            } catch (error) {
                console.error('Error getting currency symbol:', error);
            }

            return 'ر.ي'; // Default currency symbol
        }

        // Update currency displays
        function updateCurrencyDisplays() {
            const currencySymbol = getCurrencySymbol();

            // Update main summary currencies
            const currencyElements = [
                'purchasesCurrency',
                'salesCurrency',
                'returnsCurrency',
                'profitCurrency'
            ];

            currencyElements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    element.textContent = currencySymbol;
                }
            });

            // Update detailed report currencies
            const reportElements = [
                'salesReportTotal',
                'purchasesReportTotal',
                'returnsReportTotal',
                'posReportTotal',
                'profitReportTotal',
                'salesTaxTotal',
                'purchasesTaxTotal'
            ];

            reportElements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    const text = element.textContent;
                    const numberPart = text.replace(/[^\d,]/g, '');
                    element.textContent = `${numberPart} ${currencySymbol}`;
                }
            });
        }

        // Update reports based on filters
        function updateReports() {
            console.log('Updating reports...');
            updateCurrencyDisplays();
            // Implementation for updating reports based on filters
        }

        // Generate specific report
        function generateReport(type) {
            const reportTypes = {
                'sales': 'تقرير المبيعات',
                'purchases': 'تقرير المشتريات',
                'returns': 'تقرير المرتجعات',
                'pos': 'تقرير نقطة البيع',
                'profit': 'تقرير الأرباح',
                'tax': 'تقرير الضرائب'
            };

            alert(`سيتم إنشاء ${reportTypes[type]}`);
        }

        // Export reports
        function exportReports() {
            alert('سيتم تصدير التقارير إلى ملف Excel');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Set default date range (last 30 days)
            const today = new Date();
            const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

            document.getElementById('dateFrom').value = lastMonth.toISOString().split('T')[0];
            document.getElementById('dateTo').value = today.toISOString().split('T')[0];

            updateReports();

            // Listen for settings updates
            window.addEventListener('settingsUpdated', function() {
                console.log('Settings updated, refreshing currency display...');
                updateCurrencyDisplays();
            });

            // Also listen for settingsChanged event from appSettings
            if (window.appSettings) {
                window.appSettings.addListener('settingsChanged', function() {
                    console.log('AppSettings changed, refreshing currency display...');
                    updateCurrencyDisplays();
                });
            }
        });
    </script>

</body>
</html>
