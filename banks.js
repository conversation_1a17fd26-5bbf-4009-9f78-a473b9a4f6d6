// Banks Management System
let banks = [];

// Get currency symbol from company settings
function getCurrencySymbol() {
    const companyData = localStorage.getItem('anwar_bakery_company');
    if (companyData) {
        const company = JSON.parse(companyData);
        return company.currency || 'ر.س';
    }
    return 'ر.س';
}

// Check authentication
function checkAuth() {
    const session = localStorage.getItem('anwar_bakery_session') ||
                   sessionStorage.getItem('anwar_bakery_session');

    if (!session) {
        window.location.href = 'login.html';
        return null;
    }

    return JSON.parse(session);
}

// Load user info
function loadUserInfo() {
    const session = checkAuth();
    if (session) {
        document.getElementById('userFullName').textContent = session.fullName;
        document.getElementById('userDisplayName').textContent = session.fullName;
        document.getElementById('userRole').textContent = session.role;

        const loginTime = new Date(session.loginTime);
        document.getElementById('loginTime').textContent =
            'آخر دخول: ' + loginTime.toLocaleString('ar-SA');
    }
}

// Load company info
function loadCompanyInfo() {
    const savedData = localStorage.getItem('anwar_bakery_company');
    if (savedData) {
        const companyData = JSON.parse(savedData);
        if (companyData.companyNameAr) {
            document.getElementById('sidebarCompanyName').textContent = companyData.companyNameAr;
            document.title = `إدارة البنوك - ${companyData.companyNameAr}`;
        }
    }
}

// Load banks from localStorage
function loadBanks() {
    const savedBanks = localStorage.getItem('anwar_bakery_banks');
    if (savedBanks) {
        banks = JSON.parse(savedBanks);
    } else {
        // Create sample banks
        banks = [
            {
                id: 1,
                bankName: 'البنك الأهلي السعودي',
                accountNumber: '*********************',
                accountType: 'current',
                branchId: 1,
                branchName: 'الفرع الرئيسي',
                openingBalance: 145000.00,
                currentBalance: 150000.00,
                currency: getCurrencySymbol(),
                swiftCode: 'NCBKSARI',
                branchAddress: 'الرياض - شارع الملك فهد',
                isActive: true,
                createdAt: '2024-01-15'
            },
            {
                id: 2,
                bankName: 'بنك الراجحي',
                accountNumber: '*********************',
                accountType: 'current',
                branchId: 2,
                branchName: 'فرع الملك فهد',
                openingBalance: 80000.00,
                currentBalance: 85000.00,
                currency: getCurrencySymbol(),
                swiftCode: 'RJHISARI',
                branchAddress: 'جدة - شارع التحلية',
                isActive: true,
                createdAt: '2024-01-20'
            },
            {
                id: 3,
                bankName: 'البنك السعودي للاستثمار',
                accountNumber: '*********************',
                accountType: 'investment',
                branchId: 1,
                branchName: 'الفرع الرئيسي',
                openingBalance: 240000.00,
                currentBalance: 250000.00,
                currency: getCurrencySymbol(),
                swiftCode: 'SIBCSARI',
                branchAddress: 'الدمام - الكورنيش',
                isActive: true,
                createdAt: '2024-02-01'
            }
        ];
        saveBanks();
    }
    renderBanks();
    updateStats();
}

// Save banks to localStorage
function saveBanks() {
    localStorage.setItem('anwar_bakery_banks', JSON.stringify(banks));
}

// Update statistics
function updateStats() {
    const totalBanks = banks.length;
    const activeBanks = banks.filter(b => b.isActive).length;
    const totalBalance = banks.reduce((sum, b) => sum + (b.currentBalance || 0), 0);
    const averageBalance = totalBanks > 0 ? totalBalance / totalBanks : 0;

    document.getElementById('totalBanks').textContent = totalBanks;
    document.getElementById('activeBanks').textContent = activeBanks;
    document.getElementById('totalBalance').textContent = totalBalance.toFixed(2);
    document.getElementById('averageBalance').textContent = averageBalance.toFixed(2);
}

// Render banks table
function renderBanks(filteredBanks = banks) {
    const tbody = document.getElementById('banksTableBody');
    tbody.innerHTML = '';

    filteredBanks.forEach(bank => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';

        const accountTypeNames = {
            'current': 'حساب جاري',
            'savings': 'حساب توفير',
            'investment': 'حساب استثماري'
        };

        const statusClass = bank.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
        const statusText = bank.isActive ? 'نشط' : 'معطل';

        row.innerHTML = `
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <div class="font-medium">${bank.bankName}</div>
                <div class="text-xs text-gray-500">${bank.swiftCode || ''}</div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm font-mono text-gray-900">${bank.accountNumber}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                    ${accountTypeNames[bank.accountType] || 'غير محدد'}
                </span>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">${bank.branchName || '-'}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 font-semibold ${bank.currentBalance >= 0 ? 'text-green-600' : 'text-red-600'}">
                ${(bank.currentBalance || 0).toFixed(2)} ${bank.currency}
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">${bank.currency}</td>
            <td class="px-4 py-3 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                    ${statusText}
                </span>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-center">
                <div class="flex justify-center space-x-1">
                    <button onclick="editBank(${bank.id})" class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50" title="تعديل">
                        ✏️
                    </button>
                    <button onclick="viewBankDetails(${bank.id})" class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50" title="عرض التفاصيل">
                        👁️
                    </button>
                    <button onclick="toggleBankStatus(${bank.id})" class="text-yellow-600 hover:text-yellow-900 p-1 rounded hover:bg-yellow-50" title="${bank.isActive ? 'إلغاء تفعيل' : 'تفعيل'}">
                        ${bank.isActive ? '⏸️' : '▶️'}
                    </button>
                    <button onclick="deleteBank(${bank.id})" class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50" title="حذف">
                        🗑️
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Filter banks
function filterBanks() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const branchFilter = document.getElementById('branchFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;

    const filtered = banks.filter(bank => {
        const matchesSearch = bank.bankName.toLowerCase().includes(searchTerm) ||
                            bank.accountNumber.toLowerCase().includes(searchTerm) ||
                            (bank.swiftCode && bank.swiftCode.toLowerCase().includes(searchTerm));

        let matchesStatus = true;
        if (statusFilter === 'active') {
            matchesStatus = bank.isActive;
        } else if (statusFilter === 'inactive') {
            matchesStatus = !bank.isActive;
        }

        const matchesBranch = !branchFilter || bank.branchId == branchFilter;
        const matchesType = !typeFilter || bank.accountType === typeFilter;

        return matchesSearch && matchesStatus && matchesBranch && matchesType;
    });

    renderBanks(filtered);
}

// Load branches for filters
function loadBranches() {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    let branches = [];

    if (savedBranches) {
        branches = JSON.parse(savedBranches);
    } else {
        branches = [
            { id: 1, branchName: 'الفرع الرئيسي', isActive: true },
            { id: 2, branchName: 'فرع الملك فهد', isActive: true },
            { id: 3, branchName: 'فرع العليا', isActive: true }
        ];
        localStorage.setItem('anwar_bakery_branches', JSON.stringify(branches));
    }

    const branchFilter = document.getElementById('branchFilter');
    if (branchFilter) {
        branchFilter.innerHTML = '<option value="">جميع الفروع</option>';
        branches.filter(branch => branch.isActive).forEach(branch => {
            const option = document.createElement('option');
            option.value = branch.id;
            option.textContent = branch.branchName;
            branchFilter.appendChild(option);
        });
    }
}

// Populate branch dropdown
function populateBranchDropdown() {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    let branches = [];

    if (savedBranches) {
        branches = JSON.parse(savedBranches);
    } else {
        branches = [
            { id: 1, branchName: 'الفرع الرئيسي', isActive: true },
            { id: 2, branchName: 'فرع الملك فهد', isActive: true },
            { id: 3, branchName: 'فرع العليا', isActive: true }
        ];
        localStorage.setItem('anwar_bakery_branches', JSON.stringify(branches));
    }

    const dropdown = document.getElementById('bankBranch');
    if (dropdown) {
        dropdown.innerHTML = '<option value="">اختر الفرع</option>';
        branches.filter(branch => branch.isActive).forEach(branch => {
            const option = document.createElement('option');
            option.value = branch.id;
            option.textContent = branch.branchName;
            dropdown.appendChild(option);
        });
    }
}

// Open add modal
function openAddModal() {
    document.getElementById('modalTitle').textContent = 'إضافة بنك جديد';
    document.getElementById('bankForm').reset();
    document.getElementById('bankId').value = '';
    document.getElementById('isActive').checked = true;
    document.getElementById('currency').value = getCurrencySymbol();
    populateBranchDropdown();
    document.getElementById('bankModal').classList.add('active');
}

// Edit bank
function editBank(id) {
    const bank = banks.find(b => b.id === id);
    if (bank) {
        document.getElementById('modalTitle').textContent = 'تعديل البنك';
        document.getElementById('bankId').value = bank.id;
        document.getElementById('bankName').value = bank.bankName;
        document.getElementById('accountNumber').value = bank.accountNumber;
        document.getElementById('accountType').value = bank.accountType;
        document.getElementById('bankBranch').value = bank.branchId || '';
        document.getElementById('currentBalance').value = bank.currentBalance || 0;
        document.getElementById('currency').value = getCurrencySymbol();
        document.getElementById('swiftCode').value = bank.swiftCode || '';
        document.getElementById('branchAddress').value = bank.branchAddress || '';
        document.getElementById('isActive').checked = bank.isActive;
        populateBranchDropdown();
        document.getElementById('bankModal').classList.add('active');
    }
}

// Save bank
function saveBank(event) {
    event.preventDefault();

    const bankId = document.getElementById('bankId').value;
    const branchId = document.getElementById('bankBranch').value;
    let branchName = '';

    if (branchId) {
        const savedBranches = localStorage.getItem('anwar_bakery_branches');
        if (savedBranches) {
            const branches = JSON.parse(savedBranches);
            const branch = branches.find(b => b.id == branchId);
            branchName = branch ? branch.branchName : '';
        }
    }

    const bankData = {
        bankName: document.getElementById('bankName').value,
        accountNumber: document.getElementById('accountNumber').value,
        accountType: document.getElementById('accountType').value,
        branchId: parseInt(branchId) || null,
        branchName: branchName,
        currentBalance: parseFloat(document.getElementById('currentBalance').value) || 0,
        currency: getCurrencySymbol(),
        swiftCode: document.getElementById('swiftCode').value,
        branchAddress: document.getElementById('branchAddress').value,
        isActive: document.getElementById('isActive').checked
    };

    // Check if account number already exists
    const existingBank = banks.find(b => b.accountNumber === bankData.accountNumber && b.id != bankId);
    if (existingBank) {
        showMessage('رقم الحساب موجود مسبقاً!', 'error');
        return;
    }

    if (bankId) {
        // Update existing bank
        const bankIndex = banks.findIndex(b => b.id == bankId);
        if (bankIndex !== -1) {
            banks[bankIndex] = { ...banks[bankIndex], ...bankData };
            showMessage('تم تحديث البنك بنجاح!', 'success');
        }
    } else {
        // Add new bank
        bankData.id = Date.now();
        bankData.createdAt = new Date().toISOString().split('T')[0];
        banks.push(bankData);
        showMessage('تم إضافة البنك بنجاح!', 'success');
    }

    saveBanks();
    renderBanks();
    updateStats();
    closeModal();
}

// Close modal
function closeModal() {
    document.getElementById('bankModal').classList.remove('active');
}

// Toggle bank status
function toggleBankStatus(id) {
    const bank = banks.find(b => b.id === id);
    if (bank) {
        bank.isActive = !bank.isActive;
        saveBanks();
        renderBanks();
        updateStats();
        showMessage(`تم ${bank.isActive ? 'تفعيل' : 'إلغاء تفعيل'} البنك بنجاح!`, 'success');
    }
}

// Delete bank
function deleteBank(id) {
    if (confirm('هل أنت متأكد من حذف هذا البنك؟')) {
        banks = banks.filter(b => b.id !== id);
        saveBanks();
        renderBanks();
        updateStats();
        showMessage('تم حذف البنك بنجاح!', 'success');
    }
}

// Show message
function showMessage(message, type) {
    const container = document.getElementById('messageContainer');
    const alertClass = {
        'success': 'bg-green-50 border-green-200 text-green-700',
        'error': 'bg-red-50 border-red-200 text-red-700',
        'info': 'bg-blue-50 border-blue-200 text-blue-700'
    };

    container.innerHTML = `
        <div class="border rounded-lg p-4 ${alertClass[type]}">
            <div class="flex items-center">
                <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                ${message}
            </div>
        </div>
    `;

    setTimeout(() => {
        container.innerHTML = '';
    }, 5000);
}

// Update current date time
function updateDateTime() {
    const now = new Date();
    const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    document.getElementById('currentDateTime').textContent =
        now.toLocaleDateString('ar-SA', options);
}

// Toggle sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (sidebar.classList.contains('translate-x-full')) {
        sidebar.classList.remove('translate-x-full');
        overlay.classList.remove('hidden');
    } else {
        sidebar.classList.add('translate-x-full');
        overlay.classList.add('hidden');
    }
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('anwar_bakery_session');
        sessionStorage.removeItem('anwar_bakery_session');
        window.location.href = 'login.html';
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    checkAuth();
    loadUserInfo();
    loadCompanyInfo();
    loadBranches();
    loadBanks();
    updateDateTime();
    setInterval(updateDateTime, 60000);
});

// Close sidebar when clicking overlay
document.getElementById('mobileOverlay').addEventListener('click', toggleSidebar);
