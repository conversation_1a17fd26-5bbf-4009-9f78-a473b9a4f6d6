<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة مبيعات نقطة البيع - مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .custom-scroll {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 #f7fafc;
        }

        .custom-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scroll::-webkit-scrollbar-track {
            background: #f7fafc;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        .sale-row:hover {
            background-color: #f8fafc;
        }

        .payment-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-weight: 500;
        }

        .payment-cash {
            background-color: #dcfce7;
            color: #166534;
        }

        .payment-card {
            background-color: #dbeafe;
            color: #1e40af;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="bg-white w-64 shadow-lg flex flex-col">
            <div class="p-4 flex-shrink-0">
                <div class="flex items-center mb-8">
                    <span class="text-2xl ml-3">🍞</span>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800" id="companyName">مخبز أنوار الحي</h1>
                        <p class="text-sm text-gray-600" id="companySlogan">جودة تستحق الثقة</p>
                    </div>
                </div>
            </div>

            <nav class="flex-1 overflow-y-auto px-4 pb-4 custom-scroll">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    الرئيسية
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-purple-600 bg-purple-50 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏭</span>
                    الموردين
                </a>

                <!-- User Info & Logout -->
                <div class="mt-auto p-4 border-t border-gray-200">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                            أ
                        </div>
                        <div class="mr-3 flex-1">
                            <p id="userFullName" class="text-sm font-medium text-gray-900">أحمد محمد</p>
                            <p id="userRole" class="text-xs text-gray-500">مدير النظام</p>
                        </div>
                        <button onclick="logout()" class="text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50" title="تسجيل الخروج">
                            <span class="text-lg">🚪</span>
                        </button>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col min-h-0">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <a href="invoices.html" class="text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">←</span>
                            </a>
                            <h2 class="text-xl font-semibold text-gray-900">قائمة مبيعات نقطة البيع</h2>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="window.location.href='pos-system.html'" class="bg-purple-600 text-white px-4 py-2 rounded text-sm hover:bg-purple-700">
                                ⚡ فتح نقطة البيع
                            </button>
                            <button onclick="window.history.back()" class="text-gray-600 hover:text-gray-800 px-2 py-2 rounded text-sm">
                                ← رجوع
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6 custom-scroll">
                <!-- Filters -->
                <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">البحث والتصفية</h3>
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <input type="text" id="searchInput" placeholder="البحث برقم البيع أو الكاشير..."
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                                   onkeyup="filterSales()">
                        </div>
                        <div>
                            <select id="paymentFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm" onchange="filterSales()">
                                <option value="">جميع طرق الدفع</option>
                                <option value="cash">نقدي</option>
                                <option value="card">بطاقة</option>
                            </select>
                        </div>
                        <div>
                            <select id="cashierFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm" onchange="filterSales()">
                                <option value="">جميع الكاشيرين</option>
                            </select>
                        </div>
                        <div>
                            <input type="date" id="dateFrom"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                                   onchange="filterSales()">
                        </div>
                        <div>
                            <input type="date" id="dateTo"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                                   onchange="filterSales()">
                        </div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                                <span class="text-2xl">📊</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">إجمالي المبيعات</p>
                                <p class="text-2xl font-bold text-gray-900" id="totalSales">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <span class="text-2xl">💵</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">مبيعات نقدية</p>
                                <p class="text-2xl font-bold text-gray-900" id="cashSales">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <span class="text-2xl">💳</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">مبيعات بطاقة</p>
                                <p class="text-2xl font-bold text-gray-900" id="cardSales">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                                <span class="text-2xl">💰</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">إجمالي المبلغ</p>
                                <p class="text-2xl font-bold text-gray-900" id="totalAmount">0</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sales Table -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم البيع</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ والوقت</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكاشير</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">عدد الأصناف</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المبلغ</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">طريقة الدفع</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المدفوع</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الباقي</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="salesTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- Sales will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Sample sales data
        const posSales = [
            {
                id: 1,
                number: 'POS-2024-001',
                datetime: '2024-01-15 09:30:00',
                cashier: 'أحمد محمد',
                itemsCount: 5,
                amount: 45.50,
                paymentMethod: 'cash',
                paid: 50.00,
                change: 4.50
            },
            {
                id: 2,
                number: 'POS-2024-002',
                datetime: '2024-01-15 10:15:00',
                cashier: 'فاطمة علي',
                itemsCount: 3,
                amount: 28.75,
                paymentMethod: 'card',
                paid: 28.75,
                change: 0.00
            },
            {
                id: 3,
                number: 'POS-2024-003',
                datetime: '2024-01-15 11:00:00',
                cashier: 'محمد سالم',
                itemsCount: 8,
                amount: 67.25,
                paymentMethod: 'cash',
                paid: 70.00,
                change: 2.75
            }
        ];

        // Load sales
        function loadSales() {
            const tbody = document.getElementById('salesTableBody');
            
            tbody.innerHTML = posSales.map(sale => `
                <tr class="sale-row">
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">${sale.number}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${formatDateTime(sale.datetime)}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${sale.cashier}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${sale.itemsCount}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">${sale.amount.toFixed(2)} ر.س</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <span class="payment-badge payment-${sale.paymentMethod}">
                            ${getPaymentMethodText(sale.paymentMethod)}
                        </span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${sale.paid.toFixed(2)} ر.س</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${sale.change.toFixed(2)} ر.س</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-center text-sm font-medium">
                        <div class="flex justify-center space-x-2">
                            <button onclick="viewSale(${sale.id})" class="text-purple-600 hover:text-purple-900" title="عرض">
                                👁️
                            </button>
                            <button onclick="printReceipt(${sale.id})" class="text-blue-600 hover:text-blue-900" title="طباعة الإيصال">
                                🖨️
                            </button>
                            <button onclick="refundSale(${sale.id})" class="text-orange-600 hover:text-orange-900" title="إرجاع">
                                ↩️
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');

            updateStatistics();
        }

        // Format datetime
        function formatDateTime(datetimeString) {
            const date = new Date(datetimeString);
            return date.toLocaleString('ar-SA');
        }

        // Get payment method text
        function getPaymentMethodText(method) {
            const methodTexts = {
                'cash': 'نقدي',
                'card': 'بطاقة'
            };
            return methodTexts[method] || method;
        }

        // Update statistics
        function updateStatistics() {
            document.getElementById('totalSales').textContent = posSales.length;
            
            const cashSalesCount = posSales.filter(sale => sale.paymentMethod === 'cash').length;
            const cardSalesCount = posSales.filter(sale => sale.paymentMethod === 'card').length;
            
            document.getElementById('cashSales').textContent = cashSalesCount;
            document.getElementById('cardSales').textContent = cardSalesCount;
            
            const totalAmount = posSales.reduce((sum, sale) => sum + sale.amount, 0);
            document.getElementById('totalAmount').textContent = totalAmount.toFixed(2) + ' ر.س';
        }

        // Filter sales
        function filterSales() {
            // Implementation for filtering
            console.log('Filtering sales...');
        }

        // Action functions
        function viewSale(id) {
            alert(`عرض تفاصيل البيع رقم ${id}`);
        }

        function printReceipt(id) {
            alert(`طباعة إيصال البيع رقم ${id}`);
        }

        function refundSale(id) {
            if (confirm('هل أنت متأكد من إرجاع هذا البيع؟')) {
                alert(`تم إرجاع البيع رقم ${id}`);
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadSales();
        });
    </script>

</body>
</html>
