<?php
/**
 * Authentication API for Anwar Bakery Management System
 * واجهة برمجة التطبيقات للمصادقة - نظام إدارة مخبز أنوار الحي
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config/database.php';

class AuthAPI {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }

    /**
     * Handle API requests
     * معالجة طلبات API
     */
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = $_GET['action'] ?? '';

        try {
            switch ($method) {
                case 'POST':
                    switch ($path) {
                        case 'login':
                            return $this->login();
                        case 'logout':
                            return $this->logout();
                        case 'register':
                            return $this->register();
                        case 'change-password':
                            return $this->changePassword();
                        default:
                            return $this->sendError('Invalid action', 400);
                    }
                    break;
                
                case 'GET':
                    switch ($path) {
                        case 'verify':
                            return $this->verifyToken();
                        case 'profile':
                            return $this->getProfile();
                        default:
                            return $this->sendError('Invalid action', 400);
                    }
                    break;
                
                default:
                    return $this->sendError('Method not allowed', 405);
            }
        } catch (Exception $e) {
            return $this->sendError($e->getMessage(), 500);
        }
    }

    /**
     * User login
     * تسجيل دخول المستخدم
     */
    private function login() {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($input['username']) || !isset($input['password'])) {
            return $this->sendError('Username and password are required', 400);
        }

        $username = trim($input['username']);
        $password = $input['password'];

        // Get user from database
        $stmt = $this->conn->prepare("
            SELECT id, username, password_hash, full_name, email, phone, role, is_active 
            FROM users 
            WHERE username = ? AND is_active = 1
        ");
        $stmt->execute([$username]);
        $user = $stmt->fetch();

        if (!$user) {
            return $this->sendError('Invalid username or password', 401);
        }

        // Verify password
        if (!password_verify($password, $user['password_hash'])) {
            return $this->sendError('Invalid username or password', 401);
        }

        // Update last login
        $updateStmt = $this->conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        $updateStmt->execute([$user['id']]);

        // Generate token
        $token = $this->generateToken($user);

        // Log login
        $this->logActivity($user['id'], 'login', 'users', $user['id']);

        return $this->sendSuccess([
            'token' => $token,
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'fullName' => $user['full_name'],
                'email' => $user['email'],
                'phone' => $user['phone'],
                'role' => $user['role']
            ]
        ], 'Login successful');
    }

    /**
     * User logout
     * تسجيل خروج المستخدم
     */
    private function logout() {
        $user = $this->getCurrentUser();
        if ($user) {
            $this->logActivity($user['id'], 'logout', 'users', $user['id']);
        }
        
        return $this->sendSuccess([], 'Logout successful');
    }

    /**
     * Register new user
     * تسجيل مستخدم جديد
     */
    private function register() {
        $currentUser = $this->getCurrentUser();
        if (!$currentUser || $currentUser['role'] !== 'admin') {
            return $this->sendError('Unauthorized', 403);
        }

        $input = json_decode(file_get_contents('php://input'), true);
        
        $required = ['username', 'password', 'fullName', 'role'];
        foreach ($required as $field) {
            if (!isset($input[$field]) || empty(trim($input[$field]))) {
                return $this->sendError("Field {$field} is required", 400);
            }
        }

        $username = trim($input['username']);
        $password = $input['password'];
        $fullName = trim($input['fullName']);
        $email = trim($input['email'] ?? '');
        $phone = trim($input['phone'] ?? '');
        $role = $input['role'];

        // Validate role
        $validRoles = ['admin', 'manager', 'cashier', 'viewer'];
        if (!in_array($role, $validRoles)) {
            return $this->sendError('Invalid role', 400);
        }

        // Check if username exists
        $stmt = $this->conn->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->execute([$username]);
        if ($stmt->fetch()) {
            return $this->sendError('Username already exists', 409);
        }

        // Hash password
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);

        // Insert user
        $stmt = $this->conn->prepare("
            INSERT INTO users (username, password_hash, full_name, email, phone, role) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        if ($stmt->execute([$username, $passwordHash, $fullName, $email, $phone, $role])) {
            $userId = $this->conn->lastInsertId();
            
            // Log activity
            $this->logActivity($currentUser['id'], 'create_user', 'users', $userId);
            
            return $this->sendSuccess([
                'id' => $userId,
                'username' => $username,
                'fullName' => $fullName,
                'role' => $role
            ], 'User created successfully');
        } else {
            return $this->sendError('Failed to create user', 500);
        }
    }

    /**
     * Change password
     * تغيير كلمة المرور
     */
    private function changePassword() {
        $user = $this->getCurrentUser();
        if (!$user) {
            return $this->sendError('Unauthorized', 401);
        }

        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($input['currentPassword']) || !isset($input['newPassword'])) {
            return $this->sendError('Current password and new password are required', 400);
        }

        $currentPassword = $input['currentPassword'];
        $newPassword = $input['newPassword'];

        // Get current password hash
        $stmt = $this->conn->prepare("SELECT password_hash FROM users WHERE id = ?");
        $stmt->execute([$user['id']]);
        $result = $stmt->fetch();

        if (!password_verify($currentPassword, $result['password_hash'])) {
            return $this->sendError('Current password is incorrect', 400);
        }

        // Update password
        $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);
        $stmt = $this->conn->prepare("UPDATE users SET password_hash = ? WHERE id = ?");
        
        if ($stmt->execute([$newPasswordHash, $user['id']])) {
            $this->logActivity($user['id'], 'change_password', 'users', $user['id']);
            return $this->sendSuccess([], 'Password changed successfully');
        } else {
            return $this->sendError('Failed to change password', 500);
        }
    }

    /**
     * Verify token
     * التحقق من الرمز المميز
     */
    private function verifyToken() {
        $user = $this->getCurrentUser();
        if ($user) {
            return $this->sendSuccess([
                'valid' => true,
                'user' => $user
            ]);
        } else {
            return $this->sendError('Invalid token', 401);
        }
    }

    /**
     * Get user profile
     * الحصول على ملف المستخدم الشخصي
     */
    private function getProfile() {
        $user = $this->getCurrentUser();
        if (!$user) {
            return $this->sendError('Unauthorized', 401);
        }

        return $this->sendSuccess($user);
    }

    /**
     * Generate JWT token
     * إنشاء رمز JWT
     */
    private function generateToken($user) {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload = json_encode([
            'user_id' => $user['id'],
            'username' => $user['username'],
            'role' => $user['role'],
            'iat' => time(),
            'exp' => time() + (24 * 60 * 60) // 24 hours
        ]);

        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, 'anwar_bakery_secret', true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }

    /**
     * Get current user from token
     * الحصول على المستخدم الحالي من الرمز المميز
     */
    private function getCurrentUser() {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? '';
        
        if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return null;
        }

        $token = $matches[1];
        $parts = explode('.', $token);
        
        if (count($parts) !== 3) {
            return null;
        }

        $payload = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $parts[1])), true);
        
        if (!$payload || $payload['exp'] < time()) {
            return null;
        }

        // Get user from database
        $stmt = $this->conn->prepare("
            SELECT id, username, full_name, email, phone, role 
            FROM users 
            WHERE id = ? AND is_active = 1
        ");
        $stmt->execute([$payload['user_id']]);
        
        return $stmt->fetch();
    }

    /**
     * Log user activity
     * تسجيل نشاط المستخدم
     */
    private function logActivity($userId, $action, $tableName = null, $recordId = null) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO system_logs (user_id, action, table_name, record_id, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $userId,
                $action,
                $tableName,
                $recordId,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
        } catch (Exception $e) {
            error_log("Failed to log activity: " . $e->getMessage());
        }
    }

    /**
     * Send success response
     * إرسال استجابة نجاح
     */
    private function sendSuccess($data, $message = 'Success') {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => $message,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }

    /**
     * Send error response
     * إرسال استجابة خطأ
     */
    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'message' => $message
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
}

// Handle the request
$api = new AuthAPI();
$api->handleRequest();
?>
