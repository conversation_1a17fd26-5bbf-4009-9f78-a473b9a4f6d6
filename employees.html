<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموظفين - مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .custom-scroll {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 #f7fafc;
        }

        .custom-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scroll::-webkit-scrollbar-track {
            background: #f7fafc;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        .modal {
            display: none;
        }

        .modal.active {
            display: flex;
        }

        .employee-card {
            transition: all 0.3s ease;
        }

        .employee-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="bg-white w-64 shadow-lg flex flex-col">
            <div class="p-4 flex-shrink-0">
                <div class="flex items-center mb-8">
                    <span class="text-2xl ml-3">🍞</span>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800" id="companyName">مخبز أنوار الحي</h1>
                        <p class="text-sm text-gray-600" id="companySlogan">جودة تستحق الثقة</p>
                    </div>
                </div>
            </div>

            <nav class="flex-1 overflow-y-auto px-4 pb-4 custom-scroll">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    الرئيسية
                </a>
                <a href="users-management.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👥</span>
                    إدارة المستخدمين
                </a>
                <a href="employees.html" class="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">
                    <span class="ml-3">👨‍💼</span>
                    إدارة الموظفين
                </a>
                <a href="company-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏢</span>
                    بيانات المنشأة
                </a>
                <a href="branches-warehouses.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏪</span>
                    الفروع والمخازن
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏭</span>
                    الموردين
                </a>
                <a href="cash-registers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">💰</span>
                    الصناديق
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>

                <!-- User Info & Logout -->
                <div class="mt-auto p-4 border-t border-gray-200">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                            أ
                        </div>
                        <div class="mr-3 flex-1">
                            <p id="userFullName" class="text-sm font-medium text-gray-900">أحمد محمد</p>
                            <p id="userRole" class="text-xs text-gray-500">مدير النظام</p>
                        </div>
                        <button onclick="logout()" class="text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50" title="تسجيل الخروج">
                            <span class="text-lg">🚪</span>
                        </button>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col min-h-0">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <h2 class="text-xl font-semibold text-gray-900">إدارة الموظفين</h2>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="openSalariesModal()" class="bg-green-600 text-white px-3 py-2 rounded hover:bg-green-700 text-sm">
                                💰 المرتبات
                            </button>
                            <button onclick="openPenaltiesModal()" class="bg-red-600 text-white px-3 py-2 rounded hover:bg-red-700 text-sm">
                                ⚠️ الجزاءات
                            </button>
                            <button onclick="openDeductionsModal()" class="bg-orange-600 text-white px-3 py-2 rounded hover:bg-orange-700 text-sm">
                                📉 الخصومات
                            </button>
                            <button onclick="openSalaryReportModal()" class="bg-purple-600 text-white px-3 py-2 rounded hover:bg-purple-700 text-sm">
                                📋 تقرير المرتبات
                            </button>
                            <button onclick="exportEmployees()" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded text-sm">
                                📊 تصدير
                            </button>
                            <button onclick="openAddEmployeeModal()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center text-sm">
                                <span class="ml-1">➕</span>
                                إضافة موظف
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6 custom-scroll">
                <!-- Header -->
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900 mb-2 flex items-center">
                        <span class="ml-3">👨‍💼</span>
                        إدارة الموظفين
                    </h1>
                    <p class="text-gray-600">
                        إدارة شاملة لبيانات الموظفين والرواتب والحضور والانصراف
                    </p>
                </div>

                <!-- Success/Error Messages -->
                <div id="messageContainer" class="mb-6"></div>

                <!-- Statistics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg p-4 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100 text-sm">إجمالي الموظفين</p>
                                <p class="text-2xl font-bold" id="totalEmployees">0</p>
                            </div>
                            <div class="text-2xl opacity-80">👥</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-lg p-4 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-100 text-sm">الموظفين النشطين</p>
                                <p class="text-2xl font-bold" id="activeEmployees">0</p>
                            </div>
                            <div class="text-2xl opacity-80">✅</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-lg p-4 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-100 text-sm">إجمالي الرواتب</p>
                                <p class="text-2xl font-bold" id="totalSalaries">0</p>
                            </div>
                            <div class="text-2xl opacity-80">💰</div>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg shadow-lg p-4 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-orange-100 text-sm">الحاضرين اليوم</p>
                                <p class="text-2xl font-bold" id="presentToday">0</p>
                            </div>
                            <div class="text-2xl opacity-80">📅</div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <input type="text" id="searchInput" placeholder="البحث بالاسم أو الرقم الوظيفي..."
                                   class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                                   onkeyup="filterEmployees()">
                        </div>
                        <div>
                            <select id="branchFilter" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm" onchange="filterEmployees()">
                                <option value="">جميع الفروع</option>
                            </select>
                        </div>
                        <div>
                            <select id="departmentFilter" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm" onchange="filterEmployees()">
                                <option value="">جميع الأقسام</option>
                                <option value="management">الإدارة</option>
                                <option value="production">الإنتاج</option>
                                <option value="sales">المبيعات</option>
                                <option value="accounting">المحاسبة</option>
                                <option value="maintenance">الصيانة</option>
                            </select>
                        </div>
                        <div>
                            <select id="positionFilter" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm" onchange="filterEmployees()">
                                <option value="">جميع المناصب</option>
                                <option value="manager">مدير</option>
                                <option value="supervisor">مشرف</option>
                                <option value="baker">خباز</option>
                                <option value="cashier">كاشير</option>
                                <option value="accountant">محاسب</option>
                                <option value="worker">عامل</option>
                            </select>
                        </div>
                        <div>
                            <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm" onchange="filterEmployees()">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="vacation">في إجازة</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Employees Table -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الصورة</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الرقم الوظيفي</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الاسم</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المنصب</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">القسم</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الفرع</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الراتب</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المرتبات</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الجزاءات</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="employeesTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- Employees will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add/Edit Employee Modal -->
    <div id="employeeModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900" id="modalTitle">إضافة موظف جديد</h3>
                    <button onclick="closeEmployeeModal()" class="text-gray-400 hover:text-gray-600">
                        <span class="text-2xl">×</span>
                    </button>
                </div>

                <form id="employeeForm" onsubmit="saveEmployee(event)" class="space-y-4">
                    <input type="hidden" id="employeeId">

                    <!-- Personal Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الاسم الكامل *</label>
                            <input type="text" id="fullName" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">رقم الهوية *</label>
                            <input type="text" id="nationalId" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">رقم الجوال *</label>
                            <input type="tel" id="phone" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                            <input type="email" id="email"
                                   class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                        </div>
                    </div>

                    <!-- Work Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الرقم الوظيفي *</label>
                            <input type="text" id="employeeNumber" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الفرع *</label>
                            <select id="branchId" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                                <option value="">اختر الفرع</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">القسم *</label>
                            <select id="department" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                                <option value="">اختر القسم</option>
                                <option value="management">الإدارة</option>
                                <option value="production">الإنتاج</option>
                                <option value="sales">المبيعات</option>
                                <option value="accounting">المحاسبة</option>
                                <option value="maintenance">الصيانة</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">المنصب *</label>
                            <select id="position" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                                <option value="">اختر المنصب</option>
                                <option value="manager">مدير</option>
                                <option value="supervisor">مشرف</option>
                                <option value="baker">خباز</option>
                                <option value="cashier">كاشير</option>
                                <option value="accountant">محاسب</option>
                                <option value="worker">عامل</option>
                            </select>
                        </div>
                    </div>

                    <!-- Salary and Dates -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الراتب الأساسي *</label>
                            <input type="number" id="basicSalary" required min="0" step="0.01"
                                   class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ التوظيف *</label>
                            <input type="date" id="hireDate" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                            <select id="status"
                                    class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                                <option value="vacation">في إجازة</option>
                            </select>
                        </div>
                    </div>

                    <!-- Address -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">العنوان</label>
                        <textarea id="address" rows="2"
                                  class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                                  placeholder="العنوان الكامل..."></textarea>
                    </div>

                    <!-- Notes -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">ملاحظات</label>
                        <textarea id="notes" rows="2"
                                  class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
                                  placeholder="ملاحظات إضافية..."></textarea>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex justify-end space-x-2 pt-4">
                        <button type="button" onclick="closeEmployeeModal()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 text-sm">
                            إلغاء
                        </button>
                        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 text-sm">
                            حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Salaries Management Modal -->
    <div id="salariesModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-5/6 lg:w-4/5 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">إدارة المرتبات</h3>
                    <button onclick="closeSalariesModal()" class="text-gray-400 hover:text-gray-600">
                        <span class="text-2xl">×</span>
                    </button>
                </div>

                <!-- Salary Filters -->
                <div class="bg-gray-50 p-4 rounded-lg mb-4">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الشهر</label>
                            <select id="salaryMonth" class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                                <option value="1">يناير</option>
                                <option value="2">فبراير</option>
                                <option value="3">مارس</option>
                                <option value="4">أبريل</option>
                                <option value="5">مايو</option>
                                <option value="6">يونيو</option>
                                <option value="7">يوليو</option>
                                <option value="8">أغسطس</option>
                                <option value="9">سبتمبر</option>
                                <option value="10">أكتوبر</option>
                                <option value="11">نوفمبر</option>
                                <option value="12">ديسمبر</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">السنة</label>
                            <select id="salaryYear" class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                                <option value="2024">2024</option>
                                <option value="2023">2023</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الفرع</label>
                            <select id="salaryBranchFilter" class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                                <option value="">جميع الفروع</option>
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button onclick="generateSalaries()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 text-sm">
                                🧮 احتساب المرتبات
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Salaries Table -->
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500">الموظف</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500">الراتب الأساسي</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500">البدلات</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500">الخصومات</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500">الجزاءات</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500">صافي الراتب</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500">الحالة</th>
                                <th class="px-3 py-2 text-center text-xs font-medium text-gray-500">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="salariesTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- Salaries will be populated here -->
                        </tbody>
                    </table>
                </div>

                <div class="flex justify-end mt-4">
                    <button onclick="closeSalariesModal()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 text-sm">
                        إغلاق
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Penalties Management Modal -->
    <div id="penaltiesModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">إدارة الجزاءات</h3>
                    <button onclick="closePenaltiesModal()" class="text-gray-400 hover:text-gray-600">
                        <span class="text-2xl">×</span>
                    </button>
                </div>

                <!-- Add Penalty Form -->
                <div class="bg-red-50 p-4 rounded-lg mb-4">
                    <h4 class="text-md font-semibold text-red-800 mb-3">إضافة جزاء جديد</h4>
                    <form id="penaltyForm" onsubmit="addPenalty(event)">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">الموظف *</label>
                                <select id="penaltyEmployeeId" required class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                                    <option value="">اختر الموظف</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">نوع الجزاء *</label>
                                <select id="penaltyType" required class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                                    <option value="">اختر النوع</option>
                                    <option value="late">تأخير</option>
                                    <option value="absence">غياب</option>
                                    <option value="misconduct">سوء سلوك</option>
                                    <option value="violation">مخالفة</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">المبلغ *</label>
                                <input type="number" id="penaltyAmount" required step="0.01" min="0"
                                       class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">التاريخ *</label>
                                <input type="date" id="penaltyDate" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                            </div>
                        </div>
                        <div class="mt-3">
                            <label class="block text-sm font-medium text-gray-700 mb-1">السبب</label>
                            <textarea id="penaltyReason" rows="2"
                                      class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                      placeholder="تفاصيل سبب الجزاء..."></textarea>
                        </div>
                        <div class="mt-3 flex justify-end">
                            <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 text-sm">
                                إضافة الجزاء
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Penalties Table -->
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500">التاريخ</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500">الموظف</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500">نوع الجزاء</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500">المبلغ</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500">السبب</th>
                                <th class="px-3 py-2 text-center text-xs font-medium text-gray-500">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="penaltiesTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- Penalties will be populated here -->
                        </tbody>
                    </table>
                </div>

                <div class="flex justify-end mt-4">
                    <button onclick="closePenaltiesModal()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 text-sm">
                        إغلاق
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Deductions Management Modal -->
    <div id="deductionsModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">إدارة الخصومات</h3>
                    <button onclick="closeDeductionsModal()" class="text-gray-400 hover:text-gray-600">
                        <span class="text-2xl">×</span>
                    </button>
                </div>

                <!-- Add Deduction Form -->
                <div class="bg-orange-50 p-4 rounded-lg mb-4">
                    <h4 class="text-md font-semibold text-orange-800 mb-3">إضافة خصم جديد</h4>
                    <form id="deductionForm" onsubmit="addDeduction(event)">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">الموظف *</label>
                                <select id="deductionEmployeeId" required class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                                    <option value="">اختر الموظف</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">نوع الخصم *</label>
                                <select id="deductionType" required class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                                    <option value="">اختر النوع</option>
                                    <option value="insurance">تأمين</option>
                                    <option value="loan">قرض</option>
                                    <option value="advance">سلفة</option>
                                    <option value="tax">ضريبة</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">المبلغ *</label>
                                <input type="number" id="deductionAmount" required step="0.01" min="0"
                                       class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">التاريخ *</label>
                                <input type="date" id="deductionDate" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                            </div>
                        </div>
                        <div class="mt-3">
                            <label class="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
                            <textarea id="deductionDescription" rows="2"
                                      class="w-full px-3 py-2 border border-gray-300 rounded text-sm"
                                      placeholder="تفاصيل الخصم..."></textarea>
                        </div>
                        <div class="mt-3 flex justify-end">
                            <button type="submit" class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 text-sm">
                                إضافة الخصم
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Deductions Table -->
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500">التاريخ</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500">الموظف</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500">نوع الخصم</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500">المبلغ</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500">الوصف</th>
                                <th class="px-3 py-2 text-center text-xs font-medium text-gray-500">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="deductionsTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- Deductions will be populated here -->
                        </tbody>
                    </table>
                </div>

                <div class="flex justify-end mt-4">
                    <button onclick="closeDeductionsModal()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 text-sm">
                        إغلاق
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Salary Report Modal -->
    <div id="salaryReportModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-5 mx-auto p-5 border w-11/12 md:w-5/6 lg:w-4/5 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">📋 تقرير قيود المرتبات</h3>
                    <button onclick="closeSalaryReportModal()" class="text-gray-400 hover:text-gray-600">
                        <span class="text-2xl">×</span>
                    </button>
                </div>

                <!-- Report Filters -->
                <div class="bg-purple-50 p-4 rounded-lg mb-4">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                            <input type="date" id="reportFromDate" class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                            <input type="date" id="reportToDate" class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">الموظف</label>
                            <select id="reportEmployeeFilter" class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
                                <option value="">جميع الموظفين</option>
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button onclick="generateSalaryReport()" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 text-sm">
                                🔍 إنشاء التقرير
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Report Summary -->
                <div id="reportSummary" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 hidden">
                    <div class="bg-blue-100 p-3 rounded-lg">
                        <div class="text-sm text-blue-600">إجمالي المرتبات</div>
                        <div class="text-lg font-bold text-blue-800" id="totalSalariesReport">0</div>
                    </div>
                    <div class="bg-green-100 p-3 rounded-lg">
                        <div class="text-sm text-green-600">المدفوع</div>
                        <div class="text-lg font-bold text-green-800" id="totalPaidReport">0</div>
                    </div>
                    <div class="bg-yellow-100 p-3 rounded-lg">
                        <div class="text-sm text-yellow-600">المستحق</div>
                        <div class="text-lg font-bold text-yellow-800" id="totalAccruedReport">0</div>
                    </div>
                    <div class="bg-red-100 p-3 rounded-lg">
                        <div class="text-sm text-red-600">المعلق</div>
                        <div class="text-lg font-bold text-red-800" id="totalPendingReport">0</div>
                    </div>
                </div>

                <!-- Report Table -->
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead class="bg-purple-50">
                            <tr>
                                <th class="px-3 py-2 text-right text-xs font-medium text-purple-800">التاريخ</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-purple-800">الموظف</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-purple-800">الشهر</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-purple-800">الراتب الأساسي</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-purple-800">صافي الراتب</th>
                                <th class="px-3 py-2 text-right text-xs font-medium text-purple-800">الحالة</th>
                                <th class="px-3 py-2 text-center text-xs font-medium text-purple-800">القيد المحاسبي</th>
                            </tr>
                        </thead>
                        <tbody id="salaryReportTableBody" class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td colspan="7" class="text-center py-8 text-gray-500">
                                    اختر التواريخ واضغط "إنشاء التقرير" لعرض البيانات
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="flex justify-between mt-4">
                    <button onclick="exportSalaryReport()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 text-sm">
                        📊 تصدير Excel
                    </button>
                    <button onclick="closeSalaryReportModal()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 text-sm">
                        إغلاق
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Journal Entry Details Modal -->
    <div id="journalEntryModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900" id="journalEntryTitle">تفاصيل القيد المحاسبي</h3>
                    <button onclick="closeJournalEntryModal()" class="text-gray-400 hover:text-gray-600">
                        <span class="text-2xl">×</span>
                    </button>
                </div>

                <div id="journalEntryContent" class="space-y-4">
                    <!-- Journal entry details will be populated here -->
                </div>

                <div class="flex justify-end mt-4">
                    <button onclick="closeJournalEntryModal()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 text-sm">
                        إغلاق
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Include advanced libraries -->
    <script src="advanced-excel.js"></script>
    <script src="advanced-print.js"></script>
    <script src="excel-utils.js"></script>
    <script src="global-advanced-features.js"></script>

    <script src="employees.js"></script>
    <script src="currency-test.js"></script>
</body>
</html>
