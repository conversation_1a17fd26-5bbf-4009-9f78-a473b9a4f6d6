// مستمع تحديث العملة الفوري
(function() {
    'use strict';
    
    // وظيفة لتحديث العملة في جميع العناصر
    function updateCurrencyDisplay() {
        console.log('🔄 Updating currency display...');
        
        // الحصول على رمز العملة الجديد
        const currencySymbol = getCurrentCurrencySymbol();
        console.log('New currency symbol:', currencySymbol);
        
        // تحديث الإحصائيات
        if (typeof updateStatistics === 'function') {
            updateStatistics();
        }
        
        // تحديث جدول الموظفين
        if (typeof loadEmployees === 'function') {
            loadEmployees();
        }
        
        // تحديث جدول الرواتب
        if (typeof loadSalariesTable === 'function') {
            loadSalariesTable();
        }
        
        // إصلاح العملة في الصفحة
        if (typeof window.fixCurrency === 'function') {
            setTimeout(window.fixCurrency, 200);
        }
        
        console.log('✅ Currency display updated');
    }
    
    // الحصول على رمز العملة الحالي
    function getCurrentCurrencySymbol() {
        try {
            const companyData = localStorage.getItem('anwar_bakery_company');
            if (companyData) {
                const company = JSON.parse(companyData);
                
                // أولاً: تحقق من وجود رمز العملة المحفوظ
                if (company.currencySymbol) {
                    return company.currencySymbol;
                }
                
                // ثانياً: حول كود العملة إلى رمز
                const currencySymbols = {
                    'SAR': 'ر.س',
                    'YER': 'ر.ي',
                    'USD': '$',
                    'EUR': '€',
                    'AED': 'د.إ',
                    'KWD': 'د.ك',
                    'QAR': 'ر.ق'
                };
                
                if (company.currency && currencySymbols[company.currency]) {
                    return currencySymbols[company.currency];
                }
            }
        } catch (error) {
            console.error('Error getting currency symbol:', error);
        }
        
        return 'ر.س';
    }
    
    // الاستماع لتغييرات localStorage
    window.addEventListener('storage', function(event) {
        if (event.key === 'anwar_bakery_company') {
            console.log('📢 Company data changed, updating currency...');
            setTimeout(updateCurrencyDisplay, 100);
        }
    });
    
    // الاستماع لأحداث تحديث بيانات الشركة
    window.addEventListener('companyDataUpdated', function(event) {
        console.log('📢 Company data updated event received');
        setTimeout(updateCurrencyDisplay, 100);
    });
    
    // الاستماع لتغييرات العملة المباشرة
    window.addEventListener('currencyChanged', function(event) {
        console.log('📢 Currency changed event received');
        setTimeout(updateCurrencyDisplay, 100);
    });
    
    // تحديث فوري عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(updateCurrencyDisplay, 1500);
    });
    
    // إضافة وظيفة عامة للتحديث اليدوي
    window.updateCurrencyDisplay = updateCurrencyDisplay;
    
    console.log('✅ Currency Update Listener loaded');
    
})();
