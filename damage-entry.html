<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قيد التالف - مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .custom-scroll::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scroll::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        .custom-scroll::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        .custom-scroll::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Modal styles */
        .modal {
            display: none;
            z-index: 9999 !important;
        }

        .modal.active {
            display: flex !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <!-- Mobile overlay -->
    <div id="mobileOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden hidden"></div>

    <div class="flex h-screen bg-gray-100">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0">
            <!-- Logo -->
            <div class="flex items-center justify-center h-16 px-4 bg-gradient-to-r from-blue-600 to-blue-700">
                <div class="flex items-center">
                    <span class="text-2xl">🍞</span>
                    <div class="mr-3">
                        <h1 class="text-lg font-bold text-white">مخبز أنوار الحي</h1>
                        <p class="text-xs text-blue-100">نظام إدارة المخابز</p>
                    </div>
                </div>
            </div>

            <nav class="flex-1 overflow-y-auto px-4 pb-4 custom-scroll">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    الرئيسية
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="damage-entry.html" class="flex items-center px-3 py-2 text-sm font-medium text-red-600 bg-red-50 rounded-md">
                    <span class="ml-3">🗑️</span>
                    قيد التالف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🚚</span>
                    الموردين
                </a>
                <a href="employees.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👨‍💼</span>
                    الموظفين
                </a>
                <a href="cash-registers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">💰</span>
                    الصناديق
                </a>
                <a href="banks.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏦</span>
                    البنوك
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="journal-entry.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📝</span>
                    القيود اليومية
                </a>
                <a href="chart-of-accounts.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📊</span>
                    دليل الحسابات
                </a>
                <a href="reports.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📈</span>
                    التقارير
                </a>
                <a href="system-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">⚙️</span>
                    إعدادات النظام
                </a>
            </nav>

            <!-- User Info & Logout -->
            <div class="flex-shrink-0 p-4 border-t border-gray-200">
                <div class="bg-gray-50 rounded-lg p-3 mb-2">
                    <p id="userFullName" class="text-sm font-medium text-gray-900">مدير النظام</p>
                    <p id="userRole" class="text-xs text-gray-500">admin</p>
                    <p id="loginTime" class="text-xs text-gray-400"></p>
                </div>
                <button onclick="logout()" class="w-full text-right px-3 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50 flex items-center">
                    <span class="ml-3">🚪</span>
                    تسجيل الخروج
                </button>
            </div>
        </div>

        <!-- Main content -->
        <div class="flex-1 overflow-hidden">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <button onclick="toggleSidebar()" class="lg:hidden text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">☰</span>
                            </button>
                            <span class="text-lg font-semibold text-gray-900">قيد التالف</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-500" id="currentDateTime"></span>
                            <div class="flex items-center space-x-2">
                                <span id="userDisplayName" class="text-sm font-medium text-gray-700">مدير النظام</span>
                                <button onclick="logout()" class="text-red-600 hover:text-red-800 text-sm">
                                    خروج
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Header -->
                <div class="mb-6">
                    <div class="flex justify-between items-center">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                                <span class="text-3xl ml-3">🗑️</span>
                                قيد التالف
                            </h1>
                            <p class="text-gray-600 mt-1">إدارة وتسجيل الأصناف التالفة والمنتهية الصلاحية</p>
                        </div>
                        <button onclick="openNewDamageModal()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 flex items-center">
                            <span class="ml-2">➕</span>
                            قيد تالف جديد
                        </button>
                    </div>
                </div>

                <!-- Message Container -->
                <div id="messageContainer" class="mb-4"></div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-red-100 rounded-lg">
                                <span class="text-red-600 text-xl">🗑️</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">إجمالي قيود التالف</p>
                                <p class="text-2xl font-bold text-gray-900" id="totalDamageEntries">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-orange-100 rounded-lg">
                                <span class="text-orange-600 text-xl">📅</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">قيود هذا الشهر</p>
                                <p class="text-2xl font-bold text-gray-900" id="monthlyDamageEntries">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-yellow-100 rounded-lg">
                                <span class="text-yellow-600 text-xl">💰</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">قيمة التالف الشهرية</p>
                                <p class="text-2xl font-bold text-gray-900" id="monthlyDamageValue">0.00</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-2 bg-purple-100 rounded-lg">
                                <span class="text-purple-600 text-xl">📊</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">إجمالي قيمة التالف</p>
                                <p class="text-2xl font-bold text-gray-900" id="totalDamageValue">0.00</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="bg-white rounded-lg shadow mb-6 p-4">
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <input type="text" id="searchInput" placeholder="البحث بالصنف أو رقم القيد..."
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                                   onkeyup="filterDamageEntries()">
                        </div>
                        <div>
                            <select id="branchFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                                    onchange="filterDamageEntries()">
                                <option value="">جميع الفروع</option>
                            </select>
                        </div>
                        <div>
                            <select id="warehouseFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                                    onchange="filterDamageEntries()">
                                <option value="">جميع المخازن</option>
                            </select>
                        </div>
                        <div>
                            <select id="reasonFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"
                                    onchange="filterDamageEntries()">
                                <option value="">جميع الأسباب</option>
                                <option value="expired">منتهي الصلاحية</option>
                                <option value="damaged">تالف</option>
                                <option value="contaminated">ملوث</option>
                                <option value="defective">معيب</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div>
                            <button onclick="exportDamageEntries()" class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                                📊 تصدير Excel
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Damage Entries Table -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم القيد</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الصنف</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المخزن</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">القيمة</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">السبب</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="damageEntriesTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- Damage entries will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add New Damage Entry Modal -->
    <div id="newDamageModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <span class="ml-2">🗑️</span>
                    قيد تالف جديد
                </h3>
                <button onclick="closeNewDamageModal()" class="text-gray-400 hover:text-gray-600">
                    <span class="text-xl">✕</span>
                </button>
            </div>

            <form id="newDamageForm" onsubmit="saveNewDamageEntry(event)" class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">رقم القيد</label>
                        <input type="text" id="newDamageEntryNumber" readonly
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:outline-none">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">التاريخ</label>
                        <input type="date" id="newDamageDate" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الصنف *</label>
                        <select id="newDamageItemId" required onchange="updateNewDamageItemDetails()"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500">
                            <option value="">اختر الصنف</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">المخزن *</label>
                        <select id="newDamageWarehouseId" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500">
                            <option value="">اختر المخزن</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الكمية *</label>
                        <input type="number" id="newDamageQuantity" step="0.01" min="0.01" required onchange="calculateNewDamageTotal()"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الوحدة</label>
                        <input type="text" id="newDamageUnitName" readonly
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">تكلفة الوحدة</label>
                        <input type="number" id="newDamageUnitCost" step="0.01" readonly
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">سبب التالف *</label>
                        <select id="newDamageReason" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500">
                            <option value="">اختر السبب</option>
                            <option value="expired">منتهي الصلاحية</option>
                            <option value="damaged">تالف</option>
                            <option value="contaminated">ملوث</option>
                            <option value="defective">معيب</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">إجمالي القيمة</label>
                        <input type="number" id="newDamageTotalValue" step="0.01" readonly
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 font-bold text-red-600">
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                    <textarea id="newDamageNotes" rows="3" placeholder="أدخل أي ملاحظات إضافية..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500"></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeNewDamageModal()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300">
                        إلغاء
                    </button>
                    <button type="submit"
                            class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700">
                        حفظ قيد التالف
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="damage-entry.js"></script>
</body>
</html>
