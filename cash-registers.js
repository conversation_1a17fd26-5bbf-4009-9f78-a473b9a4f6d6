// Cash Registers Management System
let cashRegisters = [];
let filteredRegisters = [];
let transfers = [];

// Sample cash registers data
const sampleRegisters = [
    {
        id: 1,
        code: 'CR-001',
        name: 'الصندوق الرئيسي',
        type: 'main',
        branchId: 1,
        branchName: 'الفرع الرئيسي',
        responsibleEmployee: 'أحمد محمد',
        openingBalance: 10000,
        currentBalance: 15750,
        status: 'active',
        description: 'الصندوق الرئيسي للفرع الرئيسي',
        createdAt: '2024-01-01',
        lastTransaction: '2024-01-20',
        accountCode: '1101001' // حساب الصندوق في شجرة الحسابات
    },
    {
        id: 2,
        code: 'CR-002',
        name: 'نقطة البيع 1',
        type: 'pos',
        branchId: 1,
        branchName: 'الفرع الرئيسي',
        responsibleEmployee: 'فاطمة علي',
        openingBalance: 2000,
        currentBalance: 3250,
        status: 'active',
        description: 'نقطة بيع رقم 1 في الفرع الرئيسي',
        createdAt: '2024-01-05',
        lastTransaction: '2024-01-20',
        accountCode: '1101002'
    },
    {
        id: 3,
        code: 'CR-003',
        name: 'صندوق المصروفات الصغيرة',
        type: 'petty',
        branchId: 1,
        branchName: 'الفرع الرئيسي',
        responsibleEmployee: 'محمد سالم',
        openingBalance: 500,
        currentBalance: 275,
        status: 'active',
        description: 'صندوق للمصروفات الصغيرة والطارئة',
        createdAt: '2024-01-10',
        lastTransaction: '2024-01-19',
        accountCode: '1101003'
    }
];

// Initialize cash registers
function initializeRegisters() {
    const storedRegisters = localStorage.getItem('anwar_bakery_cash_registers');
    if (storedRegisters) {
        cashRegisters = JSON.parse(storedRegisters);
    } else {
        // Initialize with realistic data based on company settings
        initializeRealisticData();
    }
    filteredRegisters = [...cashRegisters];
}

// Initialize realistic data based on company and branch settings
function initializeRealisticData() {
    const companyData = JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
    const branchesData = JSON.parse(localStorage.getItem('anwar_bakery_branches') || '[]');
    const employeesData = JSON.parse(localStorage.getItem('anwar_bakery_employees') || '[]');

    // Get currency symbol
    const currency = companyData.currency || 'ر.س';

    // Create realistic cash registers based on actual branches
    cashRegisters = [];
    let registerId = 1;

    if (branchesData.length > 0) {
        branchesData.forEach((branch, index) => {
            if (branch.isActive) {
                // Main cash register for each branch
                cashRegisters.push({
                    id: registerId++,
                    code: `CR${registerId.toString().padStart(4, '0')}`,
                    name: `الصندوق الرئيسي - ${branch.branchName}`,
                    type: 'main',
                    branchId: branch.id,
                    branchName: branch.branchName,
                    responsibleEmployeeId: null,
                    responsibleEmployeeName: '',
                    openingBalance: index === 0 ? 50000 : 25000, // Main branch gets more
                    currentBalance: index === 0 ? 52750 : 27500,
                    status: 'active',
                    description: `الصندوق الرئيسي لـ ${branch.branchName}`,
                    createdAt: new Date().toISOString().split('T')[0],
                    lastTransaction: new Date().toISOString().split('T')[0],
                    accountCode: `1101${registerId.toString().padStart(3, '0')}`
                });

                // POS register for each branch
                cashRegisters.push({
                    id: registerId++,
                    code: `CR${registerId.toString().padStart(4, '0')}`,
                    name: `نقطة البيع - ${branch.branchName}`,
                    type: 'pos',
                    branchId: branch.id,
                    branchName: branch.branchName,
                    responsibleEmployeeId: null,
                    responsibleEmployeeName: '',
                    openingBalance: 5000,
                    currentBalance: 6250,
                    status: 'active',
                    description: `نقطة البيع الرئيسية في ${branch.branchName}`,
                    createdAt: new Date().toISOString().split('T')[0],
                    lastTransaction: new Date().toISOString().split('T')[0],
                    accountCode: `1101${registerId.toString().padStart(3, '0')}`
                });
            }
        });
    } else {
        // Default registers if no branches exist
        cashRegisters = [
            {
                id: 1,
                code: 'CR0001',
                name: 'الصندوق الرئيسي',
                type: 'main',
                branchId: 1,
                branchName: 'الفرع الرئيسي',
                responsibleEmployeeId: null,
                responsibleEmployeeName: '',
                openingBalance: 50000,
                currentBalance: 52750,
                status: 'active',
                description: 'الصندوق الرئيسي للمخبز',
                createdAt: new Date().toISOString().split('T')[0],
                lastTransaction: new Date().toISOString().split('T')[0],
                accountCode: '1101001'
            },
            {
                id: 2,
                code: 'CR0002',
                name: 'نقطة البيع الرئيسية',
                type: 'pos',
                branchId: 1,
                branchName: 'الفرع الرئيسي',
                responsibleEmployeeId: null,
                responsibleEmployeeName: '',
                openingBalance: 5000,
                currentBalance: 6250,
                status: 'active',
                description: 'نقطة البيع الرئيسية',
                createdAt: new Date().toISOString().split('T')[0],
                lastTransaction: new Date().toISOString().split('T')[0],
                accountCode: '1101002'
            }
        ];
    }

    // Add petty cash register
    cashRegisters.push({
        id: cashRegisters.length + 1,
        code: `CR${(cashRegisters.length + 1).toString().padStart(4, '0')}`,
        name: 'صندوق المصروفات الصغيرة',
        type: 'petty',
        branchId: cashRegisters[0]?.branchId || 1,
        branchName: cashRegisters[0]?.branchName || 'الفرع الرئيسي',
        responsibleEmployeeId: null,
        responsibleEmployeeName: '',
        openingBalance: 2000,
        currentBalance: 1750,
        status: 'active',
        description: 'صندوق للمصروفات الصغيرة والطارئة',
        createdAt: new Date().toISOString().split('T')[0],
        lastTransaction: new Date().toISOString().split('T')[0],
        accountCode: `1101${(cashRegisters.length).toString().padStart(3, '0')}`
    });

    saveRegisters();

    // Add to chart of accounts
    cashRegisters.forEach(register => {
        addToChartOfAccounts({
            code: register.accountCode,
            name: register.name,
            type: 'cash_register',
            parentCode: '1101', // النقدية
            level: 3,
            isActive: true,
            balance: register.currentBalance
        });
    });
}

// Save cash registers to localStorage
function saveRegisters() {
    localStorage.setItem('anwar_bakery_cash_registers', JSON.stringify(cashRegisters));

    // Also update chart of accounts balances
    cashRegisters.forEach(register => {
        updateChartOfAccounts(register.accountCode, {
            balance: register.currentBalance,
            lastUpdated: new Date().toISOString()
        });
    });
}

// Load cash registers and display
function loadRegisters() {
    initializeRegisters();
    displayRegisters();
    updateRegisterStats();
}

// Display cash registers in grid
function displayRegisters() {
    const grid = document.getElementById('registersGrid');

    if (filteredRegisters.length === 0) {
        grid.innerHTML = `
            <div class="col-span-full text-center py-8 text-gray-500">
                لا توجد صناديق مطابقة للبحث
            </div>
        `;
        return;
    }

    grid.innerHTML = filteredRegisters.map(register => `
        <div class="cash-register-card bg-white rounded-xl shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-${getTypeColor(register.type)}-500 to-${getTypeColor(register.type)}-600 p-4 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-bold">${register.name}</h3>
                        <p class="text-${getTypeColor(register.type)}-100 text-sm">${register.code}</p>
                    </div>
                    <div class="text-3xl opacity-80">
                        ${getTypeIcon(register.type)}
                    </div>
                </div>
            </div>

            <div class="p-4">
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">النوع:</span>
                        <span class="text-sm font-medium">${getTypeText(register.type)}</span>
                    </div>

                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">الفرع:</span>
                        <span class="text-sm font-medium">${register.branchName}</span>
                    </div>

                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">المسؤول:</span>
                        <span class="text-sm font-medium">${register.responsibleEmployee || 'غير محدد'}</span>
                    </div>

                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">الرصيد الحالي:</span>
                        <span class="text-lg font-bold text-${register.currentBalance >= 0 ? 'green' : 'red'}-600" title="الرصيد الكامل: ${register.currentBalance.toLocaleString()} ${getCurrencySymbol()}">
                            ${formatCurrency(register.currentBalance)}
                        </span>
                    </div>

                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">الحالة:</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusClass(register.status)}">
                            ${getStatusText(register.status)}
                        </span>
                    </div>
                </div>

                <div class="mt-4 pt-4 border-t border-gray-200">
                    <div class="flex justify-between space-x-2">
                        <button onclick="viewRegisterDetails(${register.id})" class="flex-1 bg-blue-100 text-blue-700 px-3 py-2 rounded text-sm hover:bg-blue-200">
                            📊 التفاصيل
                        </button>
                        <button onclick="editRegister(${register.id})" class="flex-1 bg-green-100 text-green-700 px-3 py-2 rounded text-sm hover:bg-green-200">
                            ✏️ تعديل
                        </button>
                        <button onclick="toggleRegisterStatus(${register.id})" class="flex-1 bg-orange-100 text-orange-700 px-3 py-2 rounded text-sm hover:bg-orange-200">
                            🔄 الحالة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// Format large numbers for display
function formatNumber(number) {
    if (number >= 1000000000) {
        return (number / 1000000000).toFixed(1) + 'B';
    } else if (number >= 1000000) {
        return (number / 1000000).toFixed(1) + 'M';
    } else if (number >= 1000) {
        return (number / 1000).toFixed(1) + 'K';
    } else {
        return number.toLocaleString();
    }
}

// Format currency with proper display
function formatCurrency(amount, showFullNumber = false) {
    const currencySymbol = getCurrencySymbol();

    if (showFullNumber || amount < 100000) {
        // Show full number for smaller amounts
        return amount.toLocaleString() + ' ' + currencySymbol;
    } else {
        // Use abbreviated format for large amounts
        return formatNumber(amount) + ' ' + currencySymbol;
    }
}

// Update cash register statistics - FIXED FOR LARGE NUMBERS
function updateRegisterStats() {
    const totalRegisters = cashRegisters.length;
    const activeRegisters = cashRegisters.filter(r => r.status === 'active').length;
    const closedRegisters = cashRegisters.filter(r => r.status === 'closed').length;
    const totalBalance = cashRegisters.reduce((sum, r) => sum + (r.currentBalance || 0), 0);

    document.getElementById('totalRegisters').textContent = totalRegisters;
    document.getElementById('activeRegisters').textContent = activeRegisters;
    document.getElementById('closedRegisters').textContent = closedRegisters;

    // Format the total balance with proper display
    const balanceElement = document.getElementById('totalBalance');
    if (balanceElement) {
        balanceElement.textContent = formatCurrency(totalBalance);
        balanceElement.title = `الرصيد الكامل: ${totalBalance.toLocaleString()} ${getCurrencySymbol()}`;

        // Add responsive text size based on length
        const textLength = balanceElement.textContent.length;
        if (textLength > 15) {
            balanceElement.classList.add('text-lg');
            balanceElement.classList.remove('text-2xl');
        } else {
            balanceElement.classList.add('text-2xl');
            balanceElement.classList.remove('text-lg');
        }
    }
}

// Generate register code
function generateRegisterCode() {
    const nextNumber = cashRegisters.length + 1;
    const code = `CR-${nextNumber.toString().padStart(3, '0')}`;
    document.getElementById('registerCode').value = code;
    return code;
}

// Get type color
function getTypeColor(type) {
    const colors = {
        'main': 'green',
        'pos': 'blue',
        'petty': 'purple'
    };
    return colors[type] || 'gray';
}

// Get type icon
function getTypeIcon(type) {
    const icons = {
        'main': '🏦',
        'pos': '💳',
        'petty': '💰'
    };
    return icons[type] || '💰';
}

// Get type text
function getTypeText(type) {
    const texts = {
        'main': 'صندوق رئيسي',
        'pos': 'نقطة بيع',
        'petty': 'مصروفات صغيرة'
    };
    return texts[type] || 'غير محدد';
}

// Get status class
function getStatusClass(status) {
    const classes = {
        'active': 'bg-green-100 text-green-800',
        'closed': 'bg-red-100 text-red-800',
        'maintenance': 'bg-yellow-100 text-yellow-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
}

// Get status text
function getStatusText(status) {
    const texts = {
        'active': 'نشط',
        'closed': 'مغلق',
        'maintenance': 'صيانة'
    };
    return texts[status] || 'غير محدد';
}

// Get branch name
function getBranchName(branchCode) {
    const branches = {
        'main': 'الفرع الرئيسي',
        'branch1': 'فرع الملك فهد',
        'branch2': 'فرع العليا'
    };
    return branches[branchCode] || 'غير محدد';
}

// Filter cash registers
function filterRegisters() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const branchFilter = document.getElementById('branchFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;

    filteredRegisters = cashRegisters.filter(register => {
        const matchesSearch = register.name.toLowerCase().includes(searchTerm) ||
                            register.code.toLowerCase().includes(searchTerm);

        const matchesStatus = !statusFilter || register.status === statusFilter;
        const matchesBranch = !branchFilter || register.branchId == branchFilter;
        const matchesType = !typeFilter || register.type === typeFilter;

        return matchesSearch && matchesStatus && matchesBranch && matchesType;
    });

    displayRegisters();
}

// Modal functions
function openAddModal() {
    document.getElementById('addRegisterModal').classList.add('active');
    generateRegisterCode();
}

function closeAddModal() {
    document.getElementById('addRegisterModal').classList.remove('active');
    document.getElementById('addRegisterForm').reset();
    generateRegisterCode();
}

// Add cash register
function addRegister(event) {
    event.preventDefault();

    const registerData = {
        id: Date.now(),
        code: document.getElementById('registerCode').value,
        name: document.getElementById('registerName').value,
        type: document.getElementById('registerType').value,
        branch: document.getElementById('registerBranch').value,
        responsibleEmployee: document.getElementById('responsibleEmployee').value,
        openingBalance: parseFloat(document.getElementById('openingBalance').value) || 0,
        currentBalance: parseFloat(document.getElementById('openingBalance').value) || 0,
        status: 'active',
        description: document.getElementById('registerDescription').value,
        createdAt: new Date().toISOString().split('T')[0],
        lastTransaction: null,
        accountCode: generateAccountCode('cash_register', registerData.id)
    };

    cashRegisters.push(registerData);
    saveRegisters();

    // Add to chart of accounts
    addToChartOfAccounts({
        code: registerData.accountCode,
        name: registerData.name,
        type: 'cash_register',
        parentCode: '1101', // النقدية
        level: 3,
        isActive: true
    });

    loadRegisters();
    closeAddModal();

    showMessage('تم إضافة الصندوق بنجاح', 'success');
}

// View register details
function viewRegisterDetails(registerId) {
    const register = cashRegisters.find(r => r.id === registerId);
    if (!register) return;

    alert(`تفاصيل الصندوق: ${register.name}\nالكود: ${register.code}\nرقم الحساب: ${register.accountCode}\nالرصيد الحالي: ${register.currentBalance.toFixed(2)} ر.س\nآخر معاملة: ${register.lastTransaction || 'لا توجد'}`);
}

// Edit register - Fully functional
function editRegister(registerId) {
    console.log('Editing register with ID:', registerId);
    const register = cashRegisters.find(r => r.id === registerId);
    if (!register) {
        showMessage('❌ لم يتم العثور على الصندوق', 'error');
        return;
    }

    console.log('Found register:', register);

    // Create and show edit modal
    createAndShowEditModal(register);
}

// Create and show edit modal
function createAndShowEditModal(register) {
    console.log('Creating edit modal for register:', register);

    // Remove existing edit modal if any
    const existingModal = document.getElementById('editRegisterModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Create edit modal HTML
    const modalHTML = `
        <div id="editRegisterModal" class="modal fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" style="display: flex;">
            <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                <div class="mt-3">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">تعديل الصندوق: ${register.name}</h3>
                        <button onclick="closeEditModal()" class="text-gray-400 hover:text-gray-600">
                            <span class="text-xl">✕</span>
                        </button>
                    </div>

                    <form id="editRegisterForm" onsubmit="updateRegister(event)">
                        <input type="hidden" id="editRegisterId" value="${register.id}">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <!-- Register Code -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">كود الصندوق *</label>
                                <input type="text" id="editRegisterCode" value="${register.code}" readonly
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50">
                            </div>

                            <!-- Register Name -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">اسم الصندوق *</label>
                                <input type="text" id="editRegisterName" value="${register.name}" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                            </div>

                            <!-- Register Type -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">نوع الصندوق *</label>
                                <select id="editRegisterType" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                                    <option value="main" ${register.type === 'main' ? 'selected' : ''}>صندوق رئيسي</option>
                                    <option value="pos" ${register.type === 'pos' ? 'selected' : ''}>نقطة بيع</option>
                                    <option value="petty" ${register.type === 'petty' ? 'selected' : ''}>مصروفات صغيرة</option>
                                </select>
                            </div>

                            <!-- Branch -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">الفرع *</label>
                                <select id="editRegisterBranch" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                                    <option value="">اختر الفرع</option>
                                </select>
                            </div>

                            <!-- Responsible Employee -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">الموظف المسؤول</label>
                                <select id="editResponsibleEmployee"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                                    <option value="">اختر الموظف</option>
                                </select>
                            </div>

                            <!-- Current Balance -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">الرصيد الحالي</label>
                                <input type="number" id="editCurrentBalance" value="${register.currentBalance}" step="0.01"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                            </div>

                            <!-- Status -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">الحالة *</label>
                                <select id="editRegisterStatus" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                                    <option value="active" ${register.status === 'active' ? 'selected' : ''}>نشط</option>
                                    <option value="closed" ${register.status === 'closed' ? 'selected' : ''}>مغلق</option>
                                    <option value="maintenance" ${register.status === 'maintenance' ? 'selected' : ''}>صيانة</option>
                                </select>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
                            <textarea id="editRegisterDescription" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                                      placeholder="وصف الصندوق واستخدامه">${register.description || ''}</textarea>
                        </div>

                        <!-- Form Actions -->
                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="closeEditModal()"
                                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                                إلغاء
                            </button>
                            <button type="submit"
                                    class="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700">
                                حفظ التعديلات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Load branches and employees for the edit form
    loadEditFormData(register);

    console.log('✅ Edit modal created and displayed');
}

// Load data for edit form
function loadEditFormData(register) {
    // Load branches
    try {
        const branches = JSON.parse(localStorage.getItem('anwar_bakery_branches') || '[]');
        const branchSelect = document.getElementById('editRegisterBranch');

        if (branchSelect) {
            branchSelect.innerHTML = '<option value="">اختر الفرع</option>';
            branches.forEach(branch => {
                if (branch.isActive) {
                    const selected = branch.id == register.branchId ? 'selected' : '';
                    branchSelect.innerHTML += `<option value="${branch.id}" ${selected}>${branch.branchName}</option>`;
                }
            });
        }
    } catch (error) {
        console.log('Could not load branches for edit form:', error);
    }

    // Load employees
    try {
        const employees = JSON.parse(localStorage.getItem('anwar_bakery_employees') || '[]');
        const employeeSelect = document.getElementById('editResponsibleEmployee');

        if (employeeSelect) {
            employeeSelect.innerHTML = '<option value="">اختر الموظف</option>';
            employees.forEach(employee => {
                if (employee.status === 'active') {
                    const selected = employee.id == register.responsibleEmployeeId ? 'selected' : '';
                    employeeSelect.innerHTML += `<option value="${employee.id}" ${selected}>${employee.fullName}</option>`;
                }
            });
        }
    } catch (error) {
        console.log('Could not load employees for edit form:', error);
    }
}

// Close edit modal
function closeEditModal() {
    const modal = document.getElementById('editRegisterModal');
    if (modal) {
        modal.remove();
    }
}

// Update register
function updateRegister(event) {
    event.preventDefault();

    const registerId = parseInt(document.getElementById('editRegisterId').value);
    const registerIndex = cashRegisters.findIndex(r => r.id === registerId);

    if (registerIndex === -1) {
        showMessage('❌ لم يتم العثور على الصندوق', 'error');
        return;
    }

    // Get form data
    const updatedData = {
        name: document.getElementById('editRegisterName').value.trim(),
        type: document.getElementById('editRegisterType').value,
        branchId: parseInt(document.getElementById('editRegisterBranch').value),
        responsibleEmployeeId: document.getElementById('editResponsibleEmployee').value || null,
        currentBalance: parseFloat(document.getElementById('editCurrentBalance').value) || 0,
        status: document.getElementById('editRegisterStatus').value,
        description: document.getElementById('editRegisterDescription').value.trim()
    };

    // Validate required fields
    if (!updatedData.name || !updatedData.type || !updatedData.branchId) {
        showMessage('❌ يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // Get branch name
    try {
        const branches = JSON.parse(localStorage.getItem('anwar_bakery_branches') || '[]');
        const branch = branches.find(b => b.id == updatedData.branchId);
        updatedData.branchName = branch ? branch.branchName : 'غير محدد';
    } catch (error) {
        updatedData.branchName = 'غير محدد';
    }

    // Get employee name
    if (updatedData.responsibleEmployeeId) {
        try {
            const employees = JSON.parse(localStorage.getItem('anwar_bakery_employees') || '[]');
            const employee = employees.find(e => e.id == updatedData.responsibleEmployeeId);
            updatedData.responsibleEmployeeName = employee ? employee.fullName : '';
        } catch (error) {
            updatedData.responsibleEmployeeName = '';
        }
    } else {
        updatedData.responsibleEmployeeName = '';
    }

    // Update register
    const oldRegister = { ...cashRegisters[registerIndex] };
    cashRegisters[registerIndex] = {
        ...cashRegisters[registerIndex],
        ...updatedData,
        lastUpdated: new Date().toISOString()
    };

    // Save changes
    saveRegisters();

    // Update chart of accounts if balance changed
    if (oldRegister.currentBalance !== updatedData.currentBalance) {
        updateChartOfAccounts(cashRegisters[registerIndex].accountCode, {
            balance: updatedData.currentBalance,
            lastUpdated: new Date().toISOString()
        });
    }

    // Refresh display
    loadRegisters();

    // Close modal
    closeEditModal();

    // Show success message
    showMessage(`✅ تم تحديث الصندوق "${updatedData.name}" بنجاح`, 'success');

    console.log('✅ Register updated successfully:', cashRegisters[registerIndex]);
}

// Helper functions for chart of accounts integration
function addToChartOfAccounts(accountData) {
    try {
        const accounts = JSON.parse(localStorage.getItem('anwar_bakery_accounts') || '[]');

        // Check if account already exists
        const existingAccount = accounts.find(acc => acc.code === accountData.code);
        if (existingAccount) {
            console.log('Account already exists in chart:', accountData.code);
            return;
        }

        // Add new account
        const newAccount = {
            id: Date.now(),
            code: accountData.code,
            name: accountData.name,
            type: 'assets', // Cash registers are assets
            level: accountData.level || 3,
            parentId: null, // Will be set based on parent code
            isActive: accountData.isActive !== false,
            balance: accountData.balance || 0,
            debitBalance: accountData.balance || 0,
            creditBalance: 0,
            hasChildren: false,
            isExpanded: false,
            isDefault: false,
            canDelete: true,
            description: `حساب الصندوق: ${accountData.name}`,
            createdAt: new Date().toISOString()
        };

        accounts.push(newAccount);
        localStorage.setItem('anwar_bakery_accounts', JSON.stringify(accounts));

        console.log('✅ Added cash register to chart of accounts:', newAccount);
    } catch (error) {
        console.error('❌ Error adding to chart of accounts:', error);
    }
}

function updateChartOfAccounts(accountCode, updateData) {
    try {
        const accounts = JSON.parse(localStorage.getItem('anwar_bakery_accounts') || '[]');
        const accountIndex = accounts.findIndex(acc => acc.code === accountCode);

        if (accountIndex !== -1) {
            accounts[accountIndex] = {
                ...accounts[accountIndex],
                ...updateData,
                lastUpdated: new Date().toISOString()
            };

            localStorage.setItem('anwar_bakery_accounts', JSON.stringify(accounts));
            console.log('✅ Updated chart of accounts:', accountCode, updateData);
        }
    } catch (error) {
        console.error('❌ Error updating chart of accounts:', error);
    }
}

function generateAccountCode(type, id) {
    // Generate account code for cash registers
    // Format: 1101XXX (where XXX is a unique number)
    const baseCode = '1101';
    const suffix = id.toString().padStart(3, '0');
    return baseCode + suffix;
}

// Toggle register status
function toggleRegisterStatus(registerId) {
    const registerIndex = cashRegisters.findIndex(r => r.id === registerId);
    if (registerIndex === -1) return;

    const currentStatus = cashRegisters[registerIndex].status;
    const newStatus = currentStatus === 'active' ? 'closed' : 'active';

    if (confirm(`هل أنت متأكد من ${newStatus === 'active' ? 'تفعيل' : 'إغلاق'} هذا الصندوق؟`)) {
        cashRegisters[registerIndex].status = newStatus;
        saveRegisters();
        loadRegisters();

        showMessage(`تم ${newStatus === 'active' ? 'تفعيل' : 'إغلاق'} الصندوق بنجاح`, 'success');
    }
}



// Populate edit branch dropdowns
function populateEditBranchDropdowns() {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    let branches = [];

    if (savedBranches) {
        branches = JSON.parse(savedBranches);
    } else {
        branches = [
            { id: 1, branchName: 'الفرع الرئيسي', isActive: true },
            { id: 2, branchName: 'فرع الملك فهد', isActive: true },
            { id: 3, branchName: 'فرع العليا', isActive: true }
        ];
    }

    const dropdown = document.getElementById('editRegisterBranch');
    if (dropdown) {
        dropdown.innerHTML = '<option value="">اختر الفرع</option>';
        branches.filter(branch => branch.isActive).forEach(branch => {
            const option = document.createElement('option');
            option.value = branch.id;
            option.textContent = branch.branchName;
            dropdown.appendChild(option);
        });
    }
}

// Populate edit employee dropdowns
function populateEditEmployeeDropdowns() {
    const savedEmployees = localStorage.getItem('anwar_bakery_employees');
    let employees = [];

    if (savedEmployees) {
        try {
            employees = JSON.parse(savedEmployees);
        } catch (error) {
            console.error('Error parsing employees:', error);
            employees = [];
        }
    }

    const dropdown = document.getElementById('editResponsibleEmployee');
    if (dropdown) {
        dropdown.innerHTML = '<option value="">اختر الموظف</option>';
        employees.filter(emp => emp.status === 'active' || emp.isActive !== false).forEach(employee => {
            const option = document.createElement('option');
            option.value = employee.id;
            // Try different name fields
            const employeeName = employee.fullName || employee.name || employee.firstName + ' ' + (employee.lastName || '') || 'موظف غير محدد';
            option.textContent = employeeName.trim();
            dropdown.appendChild(option);
        });
        console.log('Populated employees dropdown with', employees.length, 'employees');
    }
}



// Export to Excel - Advanced
function exportToExcel() {
    showMessage('🔄 جاري تصدير بيانات الصناديق إلى Excel...', 'info');

    if (typeof window.advancedExcel !== 'undefined') {
        try {
            // Prepare cash registers data for advanced export
            const registersData = cashRegisters.map(register => ({
                'كود الصندوق': register.code,
                'اسم الصندوق': register.name,
                'النوع': getTypeText(register.type),
                'الفرع': register.branchName || 'غير محدد',
                'الموظف المسؤول': register.responsibleEmployeeName || 'غير محدد',
                'الرصيد الافتتاحي': register.openingBalance,
                'الرصيد الحالي': register.currentBalance,
                'الحالة': getStatusText(register.status),
                'تاريخ الإنشاء': register.createdAt,
                'آخر معاملة': register.lastTransaction || 'لا توجد',
                'الوصف': register.description || ''
            }));

            const reportData = {
                main: registersData,
                summary: {
                    totalRegisters: cashRegisters.length,
                    activeRegisters: cashRegisters.filter(r => r.status === 'active').length,
                    totalBalance: cashRegisters.reduce((sum, r) => sum + r.currentBalance, 0),
                    totalOpeningBalance: cashRegisters.reduce((sum, r) => sum + r.openingBalance, 0)
                }
            };

            const result = window.advancedExcel.exportFinancialReport(reportData, 'cash_registers_report', {
                includeTransfers: true,
                includeBalances: true
            });

            if (result.success) {
                showMessage('✅ تم تصدير بيانات الصناديق بنجاح!', 'success');
            } else {
                showMessage('❌ فشل في تصدير البيانات: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('Export error:', error);
            showMessage('❌ خطأ في تصدير البيانات: ' + error.message, 'error');
        }
    } else {
        // Fallback to basic export
        const basicData = cashRegisters.map(register => ({
            'كود الصندوق': register.code,
            'اسم الصندوق': register.name,
            'النوع': getTypeText(register.type),
            'الرصيد الحالي': register.currentBalance,
            'الحالة': getStatusText(register.status)
        }));

        if (typeof window.excelUtils !== 'undefined') {
            window.excelUtils.exportToExcel(basicData, 'الصناديق', 'قائمة الصناديق');
            showMessage('✅ تم تصدير بيانات الصناديق بنجاح!', 'success');
        } else {
            showMessage('❌ نظام التصدير غير متوفر', 'error');
        }
    }
}

// Generate account code for chart of accounts
function generateAccountCode(type, id) {
    const baseCode = '1101'; // النقدية
    const nextNumber = (id % 1000).toString().padStart(3, '0');
    return `${baseCode}${nextNumber}`;
}

// Clean up and validate cash registers data
function validateAndCleanRegisters() {
    console.log('🧹 Cleaning and validating cash registers data...');

    // Remove any invalid entries
    cashRegisters = cashRegisters.filter(register => {
        return register &&
               register.id &&
               register.name &&
               register.code &&
               typeof register.currentBalance === 'number';
    });

    // Ensure all registers have required fields
    cashRegisters.forEach(register => {
        if (!register.status) register.status = 'active';
        if (!register.type) register.type = 'main';
        if (typeof register.currentBalance !== 'number') register.currentBalance = 0;
        if (typeof register.openingBalance !== 'number') register.openingBalance = 0;
        if (!register.createdAt) register.createdAt = new Date().toISOString().split('T')[0];
        if (!register.accountCode) register.accountCode = generateAccountCode(register.type, register.id);
    });

    console.log(`✅ Validated ${cashRegisters.length} cash registers`);
}



// Show message function
function showMessage(message, type = 'info') {
    const messageContainer = document.getElementById('messageContainer');
    const alertClass = type === 'success' ? 'bg-green-100 border-green-400 text-green-700' :
                     type === 'error' ? 'bg-red-100 border-red-400 text-red-700' :
                     'bg-blue-100 border-blue-400 text-blue-700';

    messageContainer.innerHTML = `
        <div class="border-r-4 ${alertClass} p-4 rounded">
            <p>${message}</p>
        </div>
    `;

    setTimeout(() => {
        messageContainer.innerHTML = '';
    }, 3000);
}



function updateFromBalance() {
    const fromRegisterId = document.getElementById('fromRegister').value;
    const fromBalanceDiv = document.getElementById('fromBalance');

    if (fromRegisterId) {
        const register = cashRegisters.find(r => r.id == fromRegisterId);
        if (register) {
            fromBalanceDiv.textContent = `الرصيد الحالي: ${register.currentBalance.toFixed(2)} ${getCurrencySymbol()}`;
            fromBalanceDiv.className = 'text-sm text-blue-600 mt-1 font-medium';
        }
    } else {
        fromBalanceDiv.textContent = '';
    }

    updateTransferSummary();
}

function updateToBalance() {
    const toRegisterId = document.getElementById('toRegister').value;
    const toBalanceDiv = document.getElementById('toBalance');

    if (toRegisterId) {
        const register = cashRegisters.find(r => r.id == toRegisterId);
        if (register) {
            toBalanceDiv.textContent = `الرصيد الحالي: ${register.currentBalance.toFixed(2)} ${getCurrencySymbol()}`;
            toBalanceDiv.className = 'text-sm text-green-600 mt-1 font-medium';
        }
    } else {
        toBalanceDiv.textContent = '';
    }

    updateTransferSummary();
}

function updateTransferSummary() {
    const fromId = document.getElementById('fromRegister').value;
    const toId = document.getElementById('toRegister').value;
    const amount = document.getElementById('transferAmount').value;
    const date = document.getElementById('transferDate').value;

    if (fromId && toId && amount && date) {
        const fromRegister = cashRegisters.find(r => r.id == fromId);
        const toRegister = cashRegisters.find(r => r.id == toId);

        if (fromRegister && toRegister && fromId !== toId) {
            document.getElementById('summaryFrom').textContent = fromRegister.name;
            document.getElementById('summaryTo').textContent = toRegister.name;
            document.getElementById('summaryAmount').textContent = `${parseFloat(amount).toFixed(2)} ${getCurrencySymbol()}`;
            document.getElementById('summaryDate').textContent = new Date(date).toLocaleDateString('ar-SA');
            document.getElementById('transferSummary').classList.remove('hidden');
        } else {
            document.getElementById('transferSummary').classList.add('hidden');
        }
    } else {
        document.getElementById('transferSummary').classList.add('hidden');
    }
}



function transferMoney(event) {
    event.preventDefault();

    const fromId = parseInt(document.getElementById('fromRegister').value);
    const toId = parseInt(document.getElementById('toRegister').value);
    const amount = parseFloat(document.getElementById('transferAmount').value);
    const date = document.getElementById('transferDate').value;
    const reference = document.getElementById('transferReference').value;
    const notes = document.getElementById('transferNotes').value;

    // Enhanced validation
    if (!fromId || !toId) {
        showMessage('❌ يجب اختيار الصناديق المرسل والمستقبل', 'error');
        return;
    }

    if (fromId === toId) {
        showMessage('❌ لا يمكن التحويل من وإلى نفس الصندوق', 'error');
        return;
    }

    if (!amount || amount <= 0) {
        showMessage('❌ يجب إدخال مبلغ صحيح أكبر من صفر', 'error');
        return;
    }

    const fromRegister = cashRegisters.find(r => r.id === fromId);
    const toRegister = cashRegisters.find(r => r.id === toId);

    if (!fromRegister || !toRegister) {
        showMessage('❌ خطأ في بيانات الصناديق', 'error');
        return;
    }

    if (fromRegister.status !== 'active' || toRegister.status !== 'active') {
        showMessage('❌ لا يمكن التحويل من أو إلى صندوق غير نشط', 'error');
        return;
    }

    if (fromRegister.currentBalance < amount) {
        showMessage(`❌ الرصيد غير كافي في ${fromRegister.name}. الرصيد الحالي: ${fromRegister.currentBalance.toFixed(2)} ${getCurrencySymbol()}`, 'error');
        return;
    }

    // Confirm transfer
    if (!confirm(`هل أنت متأكد من تحويل ${amount.toFixed(2)} ${getCurrencySymbol()} من ${fromRegister.name} إلى ${toRegister.name}؟`)) {
        return;
    }

    try {
        // Create transfer record
        const transferRecord = {
            id: Date.now(),
            transferNumber: `TRF-${Date.now()}`,
            fromRegisterId: fromId,
            fromRegisterName: fromRegister.name,
            fromRegisterCode: fromRegister.code,
            toRegisterId: toId,
            toRegisterName: toRegister.name,
            toRegisterCode: toRegister.code,
            amount: amount,
            date: date,
            reference: reference || '',
            notes: notes || '',
            createdAt: new Date().toISOString(),
            createdBy: getCurrentUser(),
            status: 'completed',
            fromBalanceBefore: fromRegister.currentBalance,
            toBalanceBefore: toRegister.currentBalance,
            fromBalanceAfter: fromRegister.currentBalance - amount,
            toBalanceAfter: toRegister.currentBalance + amount
        };

        // Update balances
        const fromIndex = cashRegisters.findIndex(r => r.id === fromId);
        const toIndex = cashRegisters.findIndex(r => r.id === toId);

        cashRegisters[fromIndex].currentBalance -= amount;
        cashRegisters[fromIndex].lastTransaction = date;

        cashRegisters[toIndex].currentBalance += amount;
        cashRegisters[toIndex].lastTransaction = date;

        // Save transfer record
        if (!transfers) transfers = [];
        transfers.push(transferRecord);
        localStorage.setItem('anwar_bakery_cash_transfers', JSON.stringify(transfers));

        // Create journal entry for the transfer
        createTransferJournalEntry(transferRecord);

        // Save updated registers
        saveRegisters();

        // Refresh display
        loadRegisters();
        closeTransferModal();

        showMessage(`✅ تم تحويل ${amount.toFixed(2)} ${getCurrencySymbol()} من ${fromRegister.name} إلى ${toRegister.name} بنجاح`, 'success');

        // Show transfer receipt option
        if (confirm('هل تريد طباعة إيصال التحويل؟')) {
            printTransferReceipt(transferRecord);
        }

    } catch (error) {
        console.error('Transfer error:', error);
        showMessage('❌ حدث خطأ أثناء عملية التحويل', 'error');
    }
}

// Create journal entry for transfer
function createTransferJournalEntry(transferRecord) {
    const journalEntry = {
        id: Date.now(),
        entryNumber: `JE-TRF-${Date.now()}`,
        date: transferRecord.date,
        description: `تحويل نقدي من ${transferRecord.fromRegisterName} إلى ${transferRecord.toRegisterName}`,
        reference: transferRecord.transferNumber,
        entries: [
            {
                accountCode: cashRegisters.find(r => r.id === transferRecord.toRegisterId).accountCode,
                accountName: transferRecord.toRegisterName,
                debit: transferRecord.amount,
                credit: 0,
                description: `استلام تحويل من ${transferRecord.fromRegisterName}`
            },
            {
                accountCode: cashRegisters.find(r => r.id === transferRecord.fromRegisterId).accountCode,
                accountName: transferRecord.fromRegisterName,
                debit: 0,
                credit: transferRecord.amount,
                description: `تحويل إلى ${transferRecord.toRegisterName}`
            }
        ],
        totalDebit: transferRecord.amount,
        totalCredit: transferRecord.amount,
        createdBy: transferRecord.createdBy,
        createdAt: transferRecord.createdAt,
        type: 'cash_transfer'
    };

    // Save to journal entries
    const journalEntries = JSON.parse(localStorage.getItem('anwar_bakery_journal_entries') || '[]');
    journalEntries.push(journalEntry);
    localStorage.setItem('anwar_bakery_journal_entries', JSON.stringify(journalEntries));
}

// Print transfer receipt
function printTransferReceipt(transferRecord) {
    if (typeof window.advancedPrint !== 'undefined') {
        const receiptData = {
            number: transferRecord.transferNumber,
            date: transferRecord.date,
            time: new Date(transferRecord.createdAt).toLocaleTimeString('ar-SA'),
            type: 'transfer_receipt',
            items: [{
                name: `تحويل من ${transferRecord.fromRegisterName} إلى ${transferRecord.toRegisterName}`,
                quantity: 1,
                unit: 'تحويل',
                price: transferRecord.amount,
                total: transferRecord.amount
            }],
            subtotal: transferRecord.amount,
            tax: 0,
            total: transferRecord.amount,
            notes: transferRecord.notes || 'تحويل نقدي بين الصناديق',
            reference: transferRecord.reference
        };

        window.advancedPrint.printReceipt(receiptData, {
            width: '80mm',
            fontSize: '12px'
        });
    } else {
        alert(`إيصال التحويل:\nرقم التحويل: ${transferRecord.transferNumber}\nمن: ${transferRecord.fromRegisterName}\nإلى: ${transferRecord.toRegisterName}\nالمبلغ: ${transferRecord.amount.toFixed(2)} ${getCurrencySymbol()}\nالتاريخ: ${transferRecord.date}`);
    }
}

// Get current user
function getCurrentUser() {
    const session = localStorage.getItem('anwar_bakery_session') || sessionStorage.getItem('anwar_bakery_session');
    if (session) {
        const userData = JSON.parse(session);
        return userData.fullName || userData.username || 'مستخدم النظام';
    }
    return 'مستخدم النظام';
}

// Get currency symbol from system settings - FIXED FOR CASH REGISTERS
function getCurrencySymbol() {
    try {
        // خريطة تحويل العملات
        const currencyMap = {
            'SAR': 'ر.س',
            'YER': 'ر.ي',
            'USD': '$',
            'EUR': '€',
            'AED': 'د.إ',
            'KWD': 'د.ك',
            'QAR': 'ر.ق',
            'BHD': 'د.ب',
            'OMR': 'ر.ع'
        };

        // أولاً: محاولة استخدام نظام appSettings
        if (window.appSettings) {
            const financial = window.appSettings.get('financial');
            if (financial) {
                if (financial.currencySymbol) {
                    return financial.currencySymbol;
                }
                if (financial.baseCurrency && currencyMap[financial.baseCurrency]) {
                    return currencyMap[financial.baseCurrency];
                }
            }
        }

        // ثانياً: محاولة قراءة من localStorage مباشرة
        const savedSettings = localStorage.getItem('anwar_bakery_settings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);

            if (settings.financial) {
                if (settings.financial.currencySymbol) {
                    return settings.financial.currencySymbol;
                }
                if (settings.financial.baseCurrency && currencyMap[settings.financial.baseCurrency]) {
                    return currencyMap[settings.financial.baseCurrency];
                }
            }

            if (settings.currencySymbol) {
                return settings.currencySymbol;
            }
            if (settings.currency && currencyMap[settings.currency]) {
                return currencyMap[settings.currency];
            }
        }

        // ثالثاً: محاولة قراءة من بيانات الشركة
        const companyData = localStorage.getItem('anwar_bakery_company');
        if (companyData) {
            const company = JSON.parse(companyData);
            if (company.currencySymbol) {
                return company.currencySymbol;
            }
            if (company.currency && currencyMap[company.currency]) {
                return currencyMap[company.currency];
            }
        }

        // رابعاً: استخدام currency manager إذا كان متوفراً
        if (window.currencyManager) {
            return window.currencyManager.getCurrentCurrencySymbol();
        }

        // القيمة الافتراضية
        return 'ر.ي';
    } catch (error) {
        console.error('Error getting currency symbol:', error);
        return 'ر.ي';
    }
}

// Print cash registers list
function printCashRegistersList() {
    showMessage('🔄 جاري تحضير قائمة الصناديق للطباعة...', 'info');

    if (typeof window.advancedPrint !== 'undefined') {
        try {
            const printData = {
                number: `CR-LIST-${Date.now()}`,
                date: new Date().toISOString().split('T')[0],
                time: new Date().toLocaleTimeString('ar-SA'),
                type: 'cash_registers_list',
                title: 'قائمة الصناديق',
                items: cashRegisters.map(register => ({
                    name: register.name,
                    code: register.code,
                    type: getTypeText(register.type),
                    branch: register.branchName || 'غير محدد',
                    balance: register.currentBalance,
                    status: getStatusText(register.status),
                    employee: register.responsibleEmployeeName || 'غير محدد'
                })),
                summary: {
                    totalRegisters: cashRegisters.length,
                    activeRegisters: cashRegisters.filter(r => r.status === 'active').length,
                    totalBalance: cashRegisters.reduce((sum, r) => sum + r.currentBalance, 0)
                },
                notes: 'قائمة شاملة بجميع الصناديق وأرصدتها'
            };

            const result = window.advancedPrint.printInvoice(printData, {
                showLogo: true,
                showHeader: true,
                showFooter: true,
                paperSize: 'A4',
                orientation: 'portrait'
            });

            if (result.success) {
                showMessage('✅ تم إرسال قائمة الصناديق للطباعة بنجاح!', 'success');
            } else {
                showMessage('❌ فشل في طباعة القائمة: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('Print error:', error);
            showMessage('❌ خطأ في طباعة القائمة: ' + error.message, 'error');
        }
    } else {
        // Fallback to basic print
        const printWindow = window.open('', '_blank');
        const currency = getCurrencySymbol();

        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>قائمة الصناديق</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .company-name { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                    .report-title { font-size: 18px; margin-bottom: 20px; }
                    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                    th { background-color: #f2f2f2; font-weight: bold; }
                    .summary { background-color: #f9f9f9; padding: 15px; border-radius: 5px; }
                    .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="company-name">مخبز أنوار الحي</div>
                    <div class="report-title">قائمة الصناديق</div>
                    <div>التاريخ: ${new Date().toLocaleDateString('ar-SA')}</div>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>الكود</th>
                            <th>اسم الصندوق</th>
                            <th>النوع</th>
                            <th>الفرع</th>
                            <th>الموظف المسؤول</th>
                            <th>الرصيد الحالي</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${cashRegisters.map(register => `
                            <tr>
                                <td>${register.code}</td>
                                <td>${register.name}</td>
                                <td>${getTypeText(register.type)}</td>
                                <td>${register.branchName || 'غير محدد'}</td>
                                <td>${register.responsibleEmployeeName || 'غير محدد'}</td>
                                <td>${register.currentBalance.toFixed(2)} ${currency}</td>
                                <td>${getStatusText(register.status)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>

                <div class="summary">
                    <h3>ملخص الصناديق</h3>
                    <p><strong>إجمالي الصناديق:</strong> ${cashRegisters.length}</p>
                    <p><strong>الصناديق النشطة:</strong> ${cashRegisters.filter(r => r.status === 'active').length}</p>
                    <p><strong>إجمالي الأرصدة:</strong> ${cashRegisters.reduce((sum, r) => sum + r.currentBalance, 0).toFixed(2)} ${currency}</p>
                </div>

                <div class="footer">
                    تم إنشاء هذا التقرير في ${new Date().toLocaleString('ar-SA')}
                </div>
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.print();

        showMessage('✅ تم فتح نافذة الطباعة!', 'success');
    }
}

// Load transfers from localStorage
function loadTransfers() {
    const storedTransfers = localStorage.getItem('anwar_bakery_cash_transfers');
    if (storedTransfers) {
        transfers = JSON.parse(storedTransfers);
    }
}

// Load branches for filters
function loadBranches() {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    let branches = [];

    if (savedBranches) {
        branches = JSON.parse(savedBranches);
    } else {
        branches = [
            { id: 1, branchName: 'الفرع الرئيسي', isActive: true },
            { id: 2, branchName: 'فرع الملك فهد', isActive: true },
            { id: 3, branchName: 'فرع العليا', isActive: true }
        ];
        localStorage.setItem('anwar_bakery_branches', JSON.stringify(branches));
    }

    const branchFilter = document.getElementById('branchFilter');
    if (branchFilter) {
        branchFilter.innerHTML = '<option value="">جميع الفروع</option>';
        branches.filter(branch => branch.isActive).forEach(branch => {
            const option = document.createElement('option');
            option.value = branch.id;
            option.textContent = branch.branchName;
            branchFilter.appendChild(option);
        });
    }
}

// Open add register modal
function openAddModal() {
    document.getElementById('addRegisterModal').classList.remove('hidden');
    document.getElementById('addRegisterModal').style.display = 'block';
    generateRegisterCode();
    populateBranchDropdowns();
    populateEmployeeDropdowns();
}

// Close add register modal
function closeAddModal() {
    document.getElementById('addRegisterModal').classList.add('hidden');
    document.getElementById('addRegisterModal').style.display = 'none';
    document.getElementById('addRegisterForm').reset();
}

// Generate register code
function generateRegisterCode() {
    const nextId = Math.max(...cashRegisters.map(r => r.id), 0) + 1;
    const code = `CR${nextId.toString().padStart(4, '0')}`;
    document.getElementById('registerCode').value = code;
}

// Populate branch dropdowns
function populateBranchDropdowns() {
    const savedBranches = localStorage.getItem('anwar_bakery_branches');
    let branches = [];

    if (savedBranches) {
        branches = JSON.parse(savedBranches);
    } else {
        branches = [
            { id: 1, branchName: 'الفرع الرئيسي', isActive: true },
            { id: 2, branchName: 'فرع الملك فهد', isActive: true },
            { id: 3, branchName: 'فرع العليا', isActive: true }
        ];
        localStorage.setItem('anwar_bakery_branches', JSON.stringify(branches));
    }

    const dropdown = document.getElementById('registerBranch');
    if (dropdown) {
        dropdown.innerHTML = '<option value="">اختر الفرع</option>';
        branches.filter(branch => branch.isActive).forEach(branch => {
            const option = document.createElement('option');
            option.value = branch.id;
            option.textContent = branch.branchName;
            dropdown.appendChild(option);
        });
    }
}

// Populate employee dropdowns - FIXED
function populateEmployeeDropdowns() {
    console.log('🔄 Loading employees for cash register form...');

    const savedEmployees = localStorage.getItem('anwar_bakery_employees');
    let employees = [];

    if (savedEmployees) {
        try {
            employees = JSON.parse(savedEmployees);
            console.log(`✅ Found ${employees.length} employees in database`);
        } catch (error) {
            console.error('❌ Error parsing employees data:', error);
            employees = [];
        }
    } else {
        console.log('⚠️ No employees found in localStorage');
    }

    const dropdown = document.getElementById('responsibleEmployee');
    if (!dropdown) {
        console.error('❌ responsibleEmployee dropdown not found');
        return;
    }

    // Clear existing options
    dropdown.innerHTML = '<option value="">اختر الموظف</option>';

    if (employees.length === 0) {
        console.log('⚠️ No employees available');
        dropdown.innerHTML += '<option value="" disabled>لا توجد موظفين</option>';
        return;
    }

    // Filter active employees and add them to dropdown
    const activeEmployees = employees.filter(emp =>
        emp.status === 'active' || emp.isActive === true || emp.isActive !== false
    );

    console.log(`✅ Found ${activeEmployees.length} active employees`);

    activeEmployees.forEach(employee => {
        const option = document.createElement('option');
        option.value = employee.id;

        // Try different name fields to ensure compatibility
        const employeeName = employee.fullName ||
                            employee.name ||
                            `${employee.firstName || ''} ${employee.lastName || ''}`.trim() ||
                            employee.employeeNumber ||
                            'موظف غير محدد';

        // Add employee number if available
        const employeeNumber = employee.employeeNumber ? ` (${employee.employeeNumber})` : '';
        option.textContent = `${employeeName}${employeeNumber}`;

        dropdown.appendChild(option);

        console.log(`✅ Added employee: ${employeeName} (ID: ${employee.id})`);
    });

    console.log(`✅ Populated employees dropdown with ${activeEmployees.length} employees`);
}

// Add register
function addRegister(event) {
    event.preventDefault();

    const branchId = document.getElementById('registerBranch').value;
    let branchName = '';

    if (branchId) {
        const savedBranches = localStorage.getItem('anwar_bakery_branches');
        if (savedBranches) {
            const branches = JSON.parse(savedBranches);
            const branch = branches.find(b => b.id == branchId);
            branchName = branch ? branch.branchName : '';
        }
    }

    const employeeId = document.getElementById('responsibleEmployee').value;
    let employeeName = '';

    if (employeeId) {
        const savedEmployees = localStorage.getItem('anwar_bakery_employees');
        if (savedEmployees) {
            const employees = JSON.parse(savedEmployees);
            const employee = employees.find(e => e.id == employeeId);
            employeeName = employee ? employee.name : '';
        }
    }

    const openingBalance = parseFloat(document.getElementById('openingBalance').value) || 0;

    const newRegister = {
        id: Math.max(...cashRegisters.map(r => r.id), 0) + 1,
        code: document.getElementById('registerCode').value,
        name: document.getElementById('registerName').value,
        type: document.getElementById('registerType').value,
        branchId: parseInt(branchId) || null,
        branchName: branchName,
        responsibleEmployeeId: parseInt(employeeId) || null,
        responsibleEmployeeName: employeeName,
        openingBalance: openingBalance,
        currentBalance: openingBalance,
        description: document.getElementById('registerDescription').value,
        status: 'active',
        createdAt: new Date().toISOString().split('T')[0],
        lastTransaction: new Date().toISOString().split('T')[0]
    };

    cashRegisters.push(newRegister);
    saveRegisters();
    loadRegisters();
    closeAddModal();
    showMessage('تم إضافة الصندوق بنجاح!', 'success');
}

// Edit register - FIXED AND ACTIVATED
function editRegister(id) {
    console.log('🔄 Starting edit for register ID:', id);

    const register = cashRegisters.find(r => r.id === id);
    if (!register) {
        showMessage('❌ لم يتم العثور على الصندوق', 'error');
        return;
    }

    console.log('✅ Found register:', register);

    // Use the existing createAndShowEditModal function
    createAndShowEditModal(register);
}

// View register details
function viewRegisterDetails(id) {
    const register = cashRegisters.find(r => r.id === id);
    if (register) {
        const currencySymbol = getCurrencySymbol();
        let details = `تفاصيل الصندوق: ${register.name}\n\n`;
        details += `🆔 الكود: ${register.code}\n`;
        details += `📂 النوع: ${getRegisterTypeName(register.type)}\n`;
        details += `🏢 الفرع: ${register.branchName || 'غير محدد'}\n`;
        details += `👤 الموظف المسؤول: ${register.responsibleEmployeeName || 'غير محدد'}\n`;
        details += `💰 الرصيد الافتتاحي: ${register.openingBalance.toFixed(2)} ${currencySymbol}\n`;
        details += `💵 الرصيد الحالي: ${register.currentBalance.toFixed(2)} ${currencySymbol}\n`;
        details += `📊 الحالة: ${register.status === 'active' ? 'نشط' : 'غير نشط'}\n`;
        details += `📅 آخر معاملة: ${register.lastTransaction}\n`;
        if (register.description) {
            details += `📝 الوصف: ${register.description}\n`;
        }

        alert(details);
    }
}

// Get register type name in Arabic
function getRegisterTypeName(type) {
    const types = {
        'main': 'صندوق رئيسي',
        'pos': 'نقطة بيع',
        'petty': 'مصروفات صغيرة'
    };
    return types[type] || type;
}

// Show message
function showMessage(message, type) {
    // Create a temporary message element
    const messageDiv = document.createElement('div');
    messageDiv.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
        type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' :
        type === 'error' ? 'bg-red-100 text-red-800 border border-red-200' :
        'bg-blue-100 text-blue-800 border border-blue-200'
    }`;
    messageDiv.innerHTML = `
        <div class="flex items-center">
            <span class="ml-2">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
            ${message}
        </div>
    `;

    document.body.appendChild(messageDiv);

    setTimeout(() => {
        document.body.removeChild(messageDiv);
    }, 5000);
}

// Load user info
function loadUserInfo() {
    try {
        const user = JSON.parse(localStorage.getItem('anwar_bakery_current_user') || '{}');
        if (user.fullName) {
            const userNameElement = document.getElementById('userFullName');
            const userRoleElement = document.getElementById('userRole');
            if (userNameElement) userNameElement.textContent = user.fullName;
            if (userRoleElement) userRoleElement.textContent = user.role || 'مستخدم';
        }
    } catch (error) {
        console.log('Could not load user info:', error);
    }
}

// Load company info
function loadCompanyInfo() {
    try {
        const company = JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
        if (company.companyName) {
            const companyNameElement = document.getElementById('companyName');
            const companySloganElement = document.getElementById('companySlogan');
            if (companyNameElement) companyNameElement.textContent = company.companyName;
            if (companySloganElement) companySloganElement.textContent = company.slogan || '';
        }
    } catch (error) {
        console.log('Could not load company info:', error);
    }
}

// Load branches for filters
function loadBranches() {
    try {
        const branches = JSON.parse(localStorage.getItem('anwar_bakery_branches') || '[]');
        const branchFilter = document.getElementById('branchFilter');
        const registerBranch = document.getElementById('registerBranch');

        if (branchFilter) {
            branchFilter.innerHTML = '<option value="">جميع الفروع</option>';
            branches.forEach(branch => {
                if (branch.isActive) {
                    branchFilter.innerHTML += `<option value="${branch.id}">${branch.branchName}</option>`;
                }
            });
        }

        if (registerBranch) {
            registerBranch.innerHTML = '<option value="">اختر الفرع</option>';
            branches.forEach(branch => {
                if (branch.isActive) {
                    registerBranch.innerHTML += `<option value="${branch.id}">${branch.branchName}</option>`;
                }
            });
        }
    } catch (error) {
        console.log('Could not load branches:', error);
    }
}

// Load employees for responsible employee selection - FIXED
function loadEmployees() {
    console.log('🔄 Loading employees for cash register selection...');

    try {
        const employees = JSON.parse(localStorage.getItem('anwar_bakery_employees') || '[]');
        const responsibleEmployee = document.getElementById('responsibleEmployee');

        if (!responsibleEmployee) {
            console.log('⚠️ responsibleEmployee element not found');
            return;
        }

        console.log(`✅ Found ${employees.length} employees in database`);

        responsibleEmployee.innerHTML = '<option value="">اختر الموظف</option>';

        const activeEmployees = employees.filter(employee =>
            employee.status === 'active' || employee.isActive === true || employee.isActive !== false
        );

        console.log(`✅ Found ${activeEmployees.length} active employees`);

        activeEmployees.forEach(employee => {
            const employeeName = employee.fullName ||
                               employee.name ||
                               `${employee.firstName || ''} ${employee.lastName || ''}`.trim() ||
                               employee.employeeNumber ||
                               'موظف غير محدد';

            const employeeNumber = employee.employeeNumber ? ` (${employee.employeeNumber})` : '';
            responsibleEmployee.innerHTML += `<option value="${employee.id}">${employeeName}${employeeNumber}</option>`;

            console.log(`✅ Added employee: ${employeeName} (ID: ${employee.id})`);
        });

        console.log(`✅ Loaded ${activeEmployees.length} employees successfully`);
    } catch (error) {
        console.error('❌ Could not load employees:', error);
    }
}

// Load transfers
function loadTransfers() {
    try {
        const storedTransfers = localStorage.getItem('anwar_bakery_cash_transfers');
        if (storedTransfers) {
            transfers = JSON.parse(storedTransfers);
        }
    } catch (error) {
        console.log('Could not load transfers:', error);
        transfers = [];
    }
}

// Transfer Money Functions - COMPLETE AND ACTIVATED
function openTransferModal() {
    console.log('🔄 Opening transfer modal...');

    const modal = document.getElementById('transferModal');
    if (!modal) {
        console.error('❌ Transfer modal not found!');
        showMessage('❌ خطأ في فتح نافذة التحويل', 'error');
        return;
    }

    // Show modal
    modal.style.display = 'flex';
    modal.classList.add('active');

    // Populate dropdowns with active registers
    populateTransferDropdowns();

    // Set current date
    const dateInput = document.getElementById('transferDate');
    if (dateInput) {
        dateInput.value = new Date().toISOString().split('T')[0];
    }

    // Clear form but keep the date
    const form = document.getElementById('transferForm');
    if (form) {
        form.reset();
        if (dateInput) {
            dateInput.value = new Date().toISOString().split('T')[0];
        }
    }

    // Hide summary initially
    const summary = document.getElementById('transferSummary');
    if (summary) {
        summary.classList.add('hidden');
    }

    // Clear balance displays
    const fromBalance = document.getElementById('fromBalance');
    const toBalance = document.getElementById('toBalance');
    if (fromBalance) fromBalance.textContent = '';
    if (toBalance) toBalance.textContent = '';

    console.log('✅ Transfer modal opened successfully');
}

function closeTransferModal() {
    console.log('🔄 Closing transfer modal...');

    const modal = document.getElementById('transferModal');
    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('active');
    }

    const form = document.getElementById('transferForm');
    if (form) {
        form.reset();
    }

    const summary = document.getElementById('transferSummary');
    if (summary) {
        summary.classList.add('hidden');
    }

    // Clear balance displays
    const fromBalance = document.getElementById('fromBalance');
    const toBalance = document.getElementById('toBalance');
    if (fromBalance) fromBalance.textContent = '';
    if (toBalance) toBalance.textContent = '';

    console.log('✅ Transfer modal closed successfully');
}

// Populate transfer dropdowns - FIXED
function populateTransferDropdowns() {
    console.log('🔄 Populating transfer dropdowns...');

    const activeRegisters = cashRegisters.filter(r => r.status === 'active');
    const fromSelect = document.getElementById('fromRegister');
    const toSelect = document.getElementById('toRegister');

    if (!fromSelect || !toSelect) {
        console.error('❌ Transfer dropdown elements not found');
        showMessage('❌ خطأ في عناصر نموذج التحويل', 'error');
        return;
    }

    // Clear existing options
    fromSelect.innerHTML = '<option value="">اختر الصندوق المرسل</option>';
    toSelect.innerHTML = '<option value="">اختر الصندوق المستقبل</option>';

    if (activeRegisters.length === 0) {
        console.warn('⚠️ No active registers found for transfer');
        showMessage('لا توجد صناديق نشطة للتحويل', 'info');
        return;
    }

    // Populate both dropdowns
    activeRegisters.forEach(register => {
        const option1 = document.createElement('option');
        option1.value = register.id;
        option1.textContent = `${register.name} (${formatCurrency(register.currentBalance, true)})`;
        fromSelect.appendChild(option1);

        const option2 = document.createElement('option');
        option2.value = register.id;
        option2.textContent = `${register.name} (${formatCurrency(register.currentBalance, true)})`;
        toSelect.appendChild(option2);
    });

    console.log(`✅ Populated transfer dropdowns with ${activeRegisters.length} registers`);
}

// Transfer Entries Management Functions
function loadTransferEntries() {
    console.log('🔄 Loading transfer entries...');

    const storedTransfers = localStorage.getItem('anwar_bakery_cash_transfers');
    let transferEntries = [];

    if (storedTransfers) {
        try {
            transferEntries = JSON.parse(storedTransfers);
            console.log(`✅ Found ${transferEntries.length} transfer entries`);
        } catch (error) {
            console.error('❌ Error parsing transfer entries:', error);
            transferEntries = [];
        }
    }

    displayTransferEntries(transferEntries);
}

function displayTransferEntries(transferEntries) {
    const tableBody = document.getElementById('transferEntriesTableBody');
    const emptyState = document.getElementById('transferEntriesEmpty');

    if (!tableBody) {
        console.log('⚠️ Transfer entries table not found');
        return;
    }

    if (transferEntries.length === 0) {
        tableBody.innerHTML = '';
        if (emptyState) emptyState.classList.remove('hidden');
        return;
    }

    if (emptyState) emptyState.classList.add('hidden');

    const currencySymbol = getCurrencySymbol();

    tableBody.innerHTML = transferEntries.map(entry => `
        <tr class="hover:bg-gray-50">
            <td class="px-4 py-3 text-sm font-medium text-gray-900">${entry.transferNumber || entry.id}</td>
            <td class="px-4 py-3 text-sm text-gray-600">${formatDate(entry.date || entry.createdAt)}</td>
            <td class="px-4 py-3 text-sm text-gray-600">${entry.fromRegisterName || 'غير محدد'}</td>
            <td class="px-4 py-3 text-sm text-gray-600">${entry.toRegisterName || 'غير محدد'}</td>
            <td class="px-4 py-3 text-sm font-medium text-green-600">${formatCurrency(entry.amount, true)}</td>
            <td class="px-4 py-3 text-sm text-gray-600">${entry.notes || entry.description || 'تحويل بين الصناديق'}</td>
            <td class="px-4 py-3">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTransferStatusClass(entry.status)}">
                    ${getTransferStatusText(entry.status)}
                </span>
            </td>
            <td class="px-4 py-3 text-sm">
                <div class="flex space-x-2">
                    <button onclick="editTransferEntry(${entry.id})" class="text-blue-600 hover:text-blue-800 text-xs">
                        ✏️ تعديل
                    </button>
                    <button onclick="viewTransferEntry(${entry.id})" class="text-green-600 hover:text-green-800 text-xs">
                        👁️ عرض
                    </button>
                    <button onclick="printTransferEntry(${entry.id})" class="text-purple-600 hover:text-purple-800 text-xs">
                        🖨️ طباعة
                    </button>
                    <button onclick="deleteTransferEntry(${entry.id})" class="text-red-600 hover:text-red-800 text-xs">
                        🗑️ حذف
                    </button>
                </div>
            </td>
        </tr>
    `).join('');

    console.log(`✅ Displayed ${transferEntries.length} transfer entries`);
}

// Edit transfer entry
function editTransferEntry(transferId) {
    console.log('🔄 Opening edit modal for transfer:', transferId);

    const storedTransfers = localStorage.getItem('anwar_bakery_cash_transfers');
    if (!storedTransfers) {
        showMessage('❌ لا توجد قيود تحويل', 'error');
        return;
    }

    const transfers = JSON.parse(storedTransfers);
    const transfer = transfers.find(t => t.id === transferId);

    if (!transfer) {
        showMessage('❌ لم يتم العثور على قيد التحويل', 'error');
        return;
    }

    // Populate edit form
    document.getElementById('editTransferId').value = transfer.id;
    document.getElementById('editTransferNumber').value = transfer.transferNumber || transfer.id;
    document.getElementById('editTransferDate').value = transfer.date || transfer.createdAt;
    document.getElementById('editTransferAmount').value = transfer.amount;
    document.getElementById('editTransferReference').value = transfer.reference || '';
    document.getElementById('editTransferNotes').value = transfer.notes || '';
    document.getElementById('editTransferStatus').value = transfer.status || 'completed';

    // Populate dropdowns
    populateEditTransferDropdowns(transfer);

    // Show modal
    document.getElementById('editTransferModal').style.display = 'flex';
}

// Populate edit transfer dropdowns
function populateEditTransferDropdowns(transfer) {
    const activeRegisters = cashRegisters.filter(r => r.status === 'active');
    const fromSelect = document.getElementById('editFromRegister');
    const toSelect = document.getElementById('editToRegister');

    if (fromSelect) {
        fromSelect.innerHTML = '<option value="">اختر الصندوق المرسل</option>';
        activeRegisters.forEach(register => {
            const selected = register.id === transfer.fromRegisterId ? 'selected' : '';
            fromSelect.innerHTML += `<option value="${register.id}" ${selected}>${register.name}</option>`;
        });
    }

    if (toSelect) {
        toSelect.innerHTML = '<option value="">اختر الصندوق المستقبل</option>';
        activeRegisters.forEach(register => {
            const selected = register.id === transfer.toRegisterId ? 'selected' : '';
            toSelect.innerHTML += `<option value="${register.id}" ${selected}>${register.name}</option>`;
        });
    }
}

// Close edit transfer modal
function closeEditTransferModal() {
    document.getElementById('editTransferModal').style.display = 'none';
}

// Update transfer entry
function updateTransferEntry(event) {
    event.preventDefault();

    const transferId = parseInt(document.getElementById('editTransferId').value);
    const storedTransfers = localStorage.getItem('anwar_bakery_cash_transfers');

    if (!storedTransfers) {
        showMessage('❌ لا توجد قيود تحويل', 'error');
        return;
    }

    const transfers = JSON.parse(storedTransfers);
    const transferIndex = transfers.findIndex(t => t.id === transferId);

    if (transferIndex === -1) {
        showMessage('❌ لم يتم العثور على قيد التحويل', 'error');
        return;
    }

    // Update transfer data
    transfers[transferIndex] = {
        ...transfers[transferIndex],
        date: document.getElementById('editTransferDate').value,
        fromRegisterId: parseInt(document.getElementById('editFromRegister').value),
        toRegisterId: parseInt(document.getElementById('editToRegister').value),
        amount: parseFloat(document.getElementById('editTransferAmount').value),
        reference: document.getElementById('editTransferReference').value,
        notes: document.getElementById('editTransferNotes').value,
        status: document.getElementById('editTransferStatus').value,
        updatedAt: new Date().toISOString()
    };

    // Save changes
    localStorage.setItem('anwar_bakery_cash_transfers', JSON.stringify(transfers));

    // Close modal and refresh
    closeEditTransferModal();
    loadTransferEntries();

    showMessage('✅ تم تحديث قيد التحويل بنجاح', 'success');
}

function getTransferStatusClass(status) {
    const classes = {
        'completed': 'bg-green-100 text-green-800',
        'pending': 'bg-yellow-100 text-yellow-800',
        'cancelled': 'bg-red-100 text-red-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
}

function getTransferStatusText(status) {
    const texts = {
        'completed': 'مكتمل',
        'pending': 'معلق',
        'cancelled': 'ملغي'
    };
    return texts[status] || 'غير محدد';
}

function formatDate(dateString) {
    if (!dateString) return 'غير محدد';
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    } catch (error) {
        return dateString;
    }
}

// View transfer entry details
function viewTransferEntry(transferId) {
    const storedTransfers = localStorage.getItem('anwar_bakery_cash_transfers');
    if (!storedTransfers) return;

    const transfers = JSON.parse(storedTransfers);
    const transfer = transfers.find(t => t.id === transferId);

    if (!transfer) {
        showMessage('❌ لم يتم العثور على قيد التحويل', 'error');
        return;
    }

    const details = `
تفاصيل قيد التحويل:
رقم القيد: ${transfer.transferNumber || transfer.id}
التاريخ: ${formatDate(transfer.date || transfer.createdAt)}
من صندوق: ${transfer.fromRegisterName}
إلى صندوق: ${transfer.toRegisterName}
المبلغ: ${formatCurrency(transfer.amount, true)}
المرجع: ${transfer.reference || 'غير محدد'}
الملاحظات: ${transfer.notes || 'لا توجد'}
الحالة: ${getTransferStatusText(transfer.status)}
    `;

    alert(details);
}

// Delete transfer entry
function deleteTransferEntry(transferId) {
    if (!confirm('هل أنت متأكد من حذف قيد التحويل؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }

    const storedTransfers = localStorage.getItem('anwar_bakery_cash_transfers');
    if (!storedTransfers) return;

    const transfers = JSON.parse(storedTransfers);
    const updatedTransfers = transfers.filter(t => t.id !== transferId);

    localStorage.setItem('anwar_bakery_cash_transfers', JSON.stringify(updatedTransfers));
    loadTransferEntries();

    showMessage('✅ تم حذف قيد التحويل بنجاح', 'success');
}

// Refresh transfer entries
function refreshTransferEntries() {
    loadTransferEntries();
    showMessage('تم تحديث قيود التحويل', 'info');
}

// Print transfer entry
function printTransferEntry(transferId) {
    const storedTransfers = localStorage.getItem('anwar_bakery_cash_transfers');
    if (!storedTransfers) {
        showMessage('❌ لا توجد قيود تحويل', 'error');
        return;
    }

    const transfers = JSON.parse(storedTransfers);
    const transfer = transfers.find(t => t.id === transferId);

    if (!transfer) {
        showMessage('❌ لم يتم العثور على قيد التحويل', 'error');
        return;
    }

    // Get company info
    const company = JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
    const currency = getCurrencySymbol();

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>قيد تحويل مبلغ - ${transfer.transferNumber || transfer.id}</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: white;
                    color: #333;
                    direction: rtl;
                }
                .header {
                    text-align: center;
                    border-bottom: 3px solid #4F46E5;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }
                .company-name {
                    font-size: 28px;
                    font-weight: bold;
                    color: #4F46E5;
                    margin-bottom: 5px;
                }
                .document-title {
                    font-size: 22px;
                    font-weight: bold;
                    color: #1F2937;
                    margin-bottom: 10px;
                }
                .transfer-number {
                    font-size: 16px;
                    color: #6B7280;
                }
                .transfer-info {
                    background: #F9FAFB;
                    border: 1px solid #E5E7EB;
                    border-radius: 8px;
                    padding: 20px;
                    margin-bottom: 20px;
                }
                .info-row {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 15px;
                    padding: 10px 0;
                    border-bottom: 1px solid #E5E7EB;
                }
                .info-row:last-child {
                    border-bottom: none;
                    margin-bottom: 0;
                }
                .info-label {
                    font-weight: bold;
                    color: #374151;
                    width: 30%;
                }
                .info-value {
                    color: #1F2937;
                    width: 65%;
                }
                .amount-section {
                    background: #EEF2FF;
                    border: 2px solid #4F46E5;
                    border-radius: 8px;
                    padding: 20px;
                    text-align: center;
                    margin: 20px 0;
                }
                .amount-label {
                    font-size: 18px;
                    color: #4F46E5;
                    margin-bottom: 10px;
                }
                .amount-value {
                    font-size: 32px;
                    font-weight: bold;
                    color: #1F2937;
                }
                .status-badge {
                    display: inline-block;
                    padding: 6px 12px;
                    border-radius: 20px;
                    font-size: 14px;
                    font-weight: bold;
                }
                .status-completed { background: #D1FAE5; color: #065F46; }
                .status-pending { background: #FEF3C7; color: #92400E; }
                .status-cancelled { background: #FEE2E2; color: #991B1B; }
                .footer {
                    margin-top: 40px;
                    text-align: center;
                    color: #6B7280;
                    font-size: 14px;
                    border-top: 1px solid #E5E7EB;
                    padding-top: 20px;
                }
                @media print {
                    body { margin: 0; padding: 15px; }
                    .header { page-break-inside: avoid; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="company-name">${company.companyName || 'مخبز أنوار الحي'}</div>
                <div class="document-title">قيد تحويل مبلغ</div>
                <div class="transfer-number">رقم القيد: ${transfer.transferNumber || transfer.id}</div>
            </div>

            <div class="transfer-info">
                <div class="info-row">
                    <span class="info-label">تاريخ التحويل:</span>
                    <span class="info-value">${formatDate(transfer.date || transfer.createdAt)}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">من صندوق:</span>
                    <span class="info-value">${transfer.fromRegisterName || 'غير محدد'}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">إلى صندوق:</span>
                    <span class="info-value">${transfer.toRegisterName || 'غير محدد'}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">رقم المرجع:</span>
                    <span class="info-value">${transfer.reference || 'غير محدد'}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">الحالة:</span>
                    <span class="info-value">
                        <span class="status-badge status-${transfer.status || 'completed'}">
                            ${getTransferStatusText(transfer.status)}
                        </span>
                    </span>
                </div>
                ${transfer.notes ? `
                <div class="info-row">
                    <span class="info-label">الملاحظات:</span>
                    <span class="info-value">${transfer.notes}</span>
                </div>
                ` : ''}
            </div>

            <div class="amount-section">
                <div class="amount-label">المبلغ المحول</div>
                <div class="amount-value">${formatCurrency(transfer.amount, true)}</div>
            </div>

            <div class="footer">
                <p>تم إنشاء هذا القيد في: ${new Date().toLocaleString('ar-SA')}</p>
                <p>${company.companyName || 'مخبز أنوار الحي'} - نظام إدارة الصناديق</p>
            </div>
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.focus();

    setTimeout(() => {
        printWindow.print();
    }, 500);
}

// Export transfer entries
function exportTransferEntries() {
    showMessage('ميزة التصدير قيد التطوير', 'info');
}

// Add sample cash registers if none exist
function addSampleRegistersIfEmpty() {
    if (cashRegisters.length === 0) {
        console.log('📝 No cash registers found, adding sample data...');

        const sampleRegisters = [
            {
                id: 1,
                code: 'CR001',
                name: 'الصندوق الرئيسي',
                type: 'main',
                branchId: 1,
                branchName: 'الفرع الرئيسي',
                responsibleEmployeeId: null,
                responsibleEmployeeName: '',
                currentBalance: 50000,
                openingBalance: 50000,
                status: 'active',
                accountCode: '1101001',
                description: 'الصندوق الرئيسي للمخبز',
                createdAt: new Date().toISOString().split('T')[0],
                lastTransaction: null
            },
            {
                id: 2,
                code: 'CR002',
                name: 'نقطة البيع 1',
                type: 'pos',
                branchId: 1,
                branchName: 'الفرع الرئيسي',
                responsibleEmployeeId: null,
                responsibleEmployeeName: '',
                currentBalance: 15000,
                openingBalance: 15000,
                status: 'active',
                accountCode: '1101002',
                description: 'نقطة البيع الأولى',
                createdAt: new Date().toISOString().split('T')[0],
                lastTransaction: null
            },
            {
                id: 3,
                code: 'CR003',
                name: 'صندوق المصروفات الصغيرة',
                type: 'petty',
                branchId: 1,
                branchName: 'الفرع الرئيسي',
                responsibleEmployeeId: null,
                responsibleEmployeeName: '',
                currentBalance: 2000,
                openingBalance: 2000,
                status: 'active',
                accountCode: '1101003',
                description: 'صندوق للمصروفات الصغيرة',
                createdAt: new Date().toISOString().split('T')[0],
                lastTransaction: null
            }
        ];

        cashRegisters = sampleRegisters;
        saveRegisters();

        console.log(`✅ Added ${sampleRegisters.length} sample cash registers`);
        showMessage(`تم إضافة ${sampleRegisters.length} صناديق تجريبية`, 'success');

        // Refresh display
        loadRegisters();
    }
}

// Debug function to check cash registers data
function debugCashRegisters() {
    console.log('🔍 Debugging cash registers data:');
    console.log(`Total registers: ${cashRegisters.length}`);
    console.log(`Active registers: ${cashRegisters.filter(r => r.status === 'active').length}`);

    if (cashRegisters.length > 0) {
        console.log('Sample register:', cashRegisters[0]);
        console.log('All registers:', cashRegisters);
    } else {
        console.log('❌ No cash registers found!');
    }

    // Check localStorage
    const stored = localStorage.getItem('anwar_bakery_cash_registers');
    console.log('localStorage data:', stored ? 'exists' : 'missing');

    if (stored) {
        try {
            const parsed = JSON.parse(stored);
            console.log(`Stored registers count: ${parsed.length}`);
        } catch (error) {
            console.error('Error parsing stored data:', error);
        }
    }

    // Debug employees data
    debugEmployeesData();
}

// Debug function to check employees data
function debugEmployeesData() {
    console.log('🔍 Debugging employees data:');

    const storedEmployees = localStorage.getItem('anwar_bakery_employees');
    if (!storedEmployees) {
        console.log('❌ No employees found in localStorage');
        showMessage('لا توجد بيانات موظفين في قاعدة البيانات', 'warning');
        return;
    }

    try {
        const employees = JSON.parse(storedEmployees);
        console.log(`✅ Total employees: ${employees.length}`);

        const activeEmployees = employees.filter(emp =>
            emp.status === 'active' || emp.isActive === true || emp.isActive !== false
        );
        console.log(`✅ Active employees: ${activeEmployees.length}`);

        if (employees.length > 0) {
            console.log('Sample employee:', employees[0]);

            // Show employee names and IDs
            employees.forEach(emp => {
                const name = emp.fullName || emp.name || 'غير محدد';
                console.log(`Employee: ${name} (ID: ${emp.id}, Status: ${emp.status})`);
            });
        }

        showMessage(`تم العثور على ${employees.length} موظف (${activeEmployees.length} نشط)`, 'success');
    } catch (error) {
        console.error('❌ Error parsing employees data:', error);
        showMessage('خطأ في قراءة بيانات الموظفين', 'error');
    }
}

// Add event listeners for currency updates
function addCurrencyUpdateListeners() {
    // Listen for settings updates
    window.addEventListener('settingsUpdated', function() {
        console.log('📢 Settings updated, refreshing currency display...');
        setTimeout(() => {
            updateRegisterStats();
            displayRegisters();
        }, 500);
    });

    // Listen for storage changes
    window.addEventListener('storage', function(event) {
        if (event.key === 'anwar_bakery_settings') {
            console.log('📢 Settings changed in localStorage, refreshing currency...');
            setTimeout(() => {
                updateRegisterStats();
                displayRegisters();
            }, 500);
        }
    });
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔄 Initializing cash registers page...');

    try {
        loadUserInfo();
        loadCompanyInfo();
        loadBranches();
        loadEmployees();
        loadRegisters();
        loadTransfers();

        // Add sample data if needed
        addSampleRegistersIfEmpty();

        // Clean and validate data
        validateAndCleanRegisters();

        // Debug cash registers data
        debugCashRegisters();

        // Load transfer entries
        loadTransferEntries();

        // Add currency update listeners
        addCurrencyUpdateListeners();

        // Hide modals initially
        const addModal = document.getElementById('addRegisterModal');
        const transferModal = document.getElementById('transferModal');
        if (addModal) addModal.style.display = 'none';
        if (transferModal) transferModal.style.display = 'none';

        // Add event listeners for transfer form
        const transferAmount = document.getElementById('transferAmount');
        const transferDate = document.getElementById('transferDate');
        const fromRegister = document.getElementById('fromRegister');
        const toRegister = document.getElementById('toRegister');

        if (transferAmount) {
            transferAmount.addEventListener('input', updateTransferSummary);
        }
        if (transferDate) {
            transferDate.addEventListener('change', updateTransferSummary);
        }
        if (fromRegister) {
            fromRegister.addEventListener('change', function() {
                updateFromBalance();
                updateTransferSummary();
            });
        }
        if (toRegister) {
            toRegister.addEventListener('change', function() {
                updateToBalance();
                updateTransferSummary();
            });
        }

        console.log('✅ Cash registers page initialized successfully');
        showMessage('تم تحميل صفحة الصناديق بنجاح', 'success');
    } catch (error) {
        console.error('❌ Error initializing cash registers page:', error);
        showMessage('خطأ في تحميل الصفحة: ' + error.message, 'error');
    }
});
