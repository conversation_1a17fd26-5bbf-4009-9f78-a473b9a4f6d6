<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفواتير - مخبز أنور</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="app-settings.js"></script>
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .modal { display: none; }
        .modal.active { display: flex; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }

        /* تحسين مظهر النماذج */
        .modal .bg-white {
            max-height: 90vh;
        }

        input[type="text"],
        input[type="number"],
        input[type="date"],
        select,
        textarea {
            font-size: 16px !important;
            line-height: 1.5;
        }

        .tab-button {
            font-size: 14px;
            padding: 8px 12px;
            transition: all 0.2s ease;
        }

        button {
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .modal form {
            max-height: calc(90vh - 120px);
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Mobile menu overlay -->
    <div id="mobileOverlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 lg:hidden hidden"></div>

    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform translate-x-full lg:translate-x-0 lg:static lg:inset-0 sidebar-transition">
            <div class="h-16 px-4 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between">
                <h1 id="sidebarCompanyName" class="text-white text-lg font-bold">مخبز أنور</h1>
                <button onclick="toggleSidebar()" class="lg:hidden text-white">
                    <span class="text-xl">✕</span>
                </button>
            </div>

            <nav class="mt-5 px-2 space-y-1 h-full overflow-y-auto pb-20">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    لوحة التحكم
                </a>
                <a href="users-management.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👥</span>
                    إدارة المستخدمين
                </a>
                <a href="company-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏢</span>
                    بيانات المنشأة
                </a>
                <a href="system-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">⚙️</span>
                    إعدادات النظام
                </a>
                <a href="system-admin.html" class="flex items-center px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 hover:text-red-700 rounded-md border border-red-200">
                    <span class="ml-3">🔧</span>
                    إدارة النظام المتقدمة
                </a>
                <a href="branches-warehouses.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏪</span>
                    الفروع والمخازن
                </a>
                <a href="units.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📏</span>
                    وحدات القياس
                </a>
                <a href="chart-of-accounts.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🌳</span>
                    شجرة الحسابات
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="damage-entry.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🗑️</span>
                    قيد التالف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🚚</span>
                    الموردين
                </a>
                <a href="employees.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👨‍💼</span>
                    الموظفين
                </a>
                <a href="cash-registers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">💰</span>
                    الصناديق
                </a>
                <a href="banks.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏦</span>
                    البنوك
                </a>
                <a href="owners.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👑</span>
                    الملاك
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="vouchers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📋</span>
                    السندات
                </a>
                <a href="journal-entry.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📝</span>
                    القيود اليومية
                </a>
                <a href="inventory.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📊</span>
                    المخزون
                </a>
                <a href="reports.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📈</span>
                    التقارير
                </a>

                <!-- User Info & Logout -->
                <div class="absolute bottom-4 left-2 right-2">
                    <div class="bg-gray-50 rounded-lg p-3 mb-2">
                        <p id="userFullName" class="text-sm font-medium text-gray-900">مدير النظام</p>
                        <p id="userRole" class="text-xs text-gray-500">admin</p>
                    </div>
                    <button onclick="logout()" class="w-full text-right px-3 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50 flex items-center">
                        <span class="ml-3">🚪</span>
                        تسجيل الخروج
                    </button>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 overflow-hidden">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <button onclick="toggleSidebar()" class="lg:hidden text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">☰</span>
                            </button>
                            <span class="text-lg font-semibold text-gray-900">إدارة الفواتير</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-500" id="currentDateTime"></span>
                            <div class="flex items-center space-x-2">
                                <span id="userDisplayName" class="text-sm font-medium text-gray-700">مدير النظام</span>
                                <button onclick="logout()" class="text-red-600 hover:text-red-800 text-sm">
                                    خروج
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Header -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2 flex items-center">
                        <span class="ml-3">🧾</span>
                        نظام الفواتير المتقدم
                    </h1>
                    <p class="text-gray-600">
                        نظام محاسبي متكامل لإدارة جميع أنواع الفواتير مع ربط تلقائي بالمخزون والمحاسبة
                    </p>
                </div>

                <!-- Invoice Types Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Purchase Invoices -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                        <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-xl font-bold mb-2">فواتير المشتريات</h3>
                                    <p class="text-blue-100">شراء المواد والخامات من الموردين</p>
                                </div>
                                <div class="text-4xl opacity-80">🛒</div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3">
                                <button onclick="window.location.href='purchase-invoice.html'"
                                        class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center">
                                    <span class="ml-2">➕</span>
                                    فاتورة مشتريات جديدة
                                </button>
                                <button onclick="window.location.href='purchase-invoices-list.html'"
                                        class="w-full bg-blue-100 text-blue-700 py-2 px-4 rounded-lg hover:bg-blue-200 transition-colors flex items-center justify-center">
                                    <span class="ml-2">📋</span>
                                    عرض فواتير المشتريات
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Sales Invoices -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                        <div class="bg-gradient-to-r from-green-500 to-green-600 p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-xl font-bold mb-2">فواتير المبيعات</h3>
                                    <p class="text-green-100">بيع المنتجات والخدمات للعملاء</p>
                                </div>
                                <div class="text-4xl opacity-80">💰</div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3">
                                <button onclick="window.location.href='sales-invoice.html'"
                                        class="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center">
                                    <span class="ml-2">➕</span>
                                    فاتورة مبيعات جديدة
                                </button>
                                <button onclick="window.location.href='sales-invoices-list.html'"
                                        class="w-full bg-green-100 text-green-700 py-2 px-4 rounded-lg hover:bg-green-200 transition-colors flex items-center justify-center">
                                    <span class="ml-2">📋</span>
                                    عرض فواتير المبيعات
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Sale (POS) -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                        <div class="bg-gradient-to-r from-purple-500 to-purple-600 p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-xl font-bold mb-2">نقطة البيع السريع</h3>
                                    <p class="text-purple-100">مبيعات سريعة ونقدية مباشرة</p>
                                </div>
                                <div class="text-4xl opacity-80">⚡</div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3">
                                <button onclick="window.location.href='pos-system.html'"
                                        class="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors flex items-center justify-center">
                                    <span class="ml-2">⚡</span>
                                    فتح نقطة البيع
                                </button>
                                <button onclick="window.location.href='pos-sales-list.html'"
                                        class="w-full bg-purple-100 text-purple-700 py-2 px-4 rounded-lg hover:bg-purple-200 transition-colors flex items-center justify-center">
                                    <span class="ml-2">📋</span>
                                    عرض مبيعات POS
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Purchase Returns -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                        <div class="bg-gradient-to-r from-orange-500 to-orange-600 p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-xl font-bold mb-2">مرتجع المشتريات</h3>
                                    <p class="text-orange-100">إرجاع المواد للموردين</p>
                                </div>
                                <div class="text-4xl opacity-80">↩️</div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3">
                                <button onclick="window.location.href='purchase-return.html'"
                                        class="w-full bg-orange-600 text-white py-3 px-4 rounded-lg hover:bg-orange-700 transition-colors flex items-center justify-center">
                                    <span class="ml-2">➕</span>
                                    مرتجع مشتريات جديد
                                </button>
                                <button onclick="window.location.href='purchase-returns-list.html'"
                                        class="w-full bg-orange-100 text-orange-700 py-2 px-4 rounded-lg hover:bg-orange-200 transition-colors flex items-center justify-center">
                                    <span class="ml-2">📋</span>
                                    عرض مرتجعات المشتريات
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Sales Returns -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                        <div class="bg-gradient-to-r from-red-500 to-red-600 p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-xl font-bold mb-2">مرتجع المبيعات</h3>
                                    <p class="text-red-100">استقبال المنتجات من العملاء</p>
                                </div>
                                <div class="text-4xl opacity-80">↪️</div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3">
                                <button onclick="window.location.href='sales-return.html'"
                                        class="w-full bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center">
                                    <span class="ml-2">➕</span>
                                    مرتجع مبيعات جديد
                                </button>
                                <button onclick="window.location.href='sales-returns-list.html'"
                                        class="w-full bg-red-100 text-red-700 py-2 px-4 rounded-lg hover:bg-red-200 transition-colors flex items-center justify-center">
                                    <span class="ml-2">📋</span>
                                    عرض مرتجعات المبيعات
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Reports & Analytics -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                        <div class="bg-gradient-to-r from-indigo-500 to-indigo-600 p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-xl font-bold mb-2">التقارير والتحليلات</h3>
                                    <p class="text-indigo-100">تقارير شاملة لجميع الفواتير</p>
                                </div>
                                <div class="text-4xl opacity-80">📊</div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3">
                                <button onclick="window.location.href='invoices-reports.html'"
                                        class="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 transition-colors flex items-center justify-center">
                                    <span class="ml-2">📈</span>
                                    تقارير الفواتير
                                </button>
                                <button onclick="window.location.href='invoices-analytics.html'"
                                        class="w-full bg-indigo-100 text-indigo-700 py-2 px-4 rounded-lg hover:bg-indigo-200 transition-colors flex items-center justify-center">
                                    <span class="ml-2">🔍</span>
                                    تحليلات مفصلة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Success/Error Messages -->
                <div id="messageContainer" class="mb-6"></div>











            </main>
        </div>
    </div>

    <script>
        // Check authentication
        function checkAuth() {
            const session = localStorage.getItem('anwar_bakery_session') ||
                           sessionStorage.getItem('anwar_bakery_session');

            if (!session) {
                window.location.href = 'login.html';
                return null;
            }

            return JSON.parse(session);
        }

        // Load user info
        function loadUserInfo() {
            const session = checkAuth();
            if (session) {
                const userFullNameElement = document.getElementById('userFullName');
                const userRoleElement = document.getElementById('userRole');
                const userDisplayNameElement = document.getElementById('userDisplayName');

                if (userFullNameElement) userFullNameElement.textContent = session.fullName;
                if (userRoleElement) userRoleElement.textContent = session.role;
                if (userDisplayNameElement) userDisplayNameElement.textContent = session.fullName;
            }
        }

        // Load company info
        function loadCompanyInfo() {
            const savedData = localStorage.getItem('anwar_bakery_company');
            if (savedData) {
                const companyData = JSON.parse(savedData);
                const sidebarElement = document.getElementById('sidebarCompanyName');
                if (sidebarElement && companyData.companyNameAr) {
                    sidebarElement.textContent = companyData.companyNameAr;
                }
            }
        }

        // Toggle sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('mobileOverlay');

            if (sidebar && overlay) {
                if (sidebar.classList.contains('translate-x-full')) {
                    sidebar.classList.remove('translate-x-full');
                    overlay.classList.remove('hidden');
                } else {
                    sidebar.classList.add('translate-x-full');
                    overlay.classList.add('hidden');
                }
            }
        }

        // Logout function
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('anwar_bakery_session');
                sessionStorage.removeItem('anwar_bakery_session');
                window.location.href = 'login.html';
            }
        }

        // Update current date time
        function updateDateTime() {
            const now = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            const dateTimeElement = document.getElementById('currentDateTime');
            if (dateTimeElement) {
                dateTimeElement.textContent = now.toLocaleDateString('ar-SA', options);
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadUserInfo();
            loadCompanyInfo();
            updateDateTime();
            setInterval(updateDateTime, 60000);

            // Close sidebar when clicking overlay
            const overlay = document.getElementById('mobileOverlay');
            if (overlay) {
                overlay.addEventListener('click', toggleSidebar);
            }
        });
    </script>

</body>
</html>
