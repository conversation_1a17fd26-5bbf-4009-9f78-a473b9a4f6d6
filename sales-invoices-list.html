<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة فواتير المبيعات - مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .custom-scroll {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 #f7fafc;
        }

        .custom-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scroll::-webkit-scrollbar-track {
            background: #f7fafc;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        .invoice-row:hover {
            background-color: #f8fafc;
        }

        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-weight: 500;
        }

        .status-paid {
            background-color: #dcfce7;
            color: #166534;
        }

        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }

        .status-overdue {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .status-draft {
            background-color: #f3f4f6;
            color: #374151;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="bg-white w-64 shadow-lg flex flex-col">
            <div class="p-4 flex-shrink-0">
                <div class="flex items-center mb-8">
                    <span class="text-2xl ml-3">🍞</span>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800" id="companyName">مخبز أنوار الحي</h1>
                        <p class="text-sm text-gray-600" id="companySlogan">جودة تستحق الثقة</p>
                    </div>
                </div>
            </div>

            <nav class="flex-1 overflow-y-auto px-4 pb-4 custom-scroll">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    الرئيسية
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-green-600 bg-green-50 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏭</span>
                    الموردين
                </a>

                <!-- User Info & Logout -->
                <div class="mt-auto p-4 border-t border-gray-200">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                            أ
                        </div>
                        <div class="mr-3 flex-1">
                            <p id="userFullName" class="text-sm font-medium text-gray-900">أحمد محمد</p>
                            <p id="userRole" class="text-xs text-gray-500">مدير النظام</p>
                        </div>
                        <button onclick="logout()" class="text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50" title="تسجيل الخروج">
                            <span class="text-lg">🚪</span>
                        </button>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col min-h-0">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <a href="invoices.html" class="text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">←</span>
                            </a>
                            <h2 class="text-xl font-semibold text-gray-900">قائمة فواتير المبيعات</h2>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="window.location.href='sales-invoice.html'" class="bg-green-600 text-white px-4 py-2 rounded text-sm hover:bg-green-700">
                                ➕ فاتورة جديدة
                            </button>
                            <button onclick="window.history.back()" class="text-gray-600 hover:text-gray-800 px-2 py-2 rounded text-sm">
                                ← رجوع
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6 custom-scroll">
                <!-- Filters -->
                <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">البحث والتصفية</h3>
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <input type="text" id="searchInput" placeholder="البحث برقم الفاتورة أو العميل..."
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-sm"
                                   onkeyup="filterInvoices()">
                        </div>
                        <div>
                            <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-sm" onchange="filterInvoices()">
                                <option value="">جميع الحالات</option>
                                <option value="paid">مدفوعة</option>
                                <option value="pending">معلقة</option>
                                <option value="overdue">متأخرة</option>
                                <option value="draft">مسودة</option>
                            </select>
                        </div>
                        <div>
                            <select id="customerFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-sm" onchange="filterInvoices()">
                                <option value="">جميع العملاء</option>
                            </select>
                        </div>
                        <div>
                            <input type="date" id="dateFrom"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-sm"
                                   onchange="filterInvoices()">
                        </div>
                        <div>
                            <input type="date" id="dateTo"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-sm"
                                   onchange="filterInvoices()">
                        </div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <span class="text-2xl">📊</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">إجمالي الفواتير</p>
                                <p class="text-2xl font-bold text-gray-900" id="totalInvoices">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <span class="text-2xl">✅</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">مدفوعة</p>
                                <p class="text-2xl font-bold text-gray-900" id="paidInvoices">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                <span class="text-2xl">⏳</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">معلقة</p>
                                <p class="text-2xl font-bold text-gray-900" id="pendingInvoices">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <span class="text-2xl">💰</span>
                            </div>
                            <div class="mr-4">
                                <p class="text-sm font-medium text-gray-600">إجمالي المبيعات</p>
                                <p class="text-2xl font-bold text-gray-900" id="totalAmount">0</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoices Table -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم الفاتورة</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">العميل</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المبلغ</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">طريقة الدفع</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">السندات</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="invoicesTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- Invoices will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Sample invoices data
        const salesInvoices = [
            {
                id: 1,
                number: 'SAL-2024-001',
                date: '2024-01-15',
                customer: 'مطعم الأصالة',
                amount: 2500.00,
                status: 'paid',
                paymentMethod: 'نقدي',
                hasVouchers: true
            },
            {
                id: 2,
                number: 'SAL-2024-002',
                date: '2024-01-16',
                customer: 'كافيه النخبة',
                amount: 1800.00,
                status: 'pending',
                paymentMethod: 'آجل',
                hasVouchers: false
            },
            {
                id: 3,
                number: 'SAL-2024-003',
                date: '2024-01-17',
                customer: 'فندق الضيافة',
                amount: 4200.00,
                status: 'paid',
                paymentMethod: 'تحويل بنكي',
                hasVouchers: true
            }
        ];

        // Load invoices
        function loadInvoices() {
            const tbody = document.getElementById('invoicesTableBody');
            
            tbody.innerHTML = salesInvoices.map(invoice => `
                <tr class="invoice-row">
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">${invoice.number}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${formatDate(invoice.date)}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${invoice.customer}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">${invoice.amount.toFixed(2)} ر.س</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <span class="status-badge status-${invoice.status}">
                            ${getStatusText(invoice.status)}
                        </span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${invoice.paymentMethod}</div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-center">
                        ${invoice.hasVouchers ? 
                            '<span class="text-green-600">✅</span>' : 
                            '<span class="text-gray-400">➖</span>'
                        }
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-center text-sm font-medium">
                        <div class="flex justify-center space-x-2">
                            <button onclick="viewInvoice(${invoice.id})" class="text-green-600 hover:text-green-900" title="عرض">
                                👁️
                            </button>
                            <button onclick="editInvoice(${invoice.id})" class="text-blue-600 hover:text-blue-900" title="تعديل">
                                ✏️
                            </button>
                            <button onclick="printInvoice(${invoice.id})" class="text-purple-600 hover:text-purple-900" title="طباعة">
                                🖨️
                            </button>
                            <button onclick="deleteInvoice(${invoice.id})" class="text-red-600 hover:text-red-900" title="حذف">
                                🗑️
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');

            updateStatistics();
        }

        // Format date
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        // Get status text
        function getStatusText(status) {
            const statusTexts = {
                'paid': 'مدفوعة',
                'pending': 'معلقة',
                'overdue': 'متأخرة',
                'draft': 'مسودة'
            };
            return statusTexts[status] || status;
        }

        // Update statistics
        function updateStatistics() {
            document.getElementById('totalInvoices').textContent = salesInvoices.length;
            document.getElementById('paidInvoices').textContent = salesInvoices.filter(inv => inv.status === 'paid').length;
            document.getElementById('pendingInvoices').textContent = salesInvoices.filter(inv => inv.status === 'pending').length;
            
            const totalAmount = salesInvoices.reduce((sum, inv) => sum + inv.amount, 0);
            document.getElementById('totalAmount').textContent = totalAmount.toFixed(2) + ' ر.س';
        }

        // Filter invoices
        function filterInvoices() {
            // Implementation for filtering
            console.log('Filtering invoices...');
        }

        // Action functions
        function viewInvoice(id) {
            alert(`عرض الفاتورة رقم ${id}`);
        }

        function editInvoice(id) {
            alert(`تعديل الفاتورة رقم ${id}`);
        }

        function printInvoice(id) {
            alert(`طباعة الفاتورة رقم ${id}`);
        }

        function deleteInvoice(id) {
            if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
                alert(`تم حذف الفاتورة رقم ${id}`);
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadInvoices();
        });
    </script>

</body>
</html>
