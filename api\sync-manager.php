<?php
/**
 * Sync Manager - نظام المزامنة بين localStorage وقاعدة البيانات
 * Synchronization between localStorage and real database
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config/database.php';

class SyncManager {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }

    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $action = $_GET['action'] ?? '';

        try {
            switch ($method) {
                case 'POST':
                    switch ($action) {
                        case 'sync-to-server':
                            return $this->syncToServer();
                        case 'sync-from-server':
                            return $this->syncFromServer();
                        case 'full-sync':
                            return $this->fullSync();
                        case 'check-connection':
                            return $this->checkConnection();
                        default:
                            return $this->sendError('Invalid action', 400);
                    }
                    break;
                
                case 'GET':
                    switch ($action) {
                        case 'status':
                            return $this->getSyncStatus();
                        case 'last-sync':
                            return $this->getLastSyncTime();
                        default:
                            return $this->sendError('Invalid action', 400);
                    }
                    break;
                
                default:
                    return $this->sendError('Method not allowed', 405);
            }
        } catch (Exception $e) {
            return $this->sendError($e->getMessage(), 500);
        }
    }

    /**
     * Sync localStorage data to server database
     * مزامنة بيانات localStorage إلى قاعدة البيانات
     */
    private function syncToServer() {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($input['data'])) {
            return $this->sendError('No data provided', 400);
        }

        $localData = $input['data'];
        $syncResults = [];

        $this->conn->beginTransaction();

        try {
            // Sync each data type
            $dataTypes = [
                'products' => 'syncProducts',
                'customers' => 'syncCustomers',
                'suppliers' => 'syncSuppliers',
                'employees' => 'syncEmployees',
                'invoices' => 'syncInvoices',
                'vouchers' => 'syncVouchers',
                'journal_entries' => 'syncJournalEntries',
                'accounts' => 'syncAccounts'
            ];

            foreach ($dataTypes as $type => $method) {
                if (isset($localData[$type])) {
                    $result = $this->$method($localData[$type]);
                    $syncResults[$type] = $result;
                }
            }

            // Update last sync time
            $this->updateLastSyncTime();

            $this->conn->commit();

            return $this->sendSuccess([
                'message' => 'تم رفع البيانات إلى الخادم بنجاح',
                'results' => $syncResults,
                'sync_time' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $this->conn->rollback();
            return $this->sendError('فشل في رفع البيانات: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Sync server database to localStorage
     * مزامنة قاعدة البيانات إلى localStorage
     */
    private function syncFromServer() {
        try {
            $serverData = [];

            // Get all data from server
            $tables = [
                'products' => 'SELECT * FROM products WHERE is_active = 1',
                'customers' => 'SELECT * FROM customers WHERE is_active = 1',
                'suppliers' => 'SELECT * FROM suppliers WHERE is_active = 1',
                'employees' => 'SELECT * FROM employees WHERE is_active = 1',
                'invoices' => 'SELECT * FROM invoices ORDER BY created_at DESC LIMIT 1000',
                'vouchers' => 'SELECT * FROM vouchers ORDER BY created_at DESC LIMIT 1000',
                'journal_entries' => 'SELECT * FROM journal_entries ORDER BY created_at DESC LIMIT 1000',
                'accounts' => 'SELECT * FROM chart_of_accounts WHERE is_active = 1'
            ];

            foreach ($tables as $type => $query) {
                $stmt = $this->conn->query($query);
                $serverData[$type] = $stmt->fetchAll();
            }

            return $this->sendSuccess([
                'message' => 'تم تحميل البيانات من الخادم بنجاح',
                'data' => $serverData,
                'sync_time' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            return $this->sendError('فشل في تحميل البيانات: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Full bidirectional sync
     * مزامنة كاملة ثنائية الاتجاه
     */
    private function fullSync() {
        $input = json_decode(file_get_contents('php://input'), true);
        
        try {
            // First sync to server
            $uploadResult = $this->syncToServer();
            
            // Then sync from server
            $downloadResult = $this->syncFromServer();

            return $this->sendSuccess([
                'message' => 'تم إجراء المزامنة الكاملة بنجاح',
                'upload' => $uploadResult,
                'download' => $downloadResult,
                'sync_time' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            return $this->sendError('فشل في المزامنة الكاملة: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Check database connection
     * فحص اتصال قاعدة البيانات
     */
    private function checkConnection() {
        try {
            $stmt = $this->conn->query("SELECT 1");
            $isInitialized = $this->db->isDatabaseInitialized();
            
            return $this->sendSuccess([
                'connected' => true,
                'initialized' => $isInitialized,
                'server_time' => date('Y-m-d H:i:s'),
                'message' => 'الاتصال بقاعدة البيانات نشط'
            ]);

        } catch (Exception $e) {
            return $this->sendError('فشل الاتصال بقاعدة البيانات: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Sync products to database
     */
    private function syncProducts($products) {
        $inserted = 0;
        $updated = 0;

        foreach ($products as $product) {
            // Check if product exists
            $stmt = $this->conn->prepare("SELECT id FROM products WHERE id = ?");
            $stmt->execute([$product['id']]);
            
            if ($stmt->fetch()) {
                // Update existing product
                $stmt = $this->conn->prepare("
                    UPDATE products SET 
                        code = ?, name = ?, description = ?, category_id = ?, 
                        unit_id = ?, cost_price = ?, selling_price = ?, 
                        current_stock = ?, min_stock_level = ?, max_stock_level = ?,
                        updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([
                    $product['code'], $product['name'], $product['description'] ?? '',
                    $product['category_id'] ?? null, $product['unit_id'] ?? 1,
                    $product['cost_price'] ?? 0, $product['selling_price'],
                    $product['quantity'] ?? 0, $product['min_stock_level'] ?? 0,
                    $product['max_stock_level'] ?? 0, $product['id']
                ]);
                $updated++;
            } else {
                // Insert new product
                $stmt = $this->conn->prepare("
                    INSERT INTO products 
                    (id, code, name, description, category_id, unit_id, cost_price, 
                     selling_price, current_stock, min_stock_level, max_stock_level)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $product['id'], $product['code'], $product['name'],
                    $product['description'] ?? '', $product['category_id'] ?? null,
                    $product['unit_id'] ?? 1, $product['cost_price'] ?? 0,
                    $product['selling_price'], $product['quantity'] ?? 0,
                    $product['min_stock_level'] ?? 0, $product['max_stock_level'] ?? 0
                ]);
                $inserted++;
            }
        }

        return ['inserted' => $inserted, 'updated' => $updated];
    }

    /**
     * Sync customers to database
     */
    private function syncCustomers($customers) {
        $inserted = 0;
        $updated = 0;

        foreach ($customers as $customer) {
            $stmt = $this->conn->prepare("SELECT id FROM customers WHERE id = ?");
            $stmt->execute([$customer['id']]);
            
            if ($stmt->fetch()) {
                $stmt = $this->conn->prepare("
                    UPDATE customers SET 
                        code = ?, name = ?, phone = ?, email = ?, address = ?,
                        current_balance = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([
                    $customer['code'] ?? '', $customer['name'], $customer['phone'] ?? '',
                    $customer['email'] ?? '', $customer['address'] ?? '',
                    $customer['balance'] ?? 0, $customer['id']
                ]);
                $updated++;
            } else {
                $stmt = $this->conn->prepare("
                    INSERT INTO customers (id, code, name, phone, email, address, current_balance)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $customer['id'], $customer['code'] ?? '', $customer['name'],
                    $customer['phone'] ?? '', $customer['email'] ?? '',
                    $customer['address'] ?? '', $customer['balance'] ?? 0
                ]);
                $inserted++;
            }
        }

        return ['inserted' => $inserted, 'updated' => $updated];
    }

    /**
     * Get sync status
     */
    private function getSyncStatus() {
        try {
            $stmt = $this->conn->query("
                SELECT 
                    (SELECT COUNT(*) FROM products) as products_count,
                    (SELECT COUNT(*) FROM customers) as customers_count,
                    (SELECT COUNT(*) FROM invoices) as invoices_count,
                    (SELECT MAX(updated_at) FROM products) as last_product_update
            ");
            $stats = $stmt->fetch();

            return $this->sendSuccess([
                'database_stats' => $stats,
                'last_check' => date('Y-m-d H:i:s'),
                'status' => 'active'
            ]);

        } catch (Exception $e) {
            return $this->sendError('فشل في الحصول على حالة المزامنة: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Update last sync time
     */
    private function updateLastSyncTime() {
        $stmt = $this->conn->prepare("
            INSERT INTO system_settings (setting_key, setting_value, updated_at) 
            VALUES ('last_sync_time', ?, NOW())
            ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = NOW()
        ");
        $now = date('Y-m-d H:i:s');
        $stmt->execute([$now, $now]);
    }

    /**
     * Get last sync time
     */
    private function getLastSyncTime() {
        try {
            $stmt = $this->conn->prepare("
                SELECT setting_value FROM system_settings 
                WHERE setting_key = 'last_sync_time'
            ");
            $stmt->execute();
            $result = $stmt->fetch();

            return $this->sendSuccess([
                'last_sync' => $result ? $result['setting_value'] : null,
                'current_time' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            return $this->sendError('فشل في الحصول على وقت آخر مزامنة: ' . $e->getMessage(), 500);
        }
    }

    // Additional sync methods for other data types would go here...
    private function syncSuppliers($suppliers) { /* Implementation */ return ['inserted' => 0, 'updated' => 0]; }
    private function syncEmployees($employees) { /* Implementation */ return ['inserted' => 0, 'updated' => 0]; }
    private function syncInvoices($invoices) { /* Implementation */ return ['inserted' => 0, 'updated' => 0]; }
    private function syncVouchers($vouchers) { /* Implementation */ return ['inserted' => 0, 'updated' => 0]; }
    private function syncJournalEntries($entries) { /* Implementation */ return ['inserted' => 0, 'updated' => 0]; }
    private function syncAccounts($accounts) { /* Implementation */ return ['inserted' => 0, 'updated' => 0]; }

    private function sendSuccess($data, $message = 'Success') {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => $message,
            'data' => $data
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }

    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'message' => $message
        ], JSON_UNESCAPED_UNICODE);
        exit();
    }
}

$syncManager = new SyncManager();
$syncManager->handleRequest();
?>

