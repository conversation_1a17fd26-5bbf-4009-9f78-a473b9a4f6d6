// إدارة المخازن - نظام مخبز أنوار الحي
// Warehouses Management - Anwar Bakery System

// بيانات المخازن الافتراضية
let warehouses = [
    {
        id: 1,
        name: 'المخزن الرئيسي',
        description: 'المخزن الرئيسي للمخبز',
        location: 'الطابق الأرضي',
        manager: 'أحمد محمد',
        phone: '0501234567',
        capacity: 1000,
        currentUtilization: 650,
        isActive: true,
        createdAt: '2024-01-01'
    },
    {
        id: 2,
        name: 'مخزن المواد الخام',
        description: 'مخزن خاص بالمواد الخام والمكونات',
        location: 'الطابق السفلي',
        manager: 'فاطمة أحمد',
        phone: '0507654321',
        capacity: 500,
        currentUtilization: 320,
        isActive: true,
        createdAt: '2024-01-01'
    },
    {
        id: 3,
        name: 'مخزن المنتجات النهائية',
        description: 'مخزن المنتجات الجاهزة للبيع',
        location: 'الطابق الأول',
        manager: 'محمد علي',
        phone: '0509876543',
        capacity: 800,
        currentUtilization: 450,
        isActive: true,
        createdAt: '2024-01-01'
    }
];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    checkAuth();
    loadWarehouses();
    displayWarehouses();
    setupEventListeners();
});

// التحقق من المصادقة
function checkAuth() {
    const session = localStorage.getItem('anwar_bakery_session');
    if (!session) {
        window.location.href = 'login.html';
        return;
    }
}

// تحميل المخازن من التخزين المحلي
function loadWarehouses() {
    const savedWarehouses = localStorage.getItem('anwar_bakery_warehouses');
    if (savedWarehouses) {
        warehouses = JSON.parse(savedWarehouses);
    } else {
        // إنشاء المخازن الافتراضية إذا لم تكن موجودة
        warehouses = [
            {
                id: 1,
                name: 'المخزن الرئيسي',
                description: 'المخزن الرئيسي للمخبز',
                location: 'الطابق الأرضي',
                manager: 'أحمد محمد',
                phone: '0501234567',
                capacity: 1000,
                currentUtilization: 650,
                isActive: true,
                createdAt: '2024-01-01'
            },
            {
                id: 2,
                name: 'مخزن المواد الخام',
                description: 'مخزن خاص بالمواد الخام والمكونات',
                location: 'الطابق السفلي',
                manager: 'فاطمة أحمد',
                phone: '0507654321',
                capacity: 500,
                currentUtilization: 320,
                isActive: true,
                createdAt: '2024-01-01'
            },
            {
                id: 3,
                name: 'مخزن المنتجات النهائية',
                description: 'مخزن المنتجات الجاهزة للبيع',
                location: 'الطابق الأول',
                manager: 'محمد علي',
                phone: '0509876543',
                capacity: 800,
                currentUtilization: 450,
                isActive: true,
                createdAt: '2024-01-01'
            }
        ];
        saveWarehouses();
    }
}

// حفظ المخازن في التخزين المحلي
function saveWarehouses() {
    localStorage.setItem('anwar_bakery_warehouses', JSON.stringify(warehouses));
}

// عرض المخازن
function displayWarehouses() {
    const tbody = document.getElementById('warehousesTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    warehouses.forEach(warehouse => {
        const utilizationPercentage = warehouse.capacity > 0 ?
            ((warehouse.currentUtilization / warehouse.capacity) * 100).toFixed(1) : 0;

        const statusClass = warehouse.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
        const statusText = warehouse.isActive ? 'نشط' : 'غير نشط';

        const utilizationClass = utilizationPercentage > 80 ? 'text-red-600' :
                                utilizationPercentage > 60 ? 'text-yellow-600' : 'text-green-600';

        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';

        row.innerHTML = `
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">${warehouse.name}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <div class="font-medium">${warehouse.description}</div>
                <div class="text-xs text-gray-500">${warehouse.location || 'غير محدد'}</div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <div class="font-medium">${warehouse.manager || 'غير محدد'}</div>
                <div class="text-xs text-gray-500">${warehouse.phone || ''}</div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">${warehouse.capacity}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <div class="${utilizationClass} font-medium">${warehouse.currentUtilization}</div>
                <div class="text-xs text-gray-500">${utilizationPercentage}%</div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                    ${statusText}
                </span>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-center">
                <div class="flex justify-center space-x-1">
                    <button onclick="editWarehouse(${warehouse.id})"
                            class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                            title="تعديل">
                        ✏️
                    </button>
                    <button onclick="viewWarehouse(${warehouse.id})"
                            class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50"
                            title="عرض">
                        👁️
                    </button>
                    <button onclick="deleteWarehouse(${warehouse.id})"
                            class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                            title="حذف">
                        🗑️
                    </button>
                </div>
            </td>
        `;

        tbody.appendChild(row);
    });

    updateStatistics();
}

// تحديث الإحصائيات
function updateStatistics() {
    const totalWarehouses = warehouses.length;
    const activeWarehouses = warehouses.filter(w => w.isActive).length;
    const totalCapacity = warehouses.reduce((sum, w) => sum + w.capacity, 0);
    const totalUtilization = warehouses.reduce((sum, w) => sum + w.currentUtilization, 0);
    const utilizationPercentage = totalCapacity > 0 ? ((totalUtilization / totalCapacity) * 100).toFixed(1) : 0;

    document.getElementById('totalWarehouses').textContent = totalWarehouses;
    document.getElementById('activeWarehouses').textContent = activeWarehouses;
    document.getElementById('totalCapacity').textContent = totalCapacity;
    document.getElementById('utilizationPercentage').textContent = utilizationPercentage + '%';
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // البحث
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterWarehouses(this.value);
        });
    }

    // فلترة حسب الحالة
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            filterWarehouses();
        });
    }
}

// فلترة المخازن
function filterWarehouses(searchTerm = '') {
    const statusFilter = document.getElementById('statusFilter').value;
    const searchInput = document.getElementById('searchInput').value.toLowerCase();

    const filteredWarehouses = warehouses.filter(warehouse => {
        const matchesSearch = warehouse.name.toLowerCase().includes(searchInput) ||
                            warehouse.description.toLowerCase().includes(searchInput) ||
                            (warehouse.manager && warehouse.manager.toLowerCase().includes(searchInput));

        const matchesStatus = statusFilter === '' ||
                            (statusFilter === 'active' && warehouse.isActive) ||
                            (statusFilter === 'inactive' && !warehouse.isActive);

        return matchesSearch && matchesStatus;
    });

    displayFilteredWarehouses(filteredWarehouses);
}

// عرض المخازن المفلترة
function displayFilteredWarehouses(filteredWarehouses) {
    const tbody = document.getElementById('warehousesTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    filteredWarehouses.forEach(warehouse => {
        const utilizationPercentage = warehouse.capacity > 0 ?
            ((warehouse.currentUtilization / warehouse.capacity) * 100).toFixed(1) : 0;

        const statusClass = warehouse.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
        const statusText = warehouse.isActive ? 'نشط' : 'غير نشط';

        const utilizationClass = utilizationPercentage > 80 ? 'text-red-600' :
                                utilizationPercentage > 60 ? 'text-yellow-600' : 'text-green-600';

        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';

        row.innerHTML = `
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">${warehouse.name}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <div class="font-medium">${warehouse.description}</div>
                <div class="text-xs text-gray-500">${warehouse.location || 'غير محدد'}</div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <div class="font-medium">${warehouse.manager || 'غير محدد'}</div>
                <div class="text-xs text-gray-500">${warehouse.phone || ''}</div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">${warehouse.capacity}</td>
            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <div class="${utilizationClass} font-medium">${warehouse.currentUtilization}</div>
                <div class="text-xs text-gray-500">${utilizationPercentage}%</div>
            </td>
            <td class="px-4 py-3 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">
                    ${statusText}
                </span>
            </td>
            <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-center">
                <div class="flex justify-center space-x-1">
                    <button onclick="editWarehouse(${warehouse.id})"
                            class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                            title="تعديل">
                        ✏️
                    </button>
                    <button onclick="viewWarehouse(${warehouse.id})"
                            class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50"
                            title="عرض">
                        👁️
                    </button>
                    <button onclick="deleteWarehouse(${warehouse.id})"
                            class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                            title="حذف">
                        🗑️
                    </button>
                </div>
            </td>
        `;

        tbody.appendChild(row);
    });
}

// فتح نافذة إضافة مخزن جديد
function openAddWarehouseModal() {
    document.getElementById('warehouseModalTitle').textContent = 'إضافة مخزن جديد';
    document.getElementById('warehouseForm').reset();
    document.getElementById('warehouseId').value = '';
    document.getElementById('warehouseModal').classList.remove('hidden');
}

// إغلاق النافذة المنبثقة
function closeWarehouseModal() {
    document.getElementById('warehouseModal').classList.add('hidden');
}

// حفظ المخزن
function saveWarehouse() {
    const form = document.getElementById('warehouseForm');
    const formData = new FormData(form);

    const warehouseData = {
        name: formData.get('name'),
        description: formData.get('description'),
        location: formData.get('location'),
        manager: formData.get('manager'),
        phone: formData.get('phone'),
        capacity: parseFloat(formData.get('capacity')) || 0,
        currentUtilization: parseFloat(formData.get('currentUtilization')) || 0,
        isActive: formData.get('isActive') === 'on'
    };

    // التحقق من صحة البيانات
    if (!warehouseData.name.trim()) {
        showMessage('يرجى إدخال اسم المخزن', 'error');
        return;
    }

    const warehouseId = document.getElementById('warehouseId').value;

    if (warehouseId) {
        // تحديث مخزن موجود
        const index = warehouses.findIndex(w => w.id == warehouseId);
        if (index !== -1) {
            warehouses[index] = { ...warehouses[index], ...warehouseData };
            showMessage('تم تحديث المخزن بنجاح', 'success');
        }
    } else {
        // إضافة مخزن جديد
        const newWarehouse = {
            id: Date.now(),
            ...warehouseData,
            createdAt: new Date().toISOString().split('T')[0]
        };
        warehouses.push(newWarehouse);
        showMessage('تم إضافة المخزن بنجاح', 'success');
    }

    saveWarehouses();
    displayWarehouses();
    closeWarehouseModal();
}

// تعديل مخزن
function editWarehouse(id) {
    const warehouse = warehouses.find(w => w.id === id);
    if (!warehouse) return;

    document.getElementById('warehouseModalTitle').textContent = 'تعديل المخزن';
    document.getElementById('warehouseId').value = warehouse.id;
    document.getElementById('name').value = warehouse.name;
    document.getElementById('description').value = warehouse.description || '';
    document.getElementById('location').value = warehouse.location || '';
    document.getElementById('manager').value = warehouse.manager || '';
    document.getElementById('phone').value = warehouse.phone || '';
    document.getElementById('capacity').value = warehouse.capacity || 0;
    document.getElementById('currentUtilization').value = warehouse.currentUtilization || 0;
    document.getElementById('isActive').checked = warehouse.isActive;

    document.getElementById('warehouseModal').classList.remove('hidden');
}

// عرض تفاصيل المخزن
function viewWarehouse(id) {
    const warehouse = warehouses.find(w => w.id === id);
    if (!warehouse) return;

    const utilizationPercentage = warehouse.capacity > 0 ?
        ((warehouse.currentUtilization / warehouse.capacity) * 100).toFixed(1) : 0;

    const modalContent = `
        <div class="bg-white rounded-lg shadow-lg max-w-2xl mx-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">تفاصيل المخزن</h3>
            </div>
            <div class="px-6 py-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">اسم المخزن</label>
                        <div class="text-lg font-semibold text-gray-900">${warehouse.name}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">الحالة</label>
                        <span class="px-2 py-1 text-xs font-medium rounded-full ${warehouse.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${warehouse.isActive ? 'نشط' : 'غير نشط'}
                        </span>
                    </div>
                    <div class="col-span-2">
                        <label class="block text-sm font-medium text-gray-700">الوصف</label>
                        <div class="text-gray-900">${warehouse.description || 'غير محدد'}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">الموقع</label>
                        <div class="text-gray-900">${warehouse.location || 'غير محدد'}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">المسؤول</label>
                        <div class="text-gray-900">${warehouse.manager || 'غير محدد'}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">الهاتف</label>
                        <div class="text-gray-900">${warehouse.phone || 'غير محدد'}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">السعة الإجمالية</label>
                        <div class="text-lg font-semibold text-gray-900">${warehouse.capacity}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">الاستخدام الحالي</label>
                        <div class="text-lg font-semibold text-gray-900">${warehouse.currentUtilization}</div>
                    </div>
                    <div class="col-span-2">
                        <label class="block text-sm font-medium text-gray-700">نسبة الاستخدام</label>
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-blue-600 h-2.5 rounded-full" style="width: ${utilizationPercentage}%"></div>
                        </div>
                        <div class="text-sm text-gray-600 mt-1">${utilizationPercentage}%</div>
                    </div>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end">
                <button onclick="closeViewModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                    إغلاق
                </button>
            </div>
        </div>
    `;

    showModal(modalContent);
}

// حذف مخزن
function deleteWarehouse(id) {
    const warehouse = warehouses.find(w => w.id === id);
    if (!warehouse) return;

    if (confirm(`هل أنت متأكد من حذف المخزن "${warehouse.name}"؟`)) {
        warehouses = warehouses.filter(w => w.id !== id);
        saveWarehouses();
        displayWarehouses();
        showMessage('تم حذف المخزن بنجاح', 'success');
    }
}

// عرض رسالة
function showMessage(message, type = 'info') {
    const alertClass = type === 'success' ? 'bg-green-100 border-green-400 text-green-700' :
                      type === 'error' ? 'bg-red-100 border-red-400 text-red-700' :
                      'bg-blue-100 border-blue-400 text-blue-700';

    const messageDiv = document.createElement('div');
    messageDiv.className = `fixed top-4 right-4 p-4 border rounded-md ${alertClass} z-50`;
    messageDiv.textContent = message;

    document.body.appendChild(messageDiv);

    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

// عرض نافذة منبثقة مخصصة
function showModal(content) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
    modal.innerHTML = `
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            ${content}
        </div>
    `;

    document.body.appendChild(modal);

    // إغلاق عند النقر خارج النافذة
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });

    window.closeViewModal = function() {
        modal.remove();
    };
}
