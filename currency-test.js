// اختبار نظام العملة المعاد بناؤه
(function() {
    'use strict';
    
    // وظيفة اختبار تغيير العملة
    function testCurrencyChange() {
        console.log('🧪 Testing currency change system...');
        
        // الحصول على البيانات الحالية
        const currentData = JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
        console.log('Current company data:', currentData);
        
        // اختبار تغيير العملة للريال اليمني
        const testData = {
            ...currentData,
            currency: 'YER',
            currencySymbol: 'ر.ي',
            updatedAt: new Date().toISOString()
        };
        
        console.log('Setting test currency data:', testData);
        localStorage.setItem('anwar_bakery_company', JSON.stringify(testData));
        
        // إرسال حدث التحديث
        window.dispatchEvent(new CustomEvent('companyDataUpdated', { detail: testData }));
        
        console.log('✅ Currency changed to YER');
        
        // العودة للعملة الأصلية بعد 5 ثوان
        setTimeout(() => {
            const revertData = {
                ...testData,
                currency: 'SAR',
                currencySymbol: 'ر.س',
                updatedAt: new Date().toISOString()
            };
            
            console.log('Reverting to SAR...');
            localStorage.setItem('anwar_bakery_company', JSON.stringify(revertData));
            window.dispatchEvent(new CustomEvent('companyDataUpdated', { detail: revertData }));
            
            console.log('✅ Currency reverted to SAR');
        }, 5000);
    }
    
    // وظيفة فحص حالة العملة
    function checkCurrencyStatus() {
        console.log('🔍 Checking currency status...');
        
        const companyData = JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
        console.log('Company currency:', companyData.currency);
        console.log('Company currency symbol:', companyData.currencySymbol);
        
        // اختبار وظيفة getCurrencySymbol
        if (typeof getCurrencySymbol === 'function') {
            const symbol = getCurrencySymbol();
            console.log('getCurrencySymbol() returns:', symbol);
        }
        
        // اختبار وظيفة formatCurrencyAmount
        if (typeof formatCurrencyAmount === 'function') {
            const formatted = formatCurrencyAmount(50000);
            console.log('formatCurrencyAmount(50000) returns:', formatted);
        }
        
        // فحص عنصر إجمالي الرواتب
        const totalSalariesElement = document.getElementById('totalSalaries');
        if (totalSalariesElement) {
            console.log('Total salaries element content:', totalSalariesElement.textContent);
        }
    }
    
    // إضافة أزرار الاختبار
    function addTestButtons() {
        const testContainer = document.createElement('div');
        testContainer.className = 'fixed bottom-4 right-4 space-y-2 z-50';
        testContainer.innerHTML = `
            <button onclick="testCurrencyChange()" 
                    class="block w-full bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-blue-700 text-sm">
                🧪 اختبار تغيير العملة
            </button>
            <button onclick="checkCurrencyStatus()" 
                    class="block w-full bg-green-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-green-700 text-sm">
                🔍 فحص حالة العملة
            </button>
            <button onclick="refreshCurrencyDisplay()" 
                    class="block w-full bg-orange-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-orange-700 text-sm">
                🔄 تحديث العرض
            </button>
        `;
        
        document.body.appendChild(testContainer);
        console.log('✅ Test buttons added');
    }
    
    // إضافة الوظائف للنطاق العام
    window.testCurrencyChange = testCurrencyChange;
    window.checkCurrencyStatus = checkCurrencyStatus;
    
    // إضافة الأزرار عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(() => {
            addTestButtons();
            checkCurrencyStatus(); // فحص أولي
        }, 2000);
    });
    
    console.log('✅ Currency Test System loaded');
    
})();
