// تحديث العملة في جميع أنحاء التطبيق
class GlobalCurrencyUpdater {
    constructor() {
        this.init();
    }

    init() {
        // تطبيق العملة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            this.updateAllCurrencies();
        });

        // الاستماع لتغييرات العملة
        window.addEventListener('currencyChanged', () => {
            this.updateAllCurrencies();
        });

        // الاستماع لتحديث بيانات الشركة
        window.addEventListener('companyDataUpdated', () => {
            this.updateAllCurrencies();
        });
    }

    // تحديث جميع العملات في الصفحة
    updateAllCurrencies() {
        console.log('🔄 Updating all currencies in the page...');
        
        // تحديث العناصر المختلفة
        this.updateCurrencySymbols();
        this.updateAmountDisplays();
        this.updateFormFields();
        this.updateTables();
        this.updateStatistics();
        this.updateCalculations();
        
        console.log('✅ All currencies updated successfully');
    }

    // تحديث رموز العملة
    updateCurrencySymbols() {
        const currencySymbol = this.getCurrentCurrencySymbol();
        
        // تحديث عناصر رمز العملة
        document.querySelectorAll('.currency-symbol, [data-currency-symbol]').forEach(el => {
            el.textContent = currencySymbol;
        });
    }

    // تحديث عرض المبالغ
    updateAmountDisplays() {
        // تحديث المبالغ مع العملة
        document.querySelectorAll('[data-amount]').forEach(el => {
            const amount = parseFloat(el.dataset.amount) || 0;
            el.textContent = this.formatCurrency(amount);
        });

        // تحديث عناصر الأرصدة
        document.querySelectorAll('.balance-display, .amount-display').forEach(el => {
            const amount = parseFloat(el.dataset.amount || el.textContent.replace(/[^\d.-]/g, '')) || 0;
            if (amount) {
                el.textContent = this.formatCurrency(amount);
            }
        });
    }

    // تحديث حقول النماذج
    updateFormFields() {
        const currencySymbol = this.getCurrentCurrencySymbol();
        
        // تحديث خيارات العملة في النماذج
        document.querySelectorAll('select[name="currency"], #currency, #baseCurrency').forEach(select => {
            const currentCurrency = this.getCurrentCurrency();
            if (select.value !== currentCurrency) {
                select.value = currentCurrency;
            }
        });

        // تحديث رموز العملة في الحقول
        document.querySelectorAll('.currency-input-symbol').forEach(el => {
            el.textContent = currencySymbol;
        });
    }

    // تحديث الجداول
    updateTables() {
        // تحديث خلايا الجداول التي تحتوي على مبالغ
        document.querySelectorAll('td[data-currency], th[data-currency]').forEach(cell => {
            const amount = parseFloat(cell.dataset.currency) || 0;
            cell.textContent = this.formatCurrency(amount);
        });

        // تحديث أعمدة الأرصدة في الجداول
        document.querySelectorAll('.balance-cell, .amount-cell').forEach(cell => {
            const amount = parseFloat(cell.dataset.amount || cell.textContent.replace(/[^\d.-]/g, '')) || 0;
            if (amount) {
                cell.textContent = this.formatCurrency(amount);
            }
        });
    }

    // تحديث الإحصائيات
    updateStatistics() {
        // تحديث بطاقات الإحصائيات
        document.querySelectorAll('.stat-amount, .total-amount').forEach(el => {
            const amount = parseFloat(el.dataset.amount || el.textContent.replace(/[^\d.-]/g, '')) || 0;
            if (amount) {
                el.textContent = this.formatCurrency(amount);
            }
        });

        // إعادة حساب الإحصائيات إذا كانت الوظائف متاحة
        if (typeof updateStats === 'function') {
            updateStats();
        }
        if (typeof updateRegisterStats === 'function') {
            updateRegisterStats();
        }
    }

    // تحديث الحسابات
    updateCalculations() {
        // إعادة حساب المجاميع
        if (typeof calculateTotal === 'function') {
            calculateTotal();
        }
        if (typeof updateTotal === 'function') {
            updateTotal();
        }
        if (typeof recalculateAmounts === 'function') {
            recalculateAmounts();
        }
    }

    // الحصول على العملة الحالية
    getCurrentCurrency() {
        if (window.currencyManager) {
            return window.currencyManager.getCurrentCurrency();
        }
        
        // Fallback
        const companyData = this.getCompanyData();
        return companyData.currency || 'SAR';
    }

    // الحصول على رمز العملة الحالية
    getCurrentCurrencySymbol() {
        if (window.currencyManager) {
            return window.currencyManager.getCurrentCurrencySymbol();
        }
        
        // Fallback
        const companyData = this.getCompanyData();
        return companyData.currencySymbol || companyData.currency || 'ر.س';
    }

    // تنسيق العملة
    formatCurrency(amount) {
        if (window.currencyManager) {
            return window.currencyManager.formatCurrency(amount);
        }
        
        // Fallback
        const symbol = this.getCurrentCurrencySymbol();
        return `${parseFloat(amount).toFixed(2)} ${symbol}`;
    }

    // الحصول على بيانات الشركة
    getCompanyData() {
        try {
            const data = localStorage.getItem('anwar_bakery_company');
            return data ? JSON.parse(data) : {};
        } catch (error) {
            console.error('Error parsing company data:', error);
            return {};
        }
    }

    // تحديث عنصر محدد بالعملة
    updateElement(element, amount) {
        if (element && amount !== undefined) {
            element.textContent = this.formatCurrency(amount);
            element.dataset.amount = amount;
        }
    }

    // تحديث مجموعة من العناصر
    updateElements(selector, amounts) {
        const elements = document.querySelectorAll(selector);
        elements.forEach((el, index) => {
            if (amounts[index] !== undefined) {
                this.updateElement(el, amounts[index]);
            }
        });
    }

    // إضافة مستمع لتحديث العملة
    addCurrencyUpdateListener(callback) {
        window.addEventListener('currencyChanged', callback);
        window.addEventListener('companyDataUpdated', callback);
    }

    // إزالة مستمع تحديث العملة
    removeCurrencyUpdateListener(callback) {
        window.removeEventListener('currencyChanged', callback);
        window.removeEventListener('companyDataUpdated', callback);
    }
}

// إنشاء مثيل عام لمحدث العملة
window.globalCurrencyUpdater = new GlobalCurrencyUpdater();

// وظائف مساعدة عامة
window.updateAllCurrencies = () => window.globalCurrencyUpdater.updateAllCurrencies();
window.updateCurrencyElement = (element, amount) => window.globalCurrencyUpdater.updateElement(element, amount);
window.addCurrencyListener = (callback) => window.globalCurrencyUpdater.addCurrencyUpdateListener(callback);

// تحديث العملة عند تغيير إعدادات الشركة
if (typeof window.addEventListener === 'function') {
    window.addEventListener('storage', (event) => {
        if (event.key === 'anwar_bakery_company') {
            setTimeout(() => {
                window.globalCurrencyUpdater.updateAllCurrencies();
            }, 100);
        }
    });
}

console.log('✅ Global Currency Updater loaded successfully');
