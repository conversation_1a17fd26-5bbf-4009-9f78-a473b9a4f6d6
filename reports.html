<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - مخبز أنور</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .report-card { transition: transform 0.2s ease-in-out; }
        .report-card:hover { transform: translateY(-2px); }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Mobile menu overlay -->
    <div id="mobileOverlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 lg:hidden hidden"></div>

    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform translate-x-full lg:translate-x-0 lg:static lg:inset-0 sidebar-transition">
            <div class="h-16 px-4 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between">
                <h1 id="sidebarCompanyName" class="text-white text-lg font-bold">مخبز أنور</h1>
                <button onclick="toggleSidebar()" class="lg:hidden text-white">
                    <span class="text-xl">✕</span>
                </button>
            </div>

            <nav class="mt-5 px-2 space-y-1 h-full overflow-y-auto pb-20">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    لوحة التحكم
                </a>
                <a href="users-management.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👥</span>
                    إدارة المستخدمين
                </a>
                <a href="company-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏢</span>
                    بيانات المنشأة
                </a>
                <a href="branches-warehouses.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏪</span>
                    الفروع والمخازن
                </a>
                <a href="units.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📏</span>
                    وحدات القياس
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة المنتجات
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🚚</span>
                    الموردين
                </a>
                <a href="chart-of-accounts.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📊</span>
                    دليل الحسابات
                </a>
                <a href="journal-entry.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📝</span>
                    القيود اليومية
                </a>
                <a href="vouchers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    السندات
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="banks.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏦</span>
                    البنوك
                </a>
                <a href="owners.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👑</span>
                    الملاك
                </a>
                <a href="inventory.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📊</span>
                    المخزون
                </a>
                <a href="reports.html" class="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">
                    <span class="ml-3">📈</span>
                    التقارير
                </a>
                <a href="system-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">⚙️</span>
                    إعدادات النظام
                </a>
                <a href="system-admin.html" class="flex items-center px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 hover:text-red-700 rounded-md border border-red-200">
                    <span class="ml-3">🔧</span>
                    إدارة النظام المتقدمة
                </a>

                <!-- User Info & Logout -->
                <div class="absolute bottom-4 left-2 right-2">
                    <div class="bg-gray-50 rounded-lg p-3 mb-2">
                        <p id="userFullName" class="text-sm font-medium text-gray-900">مدير النظام</p>
                        <p id="userRole" class="text-xs text-gray-500">admin</p>
                        <p id="loginTime" class="text-xs text-gray-400"></p>
                    </div>
                    <button onclick="logout()" class="w-full text-right px-3 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50 flex items-center">
                        <span class="ml-3">🚪</span>
                        تسجيل الخروج
                    </button>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 overflow-hidden">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <button onclick="toggleSidebar()" class="lg:hidden text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">☰</span>
                            </button>
                            <span class="text-lg font-semibold text-gray-900">التقارير</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-500" id="currentDateTime"></span>
                            <div class="flex items-center space-x-2">
                                <span id="userDisplayName" class="text-sm font-medium text-gray-700">مدير النظام</span>
                                <button onclick="logout()" class="text-red-600 hover:text-red-800 text-sm">
                                    خروج
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Header -->
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900">التقارير</h1>
                    <p class="text-gray-600 mt-1">تقارير شاملة لجميع أنشطة المخبز</p>
                </div>

                <!-- Message Container -->
                <div id="messageContainer" class="mb-4"></div>

                <!-- Financial Reports -->
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <span class="ml-2">💰</span>
                        التقارير المالية
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="bg-white rounded-lg shadow-lg p-6 report-card cursor-pointer" onclick="generateReport('profit-loss')">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-green-100 rounded-lg">
                                    <span class="text-green-600 text-xl">📊</span>
                                </div>
                                <h3 class="mr-3 text-lg font-medium text-gray-900">قائمة الدخل</h3>
                            </div>
                            <p class="text-gray-600 text-sm">تقرير الأرباح والخسائر للفترة المحددة</p>
                        </div>

                        <div class="bg-white rounded-lg shadow-lg p-6 report-card cursor-pointer" onclick="generateReport('balance-sheet')">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-blue-100 rounded-lg">
                                    <span class="text-blue-600 text-xl">⚖️</span>
                                </div>
                                <h3 class="mr-3 text-lg font-medium text-gray-900">الميزانية العمومية</h3>
                            </div>
                            <p class="text-gray-600 text-sm">تقرير الأصول والخصوم وحقوق الملكية</p>
                        </div>

                        <div class="bg-white rounded-lg shadow-lg p-6 report-card cursor-pointer" onclick="generateReport('cash-flow')">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-purple-100 rounded-lg">
                                    <span class="text-purple-600 text-xl">💸</span>
                                </div>
                                <h3 class="mr-3 text-lg font-medium text-gray-900">التدفق النقدي</h3>
                            </div>
                            <p class="text-gray-600 text-sm">تقرير حركة النقد الداخل والخارج</p>
                        </div>
                    </div>
                </div>

                <!-- Sales Reports -->
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <span class="ml-2">🛒</span>
                        تقارير المبيعات
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="bg-white rounded-lg shadow-lg p-6 report-card cursor-pointer" onclick="generateReport('sales-summary')">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-orange-100 rounded-lg">
                                    <span class="text-orange-600 text-xl">📈</span>
                                </div>
                                <h3 class="mr-3 text-lg font-medium text-gray-900">ملخص المبيعات</h3>
                            </div>
                            <p class="text-gray-600 text-sm">تقرير إجمالي المبيعات حسب الفترة</p>
                        </div>

                        <div class="bg-white rounded-lg shadow-lg p-6 report-card cursor-pointer" onclick="generateReport('top-products')">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-yellow-100 rounded-lg">
                                    <span class="text-yellow-600 text-xl">🏆</span>
                                </div>
                                <h3 class="mr-3 text-lg font-medium text-gray-900">أكثر المنتجات مبيعاً</h3>
                            </div>
                            <p class="text-gray-600 text-sm">تقرير المنتجات الأكثر مبيعاً</p>
                        </div>

                        <div class="bg-white rounded-lg shadow-lg p-6 report-card cursor-pointer" onclick="generateReport('customer-sales')">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-pink-100 rounded-lg">
                                    <span class="text-pink-600 text-xl">👥</span>
                                </div>
                                <h3 class="mr-3 text-lg font-medium text-gray-900">مبيعات العملاء</h3>
                            </div>
                            <p class="text-gray-600 text-sm">تقرير مبيعات كل عميل</p>
                        </div>
                    </div>
                </div>

                <!-- Inventory Reports -->
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <span class="ml-2">📦</span>
                        تقارير المخزون
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="bg-white rounded-lg shadow-lg p-6 report-card cursor-pointer" onclick="generateReport('inventory-status')">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-indigo-100 rounded-lg">
                                    <span class="text-indigo-600 text-xl">📊</span>
                                </div>
                                <h3 class="mr-3 text-lg font-medium text-gray-900">حالة المخزون</h3>
                            </div>
                            <p class="text-gray-600 text-sm">تقرير الكميات المتوفرة والناقصة</p>
                        </div>

                        <div class="bg-white rounded-lg shadow-lg p-6 report-card cursor-pointer" onclick="generateReport('inventory-valuation')">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-teal-100 rounded-lg">
                                    <span class="text-teal-600 text-xl">💎</span>
                                </div>
                                <h3 class="mr-3 text-lg font-medium text-gray-900">تقييم المخزون</h3>
                            </div>
                            <p class="text-gray-600 text-sm">تقرير قيمة المخزون بالتكلفة</p>
                        </div>

                        <div class="bg-white rounded-lg shadow-lg p-6 report-card cursor-pointer" onclick="generateReport('stock-movement')">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-red-100 rounded-lg">
                                    <span class="text-red-600 text-xl">🔄</span>
                                </div>
                                <h3 class="mr-3 text-lg font-medium text-gray-900">حركة المخزون</h3>
                            </div>
                            <p class="text-gray-600 text-sm">تقرير حركة دخول وخروج المخزون</p>
                        </div>
                    </div>
                </div>

                <!-- Accounting Reports -->
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <span class="ml-2">📝</span>
                        التقارير المحاسبية
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="bg-white rounded-lg shadow-lg p-6 report-card cursor-pointer" onclick="generateReport('trial-balance')">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-gray-100 rounded-lg">
                                    <span class="text-gray-600 text-xl">⚖️</span>
                                </div>
                                <h3 class="mr-3 text-lg font-medium text-gray-900">ميزان المراجعة</h3>
                            </div>
                            <p class="text-gray-600 text-sm">تقرير أرصدة جميع الحسابات</p>
                        </div>

                        <div class="bg-white rounded-lg shadow-lg p-6 report-card cursor-pointer" onclick="generateReport('journal-entries')">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-cyan-100 rounded-lg">
                                    <span class="text-cyan-600 text-xl">📋</span>
                                </div>
                                <h3 class="mr-3 text-lg font-medium text-gray-900">دفتر اليومية</h3>
                            </div>
                            <p class="text-gray-600 text-sm">تقرير جميع القيود المحاسبية</p>
                        </div>

                        <div class="bg-white rounded-lg shadow-lg p-6 report-card cursor-pointer" onclick="generateReport('account-statement')">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-lime-100 rounded-lg">
                                    <span class="text-lime-600 text-xl">📄</span>
                                </div>
                                <h3 class="mr-3 text-lg font-medium text-gray-900">كشف حساب</h3>
                            </div>
                            <p class="text-gray-600 text-sm">تقرير حركة حساب معين</p>
                        </div>

                        <div class="bg-white rounded-lg shadow-lg p-6 report-card cursor-pointer" onclick="generateReport('cash-transfers')">
                            <div class="flex items-center mb-4">
                                <div class="p-2 bg-purple-100 rounded-lg">
                                    <span class="text-purple-600 text-xl">🔄</span>
                                </div>
                                <h3 class="mr-3 text-lg font-medium text-gray-900">تحويل المبالغ</h3>
                            </div>
                            <p class="text-gray-600 text-sm">تقرير تحويل المبالغ بين الصناديق</p>
                        </div>
                    </div>
                </div>

                <!-- Custom Reports -->
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <span class="ml-2">🎯</span>
                        تقارير مخصصة
                    </h2>
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">من تاريخ</label>
                                <input type="date" id="fromDate" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">إلى تاريخ</label>
                                <input type="date" id="toDate" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">الفرع</label>
                                <select id="branchFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">جميع الفروع</option>
                                </select>
                            </div>
                        </div>
                        <div class="flex justify-center space-x-3">
                            <button onclick="exportAllReportsToExcel()" class="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 flex items-center">
                                <span class="ml-2">📊</span>
                                تصدير جميع التقارير
                            </button>
                            <button onclick="printAllReports()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 flex items-center">
                                <span class="ml-2">🖨️</span>
                                طباعة التقارير
                            </button>
                            <button onclick="generateCustomReport()" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 flex items-center">
                                <span class="ml-2">📊</span>
                                إنشاء تقرير مخصص
                            </button>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Include Excel utilities -->
    <script src="excel-utils.js"></script>
    <script src="advanced-excel.js"></script>
    <script src="advanced-print.js"></script>
    <script src="global-advanced-features.js"></script>
    <script src="activate-advanced-features.js"></script>
    <!-- Include system diagnostics -->
    <script src="system-diagnostics.js"></script>
    <!-- Main reports script -->
    <script src="reports.js"></script>
</body>
</html>
