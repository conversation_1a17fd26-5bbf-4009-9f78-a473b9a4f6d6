<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة مبيعات - مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .custom-scroll {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 #f7fafc;
        }

        .custom-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scroll::-webkit-scrollbar-track {
            background: #f7fafc;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        .invoice-item-row:hover {
            background-color: #f8fafc;
        }

        .required-field {
            border-color: #ef4444;
        }

        .customer-toggle {
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <div class="bg-white w-64 shadow-lg flex flex-col">
            <div class="p-4 flex-shrink-0">
                <div class="flex items-center mb-8">
                    <span class="text-2xl ml-3">🍞</span>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800" id="companyName">مخبز أنوار الحي</h1>
                        <p class="text-sm text-gray-600" id="companySlogan">جودة تستحق الثقة</p>
                    </div>
                </div>
            </div>

            <nav class="flex-1 overflow-y-auto px-4 pb-4 custom-scroll">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    الرئيسية
                </a>
                <a href="employees.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👨‍💼</span>
                    إدارة الموظفين
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏭</span>
                    الموردين
                </a>

                <!-- User Info & Logout -->
                <div class="mt-auto p-4 border-t border-gray-200">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                            أ
                        </div>
                        <div class="mr-3 flex-1">
                            <p id="userFullName" class="text-sm font-medium text-gray-900">أحمد محمد</p>
                            <p id="userRole" class="text-xs text-gray-500">مدير النظام</p>
                        </div>
                        <button onclick="logout()" class="text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50" title="تسجيل الخروج">
                            <span class="text-lg">🚪</span>
                        </button>
                    </div>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col min-h-0">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <a href="invoices.html" class="text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">←</span>
                            </a>
                            <h2 class="text-xl font-semibold text-gray-900">فاتورة مبيعات جديدة</h2>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="window.history.back()" class="text-gray-600 hover:text-gray-800 px-2 py-2 rounded text-sm">
                                ← رجوع
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6 custom-scroll">
                <!-- Success/Error Messages -->
                <div id="messageContainer" class="mb-6"></div>

                <!-- Invoice Form -->
                <div class="bg-white rounded-lg shadow-sm">
                    <!-- Invoice Header - Compact -->
                    <div class="p-4 border-b border-gray-200">
                        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                            <!-- Invoice Number -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">رقم الفاتورة</label>
                                <input type="text" id="invoiceNumber" readonly
                                       class="w-full px-2 py-1.5 border border-gray-300 rounded bg-gray-50 text-sm"
                                       value="SAL-2024-001">
                            </div>

                            <!-- Invoice Date -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">التاريخ *</label>
                                <input type="date" id="invoiceDate" required
                                       class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 text-sm">
                            </div>

                            <!-- Branch -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">الفرع *</label>
                                <select id="branchId" required onchange="loadBranchData()"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 text-sm">
                                    <option value="">اختر الفرع</option>
                                </select>
                            </div>

                            <!-- Buyer Type Toggle -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">نوع المشتري</label>
                                <div class="flex space-x-2">
                                    <label class="flex items-center text-xs">
                                        <input type="radio" name="buyerType" value="customer" checked onchange="toggleBuyerType()"
                                               class="ml-1 text-green-600 focus:ring-green-500 scale-75">
                                        عميل
                                    </label>
                                    <label class="flex items-center text-xs">
                                        <input type="radio" name="buyerType" value="supplier" onchange="toggleBuyerType()"
                                               class="ml-1 text-green-600 focus:ring-green-500 scale-75">
                                        مورد
                                    </label>
                                </div>
                            </div>

                            <!-- Customer/Supplier Selection -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1" id="buyerLabel">العميل *</label>
                                <select id="buyerId" required onchange="loadBuyerData()"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 text-sm">
                                    <option value="">اختر العميل</option>
                                </select>
                            </div>

                            <!-- Cash Register -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">الصندوق *</label>
                                <select id="cashRegisterId" required
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 text-sm">
                                    <option value="">اختر الصندوق</option>
                                </select>
                            </div>
                        </div>

                        <!-- Second Row - Optional Fields -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-3 mt-3">
                            <!-- Warehouse -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">المخزن</label>
                                <select id="warehouseId"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 text-sm">
                                    <option value="">اختر المخزن</option>
                                </select>
                            </div>

                            <!-- Reference Number -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">رقم المرجع</label>
                                <input type="text" id="referenceNumber" placeholder="رقم أمر البيع أو المرجع"
                                       class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 text-sm">
                            </div>

                            <!-- Payment Method -->
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">طريقة الدفع</label>
                                <select id="paymentMethod" onchange="toggleBankField()"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 text-sm">
                                    <option value="cash">نقدي</option>
                                    <option value="credit">آجل</option>
                                    <option value="bank">تحويل بنكي</option>
                                    <option value="card">بطاقة ائتمان</option>
                                </select>
                            </div>

                            <!-- Bank Selection (Hidden by default) -->
                            <div id="bankField" style="display: none;">
                                <label class="block text-xs font-medium text-gray-600 mb-1">البنك *</label>
                                <select id="bankId"
                                        class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 text-sm">
                                    <option value="">اختر البنك</option>
                                    <option value="1">البنك الأهلي السعودي</option>
                                    <option value="2">بنك الراجحي</option>
                                    <option value="3">بنك الرياض</option>
                                    <option value="4">البنك السعودي للاستثمار</option>
                                    <option value="5">البنك السعودي الفرنسي</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Items Section - Compact -->
                    <div class="p-4">
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="text-base font-semibold text-gray-900">أصناف الفاتورة</h3>
                            <div class="flex space-x-2">
                                <button onclick="addInvoiceItem()" class="bg-green-600 text-white px-3 py-1.5 rounded hover:bg-green-700 flex items-center text-sm">
                                    <span class="ml-1">➕</span>
                                    إضافة صنف
                                </button>
                            </div>
                        </div>

                        <!-- Smart Item Search Row -->
                        <div id="addItemRow" class="mb-3 p-4 bg-green-50 rounded border border-green-200" style="display: none;">
                            <!-- Search Section -->
                            <div class="mb-3">
                                <label class="block text-sm font-medium text-gray-700 mb-2">🔍 البحث الذكي عن المنتجات</label>
                                <div class="relative">
                                    <input type="text" id="itemSearchInput" placeholder="ابحث بالاسم، الكود، الباركود، أو أي جزء من المنتج..."
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 text-sm"
                                           oninput="searchItems()" onkeydown="handleSearchKeydown(event)">
                                    <div class="absolute left-3 top-2.5 text-gray-400">
                                        <span class="text-lg">🔍</span>
                                    </div>
                                </div>

                                <!-- Search Results Dropdown -->
                                <div id="searchResults" class="absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg mt-1 max-h-60 overflow-y-auto" style="display: none;">
                                    <!-- Search results will be populated here -->
                                </div>
                            </div>

                            <!-- Selected Item Details -->
                            <div id="selectedItemDetails" class="mb-3 p-3 bg-white rounded border" style="display: none;">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
                                    <div>
                                        <label class="block text-xs font-medium text-gray-600 mb-1">المنتج المحدد</label>
                                        <div class="flex items-center">
                                            <span id="selectedItemIcon" class="text-lg ml-2">🍞</span>
                                            <div>
                                                <div id="selectedItemName" class="font-medium text-gray-900"></div>
                                                <div id="selectedItemCode" class="text-xs text-gray-500"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-xs font-medium text-gray-600 mb-1">المخزون المتاح</label>
                                        <div id="availableStock" class="text-sm font-medium text-green-600">0</div>
                                    </div>
                                    <div>
                                        <label class="block text-xs font-medium text-gray-600 mb-1">سعر البيع</label>
                                        <div id="salePrice" class="text-sm font-medium text-green-600">0</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Item Entry Form -->
                            <div class="grid grid-cols-2 md:grid-cols-6 gap-2">
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-1">الكمية *</label>
                                    <input type="number" id="newItemQuantity" placeholder="الكمية" min="0" step="0.01"
                                           class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 text-sm"
                                           onchange="calculateItemTotal()" onkeyup="calculateItemTotal()">
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-1">الوحدة</label>
                                    <select id="newItemUnit" onchange="updateUnitPrice()"
                                            class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 text-sm">
                                        <option value="">اختر الوحدة</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-1">سعر الوحدة *</label>
                                    <input type="number" id="newItemPrice" placeholder="السعر" min="0" step="0.01"
                                           class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 text-sm"
                                           onchange="calculateItemTotal()" onkeyup="calculateItemTotal()">
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-1">الخصم</label>
                                    <input type="number" id="newItemDiscount" placeholder="0" min="0" step="0.01"
                                           class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 text-sm"
                                           onchange="calculateItemTotal()" onkeyup="calculateItemTotal()">
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-600 mb-1">الإجمالي</label>
                                    <input type="number" id="newItemTotal" readonly placeholder="الإجمالي"
                                           class="w-full px-2 py-1.5 border border-gray-300 rounded bg-gray-100 text-sm font-medium">
                                </div>
                                <div class="flex flex-col space-y-1">
                                    <button onclick="confirmAddItem()" class="bg-green-600 text-white px-2 py-1.5 rounded hover:bg-green-700 text-xs">
                                        ✓ إضافة
                                    </button>
                                    <button onclick="cancelAddItem()" class="bg-gray-600 text-white px-2 py-1.5 rounded hover:bg-gray-700 text-xs">
                                        ✕ إلغاء
                                    </button>
                                </div>
                            </div>

                            <!-- Quick Add Buttons -->
                            <div class="mt-3 flex flex-wrap gap-2">
                                <button onclick="addQuickItem('finished_product')" class="bg-green-100 text-green-800 px-3 py-1 rounded text-xs hover:bg-green-200">
                                    🍞 منتجات جاهزة
                                </button>
                                <button onclick="addQuickItem('bread')" class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded text-xs hover:bg-yellow-200">
                                    🍞 خبز
                                </button>
                                <button onclick="addQuickItem('pastry')" class="bg-orange-100 text-orange-800 px-3 py-1 rounded text-xs hover:bg-orange-200">
                                    🥐 معجنات
                                </button>
                                <button onclick="addQuickItem('cake')" class="bg-pink-100 text-pink-800 px-3 py-1 rounded text-xs hover:bg-pink-200">
                                    🎂 كيك وحلويات
                                </button>
                                <button onclick="addQuickItem('drinks')" class="bg-blue-100 text-blue-800 px-3 py-1 rounded text-xs hover:bg-blue-200">
                                    🥤 مشروبات
                                </button>
                            </div>
                        </div>

                        <!-- Items Table - Compact -->
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">المنتج</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">الكود</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">الكمية</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">الوحدة</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">السعر</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">الخصم</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">الإجمالي</th>
                                        <th class="px-2 py-2 text-right text-xs font-medium text-gray-500">المخزن</th>
                                        <th class="px-2 py-2 text-center text-xs font-medium text-gray-500">إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="invoiceItemsTable" class="bg-white divide-y divide-gray-200">
                                    <!-- Items will be added here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Invoice Totals - Compact -->
                    <div class="p-4 bg-gray-50 border-t border-gray-200">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                            <!-- Notes -->
                            <div class="lg:col-span-2">
                                <label class="block text-xs font-medium text-gray-600 mb-1">ملاحظات</label>
                                <textarea id="invoiceNotes" rows="3"
                                          class="w-full px-2 py-1.5 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 text-sm"
                                          placeholder="ملاحظات إضافية..."></textarea>
                            </div>

                            <!-- Totals - Compact -->
                            <div class="bg-white p-3 rounded border">
                                <div class="space-y-2">
                                    <div class="flex justify-between items-center text-sm">
                                        <span class="text-gray-600">المجموع الفرعي:</span>
                                        <span id="subtotalAmount" class="font-semibold text-gray-900">0</span>
                                    </div>
                                    <div class="flex justify-between items-center text-sm">
                                        <span class="text-gray-600">الضريبة (15%):</span>
                                        <span id="taxAmount" class="font-semibold text-gray-900">0</span>
                                    </div>
                                    <div class="flex justify-between items-center pt-2 border-t border-gray-200">
                                        <span class="font-bold text-gray-900">الإجمالي النهائي:</span>
                                        <span id="totalAmount" class="text-lg font-bold text-green-600">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons - Compact -->
                        <div class="flex justify-between items-center mt-4 pt-3 border-t border-gray-200">
                            <!-- Voucher Actions -->
                            <div class="flex space-x-2">
                                <button onclick="createReceiptVoucher()" class="bg-green-600 text-white px-3 py-2 rounded hover:bg-green-700 text-sm flex items-center transition-all duration-200 hover:shadow-lg">
                                    <span class="ml-1">💰</span>
                                    إنشاء سند قبض
                                </button>
                                <button onclick="viewVouchers()" class="bg-purple-600 text-white px-3 py-2 rounded hover:bg-purple-700 text-sm flex items-center transition-all duration-200 hover:shadow-lg">
                                    <span class="ml-1">📋</span>
                                    السندات المرتبطة
                                </button>
                            </div>

                            <!-- Main Actions -->
                            <div class="flex space-x-2">
                                <button onclick="exportToExcel()" class="bg-emerald-600 text-white px-3 py-2 rounded hover:bg-emerald-700 text-sm flex items-center">
                                    <span class="ml-1">📊</span>
                                    تصدير Excel
                                </button>
                                <button onclick="printInvoice()" class="bg-indigo-600 text-white px-3 py-2 rounded hover:bg-indigo-700 text-sm flex items-center">
                                    <span class="ml-1">🖨️</span>
                                    طباعة
                                </button>
                                <button onclick="printReceipt()" class="bg-teal-600 text-white px-3 py-2 rounded hover:bg-teal-700 text-sm flex items-center">
                                    <span class="ml-1">🧾</span>
                                    إيصال
                                </button>
                                <button onclick="saveDraft()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 text-sm">
                                    💾 حفظ مسودة
                                </button>
                                <button onclick="saveAndPrint()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 text-sm">
                                    🖨️ حفظ وطباعة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Include advanced libraries -->
    <script src="advanced-excel.js"></script>
    <script src="advanced-print.js"></script>
    <script src="excel-utils.js"></script>
    <script src="global-advanced-features.js"></script>
    <script src="activate-advanced-features.js"></script>

    <script>
        // Simple JavaScript for sales invoice
        function loadUserInfo() {
            // Load user information from localStorage
            const user = JSON.parse(localStorage.getItem('anwar_bakery_user') || '{}');
            if (user.name) {
                document.getElementById('userFullName').textContent = user.name;
            }
        }

        function loadCompanyInfo() {
            // Load company information from localStorage
            const company = JSON.parse(localStorage.getItem('anwar_bakery_company') || '{}');
            if (company.companyName) {
                document.getElementById('companyName').textContent = company.companyName;
            }
        }

        function toggleBuyerType() {
            const buyerType = document.querySelector('input[name="buyerType"]:checked').value;
            const buyerLabel = document.getElementById('buyerLabel');
            const buyerSelect = document.getElementById('buyerId');

            if (buyerType === 'customer') {
                buyerLabel.textContent = 'العميل *';
                buyerSelect.innerHTML = '<option value="">اختر العميل</option>';
                // Load customers here
            } else {
                buyerLabel.textContent = 'المورد *';
                buyerSelect.innerHTML = '<option value="">اختر المورد</option>';
                // Load suppliers here
            }
        }

        // Toggle bank field based on payment method
        function toggleBankField() {
            const paymentMethod = document.getElementById('paymentMethod').value;
            const bankField = document.getElementById('bankField');

            if (paymentMethod === 'bank') {
                bankField.style.display = 'block';
                document.getElementById('bankId').required = true;
            } else {
                bankField.style.display = 'none';
                document.getElementById('bankId').required = false;
            }
        }

        // Create receipt voucher from invoice data
        function createReceiptVoucher() {
            const invoiceData = {
                invoiceNumber: document.getElementById('invoiceNumber').value,
                date: document.getElementById('invoiceDate').value,
                buyerId: document.getElementById('buyerId').value,
                buyerType: document.querySelector('input[name="buyerType"]:checked').value,
                amount: document.getElementById('totalAmount').textContent,
                paymentMethod: document.getElementById('paymentMethod').value,
                bankId: document.getElementById('bankId').value,
                notes: document.getElementById('invoiceNotes').value
            };

            // Store invoice data for voucher creation
            localStorage.setItem('voucherData', JSON.stringify({
                type: 'receipt', // سند قبض
                source: 'sales_invoice',
                data: invoiceData
            }));

            // Open receipt voucher page
            window.open('receipt-voucher.html', '_blank');
        }

        // View related vouchers
        function viewVouchers() {
            const invoiceNumber = document.getElementById('invoiceNumber').value;
            if (!invoiceNumber) {
                alert('يجب حفظ الفاتورة أولاً لعرض السندات المرتبطة');
                return;
            }

            // Open vouchers list filtered by this invoice
            window.open(`vouchers.html?invoice=${invoiceNumber}`, '_blank');
        }

        function addInvoiceItem() {
            const addItemRow = document.getElementById('addItemRow');
            addItemRow.style.display = addItemRow.style.display === 'none' ? 'block' : 'none';
        }

        function cancelAddItem() {
            document.getElementById('addItemRow').style.display = 'none';
        }

        // Save draft invoice
        function saveDraft() {
            try {
                const invoiceData = collectInvoiceData();

                // Validate required fields
                if (!invoiceData.date || !invoiceData.customer_id) {
                    showMessage('يرجى ملء الحقول المطلوبة (التاريخ والعميل)', 'error');
                    return false;
                }

                // Add draft status
                invoiceData.status = 'draft';
                invoiceData.id = Date.now();
                invoiceData.createdAt = new Date().toISOString();
                invoiceData.updatedAt = new Date().toISOString();

                // Save to localStorage
                const savedResult = saveInvoiceToStorage(invoiceData);

                if (savedResult.success) {
                    showMessage('✅ تم حفظ المسودة بنجاح!', 'success');

                    // Update invoice number counter
                    updateInvoiceCounter();

                    // Generate new invoice number for next invoice
                    generateInvoiceNumber();

                    return true;
                } else {
                    showMessage('❌ خطأ في حفظ المسودة: ' + savedResult.error, 'error');
                    return false;
                }
            } catch (error) {
                console.error('Error saving draft:', error);
                showMessage('❌ حدث خطأ أثناء حفظ المسودة', 'error');
                return false;
            }
        }

        // Save and print invoice
        function saveAndPrint() {
            try {
                const invoiceData = collectInvoiceData();

                // Validate required fields
                if (!invoiceData.date || !invoiceData.customer_id) {
                    showMessage('يرجى ملء الحقول المطلوبة (التاريخ والعميل)', 'error');
                    return false;
                }

                if (!invoiceData.items || invoiceData.items.length === 0) {
                    showMessage('يرجى إضافة أصناف للفاتورة', 'error');
                    return false;
                }

                // Add confirmed status
                invoiceData.status = 'confirmed';
                invoiceData.id = Date.now();
                invoiceData.createdAt = new Date().toISOString();
                invoiceData.updatedAt = new Date().toISOString();

                // Save to localStorage
                const savedResult = saveInvoiceToStorage(invoiceData);

                if (savedResult.success) {
                    showMessage('✅ تم حفظ الفاتورة بنجاح!', 'success');

                    // Update invoice number counter
                    updateInvoiceCounter();

                    // Create accounting entries
                    createAccountingEntries(invoiceData);

                    // Update inventory
                    updateInventory(invoiceData);

                    // Print invoice
                    setTimeout(() => {
                        printInvoiceAdvanced(invoiceData);
                    }, 1000);

                    // Generate new invoice number for next invoice
                    generateInvoiceNumber();

                    return true;
                } else {
                    showMessage('❌ خطأ في حفظ الفاتورة: ' + savedResult.error, 'error');
                    return false;
                }
            } catch (error) {
                console.error('Error saving and printing:', error);
                showMessage('❌ حدث خطأ أثناء حفظ وطباعة الفاتورة', 'error');
                return false;
            }
        }

        // Advanced printing function
        function printInvoice() {
            const invoiceData = collectInvoiceData();
            if (typeof window.advancedPrint !== 'undefined') {
                window.advancedPrint.printInvoice(invoiceData, {
                    showLogo: true,
                    showHeader: true,
                    showFooter: true,
                    copies: 1,
                    paperSize: 'A4'
                });
            } else {
                // Fallback to basic print
                window.print();
            }
        }

        // Print thermal receipt
        function printReceipt() {
            const invoiceData = collectInvoiceData();
            if (typeof window.advancedPrint !== 'undefined') {
                window.advancedPrint.printReceipt(invoiceData, {
                    width: '80mm',
                    fontSize: '12px'
                });
            } else {
                alert('نظام الطباعة الحرارية غير متوفر');
            }
        }

        // Export to Excel
        function exportToExcel() {
            const invoiceData = collectInvoiceData();

            if (typeof window.advancedExcel !== 'undefined') {
                const reportData = {
                    main: [invoiceData],
                    summary: {
                        totalAmount: invoiceData.total,
                        itemsCount: invoiceData.items ? invoiceData.items.length : 0
                    }
                };

                window.advancedExcel.exportFinancialReport(
                    reportData,
                    'sales_invoice',
                    { date: invoiceData.date }
                );
            } else {
                // Fallback to basic Excel export
                if (typeof window.excelUtils !== 'undefined') {
                    const data = [{
                        'رقم الفاتورة': invoiceData.number,
                        'التاريخ': invoiceData.date,
                        'العميل': invoiceData.customer_name || 'عميل نقدي',
                        'الإجمالي': invoiceData.total
                    }];
                    window.excelUtils.exportToExcel(data, 'فاتورة_مبيعات', 'الفاتورة');
                }
            }
        }

        // Collect invoice data
        function collectInvoiceData() {
            return {
                number: document.getElementById('invoiceNumber').value,
                date: document.getElementById('invoiceDate').value,
                time: new Date().toISOString(),
                type: 'sales',
                customer_id: document.getElementById('buyerId').value,
                customer_name: document.getElementById('buyerId').selectedOptions[0]?.text || 'عميل نقدي',
                items: getInvoiceItems(),
                subtotal: parseFloat(document.getElementById('subtotalAmount').textContent) || 0,
                tax: parseFloat(document.getElementById('taxAmount').textContent) || 0,
                total: parseFloat(document.getElementById('totalAmount').textContent) || 0,
                payment_method: document.getElementById('paymentMethod').value,
                notes: document.getElementById('invoiceNotes').value
            };
        }

        // Get invoice items (placeholder)
        function getInvoiceItems() {
            // This would collect items from the table
            return [
                {
                    name: 'خبز أبيض',
                    quantity: 5,
                    unit: 'رغيف',
                    price: 2.50,
                    total: 12.50
                }
            ];
        }

        // Save invoice (placeholder)
        function saveInvoice(invoiceData) {
            try {
                // Save to localStorage
                const invoices = JSON.parse(localStorage.getItem('anwar_bakery_invoices') || '[]');
                invoices.push(invoiceData);
                localStorage.setItem('anwar_bakery_invoices', JSON.stringify(invoices));
                return true;
            } catch (error) {
                console.error('Error saving invoice:', error);
                return false;
            }
        }

        // Save invoice to localStorage
        function saveInvoiceToStorage(invoiceData) {
            try {
                // Get existing invoices
                let invoices = JSON.parse(localStorage.getItem('anwar_bakery_sales_invoices') || '[]');

                // Add new invoice
                invoices.push(invoiceData);

                // Save back to localStorage
                localStorage.setItem('anwar_bakery_sales_invoices', JSON.stringify(invoices));

                console.log('Sales invoice saved successfully:', invoiceData);
                return { success: true };
            } catch (error) {
                console.error('Error saving sales invoice:', error);
                return { success: false, error: error.message };
            }
        }

        // Update invoice counter
        function updateInvoiceCounter() {
            try {
                const currentCounter = parseInt(localStorage.getItem('anwar_bakery_sales_counter') || '0');
                localStorage.setItem('anwar_bakery_sales_counter', (currentCounter + 1).toString());
            } catch (error) {
                console.error('Error updating counter:', error);
            }
        }

        // Generate new invoice number
        function generateInvoiceNumber() {
            try {
                const counter = parseInt(localStorage.getItem('anwar_bakery_sales_counter') || '0') + 1;
                const invoiceNumber = `SAL-${new Date().getFullYear()}-${counter.toString().padStart(3, '0')}`;
                document.getElementById('invoiceNumber').value = invoiceNumber;
            } catch (error) {
                console.error('Error generating invoice number:', error);
            }
        }

        // Create accounting entries for sales
        function createAccountingEntries(invoiceData) {
            try {
                const journalEntries = [];
                const entryDate = new Date().toISOString();

                // Sales entry: Debit Cash/Customer, Credit Sales
                const salesEntry = {
                    id: Date.now(),
                    date: invoiceData.date,
                    reference: invoiceData.number,
                    description: `فاتورة مبيعات رقم ${invoiceData.number}`,
                    entries: [
                        {
                            account: invoiceData.payment_method === 'cash' ? 'الصندوق' : 'العملاء',
                            debit: invoiceData.total,
                            credit: 0
                        },
                        {
                            account: 'المبيعات',
                            debit: 0,
                            credit: invoiceData.subtotal
                        }
                    ],
                    createdAt: entryDate
                };

                // Add tax entry if applicable
                if (invoiceData.tax > 0) {
                    salesEntry.entries.push({
                        account: 'ضريبة القيمة المضافة',
                        debit: 0,
                        credit: invoiceData.tax
                    });
                }

                journalEntries.push(salesEntry);

                // Save journal entries
                const existingEntries = JSON.parse(localStorage.getItem('anwar_bakery_journal_entries') || '[]');
                existingEntries.push(...journalEntries);
                localStorage.setItem('anwar_bakery_journal_entries', JSON.stringify(existingEntries));

                console.log('Sales accounting entries created:', journalEntries);
            } catch (error) {
                console.error('Error creating accounting entries:', error);
            }
        }

        // Update inventory for sales (reduce quantities)
        function updateInventory(invoiceData) {
            try {
                if (!invoiceData.items || invoiceData.items.length === 0) return;

                // Get existing inventory
                let inventory = JSON.parse(localStorage.getItem('anwar_bakery_inventory') || '[]');

                invoiceData.items.forEach(item => {
                    const existingItem = inventory.find(inv => inv.itemCode === item.code);

                    if (existingItem) {
                        // Reduce quantity for sales
                        existingItem.quantity -= parseFloat(item.quantity);
                        existingItem.lastSalePrice = parseFloat(item.price);
                        existingItem.lastSaleDate = invoiceData.date;
                        existingItem.updatedAt = new Date().toISOString();

                        // Ensure quantity doesn't go negative
                        if (existingItem.quantity < 0) {
                            console.warn(`Warning: Negative inventory for ${item.name}`);
                        }
                    }
                });

                // Save updated inventory
                localStorage.setItem('anwar_bakery_inventory', JSON.stringify(inventory));

                console.log('Inventory updated for sales successfully');
            } catch (error) {
                console.error('Error updating inventory:', error);
            }
        }

        // Advanced print function
        function printInvoiceAdvanced(invoiceData) {
            try {
                if (typeof window.advancedPrint !== 'undefined') {
                    window.advancedPrint.printInvoice(invoiceData, {
                        showLogo: true,
                        showHeader: true,
                        showFooter: true,
                        copies: 1,
                        paperSize: 'A4',
                        orientation: 'portrait'
                    });
                    showMessage('✅ تم إرسال الفاتورة للطباعة!', 'success');
                } else {
                    // Fallback to basic print
                    window.print();
                    showMessage('✅ تم إرسال الصفحة للطباعة!', 'success');
                }
            } catch (error) {
                console.error('Error printing invoice:', error);
                showMessage('❌ خطأ في طباعة الفاتورة: ' + error.message, 'error');
            }
        }

        // Show message function
        function showMessage(message, type = 'info') {
            const messageContainer = document.getElementById('messageContainer');
            if (!messageContainer) return;

            const messageDiv = document.createElement('div');

            const bgColor = type === 'success' ? 'bg-green-100 border-green-400 text-green-700' :
                           type === 'error' ? 'bg-red-100 border-red-400 text-red-700' :
                           'bg-blue-100 border-blue-400 text-blue-700';

            messageDiv.className = `border-l-4 p-4 mb-4 ${bgColor}`;
            messageDiv.innerHTML = `
                <div class="flex">
                    <div class="flex-shrink-0">
                        <span class="text-lg">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                    </div>
                    <div class="mr-3">
                        <p class="text-sm">${message}</p>
                    </div>
                </div>
            `;

            messageContainer.appendChild(messageDiv);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 5000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadUserInfo();
            loadCompanyInfo();
            // Set today's date
            document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];

            // Generate initial invoice number
            generateInvoiceNumber();

            console.log('Sales invoice page initialized');
        });
    </script>
    <script src="activate-printing-system.js"></script>

</body>
</html>