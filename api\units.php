<?php
/**
 * Units API for Anwar Bakery Management System
 * واجهة برمجة التطبيقات لوحدات القياس - نظام إدارة مخبز أنوار الحي
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config/database.php';

class UnitsAPI {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }

    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $pathParts = explode('/', trim($path, '/'));

        // Get ID from URL path or query parameter
        $id = null;
        if (isset($pathParts[2]) && is_numeric($pathParts[2])) {
            $id = (int)$pathParts[2];
        } elseif (isset($_GET['id']) && is_numeric($_GET['id'])) {
            $id = (int)$_GET['id'];
        }

        $action = $_GET['action'] ?? null;

        try {
            switch ($method) {
                case 'GET':
                    if ($id) {
                        return $this->getUnit($id);
                    } else {
                        switch ($action) {
                            case 'types':
                                return $this->getUnitTypes();
                            case 'categories':
                                return $this->getUnitCategories();
                            case 'conversion':
                                return $this->calculateConversion();
                            default:
                                return $this->getUnits();
                        }
                    }
                    break;

                case 'POST':
                    return $this->createUnit();
                    break;

                case 'PUT':
                    if ($id) {
                        return $this->updateUnit($id);
                    }
                    return $this->sendError('Unit ID required', 400);
                    break;

                case 'DELETE':
                    if ($id) {
                        return $this->deleteUnit($id);
                    }
                    return $this->sendError('Unit ID required', 400);
                    break;

                default:
                    return $this->sendError('Method not allowed', 405);
            }
        } catch (Exception $e) {
            return $this->sendError($e->getMessage(), 500);
        }
    }

    /**
     * Get all units
     */
    private function getUnits() {
        try {
            $stmt = $this->conn->prepare("
                SELECT
                    u.*,
                    bu.name as base_unit_name,
                    bu.symbol as base_unit_symbol
                FROM units u
                LEFT JOIN units bu ON u.base_unit_id = bu.id
                WHERE u.is_active = 1
                ORDER BY u.category, u.type, u.name
            ");

            $stmt->execute();
            $units = $stmt->fetchAll();

            // Transform data to match frontend format
            $transformedUnits = array_map(function($unit) {
                return [
                    'id' => (int)$unit['id'],
                    'unitName' => $unit['name'],
                    'unitType' => $unit['type'],
                    'symbol' => $unit['symbol'],
                    'category' => $unit['category'] ?? 'general',
                    'largeUnitName' => $unit['large_unit_name'] ?? $unit['name'],
                    'largeUnitCount' => (float)($unit['large_unit_count'] ?? 1),
                    'smallUnitName' => $unit['small_unit_name'] ?? $unit['name'],
                    'smallUnitCount' => (float)($unit['small_unit_count'] ?? 1),
                    'conversionFactor' => (float)($unit['conversion_factor'] ?? 1),
                    'baseUnitId' => $unit['base_unit_id'] ? (int)$unit['base_unit_id'] : null,
                    'baseUnitName' => $unit['base_unit_name'],
                    'baseConversionFactor' => (float)($unit['base_conversion_factor'] ?? 1),
                    'isActive' => (bool)$unit['is_active'],
                    'description' => $unit['description'] ?? ($unit['name'] . ' - ' . $unit['symbol']),
                    'createdAt' => $unit['created_at'],
                    'updatedAt' => $unit['updated_at']
                ];
            }, $units);

            return $this->sendSuccess([
                'units' => $transformedUnits,
                'total' => count($transformedUnits)
            ]);

        } catch (Exception $e) {
            return $this->sendError('فشل في جلب وحدات القياس: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get single unit
     */
    private function getUnit($id) {
        try {
            $stmt = $this->conn->prepare("
                SELECT
                    u.*,
                    bu.name as base_unit_name,
                    bu.symbol as base_unit_symbol
                FROM units u
                LEFT JOIN units bu ON u.base_unit_id = bu.id
                WHERE u.id = ? AND u.is_active = 1
            ");

            $stmt->execute([$id]);
            $unit = $stmt->fetch();

            if (!$unit) {
                return $this->sendError('وحدة القياس غير موجودة', 404);
            }

            // Transform data
            $transformedUnit = [
                'id' => (int)$unit['id'],
                'unitName' => $unit['name'],
                'unitType' => $unit['type'],
                'symbol' => $unit['symbol'],
                'category' => $unit['category'] ?? 'general',
                'largeUnitName' => $unit['large_unit_name'] ?? $unit['name'],
                'largeUnitCount' => (float)($unit['large_unit_count'] ?? 1),
                'smallUnitName' => $unit['small_unit_name'] ?? $unit['name'],
                'smallUnitCount' => (float)($unit['small_unit_count'] ?? 1),
                'conversionFactor' => (float)($unit['conversion_factor'] ?? 1),
                'baseUnitId' => $unit['base_unit_id'] ? (int)$unit['base_unit_id'] : null,
                'baseUnitName' => $unit['base_unit_name'],
                'baseConversionFactor' => (float)($unit['base_conversion_factor'] ?? 1),
                'isActive' => (bool)$unit['is_active'],
                'description' => $unit['description'] ?? ($unit['name'] . ' - ' . $unit['symbol']),
                'createdAt' => $unit['created_at'],
                'updatedAt' => $unit['updated_at']
            ];

            return $this->sendSuccess(['unit' => $transformedUnit]);

        } catch (Exception $e) {
            return $this->sendError('فشل في جلب وحدة القياس: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Create new unit
     */
    private function createUnit() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);

            // Validate required fields
            if (empty($input['unitName']) || empty($input['unitType']) ||
                empty($input['largeUnitName']) || empty($input['smallUnitName']) ||
                !isset($input['smallUnitCount'])) {
                return $this->sendError('جميع الحقول الأساسية مطلوبة', 400);
            }

            // Validate numeric fields
            if (!is_numeric($input['smallUnitCount']) || $input['smallUnitCount'] <= 0) {
                return $this->sendError('عدد الوحدة الصغيرة يجب أن يكون رقم موجب', 400);
            }

            // Check if unit name already exists
            $stmt = $this->conn->prepare("SELECT id FROM units WHERE name = ? AND is_active = 1");
            $stmt->execute([$input['unitName']]);
            if ($stmt->fetch()) {
                return $this->sendError('اسم وحدة القياس موجود مسبقاً', 400);
            }

            // Insert new unit
            $stmt = $this->conn->prepare("
                INSERT INTO units (
                    name, symbol, type, category,
                    large_unit_name, large_unit_count,
                    small_unit_name, small_unit_count,
                    base_unit_id, base_conversion_factor,
                    description, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $symbol = $input['symbol'] ?? substr($input['unitName'], 0, 3);
            $category = $input['category'] ?? 'general';
            $largeUnitCount = $input['largeUnitCount'] ?? 1;
            $baseUnitId = !empty($input['baseUnitId']) ? $input['baseUnitId'] : null;
            $baseConversionFactor = $input['baseConversionFactor'] ?? 1.0;
            $description = $input['description'] ?? '';

            $result = $stmt->execute([
                $input['unitName'],
                $symbol,
                $input['unitType'],
                $category,
                $input['largeUnitName'],
                $largeUnitCount,
                $input['smallUnitName'],
                $input['smallUnitCount'],
                $baseUnitId,
                $baseConversionFactor,
                $description,
                true
            ]);

            if ($result) {
                $unitId = $this->conn->lastInsertId();
                return $this->sendSuccess([
                    'message' => 'تم إنشاء وحدة القياس بنجاح',
                    'unit_id' => $unitId
                ]);
            } else {
                return $this->sendError('فشل في إنشاء وحدة القياس', 500);
            }

        } catch (Exception $e) {
            return $this->sendError('خطأ في إنشاء وحدة القياس: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Update unit
     */
    private function updateUnit($id) {
        try {
            $input = json_decode(file_get_contents('php://input'), true);

            // Check if unit exists
            $stmt = $this->conn->prepare("SELECT id FROM units WHERE id = ? AND is_active = 1");
            $stmt->execute([$id]);
            if (!$stmt->fetch()) {
                return $this->sendError('وحدة القياس غير موجودة', 404);
            }

            // Validate required fields
            if (empty($input['unitName']) || empty($input['unitType'])) {
                return $this->sendError('اسم الوحدة ونوعها مطلوبان', 400);
            }

            // Check if new name conflicts with existing units (excluding current)
            $stmt = $this->conn->prepare("SELECT id FROM units WHERE name = ? AND id != ? AND is_active = 1");
            $stmt->execute([$input['unitName'], $id]);
            if ($stmt->fetch()) {
                return $this->sendError('اسم وحدة القياس موجود مسبقاً', 400);
            }

            // Update unit
            $stmt = $this->conn->prepare("
                UPDATE units SET
                    name = ?,
                    symbol = ?,
                    type = ?,
                    category = ?,
                    large_unit_name = ?,
                    large_unit_count = ?,
                    small_unit_name = ?,
                    small_unit_count = ?,
                    base_unit_id = ?,
                    base_conversion_factor = ?,
                    description = ?
                WHERE id = ?
            ");

            $symbol = $input['symbol'] ?? substr($input['unitName'], 0, 3);
            $category = $input['category'] ?? 'general';
            $largeUnitCount = $input['largeUnitCount'] ?? 1;
            $baseUnitId = !empty($input['baseUnitId']) ? $input['baseUnitId'] : null;
            $baseConversionFactor = $input['baseConversionFactor'] ?? 1.0;
            $description = $input['description'] ?? '';

            $result = $stmt->execute([
                $input['unitName'],
                $symbol,
                $input['unitType'],
                $category,
                $input['largeUnitName'],
                $largeUnitCount,
                $input['smallUnitName'],
                $input['smallUnitCount'],
                $baseUnitId,
                $baseConversionFactor,
                $description,
                $id
            ]);

            if ($result) {
                return $this->sendSuccess(['message' => 'تم تحديث وحدة القياس بنجاح']);
            } else {
                return $this->sendError('فشل في تحديث وحدة القياس', 500);
            }

        } catch (Exception $e) {
            return $this->sendError('خطأ في تحديث وحدة القياس: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Delete unit (soft delete)
     */
    private function deleteUnit($id) {
        try {
            // Check if unit exists
            $stmt = $this->conn->prepare("SELECT id FROM units WHERE id = ? AND is_active = 1");
            $stmt->execute([$id]);
            if (!$stmt->fetch()) {
                return $this->sendError('وحدة القياس غير موجودة', 404);
            }

            // Check if unit is used in products
            $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM products WHERE unit_id = ? AND is_active = 1");
            $stmt->execute([$id]);
            $result = $stmt->fetch();

            if ($result['count'] > 0) {
                return $this->sendError('لا يمكن حذف وحدة القياس لأنها مستخدمة في المنتجات', 400);
            }

            // Soft delete
            $stmt = $this->conn->prepare("UPDATE units SET is_active = 0 WHERE id = ?");
            $result = $stmt->execute([$id]);

            if ($result) {
                return $this->sendSuccess(['message' => 'تم حذف وحدة القياس بنجاح']);
            } else {
                return $this->sendError('فشل في حذف وحدة القياس', 500);
            }

        } catch (Exception $e) {
            return $this->sendError('خطأ في حذف وحدة القياس: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get unit types
     */
    private function getUnitTypes() {
        $types = [
            ['value' => 'weight', 'label' => 'وزن (كيلوجرام، جرام)'],
            ['value' => 'volume', 'label' => 'حجم (لتر، مل)'],
            ['value' => 'length', 'label' => 'طول (متر، سم)'],
            ['value' => 'piece', 'label' => 'قطعة (حبة، عدد)'],
            ['value' => 'other', 'label' => 'أخرى']
        ];

        return $this->sendSuccess(['types' => $types]);
    }

    /**
     * Get unit categories
     */
    private function getUnitCategories() {
        $categories = [
            ['value' => 'raw_materials', 'label' => 'مواد خام'],
            ['value' => 'finished_products', 'label' => 'منتجات تامة'],
            ['value' => 'packaging', 'label' => 'تعبئة وتغليف'],
            ['value' => 'general', 'label' => 'عام']
        ];

        return $this->sendSuccess(['categories' => $categories]);
    }

    /**
     * Calculate conversion between units
     */
    private function calculateConversion() {
        $fromUnitId = $_GET['from_unit'] ?? null;
        $toUnitId = $_GET['to_unit'] ?? null;
        $amount = $_GET['amount'] ?? 1;

        if (!$fromUnitId || !$toUnitId) {
            return $this->sendError('معرف الوحدات مطلوب', 400);
        }

        try {
            // Get both units
            $stmt = $this->conn->prepare("SELECT * FROM units WHERE id IN (?, ?) AND is_active = 1");
            $stmt->execute([$fromUnitId, $toUnitId]);
            $units = $stmt->fetchAll();

            if (count($units) !== 2) {
                return $this->sendError('إحدى وحدات القياس غير موجودة', 404);
            }

            $fromUnit = null;
            $toUnit = null;
            foreach ($units as $unit) {
                if ($unit['id'] == $fromUnitId) $fromUnit = $unit;
                if ($unit['id'] == $toUnitId) $toUnit = $unit;
            }

            // Simple conversion (can be enhanced for complex conversions)
            $convertedAmount = $amount;
            if ($fromUnit['type'] === $toUnit['type']) {
                $convertedAmount = $amount * ($fromUnit['conversion_factor'] / $toUnit['conversion_factor']);
            }

            return $this->sendSuccess([
                'from_unit' => $fromUnit['name'],
                'to_unit' => $toUnit['name'],
                'original_amount' => $amount,
                'converted_amount' => $convertedAmount,
                'conversion_rate' => $fromUnit['conversion_factor'] / $toUnit['conversion_factor']
            ]);

        } catch (Exception $e) {
            return $this->sendError('خطأ في حساب التحويل: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Send success response
     */
    private function sendSuccess($data, $code = 200) {
        http_response_code($code);
        echo json_encode([
            'success' => true,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * Send error response
     */
    private function sendError($message, $code = 400) {
        http_response_code($code);
        echo json_encode([
            'success' => false,
            'error' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// Initialize and handle request
try {
    $api = new UnitsAPI();
    $api->handleRequest();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في الخادم: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}
?>
