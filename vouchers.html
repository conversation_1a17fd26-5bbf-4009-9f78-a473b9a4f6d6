<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة السندات - مخبز أنوار الحي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .custom-scroll {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 #f7fafc;
        }

        .custom-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scroll::-webkit-scrollbar-track {
            background: #f7fafc;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        .voucher-card {
            transition: all 0.3s ease;
        }

        .voucher-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Mobile menu overlay -->
    <div id="mobileOverlay" class="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 lg:hidden hidden"></div>

    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform translate-x-full lg:translate-x-0 lg:static lg:inset-0 sidebar-transition">
            <div class="h-16 px-4 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between">
                <h1 id="sidebarCompanyName" class="text-white text-lg font-bold">مخبز أنوار الحي</h1>
                <button onclick="toggleSidebar()" class="lg:hidden text-white">
                    <span class="text-xl">✕</span>
                </button>
            </div>

            <nav class="mt-5 px-2 space-y-1 h-full overflow-y-auto pb-20">
                <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏠</span>
                    لوحة التحكم
                </a>
                <a href="users-management.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👥</span>
                    إدارة المستخدمين
                </a>
                <a href="company-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏢</span>
                    بيانات المنشأة
                </a>
                <a href="system-settings.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">⚙️</span>
                    إعدادات النظام
                </a>
                <a href="system-admin.html" class="flex items-center px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 hover:text-red-700 rounded-md border border-red-200">
                    <span class="ml-3">🔧</span>
                    إدارة النظام المتقدمة
                </a>
                <a href="branches-warehouses.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏪</span>
                    الفروع والمخازن
                </a>
                <a href="units.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📏</span>
                    وحدات القياس
                </a>
                <a href="chart-of-accounts.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🌳</span>
                    شجرة الحسابات
                </a>
                <a href="products.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📦</span>
                    إدارة الأصناف
                </a>
                <a href="damage-entry.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🗑️</span>
                    قيد التالف
                </a>
                <a href="customers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👤</span>
                    العملاء
                </a>
                <a href="suppliers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🚚</span>
                    الموردين
                </a>
                <a href="employees.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👨‍💼</span>
                    الموظفين
                </a>
                <a href="cash-registers.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">💰</span>
                    الصناديق
                </a>
                <a href="banks.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🏦</span>
                    البنوك
                </a>
                <a href="owners.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">👑</span>
                    الملاك
                </a>
                <a href="invoices.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">🧾</span>
                    الفواتير
                </a>
                <a href="vouchers.html" class="flex items-center px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md">
                    <span class="ml-3">📋</span>
                    السندات
                </a>
                <a href="journal-entry.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📝</span>
                    القيود اليومية
                </a>
                <a href="inventory.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📊</span>
                    المخزون
                </a>
                <a href="reports.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">
                    <span class="ml-3">📈</span>
                    التقارير
                </a>

                <!-- User Info & Logout -->
                <div class="absolute bottom-4 left-2 right-2">
                    <div class="bg-gray-50 rounded-lg p-3 mb-2">
                        <p id="userFullName" class="text-sm font-medium text-gray-900">مدير النظام</p>
                        <p id="userRole" class="text-xs text-gray-500">admin</p>
                    </div>
                    <button onclick="logout()" class="w-full text-right px-3 py-2 text-sm font-medium rounded-md text-red-600 hover:bg-red-50 flex items-center">
                        <span class="ml-3">🚪</span>
                        تسجيل الخروج
                    </button>
                </div>
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col min-h-0">
            <!-- Top bar -->
            <div class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <button onclick="toggleSidebar()" class="lg:hidden text-gray-500 hover:text-gray-700 ml-4">
                                <span class="text-xl">☰</span>
                            </button>
                            <span class="text-lg font-semibold text-gray-900">إدارة السندات</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-500" id="currentDateTime"></span>
                            <div class="flex items-center space-x-2">
                                <button onclick="openJournalEntry()" class="bg-purple-600 text-white px-3 py-2 rounded hover:bg-purple-700 flex items-center text-sm">
                                    <span class="ml-1">⚖️</span>
                                    قيد محاسبي
                                </button>
                                <button onclick="openPaymentVoucher()" class="bg-red-600 text-white px-3 py-2 rounded hover:bg-red-700 flex items-center text-sm">
                                    <span class="ml-1">📤</span>
                                    سند صرف
                                </button>
                                <button onclick="openReceiptVoucher()" class="bg-green-600 text-white px-3 py-2 rounded hover:bg-green-700 flex items-center text-sm">
                                    <span class="ml-1">📥</span>
                                    سند قبض
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto p-6 custom-scroll">
                <!-- Header -->
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900 mb-2 flex items-center">
                        <span class="ml-3">📋</span>
                        إدارة السندات والقيود المحاسبية
                    </h1>
                    <p class="text-gray-600">
                        نظام شامل لإدارة سندات القبض والصرف والقيود المحاسبية مع ربط تلقائي بشجرة الحسابات
                    </p>
                </div>

                <!-- Success/Error Messages -->
                <div id="messageContainer" class="mb-6"></div>

                <!-- Voucher Types Cards -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <!-- Receipt Voucher -->
                    <div class="voucher-card bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg p-6 text-white cursor-pointer" onclick="openReceiptVoucher()">
                        <div class="flex items-center justify-between mb-4">
                            <div class="text-4xl">📥</div>
                            <div class="text-right">
                                <h3 class="text-xl font-bold">سند قبض</h3>
                                <p class="text-green-100 text-sm">استلام نقدية</p>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-green-100 text-sm">اليوم</span>
                            <span class="text-2xl font-bold" id="todayReceipts">0</span>
                        </div>
                    </div>

                    <!-- Payment Voucher -->
                    <div class="voucher-card bg-gradient-to-br from-red-500 to-red-600 rounded-xl shadow-lg p-6 text-white cursor-pointer" onclick="openPaymentVoucher()">
                        <div class="flex items-center justify-between mb-4">
                            <div class="text-4xl">📤</div>
                            <div class="text-right">
                                <h3 class="text-xl font-bold">سند صرف</h3>
                                <p class="text-red-100 text-sm">دفع نقدية</p>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-red-100 text-sm">اليوم</span>
                            <span class="text-2xl font-bold" id="todayPayments">0</span>
                        </div>
                    </div>

                    <!-- Journal Entry -->
                    <div class="voucher-card bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg p-6 text-white cursor-pointer" onclick="openJournalEntry()">
                        <div class="flex items-center justify-between mb-4">
                            <div class="text-4xl">⚖️</div>
                            <div class="text-right">
                                <h3 class="text-xl font-bold">قيد محاسبي</h3>
                                <p class="text-purple-100 text-sm">قيد مرن</p>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-purple-100 text-sm">اليوم</span>
                            <span class="text-2xl font-bold" id="todayJournals">0</span>
                        </div>
                    </div>
                </div>

                <!-- Recent Vouchers -->
                <div class="bg-white rounded-xl shadow-lg">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold text-gray-900">آخر السندات</h3>
                            <div class="flex space-x-2">
                                <select id="voucherTypeFilter" onchange="filterVouchers()" class="px-3 py-1 border border-gray-300 rounded text-sm">
                                    <option value="">جميع الأنواع</option>
                                    <option value="receipt">سندات قبض</option>
                                    <option value="payment">سندات صرف</option>
                                    <option value="journal">قيود محاسبية</option>
                                </select>
                                <button onclick="viewAllVouchers()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    عرض الكل
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم السند</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">النوع</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">المبلغ</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">البيان</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الفاتورة المرتبطة</th>
                                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="vouchersTableBody" class="bg-white divide-y divide-gray-200">
                                <!-- Vouchers will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="vouchers.js"></script>
</body>
</html>
