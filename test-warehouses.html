<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المخازن</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">اختبار تحميل المخازن</h1>
        
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4">قائمة المخازن المحفوظة</h2>
            <div id="warehousesList" class="space-y-2"></div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4">اختب<PERSON>ر القائمة المنسدلة</h2>
            <select id="testWarehouseSelect" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                <option value="">اختر المخزن</option>
            </select>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-lg font-semibold mb-4">إجراءات الاختبار</h2>
            <div class="space-x-2">
                <button onclick="loadAndDisplayWarehouses()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    تحميل المخازن
                </button>
                <button onclick="addTestWarehouse()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    إضافة مخزن تجريبي
                </button>
                <button onclick="clearWarehouses()" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                    مسح المخازن
                </button>
                <button onclick="populateDropdown()" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                    تحديث القائمة المنسدلة
                </button>
            </div>
        </div>
    </div>

    <script>
        // تحميل وعرض المخازن
        function loadAndDisplayWarehouses() {
            const savedWarehouses = localStorage.getItem('anwar_bakery_warehouses');
            const warehousesList = document.getElementById('warehousesList');
            
            if (savedWarehouses) {
                const warehouses = JSON.parse(savedWarehouses);
                warehousesList.innerHTML = '';
                
                if (warehouses.length === 0) {
                    warehousesList.innerHTML = '<p class="text-gray-500">لا توجد مخازن محفوظة</p>';
                } else {
                    warehouses.forEach(warehouse => {
                        const div = document.createElement('div');
                        div.className = 'p-3 border border-gray-200 rounded';
                        div.innerHTML = `
                            <div class="font-semibold">${warehouse.name || warehouse.warehouseName || 'بدون اسم'}</div>
                            <div class="text-sm text-gray-600">ID: ${warehouse.id} | نشط: ${warehouse.isActive ? 'نعم' : 'لا'}</div>
                            <div class="text-sm text-gray-600">${warehouse.description || 'بدون وصف'}</div>
                        `;
                        warehousesList.appendChild(div);
                    });
                }
                
                console.log('المخازن المحملة:', warehouses);
            } else {
                warehousesList.innerHTML = '<p class="text-red-500">لا توجد مخازن في التخزين المحلي</p>';
                console.log('لا توجد مخازن في localStorage');
            }
        }

        // إضافة مخزن تجريبي
        function addTestWarehouse() {
            const savedWarehouses = localStorage.getItem('anwar_bakery_warehouses');
            let warehouses = savedWarehouses ? JSON.parse(savedWarehouses) : [];
            
            const newWarehouse = {
                id: Date.now(),
                name: `مخزن تجريبي ${warehouses.length + 1}`,
                description: 'مخزن تجريبي للاختبار',
                location: 'موقع تجريبي',
                manager: 'مدير تجريبي',
                phone: '0501234567',
                capacity: 100,
                currentUtilization: 50,
                isActive: true,
                createdAt: new Date().toISOString().split('T')[0]
            };
            
            warehouses.push(newWarehouse);
            localStorage.setItem('anwar_bakery_warehouses', JSON.stringify(warehouses));
            
            console.log('تم إضافة مخزن تجريبي:', newWarehouse);
            loadAndDisplayWarehouses();
            populateDropdown();
        }

        // مسح جميع المخازن
        function clearWarehouses() {
            if (confirm('هل أنت متأكد من مسح جميع المخازن؟')) {
                localStorage.removeItem('anwar_bakery_warehouses');
                console.log('تم مسح جميع المخازن');
                loadAndDisplayWarehouses();
                populateDropdown();
            }
        }

        // تحديث القائمة المنسدلة
        function populateDropdown() {
            const select = document.getElementById('testWarehouseSelect');
            const savedWarehouses = localStorage.getItem('anwar_bakery_warehouses');
            
            select.innerHTML = '<option value="">اختر المخزن</option>';
            
            if (savedWarehouses) {
                const warehouses = JSON.parse(savedWarehouses);
                const activeWarehouses = warehouses.filter(warehouse => warehouse.isActive);
                
                activeWarehouses.forEach(warehouse => {
                    const option = document.createElement('option');
                    option.value = warehouse.id;
                    option.textContent = warehouse.name || warehouse.warehouseName || `مخزن ${warehouse.id}`;
                    select.appendChild(option);
                });
                
                console.log('تم تحديث القائمة المنسدلة بـ', activeWarehouses.length, 'مخزن');
            } else {
                console.log('لا توجد مخازن لتحديث القائمة المنسدلة');
            }
        }

        // إنشاء المخازن الافتراضية
        function createDefaultWarehouses() {
            const defaultWarehouses = [
                {
                    id: 1,
                    name: 'المخزن الرئيسي',
                    description: 'المخزن الرئيسي للمخبز',
                    location: 'الطابق الأرضي',
                    manager: 'أحمد محمد',
                    phone: '0501234567',
                    capacity: 1000,
                    currentUtilization: 650,
                    isActive: true,
                    createdAt: '2024-01-01'
                },
                {
                    id: 2,
                    name: 'مخزن المواد الخام',
                    description: 'مخزن خاص بالمواد الخام والمكونات',
                    location: 'الطابق السفلي',
                    manager: 'فاطمة أحمد',
                    phone: '0507654321',
                    capacity: 500,
                    currentUtilization: 320,
                    isActive: true,
                    createdAt: '2024-01-01'
                },
                {
                    id: 3,
                    name: 'مخزن المنتجات النهائية',
                    description: 'مخزن المنتجات الجاهزة للبيع',
                    location: 'الطابق الأول',
                    manager: 'محمد علي',
                    phone: '0509876543',
                    capacity: 800,
                    currentUtilization: 450,
                    isActive: true,
                    createdAt: '2024-01-01'
                }
            ];
            
            localStorage.setItem('anwar_bakery_warehouses', JSON.stringify(defaultWarehouses));
            console.log('تم إنشاء المخازن الافتراضية');
            loadAndDisplayWarehouses();
            populateDropdown();
        }

        // تحميل تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل الصفحة، جاري فحص المخازن...');
            
            const savedWarehouses = localStorage.getItem('anwar_bakery_warehouses');
            if (!savedWarehouses) {
                console.log('لا توجد مخازن، سيتم إنشاء المخازن الافتراضية');
                createDefaultWarehouses();
            } else {
                console.log('توجد مخازن محفوظة، سيتم تحميلها');
                loadAndDisplayWarehouses();
                populateDropdown();
            }
        });

        // إضافة زر لإنشاء المخازن الافتراضية
        document.addEventListener('DOMContentLoaded', function() {
            const actionsDiv = document.querySelector('.space-x-2');
            const defaultButton = document.createElement('button');
            defaultButton.onclick = createDefaultWarehouses;
            defaultButton.className = 'bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700';
            defaultButton.textContent = 'إنشاء المخازن الافتراضية';
            actionsDiv.appendChild(defaultButton);
        });
    </script>
</body>
</html>
